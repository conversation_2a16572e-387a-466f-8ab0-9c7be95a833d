(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-device-admin-components-ThirdPartyEquipmentDialog"],{"6ecf":function(e,i,t){"use strict";t.r(i);var n=function(){var e=this,i=e.$createElement,t=e._self._c||i;return t("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width,"destroy-on-close":!1},on:{"update:show":function(i){e.visible=i},"update:loading":function(i){e.isLoading=i},close:e.handleClose}},[e.visible?t("el-form",{ref:"dialogForm",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("div",[t("el-form-item",{attrs:{label:"所属组织：",prop:"organization"}},[t("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,role:"super","append-to-body":!0,filterable:!1},model:{value:e.dialogForm.organization,callback:function(i){e.$set(e.dialogForm,"organization",i)},expression:"dialogForm.organization"}})],1),t("el-form-item",{attrs:{label:"设备编号：",prop:"device_number"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入配送点名称",maxlength:"20"},model:{value:e.dialogForm.device_number,callback:function(i){e.$set(e.dialogForm,"device_number",i)},expression:"dialogForm.device_number"}})],1),t("el-form-item",{attrs:{label:"设备类型：",prop:"device_type"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.dialogForm.device_type,callback:function(i){e.$set(e.dialogForm,"device_type",i)},expression:"dialogForm.device_type"}},e._l(e.deviceTypeList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),t("el-form-item",{attrs:{label:"设备型号：",prop:"device_model"}},[t("el-select",{staticClass:"ps-select w-250",attrs:{placeholder:"请选择设备型号","popper-class":"ps-popper-select"},model:{value:e.dialogForm.device_model,callback:function(i){e.$set(e.dialogForm,"device_model",i)},expression:"dialogForm.device_model"}},e._l(e.deviceModelList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1)],1)]):e._e(),t("template",{slot:"tool"},[t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},o=[],a=t("a34a"),r=t.n(a),s=t("cbfb");function c(e,i,t,n,o,a,r){try{var s=e[a](r),c=s.value}catch(d){return void t(d)}s.done?i(c):Promise.resolve(c).then(n,o)}function d(e){return function(){var i=this,t=arguments;return new Promise((function(n,o){var a=e.apply(i,t);function r(e){c(a,n,o,r,s,"next",e)}function s(e){c(a,n,o,r,s,"throw",e)}r(void 0)}))}}var l={name:"accountDialog",components:{OrganizationSelect:s["a"]},props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"500px"},isshow:Boolean,confirm:Function,equipmentInfo:{type:Object,default:function(){}}},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,time:(new Date).getTime(),dialogForm:{organization:"",device_number:"",device_type:"",device_model:""},allChildId:[],dialogFormRules:{organization:[{required:!0,message:"请选择组织",trigger:"blur"}],device_number:[{required:!0,message:"请选择设备编号",trigger:"blur"}],device_type:[{required:!0,message:"请选择设备类型",trigger:"change"}],device_model:[{required:!0,message:"请选择设备型号",trigger:"change"}]},deviceTypeList:[],deviceModelList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){this.initLoad()}},created:function(){this.getDeviceType()},mounted:function(){},methods:{initLoad:function(){var e=this;"modify"===this.type&&this.$nextTick((function(){e.dialogForm={id:e.equipmentInfo.id,organization:e.equipmentInfo.organization,device_number:e.equipmentInfo.device_number,device_type:e.equipmentInfo.device_type,device_model:e.equipmentInfo.device_model}}))},clickConfirmHandle:function(e){var i=this;this.$refs.dialogForm.validate((function(e){e&&("add"===i.type?i.confirmAdd(i.dialogForm):i.modifyAddress(i.dialogForm))}))},confirmAdd:function(e){var i=this;return d(r.a.mark((function t(){var n;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!i.isLoading){t.next=2;break}return t.abrupt("return");case 2:return i.isLoading=!0,t.next=5,i.$apis.apiBackgroundDeviceAdminThirdDeviceAddPost(e);case 5:n=t.sent,i.isLoading=!1,0===n.code?(i.confirm(),i.$message.success(n.msg)):i.$message.error(n.msg);case 8:case"end":return t.stop()}}),t)})))()},modifyAddress:function(e){var i=this;return d(r.a.mark((function t(){var n;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!i.isLoading){t.next=2;break}return t.abrupt("return");case 2:return i.isLoading=!0,t.next=5,i.$apis.apiBackgroundDeviceAdminThirdDeviceModifyPost(e);case 5:n=t.sent,i.isLoading=!1,0===n.code?(i.confirm(),i.$message.success(n.msg)):i.$message.error(n.msg);case 8:case"end":return t.stop()}}),t)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.dialogForm={organization:"",device_number:"",device_type:"",device_model:""},this.isLoading=!1,this.visible=!1},getOrganization:function(e){this.dialogForm.organization=e},getDeviceType:function(){var e=this;return d(r.a.mark((function i(){var t;return r.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.$apis.apiBackgroundAdminDeviceThirdDeviceTypePost();case 2:t=i.sent,0===t.code?e.deviceTypeList=t.data:e.$message.error(t.msg);case 4:case"end":return i.stop()}}),i)})))()},deviceTypeChange:function(){this.dialogForm.device_model="",this.getDeviceModel()},getDeviceModel:function(){var e=this;return d(r.a.mark((function i(){var t;return r.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.$apis.apiBackgroundAdminDeviceDeviceModelPost({device_type:e.dialogForm.device_type});case 2:t=i.sent,0===t.code?e.deviceModelList=t.data:e.$message.error(t.msg);case 4:case"end":return i.stop()}}),i)})))()}}},u=l,p=(t("daac"),t("2877")),m=Object(p["a"])(u,n,o,!1,null,null,null);i["default"]=m.exports},daac:function(e,i,t){"use strict";var n=t("fbbb"),o=t.n(n);o.a},fbbb:function(e,i,t){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);
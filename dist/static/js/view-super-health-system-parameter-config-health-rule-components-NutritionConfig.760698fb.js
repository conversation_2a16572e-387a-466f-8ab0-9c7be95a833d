(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-rule-components-NutritionConfig"],{"140d":function(t,e,o){},"8d88":function(t,e,o){"use strict";var s=o("140d"),a=o.n(s);a.a},dfaf:function(t,e,o){"use strict";o.r(e);var s=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"nutrition"},[o("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[o("div",{staticClass:"table-wrapper"},[o("div",{staticClass:"table-header"},[o("div",{staticClass:"table-title"},[t._v("基本信息")])]),o("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[o("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[o("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[o("template",{slot:"append"},[t._v("分")])],2)],1),o("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[o("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.formData.config,(function(e,s,a){return o("div",{key:a,staticClass:"form-content-box m-b-20"},[o("div",[t._v("变量值")]),t._l(e.listText,(function(e,s){return o("div",{key:s,staticClass:"p-t-10"},[t._v(" "+t._s(e.text)+" "),o("span",{staticStyle:{color:"red"}},[t._v(t._s(e.tips))])])})),o("div",{staticClass:"p-t-10 p-b-10 flex-between"},[o("div",[t._v("规则配置")]),t.disabled?t._e():o("div",{staticClass:"align-r"},[o("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(o){return t.addRule(e,s)}}},[t._v(" 新增规则 ")])],1)]),o("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),o("div",{staticClass:"ps-flex-align-c"},[o("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),o("div",t._l(e.content,(function(a,i){return o("div",{key:i,staticClass:"ps-flex-align-c flex-wrap"},[o("span",{staticClass:"p-r-10 p-t-5"},[t._v(t._s(e.unitText))]),o("el-form-item",{attrs:{label:""}},[o("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_one,callback:function(e){t.$set(a,"comparison_one",e)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,e){return o("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),o("div",{staticClass:"p-l-10"},[o("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[o("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_one_score,callback:function(e){t.$set(a,"comparison_one_score",e)},expression:"contentItem.comparison_one_score"}},[o("template",{slot:"append"},[t._v("%")])],2)],1)],1),o("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),o("el-form-item",{attrs:{label:""}},[o("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.comparison_two,callback:function(e){t.$set(a,"comparison_two",e)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,e){return o("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),o("div",{staticClass:"p-l-10"},[o("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[o("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.comparison_two_score,callback:function(e){t.$set(a,"comparison_two_score",e)},expression:"contentItem.comparison_two_score"}},[o("template",{slot:"append"},[t._v("%")])],2)],1)],1),o("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[o("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:a.operation,callback:function(e){t.$set(a,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return o("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),o("div",{staticClass:"p-l-10"},[o("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[o("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:a.operation_score,callback:function(e){t.$set(a,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),o("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),o("div",{staticClass:"m-b-30"},[e.content.length>1&&!t.disabled?o("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(a,s,i)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)}))],2)]),o("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[o("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),o("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},a=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",config:{protein:{listText:[{text:"x克",tips:"（用户每餐蛋白质摄入量）"},{text:"y克",tips:"（查表法计算得出用户每餐蛋白质推荐摄入量）"}],unitText:"x完成度在y正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},carbohydrate:{listText:[{text:"x1克",tips:"（用户每餐碳水化合物摄入量）"},{text:"y1克",tips:"（查表法计算得出用户每餐碳水化合物推荐摄入量）"}],unitText:"x1完成度在y1正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},axunge:{listText:[{text:"x2克",tips:"（用户每餐脂肪摄入量）"},{text:"y2克/餐",tips:"（查表法计算得出用户每餐脂肪推荐摄入量）"}],unitText:"x2完成度在y2正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]}}},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){for(var t in console.log(this.data),this.formData.score=this.data.score,this.formData.tips=this.data.tips,this.formData.type=this.data.type,this.data.config)this.formData.config[t].content=this.data.config[t]},addRule:function(t,e){console.log(this.formData.config[e]),this.formData.config[e].content.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t,e,o){this.formData.config[e].content.splice(o,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var o={key:t.formData.type},s={};for(var a in t.formData.config)s[a]=t.formData.config[a].content;o[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:s},t.$emit("submitHandler",o)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,o,s){"confirm"===e?t.$closeCurrentTab(t.$route.path):o.confirmButtonLoading||s()}}).then((function(t){})).catch((function(t){}))}}},n=i,l=(o("8d88"),o("2877")),r=Object(l["a"])(n,s,a,!1,null,"12e29f82",null);e["default"]=r.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-AddOrEditMemberPermission"],{"3c14":function(e,r,t){"use strict";var s=t("c67a"),i=t.n(s);i.a},"7f85":function(e,r,t){"use strict";t.r(r);var s=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"AddOrEditMemberPermission container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新建":"编辑")+"会员权限")])]),t("div",[t("el-form",{ref:"memberFormRef",attrs:{model:e.memberForm,rules:e.memberFormRules,"label-width":"180px"}},[t("el-form-item",{attrs:{label:"权限名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"15"},model:{value:e.memberForm.name,callback:function(r){e.$set(e.memberForm,"name",r)},expression:"memberForm.name"}})],1),t("el-form-item",{attrs:{label:"权限说明：",prop:"remark"}},[t("el-input",{staticClass:"ps-input w-250",attrs:{type:"textarea",rows:3,maxlength:"140","show-word-limit":""},model:{value:e.memberForm.remark,callback:function(r){e.$set(e.memberForm,"remark",r)},expression:"memberForm.remark"}})],1),t("el-form-item",{attrs:{label:"权限：",prop:"permission"}},[t("select-tree",e._g(e._b({staticClass:"search-item-w w-250",attrs:{treeData:e.permissionList,treeProps:e.treeProps,loadTree:e.getMemberPermission},model:{value:e.memberForm.permission,callback:function(r){e.$set(e.memberForm,"permission",r)},expression:"memberForm.permission"}},"select-tree",e.$attrs,!1),e.$listeners))],1),t("el-form-item",[t("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:e.saveSetting}},[e._v("保存")])],1)],1)],1)])])},i=[],a=t("a34a"),n=t.n(a),m=t("fb36");function o(e,r,t,s,i,a,n){try{var m=e[a](n),o=m.value}catch(c){return void t(c)}m.done?r(o):Promise.resolve(o).then(s,i)}function c(e){return function(){var r=this,t=arguments;return new Promise((function(s,i){var a=e.apply(r,t);function n(e){o(a,s,i,n,m,"next",e)}function m(e){o(a,s,i,n,m,"throw",e)}n(void 0)}))}}var p={name:"AddOrEditMemberPermission",components:{SelectTree:m["a"]},props:{},data:function(){return{isLoading:!1,type:"",settingData:{},memberForm:{name:"",remark:"",permission:[]},memberFormRules:{name:[{required:!0,message:"请输入权限名称",trigger:"blur"}],permission:[{required:!0,message:"请选择权限",trigger:"blur"}]},permissionList:[],treeProps:{value:"key",label:"verbose_name",isLeaf:"is_leaf",children:"children"}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.query.data&&(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.memberForm.name=this.settingData.name,this.memberForm.permission=this.settingData.permission,this.memberForm.remark=this.settingData.remark),this.$route.params.type&&(this.type=this.$route.params.type),this.getMemberPermission()},saveSetting:function(){var e=this;this.$refs.memberFormRef.validate((function(r){if(r){var t,s={name:e.memberForm.name,permission:e.memberForm.permission};switch(e.memberForm.remark&&(s.remark=e.memberForm.remark),e.type){case"add":t=e.$apis.apiBackgroundMemberMemberPermissionAddPost(s);break;case"edit":s.id=Number(e.settingData.id),t=e.$apis.apiBackgroundMemberMemberPermissionModifyPost(s);break}e.confirmOperation(t)}}))},confirmOperation:function(e){var r=this;return c(n.a.mark((function t(){var s;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r.isLoading){t.next=2;break}return t.abrupt("return");case 2:return r.isLoading=!0,t.next=5,e;case 5:s=t.sent,r.isLoading=!1,0===s.code?(r.$message.success("成功"),r.$closeCurrentTab(r.$route.path)):r.$message.error(s.msg);case 8:case"end":return t.stop()}}),t)})))()},getMemberPermission:function(){var e=this;return c(n.a.mark((function r(){var t;return n.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,e.$apis.apiBackgroundMemberMemberPermissionGetMerchantPermissionsPost({page:1,page_size:99999});case 3:t=r.sent,e.isLoading=!1,0===t.code?e.permissionList=t.data:e.$message.error(t.msg);case 6:case"end":return r.stop()}}),r)})))()},addLabel:function(){this.memberForm.permissions.push("")},delLabel:function(e){this.memberForm.permissions.splice(e,1)}}},l=p,u=(t("3c14"),t("2877")),d=Object(u["a"])(l,s,i,!1,null,"31e66d84",null);r["default"]=d.exports},c67a:function(e,r,t){}}]);
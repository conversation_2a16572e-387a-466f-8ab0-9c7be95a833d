(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-label-admin-components-platformTag","view-super-health-system-label-admin-components-labelGroupDialog"],{"05ce":function(e,t,a){"use strict";var r=a("bfa4"),i=a.n(r);i.a},"5a4c":function(e,t,a){},"843f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}]},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("标签组")]),a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.clickTagDialog("add")}}},[e._v(" 添加标签组 ")])],1)]),e._l(e.tableData,(function(t,r){return a("div",{key:r,staticClass:"content-box"},[a("div",{staticClass:"ps-flex-bw"},[a("div",[e._v(" "+e._s(t.name)+" "),a("span",[e._v("（"+e._s(t.label_list.length)+"）")])]),a("div",{staticClass:"rnge-title ps-flex-align-c flex-align-c"},[e._v(" 可见范围； "),"all"===t.visible?a("span",[e._v(e._s(t.visible_alias))]):e._e(),"part"===t.visible?a("div",[a("el-popover",{attrs:{placement:"top-start",width:"200",trigger:"hover"}},[e._l(t.visible_organization_list,(function(r,i){return a("span",{key:i},[e._v(" "+e._s(i===t.visible_organization_list.length-1&&r.name||r.name+"、")+" ")])})),a("div",{staticClass:"part-box",attrs:{slot:"reference"},slot:"reference"},e._l(t.visible_organization_list,(function(r,i){return a("span",{key:i},[e._v(" "+e._s(i===t.visible_organization_list.length-1&&r.name||r.name+"、")+" ")])})),0)],2)],1):e._e()])]),a("div",{staticClass:"tag-content"},[a("div",{staticClass:"tag-box"},[t.inputVisible?a("el-input",{ref:"saveTagInput"+t.id,refInFor:!0,staticClass:"ps-input w-100  p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(a){return e.handleInputConfirm(t)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputConfirm(t)}},model:{value:t.inputValue,callback:function(a){e.$set(t,"inputValue",a)},expression:"item.inputValue"}}):a("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(a){return e.showInput(t)}}},[e._v(" 添加标签 ")]),e._l(t.label_list,(function(t,r){return a("el-tag",{key:r,staticClass:"m-r-5 m-t-5",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff"}},[e._v(" "+e._s(t.name)+" ")])}))],2),a("div",{staticClass:"fun-click"},[a("el-button",{staticClass:"ps-origin",attrs:{type:"text",size:"small",icon:"el-icon-edit"},on:{click:function(a){return e.clickTagDialog("modify",t)}}},[e._v(" 编辑 ")]),a("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small",icon:"el-icon-delete"},on:{click:function(a){return e.tagDelClick(t)}}},[e._v(" 删除 ")])],1)])])})),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),e.labelRroupDialogVisible?a("label-group-dialog",{attrs:{title:e.labelRroupTitle,type:e.formOperate,isshow:e.labelRroupDialogVisible,visibleType:"food",labelRroupInfo:e.labelRroupInfo,confirm:e.getLabelGroupList,width:"600px"},on:{"update:isshow":function(t){e.labelRroupDialogVisible=t}}}):e._e()],2)},i=[],n=a("a34a"),o=a.n(n),l=a("de5c"),s=a("ed08");function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function b(e,t){return h(e)||g(e,t)||d(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function g(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,i=!1,n=void 0;try{for(var o,l=e[Symbol.iterator]();!(r=(o=l.next()).done);r=!0)if(a.push(o.value),t&&a.length===t)break}catch(s){i=!0,n=s}finally{try{r||null==l["return"]||l["return"]()}finally{if(i)throw n}}return a}}function h(e){if(Array.isArray(e))return e}function v(e,t,a,r,i,n,o){try{var l=e[n](o),s=l.value}catch(u){return void a(u)}l.done?t(s):Promise.resolve(s).then(r,i)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function o(e){v(n,r,i,o,l,"next",e)}function l(e){v(n,r,i,o,l,"throw",e)}o(void 0)}))}}var w={props:{searchFormSetting:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,tableData:[],dialogIsLoading:!1,labelRroupTitle:"添加标签组",labelRroupDialogVisible:!1,formOperate:"add",labelRroupInfo:{},totalPageSize:0,pageSize:10,totalCount:0,currentPage:1}},components:{labelGroupDialog:l["default"]},created:function(){this.getLabelGroupList()},mounted:function(){},methods:{getLabelGroupList:function(){var e=this;return y(o.a.mark((function t(){var a,r,i,n;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(c(c({type:"food"},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,r=b(a,2),i=r[0],n=r[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===n.code?(e.totalCount=n.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=n.data.results.map((function(e){return e.inputVisible=!1,e.inputValue="",e}))):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},clickTagDialog:function(e,t){this.formOperate=e,this.labelRroupTitle="add"===e?"添加标签组":"修改标签组","modify"===e&&(this.labelRroupInfo=t),this.labelRroupDialogVisible=!0},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(a){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return y(o.a.mark((function a(){var r,i,n,l;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(r=a.sent,i=b(r,2),n=i[0],l=i[1],t.isLoading=!1,!n){a.next=11;break}return t.$message.error(n.message),a.abrupt("return");case 11:0===l.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(l.msg);case 12:case"end":return a.stop()}}),a)})))()},weightClick:function(e,t){var a={weight:0,id:t.id};a.weight="up"===e?t.weight-1:t.weight+1,this.getLabelGroupModifyWeight(a)},getLabelGroupModifyWeight:function(e){var t=this;return y(o.a.mark((function a(){var r,i,n,l;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupModifyWeightPost(e));case 3:if(r=a.sent,i=b(r,2),n=i[0],l=i[1],t.isLoading=!1,!n){a.next=11;break}return t.$message.error(n.message),a.abrupt("return");case 11:0===l.code?t.getLabelGroupList():t.$message.error(l.msg);case 12:case"end":return a.stop()}}),a)})))()},tagDelClick:function(e){var t=this;this.$confirm("确定删除该标签组？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var a=y(o.a.mark((function a(r,i,n){var l;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==r){a.next=9;break}return a.next=3,t.$apis.apiBackgroundHealthyAdminLabelGroupDeletePost({ids:[e.id]});case 3:l=a.sent,0===l.code?(t.$message.success("删除成功"),t.currentPage>1&&1===t.tableData.length&&t.currentPage--,t.getLabelGroupList()):t.$message.error(l.msg),n(),i.confirmButtonLoading=!1,a.next=10;break;case 9:i.confirmButtonLoading||n();case 10:case"end":return a.stop()}}),a)})));function r(e,t,r){return a.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}))},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()},formatQueryParams:function(e){var t={};for(var a in e)""!==e[a].value&&("select_time"!==a?t[a]=e[a].value:e[a].value&&e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]));return t}}},_=w,L=(a("8b81"),a("2877")),k=Object(L["a"])(_,r,i,!1,null,"dd38a42a",null);t["default"]=k.exports},"8b81":function(e,t,a){"use strict";var r=a("5a4c"),i=a.n(r);i.a},bfa4:function(e,t,a){},de5c:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("div",{staticClass:"labelGroupDialog"},[a("el-form",{ref:"labelGroupFormDataRef",attrs:{model:e.labelGroupFormData,"status-icon":"",rules:e.labelGroupFormDataRuls,"label-width":"125px"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"标签组名称:",prop:"name"}},[a("el-input",{staticClass:"ps-input",staticStyle:{width:"250px"},attrs:{size:"small",maxlength:"15",placeholder:"请输入标签组名称"},model:{value:e.labelGroupFormData.name,callback:function(t){e.$set(e.labelGroupFormData,"name",t)},expression:"labelGroupFormData.name"}})],1),a("el-form-item",{attrs:{label:"可见范围:"}},[a("el-radio-group",{staticClass:"ps-radio",model:{value:e.labelGroupFormData.visible,callback:function(t){e.$set(e.labelGroupFormData,"visible",t)},expression:"labelGroupFormData.visible"}},[a("el-radio",{attrs:{label:"all"}},[e._v("全部可见")]),a("el-radio",{attrs:{label:"part"}},[e._v("商户可用")])],1)],1),"part"===e.labelGroupFormData.visible?a("el-form-item",{attrs:{label:"选择可用商户：",prop:"visible_organization"}},[a("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"super",size:"small","append-to-body":!0,filterable:!0},model:{value:e.labelGroupFormData.visible_organization,callback:function(t){e.$set(e.labelGroupFormData,"visible_organization",t)},expression:"labelGroupFormData.visible_organization"}})],1):e._e(),a("el-form-item",{attrs:{prop:"merchantId",label:"标签名称:"}},[e._l(e.labelGroupFormData.laberList,(function(t,r){return a("div",{key:r,staticClass:"ps-flex-align-c"},[a("el-input",{staticClass:"ps-input",staticStyle:{width:"250px"},attrs:{size:"small",maxlength:"15",placeholder:"请输入标签名称"},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"laberItem.name"}}),a("div",{staticClass:"p-l-20"},[0!=r?a("i",{staticClass:"el-icon-remove-outline p-r-10 ps-origin",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.removeFormLaber(r)}}}):e._e(),0!=r?a("i",{staticClass:"el-icon-top p-r-10 ps-green-text",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.riseClick(r)}}}):e._e(),e.labelGroupFormData.laberList.length!==r+1?a("i",{staticClass:"el-icon-bottom",staticStyle:{"font-size":"18px",color:"#2b8bfb"},on:{click:function(t){return e.declineClick(r)}}}):e._e()])],1)})),"add"==e.type?a("div",{staticClass:"p-b-20",staticStyle:{color:"#fda04d",cursor:"pointer",width:"80px"},on:{click:function(t){return e.addFormLaber()}}},[a("i",{staticClass:"el-icon-plus"}),e._v(" 添加标签 ")]):e._e()],2)],1)],1),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},i=[],n=a("a34a"),o=a.n(n),l=a("cbfb"),s=a("ed08");function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function b(e,t){return h(e)||g(e,t)||d(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function g(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,i=!1,n=void 0;try{for(var o,l=e[Symbol.iterator]();!(r=(o=l.next()).done);r=!0)if(a.push(o.value),t&&a.length===t)break}catch(s){i=!0,n=s}finally{try{r||null==l["return"]||l["return"]()}finally{if(i)throw n}}return a}}function h(e){if(Array.isArray(e))return e}function v(e,t,a,r,i,n,o){try{var l=e[n](o),s=l.value}catch(u){return void a(u)}l.done?t(s):Promise.resolve(s).then(r,i)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function o(e){v(n,r,i,o,l,"next",e)}function l(e){v(n,r,i,o,l,"throw",e)}o(void 0)}))}}var w={name:"labelGroupDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},visibleType:{type:String,default:""},width:{type:String,default:"600px"},isshow:Boolean,labelRroupInfo:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{isLoading:!1,inputValue:"",labelGroupFormData:{name:"",visible:"all",visible_organization:[],laberList:[{name:""}]},labelGroupFormDataRuls:{name:[{required:!0,message:"请输入标签组名称",trigger:"blur"}],visible_organization:[{required:!0,message:"请选择商户",trigger:"change"}]}}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},components:{OrganizationSelect:l["a"]},created:function(){"modify"===this.type&&(this.labelGroupFormData={name:this.labelRroupInfo.name,visible:this.labelRroupInfo.visible,visible_organization:this.labelRroupInfo.visible_organization,laberList:[]},this.labelRroupInfo.label_list.length&&(this.labelGroupFormData.laberList=this.labelRroupInfo.label_list.map((function(e){return{name:e.name}}))))},mounted:function(){},methods:{removeFormLaber:function(e){this.labelGroupFormData.laberList.splice(e,1)},addFormLaber:function(){this.labelGroupFormData.laberList.push({name:""})},riseClick:function(e){this.labelGroupFormData.laberList[e]=this.labelGroupFormData.laberList.splice(e-1,1,this.labelGroupFormData.laberList[e])[0]},declineClick:function(e){this.labelGroupFormData.laberList[e]=this.labelGroupFormData.laberList.splice(e+1,1,this.labelGroupFormData.laberList[e])[0]},getParams:function(){var e={name:this.labelGroupFormData.name,visible:this.labelGroupFormData.visible,label_list:this.labelGroupFormData.laberList,type:this.visibleType};if("part"===this.labelGroupFormData.visible&&(e.visible_organization=this.labelGroupFormData.visible_organization),this.labelGroupFormData.laberList&&this.labelGroupFormData.laberList.length)for(var t=0;t<this.labelGroupFormData.laberList.length;t++)if(!this.labelGroupFormData.laberList[t].name)return this.$message.error("请输入标签名称");this.getLabelGroup(e)},getLabelGroup:function(e){var t=this;return y(o.a.mark((function a(){var r,i,n,l,u,p,f,d;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.isLoading=!0,r="",i=b(r,2),n=i[0],l=i[1],"add"!==t.type){a.next=12;break}return a.next=6,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddPost(e));case 6:u=a.sent,p=b(u,2),n=p[0],l=p[1],a.next=19;break;case 12:return a.next=15,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupModifyPost(c({id:t.labelRroupInfo.id},e)));case 15:f=a.sent,d=b(f,2),n=d[0],l=d[1];case 19:if(t.isLoading=!1,!n){a.next=23;break}return t.$message.error(n.message),a.abrupt("return");case 23:0===l.code?(t.visible=!1,t.confirm()):t.$message.error(l.msg);case 24:case"end":return a.stop()}}),a)})))()},handleChange:function(){},clickConfirmHandle:function(){var e=this;this.$refs.labelGroupFormDataRef.validate((function(t){t&&e.getParams()}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1}}},_=w,L=(a("05ce"),a("2877")),k=Object(L["a"])(_,r,i,!1,null,null,null);t["default"]=k.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-AddCommodity","view-super-health-system-components-selectLaber","view-super-health-system-health-nutrition-AddMerchantCommodityToSuper","view-super-health-system-health-nutrition-CommodityLibrary","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(e,t,a){"use strict";a.r(t),a.d(t,"DEFAULT_NUTRITION",(function(){return i})),a.d(t,"ELEMENT_NUTRITION",(function(){return n})),a.d(t,"VITAMIN_NUTRITION",(function(){return l})),a.d(t,"NUTRITION_LIST",(function(){return r})),a.d(t,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return s})),a.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return o})),a.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return c})),a.d(t,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return u})),a.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return d}));var i=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"}],n=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],l=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],r=[].concat(i,n,l),s={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},o={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},u={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},d={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"175a":function(e,t,a){},"1a24":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("div",{staticClass:"healthTagDialog"},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:e.searchHandle},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}}),a("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v(" 已选 "),a("span",[e._v(e._s(e.selectLabelIdList.length))]),e._v(" 个标签 ")])]),e._l(e.tableData,(function(t,i){return a("div",{key:i},[a("el-collapse",{model:{value:e.activeLaberList,callback:function(t){e.activeLaberList=t},expression:"activeLaberList"}},[a("el-collapse-item",{attrs:{name:t.id}},[a("template",{slot:"title"},[a("span",[e._v(" "+e._s(t.name)+" "),a("span",[e._v("（"+e._s(t.label_list.length)+"）")])]),a("span",{staticClass:"tips-r"},[a("span",{staticClass:"open"},[e._v("展开")]),a("span",{staticClass:"close"},[e._v("收起")])])]),a("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[t.inputVisible?a("el-input",{ref:"saveTagInput"+t.id,refInFor:!0,staticClass:"ps-input w-100  p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(a){return e.handleInputConfirm(t)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputConfirm(t)}},model:{value:t.inputValue,callback:function(a){e.$set(t,"inputValue",a)},expression:"item.inputValue"}}):a("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(a){return e.showInput(t)}}},[e._v(" 添加标签 ")]),a("div",{staticStyle:{flex:"1"}},[a("el-checkbox-group",{attrs:{size:"mini"},model:{value:e.selectLabelIdList,callback:function(t){e.selectLabelIdList=t},expression:"selectLabelIdList"}},e._l(t.label_list,(function(i,n){return a("el-checkbox-button",{key:n,attrs:{label:i.id,disabled:i.disabled},on:{change:function(a){return e.checkboxChangge(i,t)}}},[e._v(" "+e._s(i.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},n=[],l=a("a34a"),r=a.n(l),s=a("ed08");function o(e,t){return m(e)||p(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return d(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=new Array(t);a<t;a++)i[a]=e[a];return i}function p(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],i=!0,n=!1,l=void 0;try{for(var r,s=e[Symbol.iterator]();!(i=(r=s.next()).done);i=!0)if(a.push(r.value),t&&a.length===t)break}catch(o){n=!0,l=o}finally{try{i||null==s["return"]||s["return"]()}finally{if(n)throw l}}return a}}function m(e){if(Array.isArray(e))return e}function f(e,t,a,i,n,l,r){try{var s=e[l](r),o=s.value}catch(c){return void a(c)}s.done?t(o):Promise.resolve(o).then(i,n)}function g(e){return function(){var t=this,a=arguments;return new Promise((function(i,n){var l=e.apply(t,a);function r(e){f(l,i,n,r,s,"next",e)}function s(e){f(l,i,n,r,s,"throw",e)}r(void 0)}))}}var b={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(s["c"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var e=this;return g(r.a.mark((function t(){var a,i,n,l,c;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a={type:e.labelType,page:e.currentPage,page_size:e.pageSize},e.name&&(a.name=e.name),t.next=5,Object(s["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(a));case 5:if(i=t.sent,n=o(i,2),l=n[0],c=n[1],e.isLoading=!1,!l){t.next=13;break}return e.$message.error(l.message),t.abrupt("return");case 13:0===c.code?(e.totalCount=c.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=c.data.results.map((function(t){return t.inputVisible=!1,t.inputValue="",t.label_list.forEach((function(a){a.label_group_name=t.name,e.ruleSingleInfo.selectLabelAllIds&&e.ruleSingleInfo.selectLabelAllIds.length&&e.ruleSingleInfo.selectLabelAllIds.includes(a.id)&&!e.selectLabelIdList.includes(a.id)?a.disabled=!0:a.disabled=!1})),e.activeLaberList.push(t.id),t})),console.log(e.tableData)):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},handleChange:function(){},checkboxChangge:function(e,t){var a=this,i=this.selectLabelIdList.indexOf(e.id);-1!==i?this.selectLabelListData.push(e):this.selectLabelListData.map((function(t,i){e.id===t.id&&a.selectLabelListData.splice(i,1)}))},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(a){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return g(r.a.mark((function a(){var i,n,l,c;return r.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(i=a.sent,n=o(i,2),l=n[0],c=n[1],t.isLoading=!1,!l){a.next=11;break}return t.$message.error(l.message),a.abrupt("return");case 11:0===c.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},clickConfirmHandle:function(){var e={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",e),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()}}},h=b,v=(a("fa5f"),a("2877")),y=Object(v["a"])(h,i,n,!1,null,null,null);t["default"]=y.exports},"3fa5":function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));var i=a("5c96");function n(e,t){return new Promise((function(a,n){i["MessageBox"].confirm(e.content?e.content:"",e.title?e.title:"提示",{dangerouslyUseHTMLString:e.useHTML,distinguishCancelAndClose:!0,closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",confirmButtonText:e.confirmButtonText,cancelButtonText:e.cancelButtonText,center:""===e.center||e.center}).then((function(e){t?a(t()):a()})).catch((function(e){n(e)}))}))}},"520a":function(e,t,a){"use strict";var i=a("175a"),n=a.n(i);n.a},"7e85":function(e,t,a){},d7de:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"super-add-commodity container-wrapper"},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"foodRef",attrs:{rules:e.formRuls,model:e.formData,size:"small"}},[i("div",{staticClass:"table-wrapper"},[i("div",{staticClass:"table-header"},[i("div",{staticClass:"table-title"},[e._v("基本信息")])]),i("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[i("el-form-item",{staticClass:"block-label form-content-flex",attrs:{label:"菜品/商品名称",prop:"name"}},[i("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{placeholder:"请输入菜品/商品名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}}),i("el-tooltip",{attrs:{effect:"dark",content:"增加菜品别名",placement:"top"}},[i("img",{staticClass:"add-btn-img",attrs:{src:a("a851"),alt:""},on:{click:e.addFoodAliasName}})])],1),e.formData.aliasName.length?i("div",[i("el-form-item",{staticClass:"block-label",attrs:{label:"菜品别名："}},e._l(e.formData.aliasName,(function(t,n){return i("el-form-item",{key:n,class:[n>0?"m-t-10":"","food-alias-name-form"],attrs:{rules:e.formRuls.aliasName,prop:"aliasName["+n+"]"}},[i("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{placeholder:"请输入菜品别名"},model:{value:e.formData.aliasName[n],callback:function(t){e.$set(e.formData.aliasName,n,t)},expression:"formData.aliasName[index]"}}),i("img",{attrs:{src:a("a851"),alt:""},on:{click:e.addFoodAliasName}}),i("img",{attrs:{src:a("1597"),alt:""},on:{click:function(t){return e.delFoodAliasName(n)}}})],1)})),1)],1):e._e(),i("el-form-item",{staticClass:"block-label",attrs:{label:"属性",prop:"attributes"}},[i("el-radio-group",{staticClass:"ps-radio",model:{value:e.formData.attributes,callback:function(t){e.$set(e.formData,"attributes",t)},expression:"formData.attributes"}},[i("el-radio",{attrs:{label:"goods"}},[e._v("商品")]),i("el-radio",{attrs:{label:"foods"}},[e._v("菜品")])],1)],1),i("el-form-item",{attrs:{label:"分类：",prop:"categoryId"}},[i("el-select",{staticClass:"ps-select",staticStyle:{width:"190px"},attrs:{placeholder:"请下拉选择分类","popper-class":"ps-popper-select","collapse-tags":"",clearable:"","popper-append-to-body":!1},model:{value:e.formData.categoryId,callback:function(t){e.$set(e.formData,"categoryId",t)},expression:"formData.categoryId"}},e._l(e.foodCategoryList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),i("el-form-item",{attrs:{label:"口味",prop:"taste"}},[e.inputVisible?i("el-input",{ref:"saveTagInput",staticClass:"input-new-tag",staticStyle:{"margin-right":"10px"},attrs:{size:"small"},on:{blur:e.inputTasteConfirm},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.inputTasteConfirm(t)}},model:{value:e.inputValue,callback:function(t){e.inputValue=t},expression:"inputValue"}}):i("el-button",{staticClass:"ps-btn button-new-tag",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.showTasteInput}},[e._v("添加")]),e._l(e.formData.tasteList,(function(t){return i("el-tag",{key:t,attrs:{closable:"","disable-transitions":!1,color:"#fff"},on:{close:function(a){return e.closeTasteHandle(t)}}},[e._v(" "+e._s(t)+" ")])}))],2),i("el-form-item",{attrs:{label:"标签",prop:""}},[i("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.labelClick}},[e._v(" 选择标签 ")])],1),e._l(e.formData.labelGroupInfoList,(function(t,a,n){return i("el-form-item",{key:n,attrs:{label:a+":",prop:""}},e._l(t,(function(t,n){return i("el-tag",{key:n,staticClass:"m-r-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff",closable:""},on:{close:function(i){return e.closeTag(a,n,t)}}},[e._v(" "+e._s(t.name)+" ")])})),1)}))],2),i("div",{staticStyle:{"max-width":"900px","padding-left":"20px"}},[i("el-form-item",{staticClass:"upload-block-label upload-hidden",attrs:{label:"菜品/商品图片"}},[i("div",{staticClass:"inline-block upload-w"},[i("el-upload",{ref:"fileUpload",class:{"file-upload":!0,"hide-upload":e.formData.foodImagesList.length>0},attrs:{drag:"",action:e.serverUrl,data:e.uploadParams,"file-list":e.formData.foodImagesList,"on-success":e.handleFoodImgSuccess,"on-change":e.handelChange,"before-upload":e.beforeFoodImgUpload,limit:1,"list-type":"picture-card",multiple:!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"},scopedSlots:e._u([{key:"file",fn:function(t){var a=t.file;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:"uploading"===a.status,expression:"file.status==='uploading'"}],attrs:{"element-loading-text":"上传中"}},[i("div",{staticClass:"upload-food-img"},[i("el-image",{staticClass:"el-upload-dragger",attrs:{src:a.url,fit:"contain"}})],1),i("span",{staticClass:"el-upload-list__item-actions"},[i("span",{staticClass:"el-upload-list__item-preview",on:{click:function(t){return e.handlePictureCardPreview(a)}}},[i("i",{staticClass:"el-icon-zoom-in"})]),i("span",{staticClass:"el-upload-list__item-delete",on:{click:function(t){return e.handleImgRemove(a,"foodImages")}}},[i("i",{staticClass:"el-icon-delete"})])])])}}])},[e.fileLists.length<1?i("div",{staticClass:"upload-t"},[i("i",{staticClass:"el-icon-circle-plus"}),i("div",{staticClass:"el-upload__text"},[e._v(" 上传菜品/商品图片 ")])]):e._e()])],1),i("div",{staticClass:"inline-block upload-tips"},[e._v(" 上传：菜品/商品图片。"),i("br"),e._v(" 建议图片需清晰，图片内容与名称相符。"),i("br"),e._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")])]),i("el-form-item",{staticClass:"upload-block-label",attrs:{label:"识别图片"}},[i("div",{staticClass:"inline-block upload-w"},[i("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploadingExtra,expression:"uploadingExtra"}],ref:"fileUpload",staticClass:"file-upload",attrs:{"element-loading-text":"上传中",drag:"",action:e.serverUrl,data:e.uploadParams,"file-list":e.formData.extraImagesList,"on-success":e.handleExtraImgSuccess,"on-change":e.handelChange,"before-upload":e.beforeExtraImgUpload,limit:25,multiple:!0,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[i("div",{staticClass:"upload-t"},[i("i",{staticClass:"el-icon-circle-plus"}),i("div",{staticClass:"el-upload__text"},[e._v(" 上传识别图片 ")])])])],1),i("div",{staticClass:"inline-block upload-tips"},[e._v(" 上传：识别图片。最多25张"),i("br"),e._v(" 建议图片需清晰，图片内容与名称相符。"),i("br"),e._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")]),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showFoodImg,expression:"showFoodImg"}],staticStyle:{cursor:"pointer"},on:{click:function(t){e.showFoodImg=!1}}},[e._v(" 查看已上传的图片（"+e._s(e.formData.extraImages.length)+"张） "),i("i",{staticClass:"el-icon-arrow-up"})]),i("div",{directives:[{name:"show",rawName:"v-show",value:!e.showFoodImg,expression:"!showFoodImg"}],staticStyle:{cursor:"pointer"},on:{click:function(t){e.showFoodImg=!0}}},[e._v(" 查看已上传的图片（"+e._s(e.formData.extraImages.length)+"张） "),i("i",{staticClass:"el-icon-arrow-down"})]),i("div",{directives:[{name:"show",rawName:"v-show",value:e.showFoodImg,expression:"showFoodImg"}],staticClass:"food-img-wrap"},e._l(e.formData.extraImages,(function(t,a){return i("div",{key:a,staticClass:"food-img-item"},[i("img",{attrs:{src:t,alt:"",srcset:""}}),i("div",{staticClass:"food-img-mask"},[i("i",{staticClass:"el-icon-zoom-in",on:{click:function(a){return e.perviewFoodImg(t)}}}),i("i",{staticClass:"el-icon-delete",on:{click:function(a){return e.handleImgRemove({url:t},"extraImages")}}})])])})),0)])],1)]),i("div",{staticClass:"table-wrapper"},[i("div",{staticClass:"table-header"},[i("div",{staticClass:"table-title"},[e._v(" 食材占比 "),i("span",{staticClass:"tip-o-7"},[e._v("（菜品每100g所含食材占比，相加必须等于100%）")]),i("el-button",{staticClass:"ps-btn float-r",staticStyle:{"margin-right":"15px"},attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addIngredients}},[e._v("添加")])],1)]),i("div",{class:["table-content",e.errorMsg.percentageError?"error-border":""]},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.formData.ingredientList,"header-row-class-name":"ps-table-header-row"}},[i("el-table-column",{attrs:{prop:"no",label:"食材",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-select",{staticClass:"ps-select margin-right",attrs:{placeholder:"请下拉选择","collapse-tags":"",clearable:"",filterable:""},on:{change:e.changeIngredient},model:{value:t.row.selectId,callback:function(a){e.$set(t.row,"selectId",a)},expression:"scope.row.selectId"}},e._l(t.row.selectFoodIngredient,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id,disabled:e.disabled}})})),1)]}}])}),i("el-table-column",{attrs:{prop:"id",label:"占比",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{staticClass:"cantent ps-flex-align-c flex-align-c"},[i("el-slider",{staticClass:"cantent",attrs:{"show-input":""},on:{change:e.changePercentage},model:{value:t.row.percentage,callback:function(a){e.$set(t.row,"percentage",a)},expression:"scope.row.percentage"}}),e._v("% ")],1)]}}])}),i("el-table-column",{attrs:{prop:"xx",label:"操作",align:"center",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteIngredientHandle(t.row.index)}}},[e._v("删除")])]}}])})],1)],1),e.errorMsg.percentageError?i("div",{staticStyle:{color:"red",padding:"20px"}},[e._v(e._s(e.errorMsg.percentageError))]):e._e()]),i("div",{staticClass:"table-wrapper"},[i("div",{staticClass:"table-header"},[i("div",{staticClass:"table-title"},[e._v(" 营养信息 ")])]),i("div",{staticClass:"table-content"},[e._l(e.currentNutritionList,(function(t){return[i("div",{key:t.key,staticClass:"nutrition-item"},[i("div",{staticClass:"nutrition-label"},[e._v(e._s(t.name+"："))]),i("el-form-item",{attrs:{prop:t.key}},[i("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:"",disabled:""},model:{value:e.formData[t.key],callback:function(a){e.$set(e.formData,t.key,a)},expression:"formData[nutrition.key]"}}),i("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(t.unit))])],1)],1)]})),i("div",{staticClass:"text-center pointer"},[i("span",{staticStyle:{color:"#027DB4"},on:{click:function(t){e.showAll=!e.showAll}}},[e._v(e._s(e.showAll?"收起":"查看更多营养信息"))])])],2)]),i("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[i("el-button",{staticStyle:{width:"120px"},attrs:{disabled:e.isLoading},on:{click:e.closeHandler}},[e._v("取消")]),i("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary",disabled:e.isLoading},on:{click:e.submitHandler}},[e._v(e._s("add"===e.type?"添加":"编辑"))])],1)]),i("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})]),e.selectLaberDialogVisible?i("select-laber",{attrs:{isshow:e.selectLaberDialogVisible,width:"600px",ruleSingleInfo:e.ruleSingleInfo},on:{"update:isshow":function(t){e.selectLaberDialogVisible=t},selectLaberData:e.selectLaberData}}):e._e()],1)},n=[],l=a("a34a"),r=a.n(l),s=a("ed08"),o=a("015b"),c=a("da92"),u=a("3fa5"),d=a("1a24");function p(e,t){return h(e)||b(e,t)||f(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"===typeof e)return g(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=new Array(t);a<t;a++)i[a]=e[a];return i}function b(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],i=!0,n=!1,l=void 0;try{for(var r,s=e[Symbol.iterator]();!(i=(r=s.next()).done);i=!0)if(a.push(r.value),t&&a.length===t)break}catch(o){n=!0,l=o}finally{try{i||null==s["return"]||s["return"]()}finally{if(n)throw l}}return a}}function h(e){if(Array.isArray(e))return e}function v(e,t,a,i,n,l,r){try{var s=e[l](r),o=s.value}catch(c){return void a(c)}s.done?t(o):Promise.resolve(o).then(i,n)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(i,n){var l=e.apply(t,a);function r(e){v(l,i,n,r,s,"next",e)}function s(e){v(l,i,n,r,s,"throw",e)}r(void 0)}))}}var L={name:"SuperAddIngredients",data:function(){var e=this,t=function(e,t,a){if(t){var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("营养数据有误，仅支持保留两位小数"))}else a()},a=function(t,a,i){if(!e.formData.imageList.length)return i(new Error("请上传菜品图片"));i()};return{type:"add",isLoading:!1,formData:{name:"",aliasName:[],attributes:"foods",tasteList:[],foodImages:[],foodImagesList:[],extraImages:[],extraImagesList:[],ingredientList:[],food_id:"",categoryId:"",selectLabelListData:[],selectLabelIdList:[],labelGroupInfoList:{}},fileLists:[],serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(s["x"])()},formRuls:{name:[{required:!0,message:"食材名称不能为空",trigger:"blur"}],aliasName:[{required:!0,message:"请输入菜品别名",trigger:"blur"}],attributes:[{required:!0,message:"请选择属性",trigger:"blur"}],nutrition:[{validator:t,trigger:"change"}],imageList:[{required:!0,validator:a,trigger:"blur"}],categoryId:[{required:!0,message:"请选择分类",trigger:"blur"}]},nutritionList:o["NUTRITION_LIST"],inputVisible:!1,inputValue:"",limit:25,actionUrl:"",uploadParams:{prefix:"super_food_img"},uploadUrl:"",tableData:[{}],ingredientList:[],allSelectIngredient:[],errorMsg:{percentageError:""},selectLaberDialogVisible:!1,ruleSingleInfo:{},dialogImageUrl:"",dialogVisible:!1,showFoodImg:!0,foodCategoryList:[],showAll:!1,uploading:!1,uploadingExtra:!1}},computed:{currentNutritionList:function(){var e=[];return e=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),e}},components:{selectLaber:d["default"]},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){var e=this;return y(r.a.mark((function t(){var a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.getIngredientslist();case 3:return t.next=5,e.foodFoodCategoryList();case 5:e.isLoading=!1,"modify"===e.type?(a=e.$decodeQuery(e.$route.query.data),console.log(a),e.formData.id=a.id,e.formData.aliasName=a.alias_name,e.formData.name=a.name,e.formData.attributes=a.attributes,e.formData.categoryId=a.category,a.taste_list&&(e.formData.tasteList=a.taste_list.map((function(e){return e.name}))),a.label.length&&e.initLabelGroup(a.label),e.formData.selectLabelListData=a.label,e.formData.selectLabelIdList=a.label.map((function(e){return e.id})),a.image&&(e.formData.foodImages=[a.image],e.formData.foodImagesList=[{url:a.image,name:a.image,status:"success",uid:a.image}]),a.extra_image&&(a.extra_image.forEach((function(t){e.formData.extraImagesList.push({url:t,name:t,status:"success",uid:t})})),e.formData.extraImages=a.extra_image),e.initIngredient(a),e.setNutritonData(a),e.isDisabledOtherIngredients()):(e.initIngredient(),e.setNutritonData({}));case 7:case"end":return t.stop()}}),t)})))()},searchHandle:Object(s["c"])((function(){this.currentPage=1}),300),setNutritonData:function(e){var t=this;e.nutrition||(e.nutrition={});var a=e.nutrition.element?JSON.parse(Object(s["J"])(e.nutrition.element)):{},i=e.nutrition.vitamin?JSON.parse(Object(s["J"])(e.nutrition.vitamin)):{};o["NUTRITION_LIST"].forEach((function(n){"default"===n.type&&t.$set(t.formData,n.key,e.nutrition[n.key]?e.nutrition[n.key]:0),"element"===n.type&&t.$set(t.formData,n.key,a[n.key]?a[n.key]:0),"vitamin"===n.type&&t.$set(t.formData,n.key,i[n.key]?i[n.key]:0)}))},getIngredientslist:function(){var e=this;return y(r.a.mark((function t(){var a,i,n,l;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(s["Q"])(e.$apis.apiBackgroundAdminIngredientIngredientNamePost({page:1,page_size:999999}));case 2:if(a=t.sent,i=p(a,2),n=i[0],l=i[1],e.isLoading=!1,!n){t.next=10;break}return e.$message.error(n.message),t.abrupt("return");case 10:0===l.code?e.ingredientList=l.data:e.$message.error(l.msg);case 11:case"end":return t.stop()}}),t)})))()},initIngredient:function(e){var t=this;this.formData.ingredientList=[],"add"===this.type?this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(s["e"])(this.ingredientList)}):e&&(this.formData.ingredientList=e.ingredients_list.map((function(e,a){return e.index=a,e.selectId=Number(e.ingredient_id),e.percentage=e.ingredient_scale,e.selectFoodIngredient=Object(s["e"])(t.ingredientList),t.ingredientList.map((function(t){t.id===e.selectId&&(e.nutrition=t.nutrition_info)})),e})),this.isDisabledOtherIngredients())},formatParams:function(){var e=this,t={name:this.formData.name,alias_name:this.formData.aliasName,attributes:this.formData.attributes,taste_list:this.formData.tasteList,image:this.formData.foodImages[0],extra_image:this.formData.extraImages,label_list:this.formData.selectLabelIdList,ingredient_list:[],nutrition_info:{},category_id:this.formData.categoryId};"modify"===this.type&&(t.id=this.formData.id),this.formData.food_id&&"add"===this.type&&(t.food_id=this.formData.food_id),this.formData.ingredientList.map((function(e){if(e.selectId){var a={ingredient_id:e.selectId,ingredient_scale:e.percentage};t.ingredient_list.push(a)}}));var a={},i={};return o["NUTRITION_LIST"].forEach((function(n){"default"===n.type&&(t.nutrition_info[n.key]=e.formData[n.key]),"element"===n.type&&(a[n.key]=e.formData[n.key]),"vitamin"===n.type&&(i[n.key]=e.formData[n.key])})),t.nutrition_info.element=JSON.stringify(a),t.nutrition_info.vitamin=JSON.stringify(i),t},addIngredients:function(){this.formData.ingredientList.push({index:this.formData.ingredientList.length,selectId:"",percentage:0,selectFoodIngredient:Object(s["e"])(this.ingredientList)}),this.isDisabledOtherIngredients()},deleteIngredientHandle:function(e){this.formData.ingredientList.splice(e,1),this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage(),this.changePercentage()},changeIngredient:function(e){var t={};this.ingredientList.map((function(a){a.id===e&&(t=a)})),this.formData.ingredientList.forEach((function(e){e.selectId===t.id&&(e.nutrition=t.nutrition_info)})),this.errorMsg.percentageError="",this.isDisabledOtherIngredients(),this.computedNutritionAndPercentage()},isDisabledOtherIngredients:function(){var e=this;this.allSelectIngredient=[],this.formData.ingredientList.map((function(t,a){t.selectId&&e.allSelectIngredient.push(t.selectId)})),this.formData.ingredientList.forEach((function(t,a){t.selectFoodIngredient.forEach((function(a){e.allSelectIngredient.includes(a.id)&&t.selectId!==a.id?a.disabled=!0:a.disabled=!1}))}))},computedNutritionAndPercentage:function(){var e=this,t={};o["NUTRITION_LIST"].forEach((function(e){t[e.key]=0}));var a=0;this.formData.ingredientList.map((function(i,n){if(i.selectId){n<e.allSelectIngredient.length-1?(i.percentage=parseInt(c["a"].divide(100,e.allSelectIngredient.length)),a=c["a"].plus(i.percentage,a)):i.percentage=parseInt(c["a"].minus(100,a));var l=i.percentage/100;if(i.nutrition||(i.nutrition={}),t.energy_kcal=+i.nutrition.energy_kcal?c["a"].plus(t.energy_kcal,i.nutrition.energy_kcal*l):t.energy_kcal?t.energy_kcal:0,t.protein=+i.nutrition.protein?c["a"].plus(t.protein,i.nutrition.protein*l):t.protein?t.protein:0,t.axunge=+i.nutrition.axunge?c["a"].plus(t.axunge,i.nutrition.axunge*l):t.axunge?t.axunge:0,t.carbohydrate=+i.nutrition.carbohydrate?c["a"].plus(t.carbohydrate,i.nutrition.carbohydrate*l):t.carbohydrate?t.carbohydrate:0,i.nutrition.element&&i.nutrition.vitamin)try{var r=JSON.parse(Object(s["J"])(i.nutrition.element)),o=JSON.parse(Object(s["J"])(i.nutrition.vitamin));for(var u in r)t[u]=c["a"].plus(t[u],+r[u]?r[u]*l:0);for(var d in o)t[d]=c["a"].plus(t[d],+o[d]?o[d]*l:0)}catch(p){}e.deepFormIngredients&&e.deepFormIngredients.length&&e.deepFormIngredients.forEach((function(e){e.id===i.id&&(i.status=!0)}))}})),this.nutritionList.forEach((function(a){var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t[a.key])?e.$set(e.formData,a.key,t[a.key]):e.$set(e.formData,a.key,t[a.key].toFixed(2))}))},setNutritionAndPercentage:function(){var e=this,t={};o["NUTRITION_LIST"].forEach((function(e){t[e.key]=0})),this.formData.ingredientList.map((function(e,a){if(e.selectId){e.nutrition||(e.nutrition={});var i=e.percentage/100;if(t.energy_kcal=+e.nutrition.energy_kcal?c["a"].plus(t.energy_kcal,e.nutrition.energy_kcal*i):t.energy_kcal?t.energy_kcal:0,t.protein=+e.nutrition.protein?c["a"].plus(t.protein,e.nutrition.protein*i):t.protein?t.protein:0,t.axunge=+e.nutrition.axunge?c["a"].plus(t.axunge,e.nutrition.axunge*i):t.axunge?t.axunge:0,t.carbohydrate=+e.nutrition.carbohydrate?c["a"].plus(t.carbohydrate,e.nutrition.carbohydrate*i):t.carbohydrate?t.carbohydrate:0,e.nutrition.element&&e.nutrition.vitamin)try{var n=JSON.parse(Object(s["J"])(e.nutrition.element)),l=JSON.parse(Object(s["J"])(e.nutrition.vitamin));for(var r in n)t[r]=c["a"].plus(t[r],+n[r]?n[r]*i:0);for(var o in l)t[o]=c["a"].plus(t[o],+l[o]?l[o]*i:0)}catch(u){}}})),this.nutritionList.forEach((function(a){var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t[a.key])?e.$set(e.formData,a.key,t[a.key]):e.$set(e.formData,a.key,t[a.key].toFixed(2))}))},changePercentage:function(e){this.setNutritionAndPercentage();var t=this.formData.ingredientList.reduce((function(e,t){return c["a"].plus(t.percentage,e)}),0);this.errorMsg.percentageError=t>100||t<100?"菜品每100g所含食材占比，相加必须等于100%":"",this.formData.ingredientList.length||(this.errorMsg.percentageError="")},closeTasteHandle:function(e){this.formData.tasteList.splice(this.formData.tasteList.indexOf(e),1)},showTasteInput:function(){var e=this;this.inputVisible=!0,this.$nextTick((function(t){e.$refs.saveTagInput.$refs.input.focus()}))},inputTasteConfirm:function(){var e=this.inputValue;e&&this.formData.tasteList.push(e),this.inputVisible=!1,this.inputValue=""},labelClick:function(){this.ruleSingleInfo={labelType:"food",selectLabelIdList:this.formData.selectLabelIdList,selectLabelListData:this.formData.selectLabelListData},this.selectLaberDialogVisible=!0},closeTag:function(e,t,a){var i=this.formData.selectLabelIdList.indexOf(a.id),n=this.formData.selectLabelListData.indexOf(a);this.formData.selectLabelIdList.splice(i,1),this.formData.selectLabelListData.splice(n,1),this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},selectLaberData:function(e){this.formData.selectLabelIdList=e.selectLabelIdList,this.formData.selectLabelListData=e.selectLabelListData,this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},initLabelGroup:function(e){var t=this;e.forEach((function(e){t.formData.labelGroupInfoList[e.label_group_name]||(t.formData.labelGroupInfoList[e.label_group_name]=[]),t.formData.labelGroupInfoList[e.label_group_name]&&!t.formData.labelGroupInfoList[e.label_group_name].includes(e)&&t.formData.labelGroupInfoList[e.label_group_name].push(e)}))},handelChange:function(e,t){this.uploadParams.key=this.uploadParams.prefix+(new Date).getTime()+Math.floor(150*Math.random())+".png"},perviewFoodImg:function(e){this.dialogImageUrl=e,this.dialogVisible=!0},handleFoodImgSuccess:function(e,t,a){this.uploading=!1,0===e.code?(this.formData.foodImagesList=a,this.formData.foodImages.push(e.data.public_url)):this.$message.error(e.msg)},handleExtraImgSuccess:function(e,t,a){this.uploadingExtra=!1,0===e.code?(this.formData.extraImagesList=a,this.formData.extraImages.push(e.data.public_url)):this.$message.error(e.msg)},handleImgRemove:function(e,t){var a=this.formData[t+"List"].findIndex((function(t){return t.url===e.url}));this.formData[t].splice(a,1),this.formData[t+"List"].splice(a,1)},beforeFoodImgUpload:function(e){return this.beforeImgUpload(e,"uploading")},beforeExtraImgUpload:function(e){return this.beforeImgUpload(e,"uploadingExtra")},beforeImgUpload:function(e,t){var a=[".jpeg",".jpg",".png",".bmp"],i=e.size/1024/1024<5;return console.log(Object(s["w"])(e.name)),a.includes(Object(s["w"])(e.name))?i?void(t&&(this[t]=!0)):(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},submitHandler:function(){var e=this;this.$refs.foodRef.validate((function(t){if(t&&!e.errorMsg.percentageError){if(e.isLoading)return e.$message.error("请勿重复提交！");"modify"===e.type?e.modifyFoodList():Object(u["a"])({content:"是否确定创建该菜品？"},e.addFoodList)}else console.log("error validate"),e.$message.error("请认真检查数据格式！")}))},addFoodList:function(){var e=this;return y(r.a.mark((function t(){var a,i,n,l;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Q"])(e.$apis.apiBackgroundAdminFoodAddPost(e.formatParams()));case 3:if(a=t.sent,i=p(a,2),n=i[0],l=i[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===l.code?(e.$message.success(l.msg),e.$closeCurrentTab(e.$route.path)):2===l.code?(e.formData.food_id=l.data.food_id,Object(u["a"])({content:l.msg},e.addFoodList).catch((function(t){e.formData.food_id=""}))):e.$message.error(l.msg);case 12:case"end":return t.stop()}}),t)})))()},modifyFoodList:function(){var e=this;return y(r.a.mark((function t(){var a,i,n,l;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Q"])(e.$apis.apiBackgroundAdminFoodModifyPost(e.formatParams()));case 3:if(a=t.sent,i=p(a,2),n=i[0],l=i[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===l.code?(e.$message.success(l.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(l.msg);case 12:case"end":return t.stop()}}),t)})))()},closeHandler:function(){var e=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(t,a,i){"confirm"===t?(a.confirmButtonLoading=!0,e.$closeCurrentTab(e.$route.path),a.confirmButtonLoading=!1):a.confirmButtonLoading||i()}}).then((function(e){})).catch((function(e){}))},addFoodAliasName:function(){this.formData.aliasName.push("")},delFoodAliasName:function(e){this.formData.aliasName.splice(e,1)},foodFoodCategoryList:function(){var e=this;return y(r.a.mark((function t(){var a,i,n,l;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(s["Q"])(e.$apis.apiBackgroundAdminFoodCategoryListPost({page:1,page_size:999999}));case 2:if(a=t.sent,i=p(a,2),n=i[0],l=i[1],!n){t.next=9;break}return e.$message.error(n.message),t.abrupt("return");case 9:0===l.code?e.foodCategoryList=l.data.results:e.$message.error(l.msg);case 10:case"end":return t.stop()}}),t)})))()}}},I=L,_=(a("520a"),a("2877")),k=Object(_["a"])(I,i,n,!1,null,null,null);t["default"]=k.exports},fa5f:function(e,t,a){"use strict";var i=a("7e85"),n=a.n(i);n.a}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-HealthyHabit"],{"480d":function(t,a,s){"use strict";s.r(a);var i=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"healthy-habit records-wrapp-bg m-b-20"},[s("div",{staticClass:"p-b-10 ps-flex-bw flex-align-c"},[s("span",{staticStyle:{"font-weight":"bold"}},[t._v("习惯养成")]),s("span",{staticClass:"time"},[t._v("更新时间："+t._s(t.formData.last_update_time))])]),s("div",{staticClass:"m-b-20"},[t._v(" 累计打卡次数： "),s("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.formData.total_count)+"次")])]),s("div",{staticClass:"clock-in"},[t.formData.habit_list&&t.formData.habit_list.length?t._l(t.formData.habit_list,(function(a,i){return s("div",{key:i,staticClass:"ps-flex p-t-10 p-b-10"},[a.image?s("el-image",{staticStyle:{width:"50px",height:"40px","border-radius":"4px"},attrs:{src:a.image}}):s("div",{staticClass:"custom-style",style:"backgroundColor:"+a.color},[t._v(" "+t._s(a.name?a.name.substring(0,1):"")+" ")]),s("div",{staticClass:"clock-in-wrapp p-l-15"},[s("div",{staticClass:"ps-flex-bw"},[s("span",{staticClass:"habit-name"},[t._v(" "+t._s(a.name)+" "),a.is_use?s("span",{staticClass:"clock-in-ing"},[t._v("NOW")]):t._e()]),s("div",{staticClass:"cumulative-clock-in"},[t._v("累计打卡 "+t._s(a.count)+" 次")])]),s("div",{staticClass:"time"},[t._v("最近一次："+t._s(a.update_time))])])],1)})):s("el-empty",{attrs:{description:"暂无数据"}})],2)])},e=[],n={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(t){this.formData=t}},mounted:function(){},methods:{}},c=n,l=(s("c9ed"),s("2877")),o=Object(l["a"])(c,i,e,!1,null,"b005ab04",null);a["default"]=o.exports},c9ed:function(t,a,s){"use strict";var i=s("ff67"),e=s.n(i);e.a},ff67:function(t,a,s){}}]);
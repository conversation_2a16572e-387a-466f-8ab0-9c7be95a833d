(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-article-push-article_label-articleLabel"],{"16b0":function(e,t,a){"use strict";var r=a("c4af"),i=a.n(r);i.a},b8f3:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container-wrapper has-organization"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{id:"article-label"}},[a("div",{staticClass:"organization-tree"},[a("el-input",{staticClass:"tree-search ps-input",attrs:{placeholder:"请输入标签分类"},on:{input:e.changeArticleTag},model:{value:e.primaryName,callback:function(t){e.primaryName=t},expression:"primaryName"}}),a("div",[a("button-icon",{attrs:{color:"origin",type:""},on:{click:function(t){return e.clickShowDialogLabel("addCategory")}}},[e._v(" 新建标签分类 ")]),a("ul",{staticClass:"infinite-list",style:{overflow:"auto",height:e.labelBoxHeight+"px"}},e._l(e.foodFoodCategoryPrimaryList,(function(t,r){return a("li",{key:r},[a("div",{staticClass:"primary-label"},[a("span",{class:{active:r===e.articleTagIndex},staticStyle:{cursor:"pointer"},on:{click:function(a){return e.clickArticleTag(t,r)}}},[e._v(" "+e._s(t.name)+" ")]),a("div",[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.clickShowDialogLabel("editCategory",t)}}},[e._v(" 编辑 ")]),a("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHaldler("delCategory",t)}}},[e._v(" 删除 ")])],1)])])})),0)],1)],1),a("div",{staticClass:"label-list"},[a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")]),a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.clickShowDialogLabel("addCategoryLabel")}}},[e._v(" 新建标签 ")])],1)]),a("div",{staticClass:"table-content"},[a("el-table",{ref:"tableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:e.tableData,"row-key":"id",stripe:"","header-row-class-name":"ps-table-header-row"}},[a("el-table-column",{attrs:{prop:"name",label:"名称",align:"center"}}),a("el-table-column",{attrs:{prop:"article_number",label:"文章数",align:"center"}}),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.clickShowDialogLabel("editCategoryLabel",t.row)}}},[e._v(" 编辑 ")]),"disable"===t.row.status?a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.clickStatus(t.row,"enable")}}},[e._v(" 启用 ")]):e._e(),"enable"===t.row.status?a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.clickStatus(t.row,"disable")}}},[e._v(" 停用 ")]):e._e(),a("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHaldler("delCategoryLabel",t.row)}}},[e._v(" 删除 ")])]}}])})],1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[10,20,30,40],"page-size":e.pageSize,layout:"total, prev, pager, next,sizes,jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)])],1)]),a("el-dialog",{attrs:{title:e.dialogLabelTitle,visible:e.showDialogCategory,width:"400px","custom-class":"ps-dialog"},on:{"update:visible":function(t){e.showDialogCategory=t}}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.formCategoryLoading,expression:"formCategoryLoading"}],ref:"dialogCategoryForm",attrs:{model:e.dialogCategoryForm,"status-icon":"",rules:e.dialogCategoryRules,"label-width":"80px"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"分类",prop:"primaryCategoryName"}},[a("el-input",{staticClass:"ps-input",staticStyle:{width:"190px"},attrs:{placeholder:"请输入分类名称"},model:{value:e.dialogCategoryForm.primaryCategoryName,callback:function(t){e.$set(e.dialogCategoryForm,"primaryCategoryName",t)},expression:"dialogCategoryForm.primaryCategoryName"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.showDialogCategory=!1}}},[e._v("取 消")]),a("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:e.formCategoryLoading},on:{click:e.determineCategoryDialog}},[e._v(" 确 定 ")])],1)],1),a("el-dialog",{attrs:{title:e.dialogLabelTitle,visible:e.showDialogCategoryLabel,width:"600px","custom-class":"ps-dialog"},on:{"update:visible":function(t){e.showDialogCategoryLabel=t}}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.formCategoryLabelLoading,expression:"formCategoryLabelLoading"}],ref:"dialogCategoryLabelForm",attrs:{model:e.dialogCategoryLabelForm,"status-icon":"",rules:e.dialogCategoryLabelRules,"label-width":"100px"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"标签名称",prop:"name"}},[a("el-input",{staticClass:"ps-input",staticStyle:{width:"250px"},model:{value:e.dialogCategoryLabelForm.name,callback:function(t){e.$set(e.dialogCategoryLabelForm,"name",t)},expression:"dialogCategoryLabelForm.name"}})],1),a("el-form-item",{attrs:{label:"标签关键词"}},[a("div",{staticClass:"p-l-10",staticStyle:{color:"red"}},[e._v("关键词之间用、隔开")]),a("el-input",{staticClass:"ps-input",staticStyle:{width:"250px"},attrs:{type:"textarea",autosize:{minRows:6,maxRows:6}},model:{value:e.dialogCategoryLabelForm.keyWords,callback:function(t){e.$set(e.dialogCategoryLabelForm,"keyWords",t)},expression:"dialogCategoryLabelForm.keyWords"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.showDialogCategoryLabel=!1}}},[e._v("取 消")]),a("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:e.formCategoryLoading},on:{click:e.determineCategoryLabelDialog}},[e._v(" 确 定 ")])],1)],1)],1)},i=[],o=a("a34a"),l=a.n(o),n=a("ed08");function s(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?s(Object(a),!0).forEach((function(t){g(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function g(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t){return f(e)||b(e,t)||p(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function b(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,i=!1,o=void 0;try{for(var l,n=e[Symbol.iterator]();!(r=(l=n.next()).done);r=!0)if(a.push(l.value),t&&a.length===t)break}catch(s){i=!0,o=s}finally{try{r||null==n["return"]||n["return"]()}finally{if(i)throw o}}return a}}function f(e){if(Array.isArray(e))return e}function h(e,t,a,r,i,o,l){try{var n=e[o](l),s=n.value}catch(c){return void a(c)}n.done?t(s):Promise.resolve(s).then(r,i)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var o=e.apply(t,a);function l(e){h(o,r,i,l,n,"next",e)}function n(e){h(o,r,i,l,n,"throw",e)}l(void 0)}))}}var C={name:"articleLabel",props:{},data:function(){return{articleTagIndex:-1,labelBoxHeight:"",isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"筛选",value:"",placeholder:"请输入标签名称"},status:{type:"select",value:"",label:"标签状态",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!1,collapseTags:!0,dataList:[{name:"启用",id:"enable"},{name:"停用",id:"disable"}]}},primaryName:"",dialogLabelTitle:"",showDialogLabelType:"",showDialogCategoryLabel:!1,showDialogCategory:!1,dialogCategoryLabelForm:{name:"",keyWords:""},dialogCategoryLabelRules:{name:[{required:!0,message:"请输入标签名称",trigger:"blur"}]},dialogCategoryForm:{primaryCategoryName:""},dialogCategoryRules:{primaryCategoryName:[{required:!0,message:"请输入一级分类名称",trigger:"blur"}]},formCategoryLabelLoading:!1,formCategoryLoading:!1,foodFoodCategoryPrimaryList:[],showDialogLabelRow:{},articleTagRow:{}}},created:function(){this.getArticleTagList()},watch:{tableData:function(){this.$nextTick((function(){this.labelBoxHeight=document.getElementsByClassName("search-form-wrapper")[0].offsetHeight+document.getElementsByClassName("table-wrapper")[0].offsetHeight-140}))}},mounted:function(){},methods:{initLoad:function(){this.getArticleTagChildList({parent_id:this.articleTagRow.id})},searchHandle:Object(n["c"])((function(){this.currentPage=1,this.initLoad()}),300),searchHandleArticleTag:Object(n["c"])((function(){var e={};this.primaryName?e.name=this.primaryName:e={},this.getArticleTagList(e)}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getArticleTagList:function(e){var t=this;return y(l.a.mark((function a(){var r,i,o,s;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(n["Q"])(t.$apis.apiBackgroundArticleTagListPost(e));case 3:if(r=a.sent,i=d(r,2),o=i[0],s=i[1],t.isLoading=!1,!o){a.next=11;break}return t.$message.error(o.message),a.abrupt("return");case 11:0===s.code?(t.tableData=[],t.foodFoodCategoryPrimaryList=s.data.results,t.showDialogCategory=!1):t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},getArticleTagChildList:function(e){var t=this;return y(l.a.mark((function a(){var r,i,o,s;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(n["Q"])(t.$apis.apiBackgroundArticleTagChildListPost(c(c(c({},t.formatQueryParams(t.searchFormSetting)),e),{},{page:t.currentPage,page_size:t.pageSize})));case 3:if(r=a.sent,i=d(r,2),o=i[0],s=i[1],t.isLoading=!1,!o){a.next=11;break}return t.$message.error(o.message),a.abrupt("return");case 11:0===s.code?(t.showDialogCategoryLabel=!1,t.totalCount=s.data.count,t.tableData=s.data.results):t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},getAddArticleTag:function(e){var t=this;return y(l.a.mark((function a(){var r,i,o,s;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(n["Q"])(t.$apis.apiBackgroundArticleTagAddPost(e));case 3:if(r=a.sent,i=d(r,2),o=i[0],s=i[1],t.isLoading=!1,!o){a.next=11;break}return t.$message.error(o.message),a.abrupt("return");case 11:0===s.code?"addCategoryLabel"===t.showDialogLabelType?t.getArticleTagChildList({parent_id:t.articleTagRow.id}):t.getArticleTagList():t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},getModifyArticleTag:function(e,t){var a=this;return y(l.a.mark((function r(){var i,o,s,c;return l.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a.isLoading=!0,r.next=3,Object(n["Q"])(a.$apis.apiBackgroundArticleTagModifyPost(e));case 3:if(i=r.sent,o=d(i,2),s=o[0],c=o[1],a.isLoading=!1,!s){r.next=11;break}return a.$message.error(s.message),r.abrupt("return");case 11:0===c.code?"articleChild"===t?a.getArticleTagChildList({parent_id:a.articleTagRow.id}):a.getArticleTagList():a.$message.error(c.msg);case 12:case"end":return r.stop()}}),r)})))()},getDeleteArticleTag:function(e,t){var a=this;return y(l.a.mark((function r(){var i,o,s,c;return l.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a.isLoading=!0,r.next=3,Object(n["Q"])(a.$apis.apiBackgroundArticleTagDeletePost(t));case 3:if(i=r.sent,o=d(i,2),s=o[0],c=o[1],a.isLoading=!1,!s){r.next=11;break}return a.$message.error(s.message),r.abrupt("return");case 11:0===c.code?"delCategoryLabel"===e?a.getArticleTagChildList({parent_id:a.articleTagRow.id}):a.getArticleTagList():a.$message.error(c.msg);case 12:case"end":return r.stop()}}),r)})))()},formatQueryParams:function(e){var t={};for(var a in e)e[a].value&&("select_date"!==a?t[a]=e[a].value:e[a].value.length>0&&(t.start_date=e[a].value[0],t.end_date=e[a].value[1]));return t},changeArticleTag:function(){this.searchHandleArticleTag()},handleSizeChange:function(e){this.pageSize=e,this.getArticleTagChildList({parent_id:this.articleTagRow.id})},handleCurrentChange:function(e){this.currentPage=e,this.getArticleTagChildList({parent_id:this.articleTagRow.id})},clickArticleTag:function(e,t){this.articleTagIndex=t,this.articleTagRow=e,this.getArticleTagChildList({parent_id:e.id})},clickStatus:function(e,t){this.getModifyArticleTag({id:e.id,status:t},"articleChild")},deleteHaldler:function(e,t){var a=this;this.$confirm("是否删除该分类？","删除",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var r=y(l.a.mark((function r(i,o,n){var s;return l.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:"confirm"===i?(s={ids:[t.id]},o.confirmButtonLoading=!0,a.getDeleteArticleTag(e,s),n(),o.confirmButtonLoading=!1):o.confirmButtonLoading||n();case 1:case"end":return r.stop()}}),r)})));function i(e,t,a){return r.apply(this,arguments)}return i}()})},clickShowDialogLabel:function(e,t){if(this.showDialogLabelRow={},this.showDialogLabelType=e,"addCategory"===e)this.dialogLabelTitle="新增分类",this.dialogCategoryForm.primaryCategoryName="",this.showDialogCategory=!0;else if("editCategory"===e)this.dialogLabelTitle="编辑分类",this.dialogCategoryForm.primaryCategoryName=t.name,this.showDialogLabelRow=t,this.showDialogCategory=!0;else if("addCategoryLabel"===e){if(!this.articleTagRow.id)return this.$message.error("请先选择标签分类");this.dialogLabelTitle="新增标签",this.showDialogCategoryLabel=!0,this.dialogCategoryLabelForm={name:"",keyWords:""}}else"editCategoryLabel"===e&&(this.dialogLabelTitle="编辑标签",this.showDialogCategoryLabel=!0,this.dialogCategoryLabelForm={name:t.name,keyWords:t.key_words.join("、")},this.showDialogLabelRow=t)},determineCategoryDialog:function(){var e=this;this.$refs.dialogCategoryForm.validate((function(t){if(!t)return!1;var a={name:e.dialogCategoryForm.primaryCategoryName};"addCategory"===e.showDialogLabelType?e.getAddArticleTag(a):"editCategory"===e.showDialogLabelType&&e.getModifyArticleTag(c({id:e.showDialogLabelRow.id},a))}))},determineCategoryLabelDialog:function(){var e=this;this.$refs.dialogCategoryLabelForm.validate((function(t){if(!t)return!1;var a={name:e.dialogCategoryLabelForm.name,key_words:e.dialogCategoryLabelForm.keyWords.split("、")};"addCategoryLabel"===e.showDialogLabelType?e.getAddArticleTag(c({parent_id:e.articleTagRow.id},a)):"editCategoryLabel"===e.showDialogLabelType&&e.getModifyArticleTag(c({id:e.showDialogLabelRow.id},a),"articleChild")}))}}},w=C,v=(a("16b0"),a("2877")),L=Object(v["a"])(w,r,i,!1,null,"bc0ac366",null);t["default"]=L.exports},c4af:function(e,t,a){}}]);
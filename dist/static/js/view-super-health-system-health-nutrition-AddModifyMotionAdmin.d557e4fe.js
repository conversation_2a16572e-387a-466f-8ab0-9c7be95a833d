(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-AddModifyMotionAdmin"],{5938:function(t,e,a){"use strict";var r=a("ed66"),n=a.n(r);n.a},b2a2:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"add_modify_motion_admin container-wrapper"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small","label-width":"80px"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{padding:"0 20px"}},[a("el-form-item",{attrs:{label:"封面",prop:"image"}},[a("el-upload",{ref:"uploadFood",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[t.formData.image?a("img",{staticClass:"avatar",attrs:{src:t.formData.image}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),a("el-form-item",{attrs:{label:"运动名称",prop:"name"}},[a("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入运动名称","show-word-limit":""},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),a("el-form-item",{attrs:{label:"数量",prop:"count"}},[a("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入数量","show-word-limit":""},model:{value:t.formData.count,callback:function(e){t.$set(t.formData,"count",e)},expression:"formData.count"}})],1),a("el-form-item",{attrs:{label:"单位",prop:"counting_unit"}},[a("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.counting_unit,callback:function(e){t.$set(t.formData,"counting_unit",e)},expression:"formData.counting_unit"}},[a("el-option",{attrs:{label:"分钟",value:"minute"}}),a("el-option",{attrs:{label:"公里",value:"kilometer"}}),a("el-option",{attrs:{label:"次",value:"freq"}}),a("el-option",{attrs:{label:"组",value:"group"}}),a("el-option",{attrs:{label:"套",value:"set"}})],1)],1),a("el-form-item",{attrs:{label:"热量",prop:"energy_kcal"}},[a("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入热量",maxlength:"40","show-word-limit":""},model:{value:t.formData.energy_kcal,callback:function(e){t.$set(t.formData,"energy_kcal",e)},expression:"formData.energy_kcal"}},[a("template",{slot:"append"},[t._v("千卡")])],2)],1)],1)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"编辑")+" ")])],1)])],1)},n=[],i=a("a34a"),o=a.n(i),s=a("ed08");function l(t,e){return m(t)||p(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return d(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function p(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],r=!0,n=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done);r=!0)if(a.push(o.value),e&&a.length===e)break}catch(l){n=!0,i=l}finally{try{r||null==s["return"]||s["return"]()}finally{if(n)throw i}}return a}}function m(t){if(Array.isArray(t))return t}function f(t,e,a,r,n,i,o){try{var s=t[i](o),l=s.value}catch(c){return void a(c)}s.done?e(l):Promise.resolve(l).then(r,n)}function g(t){return function(){var e=this,a=arguments;return new Promise((function(r,n){var i=t.apply(e,a);function o(t){f(i,r,n,o,s,"next",t)}function s(t){f(i,r,n,o,s,"throw",t)}o(void 0)}))}}var h={name:"SuperAddEditArticle",data:function(){return{isLoading:!1,type:"add",formData:{image:"",name:"",count:"",counting_unit:"",energy_kcal:""},formRuls:{name:[{required:!0,message:"请输入运动名称",trigger:"blur"}],count:[{required:!0,message:"请输入数量",trigger:"blur"}],counting_unit:[{required:!0,message:"请选择是单位",trigger:"blur"}],energy_kcal:[{required:!0,message:"请输入热量",trigger:"blur"}]},actionUrl:"",uploadParams:{}}},created:function(){this.getUploadToken(),this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,image:t.image,name:t.name,count:t.count,counting_unit:t.counting_unit,energy_kcal:t.energy_kcal}}},searchHandle:Object(s["c"])((function(){this.currentPage=1}),300),getUploadToken:function(){var t=this;return g(o.a.mark((function e(){var a;return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"jpeg/png"});case 2:a=e.sent,0===a.code?(t.actionUrl=a.data.host,t.uploadParams={key:a.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:a.data.prefix,policy:a.data.policy,OSSAccessKeyId:a.data.accessid,signature:a.data.signature,callback:a.data.callback,success_action_status:"200"}):t.$message.error(a.msg);case 4:case"end":return e.stop()}}),e)})))()},handleAvatarSuccess:function(t,e){0===t.code?(this.$refs.uploadFood.clearFiles(),this.formData.image=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e="image/jpeg"===t.type||"image/png"===t.type,a=t.size/1024/1024<2;return e||this.$message.error("上传头像图片只能是 JPG 格式!"),a||this.$message.error("上传头像图片大小不能超过 2MB!"),e&&a},addModifyArticle:function(t){var e=this;return g(o.a.mark((function a(){var r,n,i,c,u,d,p,m;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.isLoading=!0,r="",n=l(r,2),i=n[0],c=n[1],"add"!==e.type){a.next=12;break}return a.next=6,Object(s["Q"])(e.$apis.apiBackgroundAdminSportsAddPost(t));case 6:u=a.sent,d=l(u,2),i=d[0],c=d[1],a.next=19;break;case 12:return a.next=15,Object(s["Q"])(e.$apis.apiBackgroundAdminSportsModifyPost(t));case 15:p=a.sent,m=l(p,2),i=m[0],c=m[1];case 19:if(e.isLoading=!1,!i){a.next=23;break}return e.$message.error(i.message),a.abrupt("return");case 23:0===c.code?(e.$message.success(c.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(c.msg);case 24:case"end":return a.stop()}}),a)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifyArticle(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,r){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||r()}}).then((function(t){})).catch((function(t){}))}}},b=h,v=(a("5938"),a("2877")),y=Object(v["a"])(b,r,n,!1,null,null,null);e["default"]=y.exports},ed66:function(t,e,a){}}]);
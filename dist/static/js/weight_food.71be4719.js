(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["weight_food"],{"5dea":function(e,t,a){},a300:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"weight-food container-wrapper"},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"header-wrap"},[a("div",[a("el-radio",{staticClass:"ps-radio",attrs:{label:1},on:{change:e.foodTypeChange},model:{value:e.foodType,callback:function(t){e.foodType=t},expression:"foodType"}},[e._v("按日期")]),a("el-radio",{staticClass:"ps-radio",attrs:{label:2},on:{change:e.foodTypeChange},model:{value:e.foodType,callback:function(t){e.foodType=t},expression:"foodType"}},[e._v("按周")]),1===e.foodType?a("div",{staticClass:"label"},[e._v("排菜日期：")]):e._e(),1===e.foodType?a("el-date-picker",{staticClass:"ps-picker",attrs:{type:"daterange",clearable:!1,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"请选择起始日期","end-placeholder":"请选择结束日期","picker-options":e.pickerOptions,"popper-class":"ps-poper-picker"},on:{change:e.foodTypeChange},model:{value:e.selectDate,callback:function(t){e.selectDate=t},expression:"selectDate"}}):e._e()],1),a("div",[e._v(" "+e._s(e.deviceName)+" ")])])]),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[a("el-table-column",{attrs:{prop:"value",label:"日期/餐段",align:"center",width:"100"}}),e._l(e.tableSetting,(function(t){return[a("el-table-column",{key:t.key,attrs:{label:t.label,prop:t.key,align:"center"},scopedSlots:e._u([{key:"default",fn:function(o){return[o.row[t.key]?a("el-button",{staticClass:"ps-black-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.openChooseDialog("food",o.row,t.key)}}},[e._v(e._s(o.row[t.key]))]):a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.openChooseDialog("food",o.row,t.key)}}},[e._v("添加菜品")])]}}],null,!0)},[t.children?[e._l(t.children,(function(t){return[a("el-table-column",{key:t.key,attrs:{label:t.label,align:"center"},scopedSlots:e._u([{key:"default",fn:function(o){return[o.row[t.key]?a("el-button",{staticClass:"ps-black-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.openChooseDialog("food",o.row,t.key)}}},[e._v(e._s(o.row[t.key]))]):a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.openChooseDialog("food",o.row,t.key)}}},[e._v("添加菜品")])]}}],null,!0)})]}))]:e._e()],2)]})),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.openChooseDialog(e.foodType,t.row)}}},[e._v("复制")])]}}])})],2)],1)]),a("el-dialog",{attrs:{"custom-class":"ps-dialog",title:e.dialogTitle,visible:e.dialogVisible,width:e.dialogWidth,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"content"},["date"===e.dialogType?a("div",[a("div",{staticStyle:{"margin-bottom":"15px"}},[e._v("已选择天数："+e._s(e.dialogForm.dateNum)+"天")]),a("el-date-picker",{staticClass:"ps-picker",attrs:{type:"daterange",clearable:!1,format:"yyyy-MM-dd","value-format":"yyyy-MM-dd",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"请选择起始日期","end-placeholder":"请选择结束日期","picker-options":e.pickerOptions,"popper-class":"ps-poper-picker"},on:{change:e.dateChange},model:{value:e.dialogForm.selectDate,callback:function(t){e.$set(e.dialogForm,"selectDate",t)},expression:"dialogForm.selectDate"}})],1):e._e(),"week"===e.dialogType?a("div",{staticClass:"weekList"},e._l(e.weekList,(function(t){return a("div",{key:t.key},[t.key!==e.dataInfo.key?a("div",{staticClass:"weekItem"},[a("el-checkbox",{staticClass:"ps-checkbox",model:{value:t.checked,callback:function(a){e.$set(t,"checked",a)},expression:"item.checked"}},[e._v(e._s(t.value))])],1):e._e()])})),0):e._e(),"food"===e.dialogType?a("div",[a("div",[a("div",{staticClass:"label"},[e._v("分类：")]),a("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择分类",clearable:""},on:{change:e.searchFood},model:{value:e.dialogForm.category,callback:function(t){e.$set(e.dialogForm,"category",t)},expression:"dialogForm.category"}},e._l(e.dialogForm.categoryList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),a("div",{staticClass:"label"},[e._v("名称：")]),a("el-input",{staticClass:"ps-input w-180",attrs:{placeholder:""},on:{input:e.searchFood},model:{value:e.dialogForm.searchName,callback:function(t){e.$set(e.dialogForm,"searchName",t)},expression:"dialogForm.searchName"}})],1),a("div",{staticClass:"foodList"},e._l(e.dialogForm.foodList,(function(t){return a("div",{key:t.id,staticClass:"foodItem"},[a("el-radio",{staticClass:"ps-radio",attrs:{label:t.id},model:{value:e.dialogForm.foodId,callback:function(t){e.$set(e.dialogForm,"foodId",t)},expression:"dialogForm.foodId"}},[e._v(e._s(t.name))])],1)})),0)]):e._e()]),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v("取消")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v("确定")])],1)])],1)},i=[],s=a("a34a"),n=a.n(s),r=a("ed08");function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){d(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function d(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function u(e,t,a,o,i,s,n){try{var r=e[s](n),l=r.value}catch(c){return void a(c)}r.done?t(l):Promise.resolve(l).then(o,i)}function p(e){return function(){var t=this,a=arguments;return new Promise((function(o,i){var s=e.apply(t,a);function n(e){u(s,o,i,n,r,"next",e)}function r(e){u(s,o,i,n,r,"throw",e)}n(void 0)}))}}var f={name:"WeightFood",data:function(){return{deviceId:"",deviceName:"",isLoading:!1,foodType:1,tableData:[],tableSetting:[{key:"LEFT",label:"左屏幕",children:[{key:"LEFT_breakfast",label:"早餐"},{key:"LEFT_lunch",label:"午餐"},{key:"LEFT_afternoon",label:"下午茶"},{key:"LEFT_dinner",label:"晚餐"},{key:"LEFT_supper",label:"宵夜"},{key:"LEFT_morning",label:"凌晨餐"}]},{key:"RIGHT",label:"右屏幕",children:[{key:"RIGHT_breakfast",label:"早餐"},{key:"RIGHT_lunch",label:"午餐"},{key:"RIGHT_afternoon",label:"下午茶"},{key:"RIGHT_dinner",label:"晚餐"},{key:"RIGHT_supper",label:"宵夜"},{key:"RIGHT_morning",label:"凌晨餐"}]}],selectDate:Object(r["p"])(7,{format:"{y}-{m}-{d}"}),pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;t.setTime(t.getTime()+6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;t.setTime(t.getTime()+2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;t.setTime(t.getTime()+7776e6),e.$emit("pick",[a,t])}}]},weekList:[{key:"1",value:"周一",checked:!1},{key:"2",value:"周二",checked:!1},{key:"3",value:"周三",checked:!1},{key:"4",value:"周四",checked:!1},{key:"5",value:"周五",checked:!1},{key:"6",value:"周六",checked:!1},{key:"7",value:"周日",checked:!1}],dialogType:"",dialogTitle:"",dialogWidth:"",dialogVisible:!1,dialogForm:{dateNum:0,selectDate:[],category:"",categoryList:[],searchName:"",foodList:[],foodId:""},dataInfo:{},dataKey:""}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.query.deviceId&&(this.deviceId=this.$route.query.deviceId),this.$route.query.deviceName&&(this.deviceName=this.$route.query.deviceName),this.$route.query.deviceModel&&"PS-C1050-2"!==this.$route.query.deviceModel&&(this.tableSetting=[{key:"LEFT_breakfast",label:"早餐"},{key:"LEFT_lunch",label:"午餐"},{key:"LEFT_afternoon",label:"下午茶"},{key:"LEFT_dinner",label:"晚餐"},{key:"LEFT_supper",label:"宵夜"},{key:"LEFT_morning",label:"凌晨餐"}]),this.getWeightFoodList(),this.getFoodList(),this.foodCategory()},refreshHandle:function(){this.currentPage=1},getWeightFoodList:function(){var e=this;return p(n.a.mark((function t(){var a,o,i,s;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={device_id:e.deviceId,mealtime_type:e.foodType,start_date:e.selectDate[0],end_date:e.selectDate[1]},t.next=3,e.$apis.apiBackgroundDeviceDeviceFoodBindListPost(a);case 3:if(o=t.sent,0===o.code)for(s in e.tableData=[],i=function(t){var a={};2===e.foodType?e.weekList.map((function(e){e.key===t&&(a.value=e.value)})):a.value=t,a.hasKey=[],o.data.results[t].map((function(e){a[e.screen+"_"+e.meal_type]=e.food.name,a[e.screen+"_"+e.meal_type+"_food_id"]=e.food.id,a[e.screen+"_"+e.meal_type+"_id"]=e.id,a.hasKey.push(e.screen+"_"+e.meal_type)})),e.tableData.push(c({key:t},a))},o.data.results)i(s);else e.$message.error(o.msg);case 5:case"end":return t.stop()}}),t)})))()},foodTypeChange:function(){this.getWeightFoodList()},openChooseDialog:function(e,t,a){this.dialogVisible=!0,1===e?(this.dialogTitle="请选择复制到的日期",this.dialogWidth="400px",this.dialogType="date"):2===e?(this.dialogTitle="请选择复制到的周/天",this.dialogWidth="400px",this.dialogType="week"):"food"===e&&(this.dialogTitle="选择/编辑菜品",this.dialogWidth="600px",this.dialogType="food"),this.dataInfo=t,this.dataKey=a},clickConfirmHandle:function(){var e=this;if("food"===this.dialogType){if(!this.dialogForm.foodId)return this.$message.error("请选择菜品");if(this.dataInfo[this.dataKey]){var t={id:this.dataInfo[this.dataKey+"_id"],food_id:this.dialogForm.foodId};this.editFood(t)}else{var a=this.dataKey.split("_"),o={device_id:this.deviceId,screen:a[0],meal_type:a[1],food_id:this.dialogForm.foodId};1===this.foodType?o.meal_date=this.dataInfo.key:o.meal_week=this.dataInfo.key,this.bindFood(o)}}else{var i=[];this.dataInfo.hasKey.map((function(t){var a=t.split("_");i.push({food_id:e.dataInfo[t+"_food_id"],screen:a[0],meal_type:a[1]})}));var s={device_id:this.deviceId,copy_data:i};if("date"===this.dialogType)s.target_start_date=this.dialogForm.selectDate[0],s.target_end_date=this.dialogForm.selectDate[1];else if("week"===this.dialogType){var n=[];this.weekList.map((function(e){e.checked&&n.push(e.key)})),s.target_week_list=n}this.copyFood(s)}},clickCancleHandle:function(){this.dialogVisible=!1},copyFood:function(e){var t=this;return p(n.a.mark((function a(){var o;return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.$apis.apiBackgroundDeviceDeviceFoodBindCopyFoodBindPost(e);case 2:o=a.sent,0===o.code?(t.$message.success("复制成功"),t.dialogVisible=!1,t.getWeightFoodList()):t.$message.error(o.msg);case 4:case"end":return a.stop()}}),a)})))()},editFood:function(e){var t=this;return p(n.a.mark((function a(){var o;return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.$apis.apiBackgroundDeviceDeviceFoodBindModifyPost(e);case 2:o=a.sent,0===o.code?(t.$message.success("修改成功"),t.dialogVisible=!1,t.getWeightFoodList()):t.$message.error(o.msg);case 4:case"end":return a.stop()}}),a)})))()},bindFood:function(e){var t=this;return p(n.a.mark((function a(){var o;return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.$apis.apiBackgroundDeviceDeviceFoodBindAddPost(e);case 2:o=a.sent,0===o.code?(t.$message.success("绑定菜品成功"),t.dialogVisible=!1,t.getWeightFoodList()):t.$message.error(o.msg);case 4:case"end":return a.stop()}}),a)})))()},dateChange:function(){var e=new Date(this.selectDate[1]).getTime()-new Date(this.selectDate[0]).getTime();this.dateNum=Math.floor(e/864e5)+1},getFoodList:function(){var e=this;return p(n.a.mark((function t(){var a,o;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={page:1,page_size:99999,bind_model:!0},e.dialogForm.category&&(a.category_id=e.dialogForm.category),e.dialogForm.searchName&&(a.food_name=e.dialogForm.searchName),t.next=5,e.$apis.apiBackgroundFoodFoodListPost(a);case 5:o=t.sent,0===o.code?e.dialogForm.foodList=o.data.results:e.$message.error(o.msg);case 7:case"end":return t.stop()}}),t)})))()},searchFood:Object(r["c"])((function(){this.getFoodList()}),300),foodCategory:function(){var e=this;return p(n.a.mark((function t(){var a,o;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={page:1,page_size:99999},t.next=3,e.$apis.apiBackgroundFoodFoodCategoryListPost(a);case 3:o=t.sent,0===o.code?e.dialogForm.categoryList=o.data.results:e.$message.error(o.msg);case 5:case"end":return t.stop()}}),t)})))()}}},g=f,h=(a("d287"),a("2877")),y=Object(h["a"])(g,o,i,!1,null,"20800c3b",null);t["default"]=y.exports},d287:function(e,t,a){"use strict";var o=a("5dea"),i=a.n(o);i.a}}]);
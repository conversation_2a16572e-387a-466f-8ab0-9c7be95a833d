(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-thirdSetting"],{"1b54":function(e,t,a){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"59bb":function(e,t,a){"use strict";var r=a("1b54"),i=a.n(r);i.a},f781:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"container-wrapper thirdSetting"},e._l(e.formThirdData,(function(t,r){return a("div",{key:r,staticClass:"third-box"},[a("el-form",{ref:"thirdRef"+r,refInFor:!0,attrs:{model:e.formThirdData[r],rules:e.thirdFormRuls,size:"small","label-width":"100px"}},[a("el-form-item",{staticClass:"third-item",attrs:{label:t.name,prop:"enable"}},[a("el-switch",{attrs:{"active-color":"#ff9b45"},on:{change:function(t){return e.changeEnableHandle(t,r)}},model:{value:t.enable,callback:function(a){e.$set(t,"enable",a)},expression:"third.enable"}})],1),t.enable?[e._l(t.template,(function(t,i){return a("el-form-item",{key:i,staticClass:"third-item",attrs:{label:t.name,prop:"data."+t.key,rules:e.thirdFormRuls[t.key]}},[t.type&&"input"!==t.type?e._e():a("el-input",{attrs:{size:"small",disabled:t.disabled},model:{value:e.formThirdData[r]["data"][t.key],callback:function(a){e.$set(e.formThirdData[r]["data"],t.key,a)},expression:"formThirdData[key]['data'][item.key]"}}),"textarea"===t.type?a("el-input",{attrs:{size:"small",type:"textarea",rows:3,disabled:t.disabled},model:{value:e.formThirdData[r]["data"][t.key],callback:function(a){e.$set(e.formThirdData[r]["data"],t.key,a)},expression:"formThirdData[key]['data'][item.key]"}}):e._e(),"select"===t.type?a("el-select",{attrs:{size:"small",disabled:t.disabled,placeholder:""},model:{value:e.formThirdData[r]["data"][t.key],callback:function(a){e.$set(e.formThirdData[r]["data"],t.key,a)},expression:"formThirdData[key]['data'][item.key]"}},e._l(t.value,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1):e._e()],1)})),a("el-form-item",[a("div",{staticClass:"add-wrapper"},[a("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.saveAppidHandle(r)}}},[e._v("保存")])],1)])]:e._e()],2)],1)})),0)},i=[],n=a("a34a"),s=a.n(n),o=a("ed08");function d(e,t){return m(e)||f(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return l(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function f(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,i=!1,n=void 0;try{for(var s,o=e[Symbol.iterator]();!(r=(s=o.next()).done);r=!0)if(a.push(s.value),t&&a.length===t)break}catch(d){i=!0,n=d}finally{try{r||null==o["return"]||o["return"]()}finally{if(i)throw n}}return a}}function m(e){if(Array.isArray(e))return e}function h(e,t,a,r,i,n,s){try{var o=e[n](s),d=o.value}catch(c){return void a(c)}o.done?t(d):Promise.resolve(d).then(r,i)}function p(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){h(n,r,i,s,o,"next",e)}function o(e){h(n,r,i,s,o,"throw",e)}s(void 0)}))}}var b={name:"SuperBindAppid",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{formOperate:"detail",isLoading:!1,thirdSettingList:[],formThirdData:{},thirdTemplateList:{},thirdData:{},thirdFormRuls:{third:[{required:!0,message:"请先输入third",trigger:"change"}]}}},computed:{checkIsFormStatus:function(){var e=!1;switch(this.formOperate){case"detail":e=!1;break;case"modify":e=!0;break}return e}},watch:{},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){var e=this;return p(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getThirdSettingTemplate();case 2:e.getThirdSetting();case 3:case"end":return t.stop()}}),t)})))()},refreshHandle:function(){this.initLoad()},searchHandle:Object(o["c"])((function(){}),300),changeOperate:function(){"modify"!==this.formOperate?this.formOperate="modify":this.formOperate="detail"},getThirdSetting:function(){var e=this;return p(s.a.mark((function t(){var a,r,i,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationGetThirdSettingsPost({id:e.organizationData.id}));case 3:if(a=t.sent,r=d(a,2),i=r[0],n=r[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===n.code?(n.data.forEach((function(t){e.thirdData[t.third_name]=t})),Object.keys(e.thirdTemplateList).forEach((function(t){e.setFormKeyValueHandle(e.formThirdData,e.thirdData[t]?e.thirdData[t]:{},t),e.thirdTemplateList[t].keys.forEach((function(t){if(t.required&&!e.thirdFormRuls[t.key]){var a="";switch(t.type){case"input":a="输入";break;case"textarea":a="输入";break;case"select":a="选择";break;default:a="输入";break}e.$set(e.thirdFormRuls,t.key,[{required:!0,message:"请".concat(a).concat(t.name),trigger:"change"}])}}))}))):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},setFormKeyValueHandle:function(e,t,a){var r=this;e[a]||this.$set(e,a,{}),this.$set(e[a],"template",this.thirdTemplateList[a].keys),this.$set(e[a],"id",t.third_id?t.third_id:""),this.$set(e[a],"name",this.thirdTemplateList[a].name),this.$set(e[a],"enable",!!t.enable&&t.enable),this.$set(e[a],"data",{}),this.thirdTemplateList[a].keys.forEach((function(i){var n=t.extra&&void 0!==t.extra[i.key]?t.extra[i.key]:"";r.$set(e[a].data,i.key,n)}))},getThirdSettingTemplate:function(){var e=this;return p(s.a.mark((function t(){var a,r,i,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationThirdTemplateListPost({id:e.organizationData.id}));case 3:if(a=t.sent,r=d(a,2),i=r[0],n=r[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===n.code?e.thirdTemplateList=n.data:e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},changeEnableHandle:function(e,t){e||this.modifyOrganization(t)},saveAppidHandle:function(e){var t=this;return p(s.a.mark((function a(){return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.isLoading){a.next=2;break}return a.abrupt("return");case 2:t.$refs["thirdRef".concat(e)][0].validate((function(a){a&&t.modifyOrganization(e)}));case 3:case"end":return a.stop()}}),a)})))()},modifyOrganization:function(e){var t=this;return p(s.a.mark((function a(){var r,i,n,c,u;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r={id:t.organizationData.id,third_name:e,enable:t.formThirdData[e].enable,extra:t.formThirdData[e].data},t.formThirdData[e].id&&(r.third_id=t.formThirdData[e].id),t.isLoading=!0,a.next=5,Object(o["Q"])(t.$apis.apiBackgroundAdminOrganizationModifyThirdSettingsPost(r));case 5:if(i=a.sent,n=d(i,2),c=n[0],u=n[1],t.isLoading=!1,!c){a.next=13;break}return t.$message.error(c.message),a.abrupt("return");case 13:0===u.code?(t.$message.success("修改成功"),t.formOperate="detail",t.restoreHandle(t.type,t.formOperate),t.initLoad()):t.$message.error(u.msg);case 14:case"end":return a.stop()}}),a)})))()}}},g=b,y=(a("59bb"),a("2877")),v=Object(y["a"])(g,r,i,!1,null,null,null);t["default"]=v.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-deductSetting"],{"6bf7":function(e,t,a){"use strict";var i=a("a227"),r=a.n(i);r.a},"70b9":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"deductsetting-wrapper"},[i("div",{staticClass:"m-b-10"},[i("span",{staticClass:"p-r-10",staticStyle:{"font-size":"14px"}},[e._v("手续费生效方式")]),i("el-radio-group",{staticClass:"ps-radio",on:{change:e.changeCommissionsChargeType},model:{value:e.commissionsChargeType,callback:function(t){e.commissionsChargeType=t},expression:"commissionsChargeType"}},[i("el-radio",{attrs:{label:0}},[e._v("订单实收金额+手续费")])],1)],1),i("div",{staticClass:"wrapper-title"},[e._v("注：优先使用【营销活动-手续费】中的设置，当用户无分组或所在分组无规则时，生效当前【扣款设置】的手续费规则")]),e._m(0),i("div",{staticClass:"table-box"},[i("el-table",{ref:"onlineWalletRef",attrs:{width:"100%","row-key":"id",data:e.onlineWalletList,"tooltip-effect":"dark","header-row-class-name":"ps-table-header-row",stripe:""}},[i("el-table-column",{attrs:{type:"index",label:"优先级",width:"80",align:"center"}}),i("el-table-column",{attrs:{label:"扣款钱包",prop:"sub_payway_alias",align:"center"}}),i("el-table-column",{attrs:{label:"商户名称",prop:"merchant_name",align:"center"}}),i("el-table-column",{attrs:{label:"商户号",prop:"merchant_id",align:"center"}}),i("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"备注",prop:"remark",align:"center"}}),i("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"手续费",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.service_fee_value?t.row.service_fee_value?i("span",{staticClass:"ps-origin",staticStyle:{cursor:"pointer"},on:{click:function(a){return e.serviceSetting(t.row)}}},[e._v(" "+e._s(e.servicePirceFormat(t.row))+" "),i("span",[e._v(e._s(1===t.row.service_fee_type?"元":"%"))])]):e._e():i("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:"CashPay"===t.row.payway||"PushiPay"===t.row.payway},on:{click:function(a){return e.serviceSetting(t.row)}}},[e._v(" 设置 ")])]}}])}),i("el-table-column",{attrs:{label:"操作",prop:"",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("img",{staticClass:"drop-img",attrs:{src:a("cd5c"),alt:""}})]}}])})],1)],1),i("div",{staticClass:"add-wrapper"},[i("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.set_order_payinfos"],expression:"['background.admin.pay_info.set_order_payinfos']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.saveWalletWeightHandle("online")}}},[e._v("保存")])],1),e._m(1),i("div",{staticClass:"table-box"},[i("el-table",{ref:"instoreWalletRef",attrs:{width:"100%","row-key":"id",data:e.instoreWalletList,"tooltip-effect":"dark","header-row-class-name":"ps-table-header-row",stripe:""}},[i("el-table-column",{attrs:{type:"index",label:"优先级",width:"80",align:"center"}}),i("el-table-column",{attrs:{label:"扣款钱包",prop:"sub_payway_alias",align:"center"}}),i("el-table-column",{attrs:{label:"商户名称",prop:"merchant_name",align:"center"}}),i("el-table-column",{attrs:{label:"商户号",prop:"merchant_id",align:"center"}}),i("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"备注",prop:"remark",align:"center"}}),i("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"手续费",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.service_fee_value?t.row.service_fee_value?i("span",{staticClass:"ps-origin",staticStyle:{cursor:"pointer"},on:{click:function(a){return e.serviceSetting(t.row)}}},[e._v(" "+e._s(e.servicePirceFormat(t.row))+" "),i("span",[e._v(e._s(1===t.row.service_fee_type?"元":"%"))])]):e._e():i("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:"CashPay"===t.row.payway||"PushiPay"===t.row.payway},on:{click:function(a){return e.serviceSetting(t.row)}}},[e._v(" 设置 ")])]}}])}),i("el-table-column",{attrs:{label:"操作",prop:"",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("img",{staticClass:"drop-img",attrs:{src:a("cd5c"),alt:""}})]}}])})],1)],1),i("div",{staticClass:"add-wrapper"},[i("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.set_order_payinfos"],expression:"['background.admin.pay_info.set_order_payinfos']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.saveWalletWeightHandle("instore")}}},[e._v("保存")])],1),e._m(2),i("div",{staticClass:"form-wrapper"},[i("el-form",{ref:"walletFormRef",attrs:{model:e.walletFormData,rules:e.walletFormRuls,"label-width":"180px"}},[i("el-form-item",{attrs:{prop:"pushiPayTime",label:"重复支付限制"}},[i("el-switch",{staticClass:"wallet-margin",attrs:{"active-color":"#ff9b45"},model:{value:e.walletFormData.isDuplicatePayLimit,callback:function(t){e.$set(e.walletFormData,"isDuplicatePayLimit",t)},expression:"walletFormData.isDuplicatePayLimit"}}),i("el-input-number",{attrs:{disabled:!e.walletFormData.isDuplicatePayLimit,min:0},model:{value:e.walletFormData.duplicatePaySecondLimit,callback:function(t){e.$set(e.walletFormData,"duplicatePaySecondLimit",t)},expression:"walletFormData.duplicatePaySecondLimit"}}),i("span",{staticClass:"wallet-margin-l"},[e._v("秒内不能重复支付")])],1)],1)],1),i("div",{staticClass:"add-wrapper"},[i("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.set_order_payinfos"],expression:"['background.admin.pay_info.set_order_payinfos']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.setSeniorSettingHandle}},[e._v("保存")])],1),i("el-dialog",{attrs:{title:"手续费设置",visible:e.serviceSettingDialog,width:"400px","custom-class":"ps-dialog"},on:{"update:visible":function(t){e.serviceSettingDialog=t}}},[i("el-form",{ref:"serviceSettingForm",attrs:{rules:e.serviceSettingDialogRuls,model:e.serviceSettingDialogFormData}},[i("div",{staticClass:"form-flex"},[i("el-form-item",{staticClass:"p-r-20"},[i("el-radio",{staticClass:"ps-radio",attrs:{label:1},model:{value:e.serviceSettingDialogFormData.service_fee_type,callback:function(t){e.$set(e.serviceSettingDialogFormData,"service_fee_type",t)},expression:"serviceSettingDialogFormData.service_fee_type"}},[e._v(" 定额 ")])],1),2!==e.serviceSettingDialogFormData.service_fee_type?i("el-form-item",{attrs:{prop:"quota"}},[i("div",{staticClass:"form-flex"},[i("el-input",{staticClass:"ps-input w-150 p-r-10",attrs:{size:"small"},model:{value:e.serviceSettingDialogFormData.quota,callback:function(t){e.$set(e.serviceSettingDialogFormData,"quota",t)},expression:"serviceSettingDialogFormData.quota"}}),i("span",[e._v("元")])],1),i("span",[e._v("实收金额=订单金额+定额")])]):e._e()],1),i("div",{staticClass:"form-flex"},[i("el-form-item",{staticClass:"p-r-20",attrs:{label:""}},[i("el-radio",{staticClass:"ps-radio",attrs:{label:2},model:{value:e.serviceSettingDialogFormData.service_fee_type,callback:function(t){e.$set(e.serviceSettingDialogFormData,"service_fee_type",t)},expression:"serviceSettingDialogFormData.service_fee_type"}},[e._v(" 百分比 ")])],1),1!==e.serviceSettingDialogFormData.service_fee_type?i("el-form-item",{attrs:{prop:"discount"}},[i("div",{staticClass:"form-flex"},[i("el-input",{staticClass:"ps-input w-150 p-r-10",attrs:{size:"small"},model:{value:e.serviceSettingDialogFormData.discount,callback:function(t){e.$set(e.serviceSettingDialogFormData,"discount",t)},expression:"serviceSettingDialogFormData.discount"}}),i("span",[e._v("%")])],1),i("span",[e._v("实收金额=订单金额+（订单金额*折扣）")])]):e._e()],1)]),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.serviceSettingDialog=!1}}},[e._v("取 消")]),i("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.determineServiceSettingDialog}},[e._v(" 确 定 ")])],1)],1)],1)},r=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"l-title"},[a("span",[e._v("线上扣款顺序")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"l-title"},[a("span",[e._v("线下扣款顺序")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"l-title"},[a("span",[e._v("扣款限制")])])}],n=a("a34a"),s=a.n(n),o=a("ed08"),l=a("aa47"),c=a("d0dd"),u=a("da92");function p(e,t){return v(e)||f(e,t)||m(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return g(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=new Array(t);a<t;a++)i[a]=e[a];return i}function f(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],i=!0,r=!1,n=void 0;try{for(var s,o=e[Symbol.iterator]();!(i=(s=o.next()).done);i=!0)if(a.push(s.value),t&&a.length===t)break}catch(l){r=!0,n=l}finally{try{i||null==o["return"]||o["return"]()}finally{if(r)throw n}}return a}}function v(e){if(Array.isArray(e))return e}function _(e,t,a,i,r,n,s){try{var o=e[n](s),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(i,r)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(i,r){var n=e.apply(t,a);function s(e){_(n,i,r,s,o,"next",e)}function o(e){_(n,i,r,s,o,"throw",e)}s(void 0)}))}}var b={name:"SuperAddOrganization",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{isOpen:!1,isLoading:!1,commissionsChargeType:0,formOperate:"detail",onlineSortable:null,instoreSortable:null,pageSize:10,currentPage:1,totalCount:0,onlineWalletList:[],instoreWalletList:[],onlineSortList:[],instoreSortList:[],walletFormData:{isDuplicatePayLimit:!1,duplicatePaySecondLimit:0},walletFormRuls:{merchantId:[{required:!0,message:"商户号不能为空",trigger:"blur"}],merchantName:[{required:!0,message:"商户名称不能为空",trigger:"blur"}],payway:[{required:!0,message:"请选择充值渠道",trigger:"blur"}]},serviceSettingDialog:!1,serviceSettingDialogFormData:{service_fee_type:1,quota:"",discount:""},serviceSettingDialogRuls:{quota:[{required:!0,message:"请输入金额",trigger:"blur"},{validator:c["c"],trigger:"blur"}],discount:[{required:!0,message:"请输入折扣",trigger:"blur"},{validator:c["f"],trigger:"blur"}]},serviceSettingData:{}}},computed:{checkIsFormStatus:function(){var e=!1;switch(this.formOperate){case"detail":e=!1;break;case"add":e=!0;break}return e}},watch:{type:function(e){},organizationData:function(e){var t=this;setTimeout((function(){t.searchHandle()}),50)}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.setChargeSetting({organization_id:this.infoData.id}),this.getWalletPayList("online"),this.getWalletPayList("instore"),this.getSettingInfo()},refreshHandle:function(){this.currentPage=1,this.initLoad()},searchHandle:Object(o["c"])((function(){this.initLoad()}),300),getWalletPayList:function(e){var t=this;return y(s.a.mark((function a(){var i,r,n,l,c;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,i={organizations:[t.organizationData.id],pay_scenes:[e],company:t.organizationData.company},a.next=4,Object(o["Q"])(t.$apis.apiBackgroundAdminPayInfoGetOrderPayinfosPost(i));case 4:if(r=a.sent,n=p(r,2),l=n[0],c=n[1],t.isLoading=!1,!l){a.next=12;break}return t.$message.error(l.message),a.abrupt("return");case 12:0===c.code?"online"===e?(t.onlineWalletList=c.data.results.sort((function(e,t){return e.weight-t.weight})),t.onlineSortable||t.$nextTick((function(){t.initSortable(e)}))):(t.instoreWalletList=c.data.results.sort((function(e,t){return e.weight-t.weight})),t.instoreSortable||t.$nextTick((function(){t.initSortable(e)}))):t.$message.error(c.msg);case 13:case"end":return a.stop()}}),a)})))()},initSortable:function(e){var t=this;this[e+"SortList"]=this[e+"WalletList"].map((function(e){return e.id}));var a=this.$refs[e+"WalletRef"].$el.querySelector(".el-table__body-wrapper > table > tbody");this[e+"Sortable"]=l["a"].create(a,{ghostClass:"sortable-active",animation:300,setData:function(e){e.setData("Text","")},onEnd:function(a){var i=t[e+"WalletList"].splice(a.oldIndex,1)[0];t[e+"WalletList"].splice(a.newIndex,0,i),console.log(t[e+"SortList"]);var r=t[e+"SortList"].splice(a.oldIndex,1)[0];t[e+"SortList"].splice(a.newIndex,0,r)}})},determineServiceSettingDialog:function(){var e=this;this.$refs.serviceSettingForm.validate((function(t){t?(e[e.serviceSettingData.pay_scene+"WalletList"].map((function(t,a){e.serviceSettingData.id===t.id&&(t.service_fee_type=e.serviceSettingDialogFormData.service_fee_type,t.service_fee_value=1===e.serviceSettingDialogFormData.service_fee_type?u["a"].times(Number(e.serviceSettingDialogFormData.quota),100):e.serviceSettingDialogFormData.discount)})),console.log(e[e.serviceSettingData.pay_scene+"WalletList"]),e.serviceSettingDialog=!1):console.log(t)}))},getSettingInfo:function(){var e=this;return y(s.a.mark((function t(){var a,i,r,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationGetSettingsPost({id:e.organizationData.id,company:e.organizationData.company}));case 3:if(a=t.sent,i=p(a,2),r=i[0],n=i[1],e.isLoading=!1,!r){t.next=11;break}return e.$message.error(r.message),t.abrupt("return");case 11:0===n.code?(e.settingInfo=n.data,e.walletFormData.isDuplicatePayLimit=!!n.data.is_duplicate_pay_limit,e.walletFormData.duplicatePaySecondLimit=n.data.duplicate_pay_second_limit):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},serviceSetting:function(e){this.serviceSettingData=e,this.serviceSettingDialogFormData.service_fee_type=e.service_fee_type,1===e.service_fee_type&&(this.serviceSettingDialogFormData.discount="",this.serviceSettingDialogFormData.quota=String(u["a"].divide(e.service_fee_value,100))),2===e.service_fee_type&&(this.serviceSettingDialogFormData.discount=e.service_fee_value,this.serviceSettingDialogFormData.quota=""),this.serviceSettingDialog=!0},saveWalletWeightHandle:function(e){var t=this;return y(s.a.mark((function a(){var i,r,n,l,c,u;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i=t[e+"WalletList"].map((function(e,t){return{id:e.id,weight:t+1,service_fee_type:e.service_fee_type,service_fee_value:e.service_fee_value}})),t.isLoading=!0,r={organizations:[t.organizationData.id],pay_scene:e,payinfos:i,company:t.organizationData.company},a.next=5,Object(o["Q"])(t.$apis.apiBackgroundAdminPayInfoSetOrderPayinfosPost(r));case 5:if(n=a.sent,l=p(n,2),c=l[0],u=l[1],t.isLoading=!1,!c){a.next=13;break}return t.$message.error(c.message),a.abrupt("return");case 13:0===u.code?(t.$message.success(u.msg),t.getWalletPayList(e)):t.$message.error(u.msg);case 14:case"end":return a.stop()}}),a)})))()},setSeniorSettingHandle:function(){var e=this;return y(s.a.mark((function t(){var a,i,r,n,l;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a={id:e.organizationData.id,is_duplicate_pay_limit:e.walletFormData.isDuplicatePayLimit?1:0,duplicate_pay_second_limit:e.walletFormData.duplicatePaySecondLimit,company:e.organizationData.company},t.next=4,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationModifySettingsPost(a));case 4:if(i=t.sent,r=p(i,2),n=r[0],l=r[1],e.isLoading=!1,!n){t.next=12;break}return e.$message.error(n.message),t.abrupt("return");case 12:0===l.code?(e.payTemplateList=l.data,e.$message.success(l.msg),e.getSettingInfo()):e.$message.error(l.msg);case 13:case"end":return t.stop()}}),t)})))()},changeCommissionsChargeType:function(){var e={type:0,organization_id:this.infoData.id,commissions_charge_type:this.commissionsChargeType};this.setChargeSetting(e)},setChargeSetting:function(e){var t=this;return y(s.a.mark((function a(){var i,r,n,l;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(o["Q"])(t.$apis.apiBackgroundPaymentPayInfoSetCommissionChargeSettingPost(e));case 2:if(i=a.sent,r=p(i,2),n=r[0],l=r[1],!n){a.next=9;break}return t.$message.error(n.message),a.abrupt("return");case 9:0===l.code?(0!==e.commissions_charge_type&&1!==e.commissions_charge_type||t.$message.success("配置成功"),t.commissionsChargeType=l.data.commissions_charge_type):t.$message.error(l.msg);case 10:case"end":return a.stop()}}),a)})))()},servicePirceFormat:function(e){return 1===e.service_fee_type?u["a"].divide(e.service_fee_value,100):e.service_fee_value}}},w=b,h=(a("6bf7"),a("2877")),S=Object(h["a"])(w,i,r,!1,null,null,null);t["default"]=S.exports},a227:function(e,t,a){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},d0dd:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r})),a.d(t,"g",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"f",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"e",(function(){return c}));var i=function(e,t,a){if(t){var i=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},r=function(e,t,a){if(t){var i=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("金额格式有误"))}else a()},n=function(e,t,a){if(!t)return a(new Error("手机号不能为空"));var i=/^1[3456789]\d{9}$/;i.test(t)?a():a(new Error("请输入正确手机号"))},s=function(e,t,a){if(!t)return a(new Error("金额有误"));var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("金额格式有误"))},o=function(e,t,a){if(""===t)return a(new Error("不能为空"));var i=/^\d+$/;i.test(t)?a():a(new Error("请输入正确数字"))},l=function(e,t,a){if(""!==t){var i=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;console.log(t,i.test(t)),i.test(t)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},c=function(e,t,a){var i=/^[\u4E00-\u9FA5\w-]+$/;i.test(t)?a():a(new Error("格式不正确，不能包含特殊字符"))}}}]);
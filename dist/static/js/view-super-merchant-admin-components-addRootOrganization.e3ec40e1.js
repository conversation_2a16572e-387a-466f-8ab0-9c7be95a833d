(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-addRootOrganization"],{c938:function(t){t.exports=JSON.parse('[{"id":"1","name":"互联网/电子商务"},{"id":"2","name":"IT软件与服务"},{"id":"3","name":"IT硬件与设备"},{"id":"4","name":"电子技术"},{"id":"5","name":"通信与运营商"},{"id":"6","name":"网络游戏"},{"id":"7","name":"银行"},{"id":"8","name":"基金|理财|信托"},{"id":"9","name":"保险"},{"id":"10","name":"餐饮"},{"id":"11","name":"酒店"},{"id":"12","name":"旅游"},{"id":"13","name":"快递"},{"id":"14","name":"物流"},{"id":"15","name":"仓储"},{"id":"16","name":"培训"},{"id":"17","name":"院校"},{"id":"18","name":"学术科研"},{"id":"19","name":"交警"},{"id":"20","name":"博物馆"},{"id":"21","name":"公共事业|非盈利机构"},{"id":"22","name":"医药医疗"},{"id":"23","name":"护理美容"},{"id":"24","name":"保健与卫生"},{"id":"25","name":"汽车相关"},{"id":"26","name":"摩托车相关"},{"id":"27","name":"火车相关"},{"id":"28","name":"飞机相关"},{"id":"29","name":"建筑"},{"id":"30","name":"物业"},{"id":"31","name":"消费品"},{"id":"32","name":"法律"},{"id":"33","name":"会展"},{"id":"34","name":"中介服务"},{"id":"35","name":"认证"},{"id":"36","name":"审计"},{"id":"37","name":"传媒"},{"id":"38","name":"体育"},{"id":"39","name":"娱乐休闲"},{"id":"40","name":"印刷"},{"id":"41","name":"其它"}]')},d0dd:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return s})),a.d(e,"g",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"f",(function(){return l})),a.d(e,"d",(function(){return n})),a.d(e,"e",(function(){return c}));var r=function(t,e,a){if(e){var r=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(e)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},s=function(t,e,a){if(e){var r=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(e)?a():a(new Error("金额格式有误"))}else a()},i=function(t,e,a){if(!e)return a(new Error("手机号不能为空"));var r=/^1[3456789]\d{9}$/;r.test(e)?a():a(new Error("请输入正确手机号"))},o=function(t,e,a){if(!e)return a(new Error("金额有误"));var r=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(e)?a():a(new Error("金额格式有误"))},l=function(t,e,a){if(""===e)return a(new Error("不能为空"));var r=/^\d+$/;r.test(e)?a():a(new Error("请输入正确数字"))},n=function(t,e,a){if(""!==e){var r=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;console.log(e,r.test(e)),r.test(e)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},c=function(t,e,a){var r=/^[\u4E00-\u9FA5\w-]+$/;r.test(e)?a():a(new Error("格式不正确，不能包含特殊字符"))}},e817:function(t,e,a){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},f351:function(t,e,a){"use strict";var r=a("e817"),s=a.n(r);s.a},f850:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container-wrapper super-add-organization is-fixed-footer"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"organizationFormRef",staticClass:"organization-form-wrapper",attrs:{rules:t.formDataRuls,model:t.formData,size:"small"}},["add"===t.operate?a("div",{staticClass:"add-title"},[t._v("新建组织")]):t._e(),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("基本信息")]),t.checkIsFormStatus?t._e():a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:t.changeOperate}},[t._v(" 编辑 ")])],1),a("div",{staticClass:"item-box clearfix"},[a("div",{staticClass:"item-b-l"},[t._v(t._s(t.labelName))]),a("div",{staticClass:"item-b-r"},[a("el-form-item",{staticClass:"block-label",attrs:{label:"组织名称：",prop:"name"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.name))])],1)],1)]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"当前组织层次：",prop:"levelName"}},[a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.levelName))])]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"官网：",prop:"url"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.url,callback:function(e){t.$set(t.formData,"url",e)},expression:"formData.url"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.url))])],1),a("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"固定电话：",prop:"tel"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.tel,callback:function(e){t.$set(t.formData,"tel",e)},expression:"formData.tel"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.tel))])],1)],1),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"form-item-box"},[a("el-form-item",{staticClass:"block-label",attrs:{label:"组织邮箱：",prop:"mailAddress"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.mailAddress,callback:function(e){t.$set(t.formData,"mailAddress",e)},expression:"formData.mailAddress"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.mailAddress))])],1)],1)])],1),a("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[a("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[a("el-form-item",{attrs:{label:"行业性质：",prop:"industry"}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{placeholder:"请选择行业性质",size:"small",disabled:!t.checkIsFormStatus},model:{value:t.formData.industry,callback:function(e){t.$set(t.formData,"industry",e)},expression:"formData.industry"}},t._l(t.industryTypeList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),a("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[a("el-form-item",{attrs:{label:"所在地址：",prop:"district"}},[a("el-cascader",{staticStyle:{display:"block"},attrs:{size:"small",options:t.addrOptions,disabled:!t.checkIsFormStatus},model:{value:t.formData.district,callback:function(e){t.$set(t.formData,"district",e)},expression:"formData.district"}})],1)],1)],1),a("div",{staticClass:"form-line"}),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("功能配置")])]),"add"===t.operate?a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"添加组织层级：",prop:"initOrganizationLevel"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.formData.initOrganizationLevel,callback:function(e){t.$set(t.formData,"initOrganizationLevel",e)},expression:"formData.initOrganizationLevel"}},t._l(t.levelList,(function(t){return a("el-option",{key:t.level,attrs:{label:t.name,value:t.level}})})),1)],1):t._e(),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"功能菜单配置：",prop:"permission"}},[t.checkIsFormStatus?a("div",{staticStyle:{margin:"3px 0 5px"}},[a("el-button",{attrs:{type:"",size:"mini"},on:{click:function(e){return t.clickSelectPermissionTree(1)}}},[t._v("全选")]),a("el-button",{attrs:{type:"",size:"mini"},on:{click:function(e){return t.clickSelectPermissionTree(0)}}},[t._v("全不选")])],1):t._e(),a("select-tree",{staticClass:"search-item-w ps-input",attrs:{placeholder:"请选择适用的下级组织",multiple:!0,"check-strictly":!0,"append-to-body":!0,"is-select-child":!0,"default-expand-all":!0,treeData:t.permissionTree,treeProps:t.permissionTreeProps,clearable:!0,disabled:!t.checkIsFormStatus},model:{value:t.formData.permission,callback:function(e){t.$set(t.formData,"permission",e)},expression:"formData.permission"}})],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:"useCardNoLimit"}},[a("span",[a("span",{staticClass:"warn"},[t._v("*")]),t._v(" IC卡验证 "),a("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.useCardNoLimit,callback:function(e){t.$set(t.formData,"useCardNoLimit",e)},expression:"formData.useCardNoLimit"}},[a("el-radio",{attrs:{label:!0}},[t._v("是")]),a("el-radio",{attrs:{label:!1}},[t._v("否")])],1)],1)]),"root"===t.type?a("el-form-item",{staticClass:"block-label form-item-box fixed-login-box",attrs:{label:"账号：",prop:"username"}},["root"===t.type&&"add"!==t.operate?a("span",{staticClass:"fixed-login"},[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:t.gotoLogin}},[t._v("登录")])],1):t._e(),"root"===t.type&&"add"===t.operate?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.username,callback:function(e){t.$set(t.formData,"username",e)},expression:"formData.username"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.username))])],1):t._e(),"root"===t.type&&"add"===t.operate?a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"密码：",prop:"password"}},[a("el-input",{staticClass:"ps-input",attrs:{disabled:!t.checkIsFormStatus,size:"small"},model:{value:t.formData.password,callback:function(e){t.$set(t.formData,"password",e)},expression:"formData.password"}}),a("div",{staticStyle:{"margin-top":"3px",color:"#F56C6C","line-height":"1","font-size":"12px"}},[t._v("密码有效期为90天，请在期限前重置密码")])],1):t._e(),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[a("span",[t._v(" 到期修改密码 "),a("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.isExpireChangePwd,callback:function(e){t.$set(t.formData,"isExpireChangePwd",e)},expression:"formData.isExpireChangePwd"}}),t.formData.isExpireChangePwd?a("el-checkbox",{staticClass:"ps-checkbox",staticStyle:{"margin-left":"10px"},attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.allowJumpChangePwd,callback:function(e){t.$set(t.formData,"allowJumpChangePwd",e)},expression:"formData.allowJumpChangePwd"}},[t._v(" 允许跳过本次 ")]):t._e()],1)]),a("div",{staticClass:"form-line"}),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("联系方式")])]),a("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"联系人：",prop:"contact"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.contact,callback:function(e){t.$set(t.formData,"contact",e)},expression:"formData.contact"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.contact))])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"手机号码：",prop:"mobile"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.mobile,callback:function(e){t.$set(t.formData,"mobile",e)},expression:"formData.mobile"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.mobile))])],1)],1)],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"短信模板：",prop:"smsTemplateId"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",rows:3},model:{value:t.formData.smsTemplateId,callback:function(e){t.$set(t.formData,"smsTemplateId",e)},expression:"formData.smsTemplateId"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.smsTemplateId))])],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"备注：",prop:"remark"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",rows:3},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.remark))])],1),a("div",{staticClass:"form-line"}),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("其它设置")])]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"钱包设置",prop:""}},[a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.storeWalletOn,callback:function(e){t.$set(t.formData,"storeWalletOn",e)},expression:"formData.storeWalletOn"}},[t._v(" 储值钱包 ")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.electronicWalletOn,callback:function(e){t.$set(t.formData,"electronicWalletOn",e)},expression:"formData.electronicWalletOn"}},[t._v(" 电子钱包 ")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.subsidyWalletOn,callback:function(e){t.$set(t.formData,"subsidyWalletOn",e)},expression:"formData.subsidyWalletOn"}},[t._v(" 补贴钱包 ")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.complimentaryWalletOn,callback:function(e){t.$set(t.formData,"complimentaryWalletOn",e)},expression:"formData.complimentaryWalletOn"}},[t._v(" 赠送钱包 ")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.otherWalletOn,callback:function(e){t.$set(t.formData,"otherWalletOn",e)},expression:"formData.otherWalletOn"}},[t._v(" 第三方钱包 ")])],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[t._v(" 钱包扣款规则 "),a("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.isWalletPayOrderAsc,callback:function(e){t.$set(t.formData,"isWalletPayOrderAsc",e)},expression:"formData.isWalletPayOrderAsc"}},[a("el-radio",{attrs:{label:!1}},[t._v("扣上级钱包余额")]),a("el-radio",{attrs:{label:!0}},[t._v("扣下级钱包余额")])],1),a("div",{staticStyle:{"margin-left":"88px",color:"#ff9b45"}},[t._v("当订单所属组织余额不足时将扣取余额充足的上级/下级钱包金额;该规则仅适合线下消费")])],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[a("span",[t._v(" 组合支付 "),a("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.combineWalletOn,callback:function(e){t.$set(t.formData,"combineWalletOn",e)},expression:"formData.combineWalletOn"}})],1)]),a("el-form-item",{staticClass:"form-item-box",attrs:{label:"开关设置",prop:""}},[a("span",{staticStyle:{"margin-right":"25px"}},[t._v(" 人脸支付 "),a("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.facepay,callback:function(e){t.$set(t.formData,"facepay",e)},expression:"formData.facepay"}})],1),a("span",[t._v(" 支持退款 "),a("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.refundOn,callback:function(e){t.$set(t.formData,"refundOn",e)},expression:"formData.refundOn"}})],1)]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[a("span",[t._v(" 是否农行项目点展示 "),a("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.isAbcProject,callback:function(e){t.$set(t.formData,"isAbcProject",e)},expression:"formData.isAbcProject"}})],1)]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[a("span",[t._v(" 人脸更新消息提醒： ")]),a("el-switch",{staticClass:"m-r-20",attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.enableUpdateNotify,callback:function(e){t.$set(t.formData,"enableUpdateNotify",e)},expression:"formData.enableUpdateNotify"}}),t.formData.enableUpdateNotify?a("div",{staticStyle:{"margin-left":"125px"}},[a("span",{staticClass:"m-r-20"},[t._v("上传人脸时间每隔")]),a("el-form-item",{staticClass:"inline-label form-item-box m-t-2 m-b-2 m-r-20",attrs:{label:"",prop:"faceUpdateTime"}},[a("el-select",{staticClass:"w-110",attrs:{clearable:"",disabled:!t.checkIsFormStatus,placeholder:"请选择"},model:{value:t.formData.faceUpdateTime,callback:function(e){t.$set(t.formData,"faceUpdateTime",e)},expression:"formData.faceUpdateTime"}},t._l(t.faceUploadOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.name,value:t.value}})})),1)],1),"auto"===t.formData.faceUpdateTime?a("el-form-item",{staticClass:"inline-label form-item-box m-t-2 m-b-2 m-r-20",attrs:{label:"",prop:"customFaceDate"}},[a("el-input",{staticClass:"w-100",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.customFaceDate,callback:function(e){t.$set(t.formData,"customFaceDate",e)},expression:"formData.customFaceDate"}}),a("span",{staticClass:"m-l-10"},[t._v("天")])],1):t._e(),a("span",{},[t._v("进行消息提醒")]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[a("span",{staticStyle:{"vertical-align":"top"}},[t._v("提醒内容：")]),a("el-input",{staticStyle:{width:"70%"},attrs:{disabled:!t.checkIsFormStatus,type:"textarea",rows:2},model:{value:t.formData.notifyMsg,callback:function(e){t.$set(t.formData,"notifyMsg",e)},expression:"formData.notifyMsg"}})],1)],1):t._e()],1),"add"!==t.operate?a("div",[a("div",{staticClass:"form-line"}),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v(" 第三方设置 "),a("el-switch",{staticStyle:{"margin-left":"15px"},attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.isThirdInterface,callback:function(e){t.$set(t.formData,"isThirdInterface",e)},expression:"formData.isThirdInterface"}})],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingThirdInfo,expression:"loadingThirdInfo"},{name:"show",rawName:"v-show",value:t.formData.isThirdInterface,expression:"formData.isThirdInterface"}]},[a("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"应用key：",prop:"thirdAppKey"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdAppKey,callback:function(e){t.$set(t.formData,"thirdAppKey",e)},expression:"formData.thirdAppKey"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.thirdAppKey))])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"应用secret：",prop:"thirdSecretKey"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdSecretKey,callback:function(e){t.$set(t.formData,"thirdSecretKey",e)},expression:"formData.thirdSecretKey"}}):a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.formData.thirdSecretKey,placement:"top"}},[a("div",{staticClass:"item-form-text ellipsis"},[t._v(t._s(t.formData.thirdSecretKey))])])],1)],1)],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"应用名称：",prop:"thirdAppName"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdAppName,callback:function(e){t.$set(t.formData,"thirdAppName",e)},expression:"formData.thirdAppName"}}):a("div",{staticClass:"item-form-text ellipsis"},[t._v(t._s(t.formData.thirdAppName))])],1),a("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"跳转地址：",prop:"thirdAppUrl"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.thirdAppUrl,callback:function(e){t.$set(t.formData,"thirdAppUrl",e)},expression:"formData.thirdAppUrl"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.thirdAppUrl))])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"回调地址：",prop:"thirdAppCallbackUrl"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{placeholder:"http://127.0.0.1/?userId={0}&bb=1，{0}将会被替换掉",size:"small"},model:{value:t.formData.thirdAppCallbackUrl,callback:function(e){t.$set(t.formData,"thirdAppCallbackUrl",e)},expression:"formData.thirdAppCallbackUrl"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.thirdAppCallbackUrl))])],1)],1)],1),t.checkIsFormStatus?a("el-form-item",{staticClass:"block-center",attrs:{label:"",prop:""}},[a("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:t.generateThirdAppinfo}},[t._v(" 重新生成 ")])],1):t._e()],1)]):t._e(),t.checkIsFormStatus?a("div",{staticClass:"form-footer"},[a("el-button",{attrs:{size:"small"},on:{click:t.cancelFormHandle}},[t._v("取消")]),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.add_root","background.admin.organization.modify"],expression:"[\n          'background.admin.organization.add_root',\n          'background.admin.organization.modify'\n        ]"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.sendFormdataHandle}},[t._v(" 保存 ")])],1):t._e()],1)],1)},s=[],i=a("a34a"),o=a.n(i),l=a("ed08"),n=a("ef6c"),c=a("c938"),m=a("d0dd"),d=function(t){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(t)},f=function(t){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(t.toString())},p=function(t,e,a){if(!e)return a();d(e)?a():a(new Error("邮箱格式错误！"))},u=function(t,e,a){if(!e)return a();f(e)?a():a(new Error("电话格式错误！"))},b=a("fb36");function h(t,e){return x(t)||D(t,e)||k(t,e)||v()}function v(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function k(t,e){if(t){if("string"===typeof t)return g(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?g(t,e):void 0}}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function D(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],r=!0,s=!1,i=void 0;try{for(var o,l=t[Symbol.iterator]();!(r=(o=l.next()).done);r=!0)if(a.push(o.value),e&&a.length===e)break}catch(n){s=!0,i=n}finally{try{r||null==l["return"]||l["return"]()}finally{if(s)throw i}}return a}}function x(t){if(Array.isArray(t))return t}function C(t,e,a,r,s,i,o){try{var l=t[i](o),n=l.value}catch(c){return void a(c)}l.done?e(n):Promise.resolve(n).then(r,s)}function y(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var i=t.apply(e,a);function o(t){C(i,r,s,o,l,"next",t)}function l(t){C(i,r,s,o,l,"throw",t)}o(void 0)}))}}var w={name:"SuperAddRootOrganization",components:{SelectTree:b["a"]},props:{type:String,infoData:{type:Object,default:function(){return{}}},treeData:Object,id:[String,Number],operate:String,restoreHandle:Function},data:function(){var t=this,e=function(t,e,a){if(!e)return a(new Error("账号不能为空"));var r=/^\w{5,20}$/;r.test(e)?a():a(new Error("账号长度5到20位，只支持数字、大小写英文或下划线组合"))},a=function(e,a,r){var s=/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/;if(!a)return"modify"===t.formOperate?void r():r(new Error("密码不能为空"));s.test(a)?r():r(new Error("密码长度8到20位，字母和数组组合"))},r=function(t,e,a){e.length>0?a():a(new Error("功能菜单配置不能为空！"))},s=function(t,e,a){if(""===e||"0"===e)return a(new Error("请输入大于0的数字"));var r=/^\d+$/;r.test(e)?a():a(new Error("请输入正确数字"))};return{labelName:"",formOperate:"detail",isLoading:!1,industryTypeList:c,addrOptions:n["regionData"],formData:{id:"",appid:"",secretKey:"",name:"",levelName:"",initOrganizationLevel:"",permission:[],username:"",password:"",url:"",district:[],contact:"",mobile:"",mailAddress:"",tel:"",industry:"",remark:"",facepay:!1,refundOn:!1,refundPassword:"",storeWalletOn:!1,electronicWalletOn:!1,subsidyWalletOn:!1,complimentaryWalletOn:!1,otherWalletOn:!1,isThirdInterface:!1,combineWalletOn:!1,thirdAppKey:"",thirdSecretKey:"",thirdAppName:"",thirdAppUrl:"",thirdAppCallbackUrl:"",smsTemplateId:"",isAbcProject:!1,isExpireChangePwd:!1,allowJumpChangePwd:!1,useCardNoLimit:!1,enableUpdateNotify:!1,faceUpdateTime:"",notifyMsg:"",isWalletPayOrderAsc:!1},formDataRuls:{name:[{required:!0,message:"组织名称不能为空",trigger:"blur"},{validator:m["e"],trigger:"blur"}],level_name:[{required:!0,message:"层级名称不能为空",trigger:"blur"}],mobile:[{required:!0,validator:m["g"],trigger:"blur"}],username:[{required:!0,validator:e,trigger:"blur"}],password:[{required:!0,validator:a,trigger:"blur"}],refundPassword:[{validator:a,trigger:"blur"}],tel:[{validator:u,trigger:"blur"}],mailAddress:[{validator:p,trigger:"blur"}],permission:[{required:!0,validator:r,trigger:"blur"}],useCardNoLimit:[{required:!0,message:"不能为空",trigger:"blur"}],faceUpdateTime:[{required:!0,message:"请选择人脸更新天数",trigger:"blur"}],customFaceDate:[{validator:s,trigger:"blur"}]},levelList:[],permissionTree:[{id:"folder",label:"Normal Folder",children:[{id:"disabled-checked",label:"Checked",isDisabled:!0},{id:"disabled-unchecked",label:"Unchecked",isDisabled:!0},{id:"item-1",label:"Item"}]}],permissionTreeProps:{value:"key",label:"verbose_name",isLeaf:"is_leaf",children:"children"},loadingThirdInfo:!1,faceUploadOptions:[{name:"60天",value:60},{name:"90天",value:90},{name:"180天",value:180},{name:"1年",value:365},{name:"自定义",value:"auto"}]}},computed:{checkIsFormStatus:function(){var t=!1;switch(this.operate){case"add":t=!0;break;case"detail":t="detail"!==this.formOperate;break;default:t="detail"!==this.formOperate;break}return t}},watch:{operate:function(t,e){t||(this.formOperate="detail")}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getLevelList(this.id),this.getPermissionTreeList(this.id),this.id&&"add"!==this.operate&&this.initInfoHandle(),this.operate&&(this.formOperate=this.operate),this.treeData&&"add"!==this.operate&&(this.labelName=this.treeData.name.substring(0,1)),"add"===this.operate&&(this.labelName="朴")},refreshHandle:function(){this.initLoad()},searchHandle:Object(l["c"])((function(){}),300),initInfoHandle:function(){var t=this,e=function(e){var a=t.infoData[Object(l["b"])(e)];if(a)switch(e){case"industry":t.formData[e]=a.toString();break;case"district":t.formData[e]=JSON.parse(a);break;case"faceUpdateTime":var r=!1;t.faceUploadOptions.forEach((function(t){t.value==a&&(r=!0)})),r?t.formData[e]=a:a&&(t.formData[e]="auto",t.formData.customFaceDate=a);break;default:t.formData[e]=a;break}};for(var a in this.formData)e(a)},permissionNormalizer:function(t){return{id:t.key,label:t.verbose_name,children:t.children}},deleteEmptyChildren:function(t,e){e=e||"children_list";var a=this;function r(t){t.map((function(t){a.checkIsFormStatus?t.isDisabled=!1:t.isDisabled=!0,t[e]&&t[e].length>0?r(t[e]):a.$delete(t,e)}))}return r(t),t},getLevelList:function(t){var e=this;return y(o.a.mark((function a(){var r,s,i,n,c;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r={},t&&(r.company_id=t),a.next=4,Object(l["Q"])(e.$apis.apiBackgroundAdminOrganizationGetLevelNameMapPost(r));case 4:if(s=a.sent,i=h(s,2),n=i[0],c=i[1],!n){a.next=11;break}return e.$message.error(n.message),a.abrupt("return");case 11:0===c.code?(e.levelList=c.data,c.data.length>0&&"add"===e.operate&&(e.formData.levelName=c.data[0].name)):e.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},getPermissionTreeList:function(){var t=this;return y(o.a.mark((function e(){var a,r,s,i;return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(l["Q"])(t.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost());case 2:if(a=e.sent,r=h(a,2),s=r[0],i=r[1],!s){e.next=9;break}return t.$message.error(s.message),e.abrupt("return");case 9:0===i.code?(t.permissionTree=t.deleteEmptyChildren(i.data,"children"),"add"===t.operate&&(t.formData.permission=Object(l["z"])(t.permissionTree,"key"))):t.$message.error(i.msg);case 10:case"end":return e.stop()}}),e)})))()},clickSelectPermissionTree:function(t){this.formData.permission=1===t?Object(l["z"])(this.permissionTree,"key"):[]},changeOperate:function(){switch(this.operate){case"add":break;default:"detail"===this.formOperate?this.formOperate="modify":this.formOperate="detail";break}this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children")},cancelFormHandle:function(){"add"===this.operate?this.$refs.organizationFormRef.resetFields():(this.$refs.organizationFormRef.clearValidate(),this.formOperate="detail",this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children")),this.restoreHandle(this.type,this.formOperate)},generateThirdAppinfo:function(){var t=this;return y(o.a.mark((function e(){var a,r,s,i;return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loadingThirdInfo=!0,e.next=3,Object(l["Q"])(t.$apis.apiBackgroundAdminOrganizationGenerateThirdAppinfoPost({id:t.id}));case 3:if(a=e.sent,r=h(a,2),s=r[0],i=r[1],t.loadingThirdInfo=!1,!s){e.next=11;break}return t.$message.error(s.message),e.abrupt("return");case 11:0===i.code?(t.formData.thirdAppKey=i.data.third_app_key,t.formData.thirdSecretKey=i.data.third_secret_key):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},sendFormdataHandle:function(){var t=this;this.$refs.organizationFormRef.validate((function(e){e&&("add"===t.operate?t.addRootOrganization(t.formatData()):t.modifyOrganization(t.formatData()))}))},formatData:function(){var t={status:"enable"};for(var e in this.formData){var a=this.formData[e];if(""!==a){switch(e){case"district":a=JSON.stringify(a);break;case"password":break;case"refundPassword":break;case"thirdAppUrl":a=encodeURIComponent(a);break;case"faceUpdateTime":a="auto"===a?this.formData.customFaceDate:a;break}"levelName"!==e&&"customFaceDate"!==e&&(t[Object(l["b"])(e)]=a)}"modify"===this.formOperate&&(t.company=this.treeData.company)}return t},getPermissionLevelParent:function(t){var e=this,a=[];function r(t,e,a){for(var s=[],i=0;i<t.length;i++){var o=t[i];if(o.key===e){s=a;break}a.push(e),o.children&&o.children.length>0&&r(o.children,e,a)}return s}return t.forEach((function(t){var s=[],i=r(e.permissionTree,t,s);a=a.concat(i)})),a},addRootOrganization:function(t){var e=this;return y(o.a.mark((function a(){var r,s,i,n;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(l["Q"])(e.$apis.apiBackgroundAdminOrganizationAddRootPost(t));case 3:if(r=a.sent,s=h(r,2),i=s[0],n=s[1],e.isLoading=!1,!i){a.next=11;break}return e.$message.error(i.message),a.abrupt("return");case 11:0===n.code?(e.$message.success("添加成功"),e.restoreHandle(e.type,e.formOperate)):e.$message.error(n.msg);case 12:case"end":return a.stop()}}),a)})))()},modifyOrganization:function(t){var e=this;return y(o.a.mark((function a(){var r,s,i,n;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(l["Q"])(e.$apis.apiBackgroundAdminOrganizationModifyPost(t));case 3:if(r=a.sent,s=h(r,2),i=s[0],n=s[1],e.isLoading=!1,!i){a.next=11;break}return e.$message.error(i.message),a.abrupt("return");case 11:0===n.code?(e.$message.success("修改成功"),e.formOperate="detail",e.restoreHandle(e.type,e.formOperate)):e.$message.error(n.msg);case 12:case"end":return a.stop()}}),a)})))()},gotoLogin:function(){this.infoData.login_token?window.open(location.origin+"/#/login?token="+this.infoData.login_token,"_blank"):this.$message.error("无法获取token!")}}},_=w,O=(a("f351"),a("2877")),S=Object(O["a"])(_,r,s,!1,null,null,null);e["default"]=S.exports}}]);
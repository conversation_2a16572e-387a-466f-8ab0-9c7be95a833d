(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-seniorSetting"],{"64df":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"deductsetting-wrapper"},[e._m(0),a("div",{staticClass:"form-wrapper",staticStyle:{"max-width":"700px"}},[a("el-form",{ref:"seniorFormRef",attrs:{model:e.seniorFormData,rules:e.seniorFormRuls,"label-width":"120px"}},[a("el-form-item",{attrs:{prop:"money",label:"可选充值金额"}},[a("el-input",{staticClass:"ps-input",staticStyle:{width:"300px"},attrs:{maxlength:9},model:{value:e.seniorFormData.money,callback:function(t){e.$set(e.seniorFormData,"money",t)},expression:"seniorFormData.money"}}),e.seniorFormData.rechargeAmountList.length<6?a("el-button",{staticClass:"add-btn",attrs:{disabled:!e.seniorFormData.money,icon:"el-icon-circle-plus",type:"text",circle:""},on:{click:e.addMoneyList}}):e._e(),a("div",{staticClass:"money-tag m-t-10"},e._l(e.seniorFormData.rechargeAmountList,(function(t,n){return a("el-tag",{key:t+n,attrs:{closable:""},on:{close:function(a){return e.closeMoneyTag(t,n)}}},[e._v(" "+e._s(t+"元")+" ")])})),1)],1),a("el-form-item",{attrs:{prop:"abcPayTime",label:"可充值任意金额"}},[a("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45"},model:{value:e.seniorFormData.allowCustomAmount,callback:function(t){e.$set(e.seniorFormData,"allowCustomAmount",t)},expression:"seniorFormData.allowCustomAmount"}}),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!e.seniorFormData.allowCustomAmount},model:{value:e.seniorFormData.openMinimumRechargeAmount,callback:function(t){e.$set(e.seniorFormData,"openMinimumRechargeAmount",t)},expression:"seniorFormData.openMinimumRechargeAmount"}},[e._v("最低需要充值")]),a("el-input",{staticClass:"ps-input",staticStyle:{width:"80px",margin:"0 10px"},attrs:{maxlength:9,disabled:!(e.seniorFormData.openMinimumRechargeAmount&&e.seniorFormData.allowCustomAmount)},model:{value:e.seniorFormData.minimumRechargeAmount,callback:function(t){e.$set(e.seniorFormData,"minimumRechargeAmount",t)},expression:"seniorFormData.minimumRechargeAmount"}}),e._v(" 元 ")],1),a("div",{staticClass:"m-b-10",staticStyle:{color:"red","font-size":"12px"}},[e._v(" 注：最低充值对应当前组织的充值限制 ")]),a("el-form-item",{attrs:{prop:"rechargeDateType",label:"指定日期可充值"}},e._l(e.rechargePaySceneType,(function(t,n){return a("div",{key:t},[a("label",{staticStyle:{"margin-right":"10px",float:"left"}},[e._v(e._s(t)+" ")]),a("div",{staticClass:"inline-block"},[a("el-checkbox-group",{staticClass:"ps-checkbox",on:{change:e.changeRechargeDate},model:{value:e.seniorFormData.rechargeDateType[n],callback:function(t){e.$set(e.seniorFormData.rechargeDateType,n,t)},expression:"seniorFormData.rechargeDateType[payScene]"}},[a("div",{staticClass:"money-tag"},[a("el-checkbox",{attrs:{label:"month"}},[e._v("每月")]),e._l(e.seniorFormData.allowRechargeDateList[n],(function(t,i){return a("el-tag",{key:t+i,staticClass:"m-l-10 m-r-10 m-b-10",attrs:{closable:""},on:{close:function(a){return e.closeDateHandle(t,i,n)}}},[e._v(" "+e._s(t+" 号")+" ")])})),e.seniorFormData.allowRechargeDateList[n].length<6?a("span",[e.inputVisible[n]?a("el-form-item",{staticClass:"inline-label",attrs:{prop:"dateValue",label:""}},[a("el-input",{ref:n+"saveTagInput",refInFor:!0,staticClass:"input-new-tag ps-input m-l-10",attrs:{size:"small",disabled:e.isDisabledDate(n)},on:{blur:function(t){return e.handleInputConfirm(n)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleInputConfirm(n)}},model:{value:e.seniorFormData.dateValue,callback:function(t){e.$set(e.seniorFormData,"dateValue",t)},expression:"seniorFormData.dateValue"}})],1):a("el-button",{staticClass:"button-new-tag",attrs:{disabled:e.isDisabledDate(n),size:"small"},on:{click:function(t){return e.showInput(n)}}},[e._v("+")])],1):e._e()],2),a("div",{},[a("el-checkbox",{attrs:{label:"lastDay"}},[e._v("每月最后一天")])],1)])],1)])})),0),a("div",{staticClass:"form-line ps-line"}),a("div",{staticClass:"l-title"},[a("span",[e._v("其它设置")])]),a("div",{staticClass:"inline"},[a("el-form-item",{attrs:{prop:"limitTodayRechargeAmount",label:"单日累计充值上限"}},[a("el-input",{staticClass:"ps-input",attrs:{maxlength:9},model:{value:e.seniorFormData.limitTodayRechargeAmount,callback:function(t){e.$set(e.seniorFormData,"limitTodayRechargeAmount",t)},expression:"seniorFormData.limitTodayRechargeAmount"}})],1),a("el-form-item",{attrs:{prop:"limitTodayConsumeAmount",label:"单日累计消费上限"}},[a("el-input",{staticClass:"ps-input",attrs:{maxlength:9},model:{value:e.seniorFormData.limitTodayConsumeAmount,callback:function(t){e.$set(e.seniorFormData,"limitTodayConsumeAmount",t)},expression:"seniorFormData.limitTodayConsumeAmount"}})],1),a("el-form-item",{attrs:{prop:"limitBalanceAmount",label:"钱包累计余额上限"}},[a("el-input",{staticClass:"ps-input",attrs:{maxlength:9},model:{value:e.seniorFormData.limitBalanceAmount,callback:function(t){e.$set(e.seniorFormData,"limitBalanceAmount",t)},expression:"seniorFormData.limitBalanceAmount"}})],1)],1),a("div",{staticStyle:{color:"red","font-size":"12px"}},[e._v(" 注：该设置只针对当前组织的储值钱包进行设置 ")]),a("div",{staticClass:"form-line ps-line"}),a("div",{staticClass:"l-title"},[a("span",[e._v("隐私设置")])]),a("div",{staticClass:"inline"},[a("el-form-item",{staticClass:"form-item-box",attrs:{label:"",prop:""}},[a("span",{staticStyle:{"margin-right":"25px"}},[e._v(" 人员编号 "),a("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45","active-value":1,"inactive-value":0},model:{value:e.seniorFormData.sensitive_json.person_no,callback:function(t){e.$set(e.seniorFormData.sensitive_json,"person_no",t)},expression:"seniorFormData.sensitive_json.person_no"}})],1),a("span",{staticStyle:{"margin-right":"25px"}},[e._v(" 手机号码 "),a("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45","active-value":1,"inactive-value":0},model:{value:e.seniorFormData.sensitive_json.phone,callback:function(t){e.$set(e.seniorFormData.sensitive_json,"phone",t)},expression:"seniorFormData.sensitive_json.phone"}})],1),a("span",{staticStyle:{"margin-right":"25px"}},[e._v(" 卡号 "),a("el-switch",{staticStyle:{"margin-right":"25px"},attrs:{"active-color":"#ff9b45","active-value":1,"inactive-value":0},model:{value:e.seniorFormData.sensitive_json.card_no,callback:function(t){e.$set(e.seniorFormData.sensitive_json,"card_no",t)},expression:"seniorFormData.sensitive_json.card_no"}})],1)])],1),a("div",{staticStyle:{color:"red","font-size":"12px"}},[e._v(" 注：关闭后商户后台则隐藏对应字段 ")]),a("div",{staticClass:"add-wrapper"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify_settings"],expression:"['background.admin.organization.modify_settings']"}],staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:e.saveWalletHandle}},[e._v("保存")])],1)],1)],1)])},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"l-title"},[a("span",[e._v("充值设置")])])}],r=a("a34a"),o=a.n(r),s=a("ed08");function l(e,t){return g(e)||h(e,t)||m(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return u(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function h(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],n=!0,i=!1,r=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(a.push(o.value),t&&a.length===t)break}catch(l){i=!0,r=l}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw r}}return a}}function g(e){if(Array.isArray(e))return e}function d(e,t,a,n,i,r,o){try{var s=e[r](o),l=s.value}catch(c){return void a(c)}s.done?t(l):Promise.resolve(l).then(n,i)}function p(e){return function(){var t=this,a=arguments;return new Promise((function(n,i){var r=e.apply(t,a);function o(e){d(r,n,i,o,s,"next",e)}function s(e){d(r,n,i,o,s,"throw",e)}o(void 0)}))}}var f={name:"SuperSeniorSetting",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){var e=function(e,t,a){var n=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;if(""!==t||"money"!==e.field&&"limitBalanceAmount"!==e.field)return"0"===t?a(new Error("金额格式有误")):void(n.test(t)?a():a(new Error("金额格式有误")));a()},t=function(e,t,a){var n=/^\+?[1-9][0-9]*$/;if("0"===t)return a(new Error("日期不能为0"));n.test(t)?(t>28&&a(new Error("不能超过28")),a()):a(new Error("日期格式有误"))};return{isLoading:!1,settingInfo:null,formOperate:"detail",rechargePaySceneType:{charge:"线上",charge_offline:"线下"},seniorFormData:{money:"",rechargeAmountList:[],allowCustomAmount:!1,openMinimumRechargeAmount:!1,minimumRechargeAmount:"",rechargeDateType:{charge:[],charge_offline:[]},allowRechargeDateList:{charge:[],charge_offline:[]},limitTodayRechargeAmount:"",limitTodayConsumeAmount:"",limitBalanceAmount:"",dateValue:"",sensitive_json:{card_no:1,phone:1,person_no:1}},seniorFormRuls:{limitTodayRechargeAmount:[{required:!0,validator:e,trigger:"blur"}],limitTodayConsumeAmount:[{required:!0,validator:e,trigger:"blur"}],limitBalanceAmount:[{validator:e,trigger:"blur"}],money:[{validator:e,trigger:"change"}],dateValue:[{validator:t,trigger:"change"}]},inputVisible:{charge:!1,charge_offline:!1},inputValue:{charge:"",charge_offline:""}}},computed:{isDisabledDate:function(){return function(e){return!this.seniorFormData.rechargeDateType[e].includes("month")}}},watch:{type:function(e){},organizationData:function(e){var t=this;setTimeout((function(){t.searchHandle()}),50)}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getSettingInfo()},refreshHandle:function(){this.currentPage=1,this.initLoad()},searchHandle:Object(s["c"])((function(){this.initLoad()}),300),getSettingInfo:function(){var e=this;return p(o.a.mark((function t(){var a,n,i,r;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Q"])(e.$apis.apiBackgroundAdminOrganizationGetSettingsPost({id:e.organizationData.id,company:e.organizationData.company}));case 3:if(a=t.sent,n=l(a,2),i=n[0],r=n[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===r.code?(e.settingInfo=r.data,e.initSettingInfo(r.data)):e.$message.error(r.msg);case 12:case"end":return t.stop()}}),t)})))()},initSettingInfo:function(e){for(var t in this.seniorFormData.rechargeAmountList=e.recharge_amount_list.map((function(e){return Object(s["h"])(e)})),this.seniorFormData.limitTodayRechargeAmount=Object(s["h"])(e.limit_today_recharge_amount),this.seniorFormData.limitTodayConsumeAmount=Object(s["h"])(e.limit_today_consume_amount),this.seniorFormData.limitBalanceAmount=Object(s["h"])(e.limit_balance_amount),this.seniorFormData.allowCustomAmount=e.allow_custom_amount,Object.keys(e.sensitive_json)&&Object.keys(e.sensitive_json).length&&(this.seniorFormData.sensitive_json=e.sensitive_json),e.minimum_recharge_amount&&(this.seniorFormData.openMinimumRechargeAmount=!0,this.seniorFormData.minimumRechargeAmount=Object(s["h"])(e.minimum_recharge_amount)),e.allow_recharge_date_list instanceof Array&&(e.allow_recharge_date_list={charge:Object(s["e"])(e.allow_recharge_date_list),charge_offline:[]}),this.rechargePaySceneType)if(this.seniorFormData.rechargeDateType[t]=[],e.allow_recharge_date_list[t]&&e.allow_recharge_date_list[t].length){var a=e.allow_recharge_date_list[t].indexOf(-1);a>-1?(e.allow_recharge_date_list[t].length>1&&this.seniorFormData.rechargeDateType[t].push("month"),e.allow_recharge_date_list[t].splice(a,1),this.seniorFormData.rechargeDateType[t].push("lastDay"),this.seniorFormData.allowRechargeDateList[t]=e.allow_recharge_date_list[t]):(this.seniorFormData.rechargeDateType[t].push("month"),this.seniorFormData.allowRechargeDateList[t]=e.allow_recharge_date_list[t])}else this.seniorFormData.rechargeDateType[t]=[]},addMoneyList:function(){var e=this,t=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;t.test(this.seniorFormData.money)?(this.seniorFormData.rechargeAmountList.push(this.seniorFormData.money),this.seniorFormData.money="",this.$nextTick((function(){e.$refs.seniorFormRef.clearValidate("money")}))):this.$message.error("金额格式有误，请重新输入！")},closeMoneyTag:function(e,t){this.seniorFormData.rechargeAmountList.splice(t,1)},changeRechargeDate:function(e){},saveWalletHandle:function(){var e=this;this.$refs.seniorFormRef.validate((function(t){if(t){if(e.isLoading)return e.$message.error("请勿重复提交!");e.setSeniorSettingHandle()}}))},setSeniorSettingHandle:function(){var e=this;return p(o.a.mark((function t(){var a,n,i,r,c,m;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(n in e.isLoading=!0,a={id:e.organizationData.id,allow_custom_amount:e.seniorFormData.allowCustomAmount,limit_today_recharge_amount:Object(s["P"])(e.seniorFormData.limitTodayRechargeAmount),limit_today_consume_amount:Object(s["P"])(e.seniorFormData.limitTodayConsumeAmount),company:e.organizationData.company,sensitive_json:e.seniorFormData.sensitive_json,allow_recharge_date_list:{charge:[],charge_offline:[]}},e.seniorFormData.rechargeAmountList.length>0&&(a.recharge_amount_list=e.seniorFormData.rechargeAmountList.map((function(e){return Object(s["P"])(e)}))),e.seniorFormData.limitBalanceAmount&&(a.limit_balance_amount=Object(s["P"])(e.seniorFormData.limitBalanceAmount)),e.seniorFormData.openMinimumRechargeAmount?a.minimum_recharge_amount=Object(s["P"])(e.seniorFormData.minimumRechargeAmount):a.minimum_recharge_amount=0,e.rechargePaySceneType)e.seniorFormData.rechargeDateType[n].length>0?(e.seniorFormData.rechargeDateType[n].includes("month")&&(a.allow_recharge_date_list[n]=Object(s["e"])(e.seniorFormData.allowRechargeDateList[n])),e.seniorFormData.rechargeDateType[n].includes("lastDay")&&(a.allow_recharge_date_list[n]&&a.allow_recharge_date_list[n].length?a.allow_recharge_date_list[n].push(-1):a.allow_recharge_date_list[n]=[-1])):a.allow_recharge_date_list[n]=[];return t.next=8,Object(s["Q"])(e.$apis.apiBackgroundAdminOrganizationModifySettingsPost(a));case 8:if(i=t.sent,r=l(i,2),c=r[0],m=r[1],e.isLoading=!1,!c){t.next=16;break}return e.$message.error(c.message),t.abrupt("return");case 16:0===m.code?(e.$message.success(m.msg),e.getSettingInfo()):e.$message.error(m.msg);case 17:case"end":return t.stop()}}),t)})))()},closeDateHandle:function(e,t,a){this.seniorFormData.allowRechargeDateList[a].splice(t,1)},showInput:function(e){var t=this;this.inputVisible[e]=!0,this.$nextTick((function(a){t.$refs[e+"saveTagInput"][0].$refs.input.focus()}))},handleInputConfirm:function(e){var t=this.seniorFormData.dateValue,a=this.seniorFormData.allowRechargeDateList[e].indexOf(Number(t)),n=/^\+?[1-9][0-9]*$/,i=!0;"0"===t&&(i=!1),(!n.test(t)||Number(t)>28||Number(t)<1)&&(i=!1),t&&i&&Number(t)&&(a<0?(this.seniorFormData.allowRechargeDateList[e].push(Number(t)),this.sortList(this.seniorFormData.allowRechargeDateList[e])):this.$message.warning("请不要添加相同的日期")),(i||""===t)&&(this.inputVisible[e]=!1,this.seniorFormData.dateValue="")},sortList:function(e){e=e.sort((function(e,t){return e-t}))}}},_=f,v=(a("8315e"),a("2877")),D=Object(v["a"])(_,n,i,!1,null,null,null);t["default"]=D.exports},"8315e":function(e,t,a){"use strict";var n=a("c991"),i=a.n(n);i.a},c991:function(e,t,a){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);
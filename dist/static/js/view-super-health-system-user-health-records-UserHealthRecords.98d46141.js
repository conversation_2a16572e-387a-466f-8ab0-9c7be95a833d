(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-UserHealthRecords","view-super-health-system-user-health-records-constants"],{"2f56":function(e,t,a){"use strict";a.r(t),a.d(t,"recentSevenDay",(function(){return n})),a.d(t,"USERHEALTHRECORDS",(function(){return o})),a.d(t,"RADAROPTION",(function(){return l})),a.d(t,"MEALTIME_SETTING",(function(){return i})),a.d(t,"BODY_DETAIL",(function(){return s}));var r=a("5a0c"),n=[r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD")],o={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},company_ids:{type:"CompanySelect",value:[],label:"组织",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,companyOpts:{label:"name",value:"company"},companyKey:"all"},status:{type:"select",value:"",label:"档案状态",clearable:!0,dataList:[{label:"全部",value:""},{label:"使用中",value:"enable"},{label:"注销中",value:"logoff"}]}},l={title:{text:0,x:"center",y:"center",textStyle:{color:"#fd953c",fontWeight:"bolder",fontSize:28}},tooltip:{trigger:"axis"},radar:{name:{textStyle:{padding:[-10,-5]},color:"#23282d"},splitLine:{lineStyle:{type:"dashed",width:1}},splitArea:{show:!1,areaStyle:{color:"rgba(255,0,0,0)"}},indicator:[{name:"食物多样性",max:100},{name:"营养均衡",max:100},{name:"能量摄入",max:100},{name:"BMI",max:100},{name:"运动情况",max:100}]},series:[{tooltip:{trigger:"item"},type:"radar",label:{show:!1},areaStyle:{color:"#fad1ae"},data:[{name:"健康分",value:[0,0,0,0,0]}]}],color:["#fca255"]},i={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.marker,a=e.percent;return t+e.name+"&nbsp;&nbsp;&nbsp;"+a+"%"}},title:{text:"0",x:"center",y:"center",top:"25%",textStyle:{color:"#fd953c",fontSize:18}},legend:{top:"62%",orient:"vertical",y:"bottom",padding:[0,0,0,0]},series:[{type:"pie",radius:["45%","60%"],avoidLabelOverlap:!1,top:"-10%",height:200,itemStyle:{borderRadius:10,borderColor:"#f1f2f5",borderWidth:3},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},s={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1}}},"32e5":function(e,t,a){"use strict";var r=a("49a7"),n=a.n(r);n.a},"49a7":function(e,t,a){},f133:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"UserHealthRecords"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")]),a("div",{staticClass:"align-r"},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"130px"},attrs:{placeholder:"请选择导出类型","popper-class":"ps-popper-select",size:"mini"},on:{change:e.changeExport},model:{value:e.exportType,callback:function(t){e.exportType=t},expression:"exportType"}},e._l(e.exportList,(function(e){return a("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1)]),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row","row-key":"user_id"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center","reserve-selection":!0,"class-name":"ps-checkbox"}}),a("el-table-column",{attrs:{prop:"id",label:"档案ID",align:"center"}}),a("el-table-column",{attrs:{prop:"user_id",label:"用户ID",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"手机号",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",align:"center"}}),a("el-table-column",{attrs:{prop:"weight_target",label:"体重目标",align:"center"}}),a("el-table-column",{attrs:{prop:"company",label:"组织",align:"center"}}),a("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{prop:"status_alias",label:"档案状态",align:"center"}}),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.clickDetails(t.row)}}},[e._v(" 详情 ")])]}}])})],1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)])],1)},n=[],o=a("a34a"),l=a.n(o),i=a("ed08"),s=a("f63a"),c=a("2f56");function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function p(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){d(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function d(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function f(e,t){return y(e)||g(e,t)||m(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return b(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function g(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,n=!1,o=void 0;try{for(var l,i=e[Symbol.iterator]();!(r=(l=i.next()).done);r=!0)if(a.push(l.value),t&&a.length===t)break}catch(s){n=!0,o=s}finally{try{r||null==i["return"]||i["return"]()}finally{if(n)throw o}}return a}}function y(e){if(Array.isArray(e))return e}function v(e,t,a,r,n,o,l){try{var i=e[o](l),s=i.value}catch(c){return void a(c)}i.done?t(s):Promise.resolve(s).then(r,n)}function x(e){return function(){var t=this,a=arguments;return new Promise((function(r,n){var o=e.apply(t,a);function l(e){v(o,r,n,l,i,"next",e)}function i(e){v(o,r,n,l,i,"throw",e)}l(void 0)}))}}var S={name:"HealthAssessment",mixins:[s["a"]],components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:c["USERHEALTHRECORDS"],exportType:"",exportList:[{name:"导出档案",key:"ExportHealthyInfoList"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getHealthyInfoList()},searchHandle:Object(i["c"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getHealthyInfoList:function(){var e=this;return x(l.a.mark((function t(){var a,r,n,o;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(i["Q"])(e.$apis.apiBackgroundAdminHealthyInfoListPost(p(p({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,r=f(a,2),n=r[0],o=r[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===o.code?(e.totalCount=o.data.count,e.tableData=o.data.results):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},changeExport:function(e){this.gotoExport(e)},gotoExport:function(e){var t={type:e,params:p(p({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};this.exportHandle(t)},handleSelectionChange:function(e){},clickDetails:function(e){this.$router.push({name:"SuperRecordsDetail",query:{id:e.id,type:e.type,user_id:e.user_id}})},handleSizeChange:function(e){this.pageSize=e,this.getHealthyInfoList()},handleCurrentChange:function(e){this.currentPage=e,this.getHealthyInfoList()},formatQueryParams:function(e){var t={};for(var a in e)""!==e[a].value&&("select_time"!==a?t[a]=e[a].value:e[a].value&&e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]));return t}}},w=S,C=(a("32e5"),a("2877")),O=Object(C["a"])(w,r,n,!1,null,"5747e3be",null);t["default"]=O.exports}}]);
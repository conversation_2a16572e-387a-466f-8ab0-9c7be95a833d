(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-components-MemberListDialog"],{"5f6f":function(e,t,a){},"8fa2":function(e,t,a){"use strict";var i=a("5f6f"),s=a.n(i);s.a},d337:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("el-form",{ref:"memberFormRef",staticClass:"member-form",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},["label"===e.type?a("div",[a("el-form-item",{attrs:{label:"自动标签："}},[e._v(" "+e._s(e.autoLabelNameList.join("，"))+" ")]),a("el-form-item",{attrs:{label:"手动标签："}},[a("div",{staticClass:"label-list"},e._l(e.labelNameList,(function(t,i){return a("div",{key:t,staticClass:"label-list-item"},[a("span",{staticClass:"m-r-5"},[e._v(e._s(t))]),a("i",{staticClass:"el-icon-close del-icon",on:{click:function(t){return e.delLabel(i)}}})])})),0)]),a("el-form-item",{attrs:{label:"新增手动标签："}},[a("el-select",{staticClass:"ps-input",attrs:{placeholder:"请选择手动标签",multiple:"","collapse-tags":""},on:{change:e.changeSelectLabel},model:{value:e.dialogForm.selectLabelList,callback:function(t){e.$set(e.dialogForm,"selectLabelList",t)},expression:"dialogForm.selectLabelList"}},e._l(e.labelList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1):e._e(),"integral"===e.type?a("div",[a("el-form-item",{attrs:{label:"当前积分："}},[e._v(e._s(e.selectInfo.integral))]),a("el-form-item",{attrs:{label:"修改后积分：",prop:"score"}},[a("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.dialogForm.score,callback:function(t){e.$set(e.dialogForm,"score",t)},expression:"dialogForm.score"}})],1)],1):e._e(),"growthScore"===e.type?a("div",[a("el-form-item",{attrs:{label:"当前成长分："}},[e._v(e._s(e.selectInfo.growth_points))]),a("el-form-item",{attrs:{label:"添加成长分：",prop:"score"}},[a("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.dialogForm.score,callback:function(t){e.$set(e.dialogForm,"score",t)},expression:"dialogForm.score"}})],1),a("el-form-item",{attrs:{label:"修改后成长分："}},[e._v(e._s(Number(e.selectInfo.growth_points)+Number(e.dialogForm.score)))])],1):e._e()]),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},s=[],l=a("a34a"),o=a.n(l);function r(e,t,a,i,s,l,o){try{var r=e[l](o),n=r.value}catch(c){return void a(c)}r.done?t(n):Promise.resolve(n).then(i,s)}function n(e){return function(){var t=this,a=arguments;return new Promise((function(i,s){var l=e.apply(t,a);function o(e){r(l,i,s,o,n,"next",e)}function n(e){r(l,i,s,o,n,"throw",e)}o(void 0)}))}}var c={name:"ForLeaveRuleDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"450px"},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){var e=function(e,t,a){if(""===t)return a(new Error("不能为空"));var i=/^\d+$/;i.test(t)?a():a(new Error("请输入正整数"))};return{isLoading:!1,dialogForm:{selectLabelList:[],score:""},dialogFormRules:{score:[{required:!0,validator:e,trigger:"blur"}]},autoLabelNameList:[],autoLabelIdList:[],labelNameList:[],labelList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){var e=this;this.visible?(this.labelNameList=[],this.autoLabelNameList=[],this.autoLabelIdList=[],this.dialogForm.selectLabelList=[],this.selectInfo.member_labels_list.map((function(t){"auto"===t.type?(e.autoLabelNameList.push(t.name),e.autoLabelIdList.push(t.id)):e.dialogForm.selectLabelList.push(t.id)})),this.getMemberLabel()):this.$refs.memberFormRef.resetFields()}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var e=this;this.$refs.memberFormRef.validate((function(t){if(t){var a,i={};switch(e.type){case"growthScore":i={user_id:e.selectInfo.id,add_growth_value:e.dialogForm.score,obtain_type:"background_add"},a=e.$apis.apiBackgroundMemberMemberGradeGrowthAddPost(i);break;case"label":i={id:e.selectInfo.id,member_labels:e.autoLabelIdList.concat(e.dialogForm.selectLabelList)},a=e.$apis.apiBackgroundMemberMemberUserModifyPost(i);break}e.confirmOperation(a)}}))},confirmOperation:function(e){var t=this;return n(o.a.mark((function a(){var i;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.isLoading){a.next=2;break}return a.abrupt("return");case 2:return t.isLoading=!0,a.next=5,e;case 5:i=a.sent,t.isLoading=!1,0===i.code?(t.$message.success("成功"),t.confirm()):t.$message.error(i.msg);case 8:case"end":return a.stop()}}),a)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.memberFormRef.resetFields()},delLabel:function(e){this.dialogForm.selectLabelList.splice(e,1),this.labelNameList.splice(e,1)},getMemberLabel:function(){var e=this;return n(o.a.mark((function t(){var a;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberLabelListPost({page:1,page_size:99999,type:"manual"});case 2:a=t.sent,0===a.code?(e.labelList=a.data.results,e.labelList.map((function(t){-1!==e.dialogForm.selectLabelList.indexOf(t.id)&&e.labelNameList.push(t.name)}))):e.$message.error(a.msg);case 4:case"end":return t.stop()}}),t)})))()},changeSelectLabel:function(){var e=this;this.labelNameList=[],this.labelList.map((function(t){-1!==e.dialogForm.selectLabelList.indexOf(t.id)&&e.labelNameList.push(t.name)}))}}},u=c,m=(a("8fa2"),a("2877")),d=Object(m["a"])(u,i,s,!1,null,"3d32e7a6",null);t["default"]=d.exports}}]);
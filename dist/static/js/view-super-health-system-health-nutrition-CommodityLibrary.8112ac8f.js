(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-CommodityLibrary","view-super-health-system-components-selectLaber","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(e,t,a){"use strict";a.r(t),a.d(t,"DEFAULT_NUTRITION",(function(){return l})),a.d(t,"ELEMENT_NUTRITION",(function(){return n})),a.d(t,"VITAMIN_NUTRITION",(function(){return i})),a.d(t,"NUTRITION_LIST",(function(){return s})),a.d(t,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return r})),a.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return u})),a.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return o})),a.d(t,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return c})),a.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return p}));var l=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"}],n=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],i=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],s=[].concat(l,n,i),r={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},u={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},o={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},c={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},p={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"1a24":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("div",{staticClass:"healthTagDialog"},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:e.searchHandle},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}}),a("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v(" 已选 "),a("span",[e._v(e._s(e.selectLabelIdList.length))]),e._v(" 个标签 ")])]),e._l(e.tableData,(function(t,l){return a("div",{key:l},[a("el-collapse",{model:{value:e.activeLaberList,callback:function(t){e.activeLaberList=t},expression:"activeLaberList"}},[a("el-collapse-item",{attrs:{name:t.id}},[a("template",{slot:"title"},[a("span",[e._v(" "+e._s(t.name)+" "),a("span",[e._v("（"+e._s(t.label_list.length)+"）")])]),a("span",{staticClass:"tips-r"},[a("span",{staticClass:"open"},[e._v("展开")]),a("span",{staticClass:"close"},[e._v("收起")])])]),a("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[t.inputVisible?a("el-input",{ref:"saveTagInput"+t.id,refInFor:!0,staticClass:"ps-input w-100  p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(a){return e.handleInputConfirm(t)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputConfirm(t)}},model:{value:t.inputValue,callback:function(a){e.$set(t,"inputValue",a)},expression:"item.inputValue"}}):a("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(a){return e.showInput(t)}}},[e._v(" 添加标签 ")]),a("div",{staticStyle:{flex:"1"}},[a("el-checkbox-group",{attrs:{size:"mini"},model:{value:e.selectLabelIdList,callback:function(t){e.selectLabelIdList=t},expression:"selectLabelIdList"}},e._l(t.label_list,(function(l,n){return a("el-checkbox-button",{key:n,attrs:{label:l.id,disabled:l.disabled},on:{change:function(a){return e.checkboxChangge(l,t)}}},[e._v(" "+e._s(l.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},n=[],i=a("a34a"),s=a.n(i),r=a("ed08");function u(e,t){return m(e)||d(e,t)||c(e,t)||o()}function o(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"===typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,l=new Array(t);a<t;a++)l[a]=e[a];return l}function d(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],l=!0,n=!1,i=void 0;try{for(var s,r=e[Symbol.iterator]();!(l=(s=r.next()).done);l=!0)if(a.push(s.value),t&&a.length===t)break}catch(u){n=!0,i=u}finally{try{l||null==r["return"]||r["return"]()}finally{if(n)throw i}}return a}}function m(e){if(Array.isArray(e))return e}function b(e,t,a,l,n,i,s){try{var r=e[i](s),u=r.value}catch(o){return void a(o)}r.done?t(u):Promise.resolve(u).then(l,n)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(l,n){var i=e.apply(t,a);function s(e){b(i,l,n,s,r,"next",e)}function r(e){b(i,l,n,s,r,"throw",e)}s(void 0)}))}}var v={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(r["c"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var e=this;return y(s.a.mark((function t(){var a,l,n,i,o;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a={type:e.labelType,page:e.currentPage,page_size:e.pageSize},e.name&&(a.name=e.name),t.next=5,Object(r["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(a));case 5:if(l=t.sent,n=u(l,2),i=n[0],o=n[1],e.isLoading=!1,!i){t.next=13;break}return e.$message.error(i.message),t.abrupt("return");case 13:0===o.code?(e.totalCount=o.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=o.data.results.map((function(t){return t.inputVisible=!1,t.inputValue="",t.label_list.forEach((function(a){a.label_group_name=t.name,e.ruleSingleInfo.selectLabelAllIds&&e.ruleSingleInfo.selectLabelAllIds.length&&e.ruleSingleInfo.selectLabelAllIds.includes(a.id)&&!e.selectLabelIdList.includes(a.id)?a.disabled=!0:a.disabled=!1})),e.activeLaberList.push(t.id),t})),console.log(e.tableData)):e.$message.error(o.msg);case 14:case"end":return t.stop()}}),t)})))()},handleChange:function(){},checkboxChangge:function(e,t){var a=this,l=this.selectLabelIdList.indexOf(e.id);-1!==l?this.selectLabelListData.push(e):this.selectLabelListData.map((function(t,l){e.id===t.id&&a.selectLabelListData.splice(l,1)}))},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(a){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return y(s.a.mark((function a(){var l,n,i,o;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(r["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(l=a.sent,n=u(l,2),i=n[0],o=n[1],t.isLoading=!1,!i){a.next=11;break}return t.$message.error(i.message),a.abrupt("return");case 11:0===o.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(o.msg);case 12:case"end":return a.stop()}}),a)})))()},clickConfirmHandle:function(){var e={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",e),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()}}},g=v,f=(a("fa5f"),a("2877")),h=Object(f["a"])(g,l,n,!1,null,null,null);t["default"]=h.exports},"7e85":function(e,t,a){},fa5f:function(e,t,a){"use strict";var l=a("7e85"),n=a.n(l);n.a}}]);
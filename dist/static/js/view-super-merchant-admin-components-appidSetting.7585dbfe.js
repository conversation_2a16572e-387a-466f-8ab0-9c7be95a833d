(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-appidSetting"],{"36b3":function(e,t,a){"use strict";var r=a("cd7e"),i=a.n(r);i.a},cd7e:function(e,t,a){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},e512:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"container-wrapper bindappid"},[a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[e._v("绑定公众号")]),"detail"===e.formOperate?a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:e.changeOperate}},[e._v("编辑")]):e._e()],1),a("div",{staticClass:"appid-box"},[a("el-form",{ref:"appidRef",attrs:{rules:e.formDataRuls,model:e.formData,size:"small","label-width":"100px"}},[a("el-form-item",{attrs:{label:"appid",prop:"appid"}},[a("el-input",{staticStyle:{"max-width":"300px"},attrs:{disabled:!e.checkIsFormStatus,placeholder:"请输入appid"},model:{value:e.formData.appid,callback:function(t){e.$set(e.formData,"appid",t)},expression:"formData.appid"}})],1),a("el-form-item",{attrs:{label:"secret_key",prop:"secret_key"}},[a("el-input",{staticStyle:{"max-width":"300px"},attrs:{disabled:!e.checkIsFormStatus,placeholder:"请输入appid"},model:{value:e.formData.secret_key,callback:function(t){e.$set(e.formData,"secret_key",t)},expression:"formData.secret_key"}})],1),!e.checkIsFormStatus&&e.qrcodeValue?a("el-form-item",{attrs:{label:"地址",prop:""}},[a("el-input",{staticStyle:{width:"300px"},attrs:{readonly:""},model:{value:e.qrcodeValue,callback:function(t){e.qrcodeValue=t},expression:"qrcodeValue"}},[a("el-button",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:e.qrcodeValue,expression:"qrcodeValue",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:e.clipboardSuccess,expression:"clipboardSuccess",arg:"success"}],attrs:{slot:"append"},slot:"append"},[e._v("复制")])],1)],1):e._e(),!e.checkIsFormStatus&&e.qrcodeValue?a("el-form-item",{attrs:{label:"二维码",prop:""}},[a("qrcode",{staticClass:"face-img",attrs:{value:e.qrcodeValue,options:e.qrcodeOptions,tag:"img",margin:10,alt:""}})],1):e._e(),a("el-form-item",{attrs:{label:"功能菜单配置",prop:"menuList"}},[a("el-select",{staticClass:"ps-select w-300",attrs:{disabled:!e.checkIsFormStatus,multiple:!0},model:{value:e.formData.menuList,callback:function(t){e.$set(e.formData,"menuList",t)},expression:"formData.menuList"}},e._l(e.allMenuList,(function(e){return a("el-option",{key:e.key,attrs:{label:e.verbose_name,value:e.key}})})),1)],1),a("el-form-item",{attrs:{"label-width":"0",prop:"templateId"}},[a("span",{staticClass:"m-r-10"},[e._v("人脸更新提醒模板ID：")]),a("el-input",{staticStyle:{"max-width":"300px"},attrs:{disabled:!e.checkIsFormStatus,placeholder:"请输入模板id"},model:{value:e.formData.templateId,callback:function(t){e.$set(e.formData,"templateId",t)},expression:"formData.templateId"}})],1)],1),e.checkIsFormStatus?a("div",{staticClass:"add-wrapper"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.saveAppidHandle}},[e._v("保存")])],1):e._e()],1)])},i=[],n=a("a34a"),s=a.n(n),o=a("ed08"),c=a("b2e5"),p=a.n(c);function d(e,t){return g(e)||f(e,t)||l(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function f(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,i=!1,n=void 0;try{for(var s,o=e[Symbol.iterator]();!(r=(s=o.next()).done);r=!0)if(a.push(s.value),t&&a.length===t)break}catch(c){i=!0,n=c}finally{try{r||null==o["return"]||o["return"]()}finally{if(i)throw n}}return a}}function g(e){if(Array.isArray(e))return e}function b(e,t,a,r,i,n,s){try{var o=e[n](s),c=o.value}catch(p){return void a(p)}o.done?t(c):Promise.resolve(c).then(r,i)}function h(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){b(n,r,i,s,o,"next",e)}function o(e){b(n,r,i,s,o,"throw",e)}s(void 0)}))}}var v={name:"SuperBindAppid",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},components:{qrcode:p.a},data:function(){return{formOperate:"detail",isLoading:!1,formData:{appid:"",secret_key:"",menuList:[],templateId:""},appid:"",secret_key:"",auth_time:"",appidList:[],formDataRuls:{appid:[{required:!1,message:"请先输入appid",trigger:"blur"}],secret_key:[{required:!1,message:"请先输入secret_key",trigger:"blur"}]},qrcodeOptions:{width:256,height:256},qrcodeValue:"pushi",allMenuList:[]}},computed:{checkIsFormStatus:function(){var e=!1;switch(this.formOperate){case"detail":e=!1;break;case"modify":e=!0;break}return e}},watch:{infoData:function(e){this.appid=e.appid}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.formData.appid=this.infoData.appid,this.formData.secret_key=this.infoData.secret_key,this.formData.templateId=this.infoData.template_id,this.getOrgIsBindAppid(),this.getAppPermission()},refreshHandle:function(){this.initLoad()},searchHandle:Object(o["c"])((function(){}),300),changeOperate:function(){"modify"!==this.formOperate?this.formOperate="modify":this.formOperate="detail"},getAppidList:function(){var e=this;return h(s.a.mark((function t(){var a,r,i,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationGetAppidListPost());case 3:if(a=t.sent,r=d(a,2),i=r[0],n=r[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===n.code?e.appidList=n.data:e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},getOrgIsBindAppid:function(){var e=this;return h(s.a.mark((function t(){var a,r,i,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationGetOrgAppidPost({id:e.organizationData.id,company:e.organizationData.company}));case 3:if(a=t.sent,r=d(a,2),i=r[0],n=r[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===n.code?(e.formData.appid=n.data.appid,e.formData.secret_key=n.data.secret_key,e.qrcodeValue=n.data.booking_url,e.formData.menuList=n.data.app_permission):e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()},saveAppidHandle:function(){var e=this;return h(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:e.$refs.appidRef.validate((function(t){t&&e.modifyOrganization()}));case 3:case"end":return t.stop()}}),t)})))()},modifyOrganization:function(){var e=this;return h(s.a.mark((function t(){var a,r,i,n,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a={id:e.organizationData.id,appid:e.formData.appid,secret_key:e.formData.secret_key,company:e.organizationData.company,app_permission:e.formData.menuList},e.formData.templateId&&(a.template_id=e.formData.templateId),e.isLoading=!0,t.next=5,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationModifyPost(a));case 5:if(r=t.sent,i=d(r,2),n=i[0],c=i[1],e.isLoading=!1,!n){t.next=13;break}return e.$message.error(n.message),t.abrupt("return");case 13:0===c.code?(e.$message.success("修改成功"),e.formOperate="detail",e.restoreHandle(e.type,e.formOperate)):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},clipboardSuccess:function(){this.$message({message:"复制成功",type:"success",duration:1500})},getAppPermission:function(){var e=this;return h(s.a.mark((function t(){var a,r,i,n;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationGetAppPermissionsPost());case 3:if(a=t.sent,r=d(a,2),i=r[0],n=r[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===n.code?e.allMenuList=n.data[0].children:e.$message.error(n.msg);case 12:case"end":return t.stop()}}),t)})))()}}},y=v,k=(a("36b3"),a("2877")),w=Object(k["a"])(y,r,i,!1,null,null,null);t["default"]=w.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-HealthyTarget"],{"4ec4":function(t,a,e){},"5f52":function(t,a,e){"use strict";e.r(a);var l=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"detail-healthy-target"},[e("div",{staticClass:"healthy-target records-wrapp-bg m-b-20"},[e("div",{staticClass:"p-b-10 ps-flex-bw flex-align-c"},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("健康目标")]),e("span",{staticClass:"time"},[t._v("更新时间："+t._s(t.formData.healthy_target_update_time))])]),e("el-form",{ref:"form",attrs:{size:"mini",model:t.formData,"label-position":"left","label-width":"80px"}},[e("el-form-item",{attrs:{label:"最新体重：","label-width":"110px"}},[e("span",[t._v(t._s(t.formData.weight)+"kg")])]),e("el-form-item",{attrs:{label:"目标：","label-width":"110px"}},[e("span",[t._v(t._s(t.formData.healthy_target))])]),e("el-form-item",{attrs:{label:"目标体重：","label-width":"110px"}},[e("span",[t._v(t._s(t.formData.weight_target)+"kg")])]),e("el-form-item",{attrs:{label:"目标达成时间：","label-width":"110px"}},[e("span",[t._v(t._s(t.formData.target_day))])]),e("el-form-item",{attrs:{label:"坚持时间：","label-width":"110px"}},[e("span",[t._v(t._s(t.formData.adherence_days))])])],1)],1)])},r=[],s={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(t){this.formData=t}},mounted:function(){},methods:{}},i=s,n=(e("9e02"),e("2877")),o=Object(n["a"])(i,l,r,!1,null,null,null);a["default"]=o.exports},"9e02":function(t,a,e){"use strict";var l=e("4ec4"),r=e.n(l);r.a}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-rule-components-FoodDiversityConfig"],{"12d5":function(t,e,s){},"78a0":function(t,e,s){"use strict";var a=s("12d5"),o=s.n(a);o.a},e989:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"food-diversity"},[s("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[s("div",{staticClass:"table-wrapper"},[s("div",{staticClass:"table-header"},[s("div",{staticClass:"table-title"},[t._v("基本信息")])]),s("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[s("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[s("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,placeholder:"请输入维度分值"},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[s("template",{slot:"append"},[t._v("分")])],2)],1),s("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[s("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.formData.config,(function(e,a,o){return s("div",{key:o,staticClass:"form-content-box m-b-20"},[s("div",[t._v("变量值")]),t._l(e.listText,(function(e,a){return s("div",{key:a,staticClass:"p-t-10"},[t._v(" "+t._s(e.text)+" "),s("span",{staticStyle:{color:"red"}},[t._v(t._s(e.tips))])])})),s("div",{staticClass:"p-t-10 p-b-10 flex-between"},[s("div",[t._v("规则配置")]),t.disabled?t._e():s("div",{staticClass:"align-r"},[s("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(s){return t.addRule(e,a)}}},[t._v(" 新增规则 ")])],1)]),s("div",{staticClass:"ps-flex-align-c"},[s("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),s("div",t._l(e.content,(function(o,i){return s("div",{key:i,staticClass:"ps-flex-align-c flex-wrap"},[s("span",{staticClass:"p-r-10 p-t-5"},[t._v(t._s(e.unitText))]),s("el-form-item",{attrs:{label:"",prop:"config."+a+".content."+i+".comparison",rules:{required:!0,message:"请选择",trigger:"blur"}}},[s("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:o.comparison,callback:function(e){t.$set(o,"comparison",e)},expression:"contentItem.comparison"}},t._l(t.comparisonList,(function(t,e){return s("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),s("div",{staticClass:"p-l-10"},[s("el-form-item",{attrs:{label:"",prop:"config."+a+".content."+i+".comparison_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[s("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:o.comparison_score,callback:function(e){t.$set(o,"comparison_score",e)},expression:"contentItem.comparison_score"}})],1)],1),s("div",{staticClass:"p-t-5 p-r-10"},[t._v("执行")]),s("el-form-item",{attrs:{label:"",prop:"config."+a+".content."+i+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[s("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:o.operation,callback:function(e){t.$set(o,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return s("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),s("div",{staticClass:"p-l-10"},[s("el-form-item",{attrs:{label:"",prop:"config."+a+".content."+i+".operation_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[s("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:o.operation_score,callback:function(e){t.$set(o,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),s("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),s("div",{staticClass:"m-b-30"},[e.content.length>1&&!t.disabled?s("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(o,a,i)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)}))],2)]),s("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[s("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),s("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",config:{diversity:{listText:[{text:"x种",tips:"（用户每餐食物多样摄入种类）"}],unitText:"x",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},cereals:{listText:[{text:"x1克/餐",tips:"（用户每餐谷物摄入量）"},{text:"y1克/餐",tips:"（查表法计算得出每餐谷物推荐摄入量）"}],unitText:"x1正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},eggsandmeat:{listText:[{text:"x2克/餐",tips:"（用户每餐鱼禽蛋肉摄入量）"},{text:"y2克/餐",tips:"（查表法计算得出用户每餐鱼禽蛋肉摄入量）"}],unitText:"x2正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},fruit:{listText:[{text:"x3克/餐",tips:"（用户每餐水果摄入量）"},{text:"y3克/餐",tips:"（查表计算得出用户每餐水果摄入量）"}],unitText:"x3正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]},vegetable:{listText:[{text:"x4克/餐",tips:"（用户每餐蔬菜摄入量）"},{text:"y4克/餐",tips:"（查表计算得出用户每餐蔬菜摄入量）"}],unitText:"x4正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]}}},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){for(var t in this.formData.score=this.data.score,this.formData.tips=this.data.tips,this.formData.type=this.data.type,this.data.config)this.formData.config[t].content=this.data.config[t];console.log(this.data)},addRule:function(t,e){console.log(this.formData.config[e]),this.formData.config[e].content.push({comparison:"",comparison_score:"",operation:"",operation_score:""})},removeRule:function(t,e,s){this.formData.config[e].content.splice(s,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var s={key:t.formData.type},a={};for(var o in t.formData.config)a[o]=t.formData.config[o].content;s[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:a},t.$emit("submitHandler",s)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,s,a){"confirm"===e?t.$closeCurrentTab(t.$route.path):s.confirmButtonLoading||a()}}).then((function(t){})).catch((function(t){}))}}},n=i,r=(s("78a0"),s("2877")),l=Object(r["a"])(n,a,o,!1,null,"cfeeba88",null);e["default"]=l.exports}}]);
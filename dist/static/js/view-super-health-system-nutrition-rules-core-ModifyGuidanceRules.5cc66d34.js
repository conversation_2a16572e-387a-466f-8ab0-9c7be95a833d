(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-nutrition-rules-core-ModifyGuidanceRules"],{"680a":function(e,t,a){"use strict";var r=a("ceda"),s=a.n(r);s.a},7587:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"mdify-guidance-rules container-wrapper"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"formGuidanceRules",attrs:{rules:e.formRuls,model:e.formData,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("基本信息")])]),a("div",{staticClass:"ps-flex"},[a("div",{staticStyle:{padding:"0 20px"}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"模型规则名称：",prop:"name"}},[a("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"模型规则名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1)],1),a("div",{staticStyle:{padding:"0 20px"}},[a("el-form-item",{attrs:{label:"适用范围：",prop:"content"}},[a("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"super","append-to-body":!0,filterable:!1},model:{value:e.formData.visible_organization,callback:function(t){e.$set(e.formData,"visible_organization",t)},expression:"formData.visible_organization"}})],1)],1)]),a("div",{staticStyle:{"max-width":"510px",padding:"0 20px"}},[a("el-form-item",{attrs:{label:"说明：",prop:"remark"}},[a("el-input",{staticClass:"ps-input p-b-10",attrs:{type:"textarea",placeholder:"请输入说明内容",autosize:{minRows:4,maxRows:10},maxlength:"60","show-word-limit":""},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1)],1)]),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("性别指导规则")])]),a("div",{staticStyle:{padding:"0 20px"}},[a("div",{staticClass:"ps-flex"},[a("el-form-item",{attrs:{label:"",prop:"comparison"}},[a("span",{staticClass:"p-r-10"},[e._v("男性人数占比")]),a("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.formData.comparison,callback:function(t){e.$set(e.formData,"comparison",t)},expression:"formData.comparison"}},[a("el-option",{attrs:{label:"等于",value:"=="}}),a("el-option",{attrs:{label:"大于",value:">"}}),a("el-option",{attrs:{label:"小于",value:"<"}})],1),a("span",{staticClass:"m-r-10"},[e._v("女性人数占比，且超过")])],1),a("el-form-item",{attrs:{label:"",prop:"comparison_score"}},[a("el-input",{staticClass:"ps-input w-100 m-r-10",attrs:{placeholder:""},model:{value:e.formData.comparison_score,callback:function(t){e.$set(e.formData,"comparison_score",t)},expression:"formData.comparison_score"}}),a("span",[e._v("%")]),a("span",[e._v("，")])],1),a("el-form-item",{attrs:{label:"",prop:"right_gender"}},[a("span",{staticClass:"m-r-10"},[e._v("以")]),a("el-select",{staticClass:"ps-select m-r-10",staticStyle:{width:"120px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.formData.right_gender,callback:function(t){e.$set(e.formData,"right_gender",t)},expression:"formData.right_gender"}},[a("el-option",{attrs:{label:"女性摄入量",value:"WOMEN"}}),a("el-option",{attrs:{label:"男性摄入量",value:"MAN"}})],1),a("span",[e._v("为准，")])],1),a("el-form-item",{attrs:{label:"",prop:"second_gender"}},[a("span",{staticClass:"m-r-10"},[e._v("反之以")]),a("el-select",{staticClass:"ps-select m-r-10",staticStyle:{width:"120px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.formData.second_gender,callback:function(t){e.$set(e.formData,"second_gender",t)},expression:"formData.second_gender"}},[a("el-option",{attrs:{label:"女性摄入量",value:"WOMEN"}}),a("el-option",{attrs:{label:"男性摄入量",value:"MAN"}})],1),a("span",[e._v("为依据。")])],1)],1)])]),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("年龄指导计算规则")])]),a("div",{staticStyle:{padding:"0 20px"}},[a("el-form-item",{attrs:{label:"",prop:"preferred_calculation_method"}},[a("span",{staticClass:"p-r-10"},[e._v(" 根据DRIs 表(中国营养学会 2013)查年龄范围所对应的营养摄入量，通过 ")]),a("el-select",{staticClass:"ps-select w-100 p-r-10",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.formData.preferred_calculation_method,callback:function(t){e.$set(e.formData,"preferred_calculation_method",t)},expression:"formData.preferred_calculation_method"}},[a("el-option",{attrs:{label:"中间值",value:"median"}}),a("el-option",{attrs:{label:"均值",value:"average"}})],1),a("span",[e._v("的计算方式，计算实际营养建议摄入量。")])],1)],1)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:e.closeHandler}},[e._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:e.submitHandler}},[e._v(" 保存 ")])],1)])],1)},s=[],i=a("a34a"),o=a.n(i),n=a("cbfb"),l=a("ed08");function c(e,t){return f(e)||m(e,t)||d(e,t)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"===typeof e)return u(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function m(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,s=!1,i=void 0;try{for(var o,n=e[Symbol.iterator]();!(r=(o=n.next()).done);r=!0)if(a.push(o.value),t&&a.length===t)break}catch(l){s=!0,i=l}finally{try{r||null==n["return"]||n["return"]()}finally{if(s)throw i}}return a}}function f(e){if(Array.isArray(e))return e}function v(e,t,a,r,s,i,o){try{var n=e[i](o),l=n.value}catch(c){return void a(c)}n.done?t(l):Promise.resolve(l).then(r,s)}function b(e){return function(){var t=this,a=arguments;return new Promise((function(r,s){var i=e.apply(t,a);function o(e){v(i,r,s,o,n,"next",e)}function n(e){v(i,r,s,o,n,"throw",e)}o(void 0)}))}}var g={data:function(){var e=function(e,t,a){if(""===t)return a(new Error("不能为空"));var r=/^\d+$/;t>100?a(new Error("比例不能大于100")):r.test(t)?a():a(new Error("请输入整数比例"))};return{type:"",isLoading:!1,formData:{name:"",remark:"",visible:"all",visible_organization:[],comparison_score:"",comparison:"==",right_gender:"MAN",second_gender:"WOMEN",preferred_calculation_method:"median"},formRuls:{comparison_score:[{required:!0,validator:e,trigger:"blur"}]}}},components:{OrganizationSelect:n["a"]},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var e=this.$decodeQuery(this.$route.query.data);this.formData={id:e.id,name:e.name,remark:e.remark,visible:e.visible,visible_organization:e.visible_organization,comparison_score:e.setting_json.comparison_score,comparison:e.setting_json.comparison,right_gender:e.setting_json.right_gender,second_gender:e.setting_json.second_gender,preferred_calculation_method:e.setting_json.preferred_calculation_method}}},getRuleModifyDris:function(e){var t=this;return b(o.a.mark((function a(){var r,s,i,n;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.visible=e.visible_organization.length?"part":"all",t.isLoading=!0,a.next=4,Object(l["Q"])(t.$apis.apiBackgroundHealthyAdminNutritionRuleModifyDrisPost(e));case 4:if(r=a.sent,s=c(r,2),i=s[0],n=s[1],t.isLoading=!1,!i){a.next=12;break}return t.$message.error(i.message),a.abrupt("return");case 12:0===n.code?(t.$message.success("修改成功"),t.$closeCurrentTab(t.$route.path)):t.$message.error(n.msg);case 13:case"end":return a.stop()}}),a)})))()},submitHandler:function(){var e=this;this.$refs.formGuidanceRules.validate((function(t){if(t){if(e.isLoading)return e.$message.error("请勿重复提交！");e.getRuleModifyDris(e.formData)}}))},closeHandler:function(){var e=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(t,a,r){"confirm"===t?e.$closeCurrentTab(e.$route.path):a.confirmButtonLoading||r()}}).then((function(e){})).catch((function(e){}))}}},h=g,_=(a("680a"),a("2877")),y=Object(_["a"])(h,r,s,!1,null,null,null);t["default"]=y.exports},ceda:function(e,t,a){}}]);
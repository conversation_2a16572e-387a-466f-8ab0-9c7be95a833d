(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-info-config-HealthInfoConfig"],{"160b":function(t,e,a){},"581f":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"HealthInfoConfig container-wrapper"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formData",attrs:{model:t.formData,"label-width":"80px",rules:t.formDataRules,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{"max-width":"450px",padding:"0 20px"}},[a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("口味偏好")]),t.isDisabled?a("el-button",{staticClass:"float-r",attrs:{size:"mini"},on:{click:t.modifyConfig}},[t._v(" 编辑 ")]):t._e()],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"请选择标签组：",prop:"taste","label-width":"130px"}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"400px"},attrs:{placeholder:"请输入关键词",multiple:"","collapse-tags":"",clearable:"",filterable:"","popper-class":"ps-popper-select"},model:{value:t.formData.taste,callback:function(e){t.$set(t.formData,"taste",e)},expression:"formData.taste"}},t._l(t.tasteList,(function(e,r){return a("el-option",{key:r,attrs:{label:e.name,value:e.id,disabled:t.isDisabled}})})),1)],1),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("人群特征")])]),a("el-form-item",{staticClass:"block-label",attrs:{label:"请选择标签组：",prop:"malady","label-width":"130px"}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"400px"},attrs:{placeholder:"请输入关键词",multiple:"","collapse-tags":"",clearable:"",filterable:"","popper-class":"ps-popper-select"},model:{value:t.formData.malady,callback:function(e){t.$set(t.formData,"malady",e)},expression:"formData.malady"}},t._l(t.maladyList,(function(e,r){return a("el-option",{key:r,attrs:{label:e.name,value:e.id,disabled:t.isDisabled}})})),1)],1),t.isDisabled?t._e():a("el-form-item",{staticClass:"t-a-c"},[a("el-button",{staticClass:"ps-cancel-btn",staticStyle:{width:"200px"},attrs:{type:"primary"},on:{click:t.modifyConfig}},[t._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",staticStyle:{width:"200px"},attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(" 保存 ")])],1)],1)])])],1)},s=[],i=a("a34a"),n=a.n(i),o=a("ed08");function l(t,e){return p(t)||d(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return f(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function d(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],r=!0,s=!1,i=void 0;try{for(var n,o=t[Symbol.iterator]();!(r=(n=o.next()).done);r=!0)if(a.push(n.value),e&&a.length===e)break}catch(l){s=!0,i=l}finally{try{r||null==o["return"]||o["return"]()}finally{if(s)throw i}}return a}}function p(t){if(Array.isArray(t))return t}function m(t,e,a,r,s,i,n){try{var o=t[i](n),l=o.value}catch(c){return void a(c)}o.done?e(l):Promise.resolve(l).then(r,s)}function b(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var i=t.apply(e,a);function n(t){m(i,r,s,n,o,"next",t)}function o(t){m(i,r,s,n,o,"throw",t)}n(void 0)}))}}var h={name:"NoticeAdd",props:{},data:function(){return{formData:{taste:"",malady:""},formDataRules:{taste:[{required:!0,message:"请选择口味偏好标签组",trigger:"blur"}],malady:[{required:!0,message:"请选择人群特征标签组",trigger:"blur"}]},tasteList:[],maladyList:[],configList:["food","user"],isLoading:!1,isDisabled:!0}},created:function(){var t=this;this.configList.forEach((function(e){t.getAllLabelGroupList(e)})),this.healthyInfoHealthyInfoSettingPost()},mounted:function(){},methods:{initLoad:function(){},healthyInfoHealthyInfoSettingPost:function(){var t=this;return b(n.a.mark((function e(){var a,r,s,i;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Q"])(t.$apis.apiBackgroundAdminHealthyInfoHealthyInfoSettingPost());case 3:if(a=e.sent,r=l(a,2),s=r[0],i=r[1],t.isLoading=!1,!s){e.next=11;break}return t.$message.error(s.message),e.abrupt("return");case 11:0===i.code?(t.formData.taste=i.data.taste,t.formData.malady=i.data.malady):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},getAllLabelGroupList:function(t){var e=this;return b(n.a.mark((function a(){var r,s,i,c;return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(o["Q"])(e.$apis.apiBackgroundHealthyLabelGroupAllLabelGroupListPost({type:t,page:1,page_size:999999}));case 3:if(r=a.sent,s=l(r,2),i=s[0],c=s[1],e.isLoading=!1,!i){a.next=11;break}return e.$message.error(i.message),a.abrupt("return");case 11:0===c.code?"food"===t?e.tasteList=c.data.results:"user"===t&&(e.maladyList=c.data.results):e.$message({type:"error",duration:1e3,message:c.msg});case 12:case"end":return a.stop()}}),a)})))()},getModifyHealthyInfoSettingPost:function(t){var e=this;return b(n.a.mark((function a(){var r,s,i,c;return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminHealthyInfoModifyHealthyInfoSettingPost(t));case 3:if(r=a.sent,s=l(r,2),i=s[0],c=s[1],e.isLoading=!1,!i){a.next=11;break}return e.$message.error(i.message),a.abrupt("return");case 11:0===c.code?(e.$message.success("保存成功"),e.healthyInfoHealthyInfoSettingPost()):e.$message({type:"error",duration:1e3,message:c.msg});case 12:case"end":return a.stop()}}),a)})))()},modifyConfig:function(){this.isDisabled=!this.isDisabled},submitForm:function(){var t=this;this.$refs.formData.validate((function(e){if(!e)return console.log("error submit!!"),!1;t.getModifyHealthyInfoSettingPost(t.formData)}))}}},g=h,y=(a("993d6"),a("2877")),v=Object(y["a"])(g,r,s,!1,null,null,null);e["default"]=v.exports},"993d6":function(t,e,a){"use strict";var r=a("160b"),s=a.n(r);s.a}}]);
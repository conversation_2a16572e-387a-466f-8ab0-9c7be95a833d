(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-assessment-HealthAssessment"],{"07ec":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"HealthAssessment"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")]),a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.gotoQuestionnaire("add")}}},[e._v(" 添加问卷 ")])],1)]),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row","tree-props":{children:"children_list",hasChildren:"has_children"}}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"问卷名称",align:"center"}}),a("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{prop:"update_time",label:"修改时间",align:"center"}}),a("el-table-column",{attrs:{prop:"",label:"启用/停用",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-color":"#f59933"},on:{change:function(a){return e.modifyStatus(a,t.row.id)}},model:{value:t.row.is_open,callback:function(a){e.$set(t.row,"is_open",a)},expression:"scope.row.is_open"}})]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.gotoQuestionnaire("modify",t.row)}}},[e._v(" 编辑 ")]),a("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[e._v("|")]),a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHandler("single",t.row.id)}}},[e._v(" 删除 ")])]}}])})],1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)])],1)},r=[],s=a("a34a"),i=a.n(s),o=a("ed08");function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){u(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function u(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t){return m(e)||g(e,t)||f(e,t)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"===typeof e)return h(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function g(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],n=!0,r=!1,s=void 0;try{for(var i,o=e[Symbol.iterator]();!(n=(i=o.next()).done);n=!0)if(a.push(i.value),t&&a.length===t)break}catch(l){r=!0,s=l}finally{try{n||null==o["return"]||o["return"]()}finally{if(r)throw s}}return a}}function m(e){if(Array.isArray(e))return e}function b(e,t,a,n,r,s,i){try{var o=e[s](i),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(n,r)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(n,r){var s=e.apply(t,a);function i(e){b(s,n,r,i,o,"next",e)}function o(e){b(s,n,r,i,o,"throw",e)}i(void 0)}))}}var v={name:"HealthAssessment",components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},name:{type:"input",label:"问卷名称",value:"",placeholder:"请输入问卷名称"},status:{type:"select",label:"状态",value:"",maxWidth:"130px",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"启用",value:"enable"},{label:"禁用",value:"disable"}]}},dialogLoading:!1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getHealthyQuestion()},searchHandle:Object(o["c"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getHealthyQuestion:function(){var e=this;return y(i.a.mark((function t(){var a,n,r,s;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundHealthyQuestionListPost(c(c({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,n=d(a,2),r=n[0],s=n[1],e.isLoading=!1,!r){t.next=11;break}return e.$message.error(r.message),t.abrupt("return");case 11:0===s.code?(e.totalCount=s.data.count,e.tableData=s.data.results.map((function(e){return e.is_open="enable"===e.status,e}))):e.$message.error(s.msg);case 12:case"end":return t.stop()}}),t)})))()},deleteHandler:function(e,t){var a=this;this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=y(i.a.mark((function e(n,r,s){var l,c,u,p;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==n){e.next=20;break}if(!a.dialogLoading){e.next=3;break}return e.abrupt("return",a.$message.error("请勿重复提交！"));case 3:return a.dialogLoading=!0,r.confirmButtonLoading=!0,e.next=7,Object(o["Q"])(a.$apis.apiBackgroundHealthyQuestionDeletePost({ids:[t]}));case 7:if(l=e.sent,c=d(l,2),u=c[0],p=c[1],a.dialogLoading=!1,!u){e.next=15;break}return a.$message.error(u.message),e.abrupt("return");case 15:0===p.code?(a.$message.success(p.msg),a.getHealthyQuestion(),a.checkList=[]):a.$message.error(p.msg),s(),r.confirmButtonLoading=!1,e.next=21;break;case 20:r.confirmButtonLoading||s();case 21:case"end":return e.stop()}}),e)})));function n(t,a,n){return e.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},modifyStatus:function(e,t){var a=this;return y(i.a.mark((function n(){var r;return i.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a.isLoading=!0,n.next=3,a.$apis.apiBackgroundHealthyQuestionModifyStatusPost({id:t,status:e?"enable":"disable"});case 3:r=n.sent,a.isLoading=!1,0===r.code?(a.$message.success("操作成功！"),a.getHealthyQuestion()):a.$message.error(r.msg);case 6:case"end":return n.stop()}}),n)})))()},gotoQuestionnaire:function(e,t){this.$router.push({name:"SuperAddEditQuestionnaire",query:{type:e,data:"modify"===e?this.$encodeQuery(t):""}})},handleSizeChange:function(e){this.pageSize=e,this.getHealthyQuestion()},handleCurrentChange:function(e){this.currentPage=e,this.getHealthyQuestion()},formatQueryParams:function(e){var t={};for(var a in e)""!==e[a].value&&("select_time"!==a?t[a]=e[a].value:e[a].value&&e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]));return t}}},w=v,x=(a("1bdf"),a("2877")),C=Object(x["a"])(w,n,r,!1,null,"8fafa66c",null);t["default"]=C.exports},"1bdf":function(e,t,a){"use strict";var n=a("6f3b"),r=a.n(n);r.a},"6f3b":function(e,t,a){}}]);
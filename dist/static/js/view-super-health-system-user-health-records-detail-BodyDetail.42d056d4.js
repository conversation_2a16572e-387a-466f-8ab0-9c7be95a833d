(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-BodyDetail","view-super-health-system-user-health-records-constants"],{"2db4":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"BodyDetail"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")]),a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"plain",type:"export"},on:{click:function(t){return e.handleExport("batch")}}},[e._v(" 导出报告 ")])],1)]),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row","row-key":"user_id"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center","reserve-selection":!0,"class-name":"ps-checkbox"}}),a("el-table-column",{attrs:{prop:"name",label:"报告名称",align:"center"}}),a("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.clickGoToBodyDetail(t.row)}}},[e._v(" 查看报告 ")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleExport("single",t.row)}}},[e._v(" 下载 ")])]}}])})],1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)])],1)},n=[],i=a("a34a"),o=a.n(i),l=a("ed08"),s=a("2f56"),c=a("f63a");function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function f(e,t){return g(e)||b(e,t)||m(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return y(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?y(e,t):void 0}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function b(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,n=!1,i=void 0;try{for(var o,l=e[Symbol.iterator]();!(r=(o=l.next()).done);r=!0)if(a.push(o.value),t&&a.length===t)break}catch(s){n=!0,i=s}finally{try{r||null==l["return"]||l["return"]()}finally{if(n)throw i}}return a}}function g(e){if(Array.isArray(e))return e}function v(e,t,a,r,n,i,o){try{var l=e[i](o),s=l.value}catch(c){return void a(c)}l.done?t(s):Promise.resolve(s).then(r,n)}function w(e){return function(){var t=this,a=arguments;return new Promise((function(r,n){var i=e.apply(t,a);function o(e){v(i,r,n,o,l,"next",e)}function l(e){v(i,r,n,o,l,"throw",e)}o(void 0)}))}}var x={name:"BodyDetail",mixins:[c["a"]],data:function(){return{params:{},isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:s["BODY_DETAIL"],selectListId:[]}},created:function(){this.params=this.$route.query,this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getHealthyInfoBodyDataList()},searchHandle:Object(l["c"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},handleSelectionChange:function(e){var t=this;this.selectListId=[];var a=Object.freeze(e);a.map((function(e){t.selectListId.push(e.id)}))},handleExport:function(e,t){if("batch"===e&&!this.selectListId.length)return this.$message.error("请选择需要导出的体检报告");var a={type:"ExportHealthyInfoBodyData",params:{ids:"batch"===e?this.selectListId:[t.id],type:this.params.type,user_id:this.params.user_id}};this.exportHandle(a)},getHealthyInfoBodyDataList:function(){var e=this;return w(o.a.mark((function t(){var a,r,n,i;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(l["Q"])(e.$apis.apiBackgroundAdminHealthyInfoBodyDataListPost(d(d({id:e.params.id},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,r=f(a,2),n=r[0],i=r[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.tableData=i.data.results):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.getHealthyInfoBodyDataList()},handleCurrentChange:function(e){this.currentPage=e,this.getHealthyInfoBodyDataList()},formatQueryParams:function(e){var t={};for(var a in e)""!==e[a].value&&("select_time"!==a?t[a]=e[a].value:e[a].value&&e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]));return t},clickGoToBodyDetail:function(e){this.$router.push({name:"SuperBodyViewReport",query:{id:e.id,type:this.params.type,user_id:this.params.user_id}})}}},S=x,D=(a("6321"),a("2877")),O=Object(D["a"])(S,r,n,!1,null,"3013956e",null);t["default"]=O.exports},"2f56":function(e,t,a){"use strict";a.r(t),a.d(t,"recentSevenDay",(function(){return n})),a.d(t,"USERHEALTHRECORDS",(function(){return i})),a.d(t,"RADAROPTION",(function(){return o})),a.d(t,"MEALTIME_SETTING",(function(){return l})),a.d(t,"BODY_DETAIL",(function(){return s}));var r=a("5a0c"),n=[r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD")],i={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},company_ids:{type:"CompanySelect",value:[],label:"组织",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,companyOpts:{label:"name",value:"company"},companyKey:"all"},status:{type:"select",value:"",label:"档案状态",clearable:!0,dataList:[{label:"全部",value:""},{label:"使用中",value:"enable"},{label:"注销中",value:"logoff"}]}},o={title:{text:0,x:"center",y:"center",textStyle:{color:"#fd953c",fontWeight:"bolder",fontSize:28}},tooltip:{trigger:"axis"},radar:{name:{textStyle:{padding:[-10,-5]},color:"#23282d"},splitLine:{lineStyle:{type:"dashed",width:1}},splitArea:{show:!1,areaStyle:{color:"rgba(255,0,0,0)"}},indicator:[{name:"食物多样性",max:100},{name:"营养均衡",max:100},{name:"能量摄入",max:100},{name:"BMI",max:100},{name:"运动情况",max:100}]},series:[{tooltip:{trigger:"item"},type:"radar",label:{show:!1},areaStyle:{color:"#fad1ae"},data:[{name:"健康分",value:[0,0,0,0,0]}]}],color:["#fca255"]},l={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.marker,a=e.percent;return t+e.name+"&nbsp;&nbsp;&nbsp;"+a+"%"}},title:{text:"0",x:"center",y:"center",top:"25%",textStyle:{color:"#fd953c",fontSize:18}},legend:{top:"62%",orient:"vertical",y:"bottom",padding:[0,0,0,0]},series:[{type:"pie",radius:["45%","60%"],avoidLabelOverlap:!1,top:"-10%",height:200,itemStyle:{borderRadius:10,borderColor:"#f1f2f5",borderWidth:3},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},s={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1}}},4317:function(e,t,a){},6321:function(e,t,a){"use strict";var r=a("4317"),n=a.n(r);n.a}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-BodyTesting"],{"027c":function(t,e,a){},"2afd":function(t,e,a){"use strict";var s=a("027c"),n=a.n(s);n.a},5698:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"body-testing records-wrapp-bg"},[a("div",{staticClass:"ps-flex-bw flex-align-c"},[a("div",{staticClass:"ps-flex flex-wrap"},[a("span",{staticClass:"p-r-10",staticStyle:{"font-weight":"bold"}},[t._v("身体检测")]),a("span",{staticClass:"testing-time"},[t._v("更新时间："+t._s(t.dartime))])]),a("div",{staticClass:"ps-flex flex-align-c flex-wrap"},[a("button-icon",{attrs:{color:"plain",type:"Import"}},[t._v(" 导入数据 ")]),a("button-icon",{attrs:{color:"plain",type:"export",size:"small"}},[t._v(" 导出数据 ")]),a("div",{staticClass:"m-l-5"},[a("el-button",{staticClass:"ps-origin-btn m-l-30",attrs:{type:"primary",size:"mini"},on:{click:t.gotoBodyDetail}},[a("div",{staticClass:"ps-flex flex-align-c"},[a("i",{staticClass:"iconfont icon-gengduo el-icon--left",staticStyle:{"font-size":"13px"}}),t._v(" 更多数据 ")])])],1)],1)]),a("div",{staticStyle:{"font-weight":"bold"}},[t._v("科室检查")]),a("div",{staticClass:"inspect-wrapp"},[Object.keys(t.formData)&&Object.keys(t.formData).length?a("div",t._l(t.formData,(function(e,s,n){return a("div",{key:n},[a("div",{staticClass:"l-title clearfix"},[a("span",[t._v(" "+t._s(e.name))])]),a("div",{staticClass:"inspect-content  ps-flex flex-wrap"},t._l(e.children,(function(e,s,n){return a("div",{key:n,staticClass:"content-wrapp ps-flex-bw p-r-20 p-b-15"},[a("span",{staticClass:"text"},[t._v(t._s(e.name)+"："),a("span",{staticClass:"shuzi"},[t._v(t._s(e.value))])]),a("span",[t._v("-- "+t._s(e.unit))])])})),0)])})),0):a("el-empty",{attrs:{description:"暂无数据"}})],1)])},n=[],i=a("5a0c"),r=a.n(i);function o(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,s)}return a}function c(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?o(Object(a),!0).forEach((function(e){l(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function l(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var p={props:{formInfoData:{type:Array,default:function(){return[]}},paramsInfo:{type:Object,default:function(){return{}}}},data:function(){return{formData:{},dartime:"",aa:{"基本信息":{"姓名":"100(cm)","脉率":"100(cm)"},"人体成分":{BMI:"300(cm)","基础代谢":"100(cm)"}}}},watch:{formInfoData:function(t){this.formData=t}},created:function(){this.gartime()},mounted:function(){console.log(this.paramsInfo,22)},methods:{gartime:function(){this.dartime=r()().format("YYYY-MM-DD hh-mm-ss")},gotoBodyDetail:function(){this.$router.push({name:"SuperBodyDetail",query:c({},this.paramsInfo)})}}},f=p,u=(a("2afd"),a("2877")),d=Object(u["a"])(f,s,n,!1,null,"de90d73c",null);e["default"]=d.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-MealFoodClassification"],{"172a":function(t,e,o){"use strict";o.r(e);var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"container-wrapper has-organization"},[o("refresh-tool",{on:{refreshPage:t.refreshHandle}}),o("div",{attrs:{id:"classification-container"}},[o("div",{staticClass:"organization-tree"},[o("el-input",{staticClass:"tree-search ps-input",attrs:{placeholder:"请输入一级分类名称"},model:{value:t.primaryName,callback:function(e){t.primaryName=e},expression:"primaryName"}}),o("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoadingFoodFoodSor,expression:"isLoadingFoodFoodSor"}]},[o("button-icon",{attrs:{color:"origin",type:""},on:{click:function(e){return t.clickShowDialogClassification("addSort")}}},[t._v(" 新建一级分类 ")]),o("ul",{staticClass:"infinite-list",style:{overflow:"auto",height:t.classificationBoxHeight+"px"}},t._l(t.foodFoodSortPrimaryList,(function(e,a){return o("li",{key:a},[o("div",{staticClass:"primary-classification"},[o("span",[t._v(t._s(e.name))]),o("div",{staticClass:"ps-flex flex-align-c"},[o("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(o){return t.clickShowDialogClassification("editSort",e)}}},[t._v(" 编辑 ")]),o("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(o){return t.deleteHaldler("delSort",e)}}},[t._v(" 删除 ")])],1)])])})),0)],1)],1),o("div",{staticClass:"classification-list"},[o("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting},on:{search:t.searchHandle}}),o("div",{staticClass:"table-wrapper"},[o("div",{staticClass:"table-header"},[o("div",{staticClass:"table-title"},[t._v("数据列表")]),o("div",{staticClass:"align-r"},[o("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.clickShowDialogClassification("addCategory")}}},[t._v(" 创建二级分类 ")]),o("button-icon",{attrs:{color:"origin",type:"mul"},on:{click:function(e){return t.deleteHaldler("delBatchCategory")}}},[t._v(" 批量删除 ")])],1)]),o("div",{staticClass:"table-content"},[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,"row-key":"id",stripe:"","row-class-name":t.tableRowClassName,"header-row-class-name":"ps-table-header-row"},on:{"selection-change":t.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"55","class-name":"ps-checkbox"}}),o("el-table-column",{attrs:{prop:"sort_name",label:"一级分类",align:"center"}}),o("el-table-column",{attrs:{prop:"name",label:"二级分类",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("span",{staticClass:"status-point",style:{backgroundColor:e.row.color}}),o("span",[t._v(t._s(e.row.name))])]}}])}),o("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),o("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(o){return t.clickShowDialogClassification("editCategory",e.row)}}},[t._v(" 编辑 ")]),o("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(o){return t.deleteHaldler("delCategory",e.row)}}},[t._v(" 删除 ")])]}}])})],1)],1),o("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[o("el-pagination",{staticClass:"ps-text",attrs:{"current-page":t.currentPage,"page-sizes":[10,20,30,40],"page-size":t.pageSize,layout:"total, prev, pager, next,sizes,jumper",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1)]),o("el-dialog",{attrs:{title:"批量导入",visible:t.importShowDialog,width:"600px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.importShowDialog=e}}},[o("import-upload-file",{attrs:{uploadFormItemLabel:"导入分类","file-type":"zip",link:"https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9d831119671ac5dd0f34398007cd4b1a1617760590210.zip"},on:{publicUrl:t.publicUrl}}),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{staticClass:"ps-cancel-btn",on:{click:function(e){t.importShowDialog=!1}}},[t._v("取 消")]),o("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.mulImortFace}},[t._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:t.dialogClassificationTitle,visible:t.showDialogSort,width:"400px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialogSort=e}}},[o("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.formSortLoading,expression:"formSortLoading"}],ref:"dialogSortForm",attrs:{model:t.dialogSortForm,"status-icon":"",rules:t.dialogSortFormRules,"label-width":"80px"},nativeOn:{submit:function(t){t.preventDefault()}}},[o("el-form-item",{attrs:{label:"一级分类",prop:"primarySortName"}},[o("el-input",{staticClass:"ps-input",staticStyle:{width:"190px"},attrs:{placeholder:"请输入一级分类名称",maxlength:"15"},model:{value:t.dialogSortForm.primarySortName,callback:function(e){t.$set(t.dialogSortForm,"primarySortName",e)},expression:"dialogSortForm.primarySortName"}})],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.showDialogSort=!1}}},[t._v("取 消")]),o("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:t.formSortLoading},on:{click:t.determineSortDialog}},[t._v(" 确 定 ")])],1)],1),o("el-dialog",{attrs:{title:t.dialogClassificationTitle,visible:t.showDialogCategory,width:"400px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.showDialogCategory=e}}},[o("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.formCategoryLoading,expression:"formCategoryLoading"}],ref:"dialogCategoryForm",attrs:{model:t.dialogCategoryForm,"status-icon":"",rules:t.dialogCategoryRules,"label-width":"80px"},nativeOn:{submit:function(t){t.preventDefault()}}},[o("el-form-item",{attrs:{label:"一级分类",prop:"primarySortId"}},[o("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择一级分类","popper-class":"ps-popper-select","collapse-tags":"",filterable:"",clearable:""},model:{value:t.dialogCategoryForm.primarySortId,callback:function(e){t.$set(t.dialogCategoryForm,"primarySortId",e)},expression:"dialogCategoryForm.primarySortId"}},t._l(this.foodFoodSortPrimaryList,(function(t){return o("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),o("el-form-item",{attrs:{label:"二级分类",prop:"categoryName"}},[o("el-input",{staticClass:"ps-input",staticStyle:{width:"190px"},attrs:{maxlength:"15"},model:{value:t.dialogCategoryForm.categoryName,callback:function(e){t.$set(t.dialogCategoryForm,"categoryName",e)},expression:"dialogCategoryForm.categoryName"}})],1),o("el-form-item",{attrs:{label:"添加颜色",prop:"color"}},[o("el-color-picker",{model:{value:t.dialogCategoryForm.color,callback:function(e){t.$set(t.dialogCategoryForm,"color",e)},expression:"dialogCategoryForm.color"}})],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.showDialogCategory=!1}}},[t._v("取 消")]),o("el-button",{staticClass:"ps-btn",attrs:{type:"primary",loading:t.formCategoryLoading},on:{click:t.determineCategoryDialog}},[t._v(" 确 定 ")])],1)],1)],1)},r=[],i=o("a34a"),s=o.n(i),n=o("ed08"),l=o("a1d6");function c(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,a)}return o}function d(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?c(Object(o),!0).forEach((function(e){g(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):c(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function g(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function u(t,e){return y(t)||h(t,e)||p(t,e)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"===typeof t)return f(t,e);var o=Object.prototype.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,a=new Array(e);o<e;o++)a[o]=t[o];return a}function h(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var o=[],a=!0,r=!1,i=void 0;try{for(var s,n=t[Symbol.iterator]();!(a=(s=n.next()).done);a=!0)if(o.push(s.value),e&&o.length===e)break}catch(l){r=!0,i=l}finally{try{a||null==n["return"]||n["return"]()}finally{if(r)throw i}}return o}}function y(t){if(Array.isArray(t))return t}function b(t,e,o,a,r,i,s){try{var n=t[i](s),l=n.value}catch(c){return void o(c)}n.done?e(l):Promise.resolve(l).then(a,r)}function C(t){return function(){var e=this,o=arguments;return new Promise((function(a,r){var i=t.apply(e,o);function s(t){b(i,a,r,s,n,"next",t)}function n(t){b(i,a,r,s,n,"throw",t)}s(void 0)}))}}var w={name:"MealFoodClassification",props:{},data:function(){return{classificationBoxHeight:"",isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"二级分类",value:"",placeholder:"请输入二级分类名称"}},primaryName:"",importShowDialog:!1,dialogClassificationTitle:"",showDialogClassificationType:"",showDialogCategory:!1,showDialogSort:!1,selectList:[{name:"特价",id:"1"},{name:"折扣",id:"2"}],dialogCategoryForm:{primarySortId:"",categoryName:"",color:"#409EFF"},dialogCategoryRules:{primarySortId:[{required:!0,message:"请选择一级分类",trigger:"change"}],categoryName:[{required:!0,message:"请输入一级分类名称",trigger:"blur"}]},dialogSortForm:{primarySortName:""},dialogSortFormRules:{primarySortName:[{required:!0,message:"请输入一级分类名称",trigger:"blur"}]},isLoadingFoodFoodSor:!1,formSortLoading:!1,formCategoryLoading:!1,foodFoodSortPrimaryList:[],showDialogClassificationRow:{},selectListId:[],delType:""}},components:{ImportUploadFile:l["a"]},created:function(){this.initLoad()},watch:{tableData:function(){this.$nextTick((function(){this.classificationBoxHeight=document.getElementsByClassName("search-form-wrapper")[0].offsetHeight+document.getElementsByClassName("table-wrapper")[0].offsetHeight-140}))}},mounted:function(){},methods:{initLoad:function(){this.foodFoodSortList(),this.foodFoodCategoryList()},searchHandle:Object(n["c"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},foodFoodSortList:function(){var t=this;return C(s.a.mark((function e(){var o,a,r,i,l;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoadingFoodFoodSor=!0,o={page:1,page_size:999999},t.primaryName&&(o.name=t.primaryName),e.next=5,Object(n["Q"])(t.$apis.apiBackgroundAdminFoodSortListPost(o));case 5:if(a=e.sent,r=u(a,2),i=r[0],l=r[1],t.isLoadingFoodFoodSor=!1,!i){e.next=13;break}return t.$message.error(i.message),e.abrupt("return");case 13:0===l.code?(console.log(l),t.foodFoodSortPrimaryList=l.data.results):t.$message.error(l.msg);case 14:case"end":return e.stop()}}),e)})))()},foodFoodSortAdd:function(){var t=this;return C(s.a.mark((function e(){var o,a,r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.formSortLoading=!0,e.next=3,Object(n["Q"])(t.$apis.apiBackgroundAdminFoodSortAddPost({name:t.dialogSortForm.primarySortName}));case 3:if(o=e.sent,a=u(o,2),r=a[0],i=a[1],t.formSortLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===i.code?(t.showDialogSort=!1,t.foodFoodSortList()):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},foodFoodSortModify:function(){var t=this;return C(s.a.mark((function e(){var o,a,r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.formSortLoading=!0,e.next=3,Object(n["Q"])(t.$apis.apiBackgroundAdminFoodSortModifyPost({name:t.dialogSortForm.primarySortName,id:t.showDialogClassificationRow.id}));case 3:if(o=e.sent,a=u(o,2),r=a[0],i=a[1],t.formSortLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===i.code?(t.showDialogSort=!1,t.foodFoodSortList()):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},foodFoodSorttDelete:function(t){var e=this;return C(s.a.mark((function o(){var a,r,i,l;return s.a.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,Object(n["Q"])(e.$apis.apiBackgroundAdminFoodSortDeletePost({ids:[t.id]}));case 2:if(a=o.sent,r=u(a,2),i=r[0],l=r[1],!i){o.next=9;break}return e.$message.error(i.message),o.abrupt("return");case 9:0===l.code?(e.$message.success(l.msg),e.foodFoodSortList()):e.$message.error(l.msg);case 10:case"end":return o.stop()}}),o)})))()},foodFoodCategoryList:function(){var t=this;return C(s.a.mark((function e(){var o,a,r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(n["Q"])(t.$apis.apiBackgroundAdminFoodCategoryListPost(d(d({},t.formatQueryParams(t.searchFormSetting)),{},{page:t.currentPage,page_size:t.pageSize})));case 3:if(o=e.sent,a=u(o,2),r=a[0],i=a[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===i.code?(t.totalCount=i.data.count,t.tableData=i.data.results):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},foodFoodCategoryAdd:function(){var t=this;return C(s.a.mark((function e(){var o,a,r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.formCategoryLoading=!0,e.next=3,Object(n["Q"])(t.$apis.apiBackgroundAdminFoodCategoryAddPost({sort_id:t.dialogCategoryForm.primarySortId,name:t.dialogCategoryForm.categoryName,color:t.dialogCategoryForm.color}));case 3:if(o=e.sent,a=u(o,2),r=a[0],i=a[1],t.formCategoryLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===i.code?(t.showDialogCategory=!1,t.searchHandle()):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},foodFoodCategoryModify:function(){var t=this;return C(s.a.mark((function e(){var o,a,r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.formCategoryLoading=!0,e.next=3,Object(n["Q"])(t.$apis.apiBackgroundAdminFoodCategoryModifyPost({id:t.showDialogClassificationRow.id,status:t.showDialogClassificationRow.status,organization:t.showDialogClassificationRow.organization,sort_id:t.dialogCategoryForm.primarySortId,name:t.dialogCategoryForm.categoryName,color:t.dialogCategoryForm.color}));case 3:if(o=e.sent,a=u(o,2),r=a[0],i=a[1],t.formCategoryLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===i.code?(t.showDialogCategory=!1,t.foodFoodCategoryList()):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},foodFoodCategoryDelete:function(t){var e=this;return C(s.a.mark((function o(){var a,r,i,l;return s.a.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,Object(n["Q"])(e.$apis.apiBackgroundAdminFoodCategoryDeletePost({ids:"delCategory"===e.delType?[t.id]:e.selectListId}));case 2:if(a=o.sent,r=u(a,2),i=r[0],l=r[1],!i){o.next=9;break}return e.$message.error(i.message),o.abrupt("return");case 9:0===l.code?(e.$message.success(l.msg),e.searchHandle()):e.$message.error(l.msg);case 10:case"end":return o.stop()}}),o)})))()},handleSelectionChange:function(t){var e=this;this.selectListId=[];var o=Object.freeze(t);o.map((function(t){e.selectListId.push(t.id)}))},formatQueryParams:function(t){var e={};for(var o in t)t[o].value&&("select_date"!==o?e[o]=t[o].value:t[o].value.length>0&&(e.start_date=t[o].value[0],e.end_date=t[o].value[1]));return e},handleSizeChange:function(t){this.pageSize=t,this.foodFoodCategoryList()},handleCurrentChange:function(t){this.currentPage=t,this.foodFoodCategoryList()},tableRowClassName:function(t){t.row;var e=t.rowIndex,o="";return(e+1)%2===0&&(o+="table-header-row"),o},addAndEditMealFood:function(){},deleteHaldler:function(t,e){if("delBatchCategory"===t&&!this.selectListId.length)return this.$message.error("请先选择数据！");var o=this,a="";switch(this.delType=t,t){case"delSort":a="";break;case"delCategory":a="";break;case"delBatchCategory":a="批量";break;default:break}this.$confirm("是否".concat(a,"删除该分类？"),"".concat(a,"删除"),{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var a=C(s.a.mark((function a(r,i,n){return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==r){a.next=13;break}i.confirmButtonLoading=!0,a.t0=t,a.next="delSort"===a.t0?5:7;break;case 5:return o.foodFoodSorttDelete(e),a.abrupt("break",9);case 7:return o.foodFoodCategoryDelete(e),a.abrupt("break",9);case 9:n(),i.confirmButtonLoading=!1,a.next=14;break;case 13:i.confirmButtonLoading||n();case 14:case"end":return a.stop()}}),a)})));function r(t,e,o){return a.apply(this,arguments)}return r}()})},publicUrl:function(t){console.log(t)},mulImortFace:function(){this.uploadUrl||this.$message.error("食材还没上传完毕或未上传")},clickShowDialogClassification:function(t,e){this.showDialogClassificationRow={},this.showDialogClassificationType=t,"addSort"===t?(this.dialogClassificationTitle="新增一级分类",this.dialogSortForm.primarySortName="",this.showDialogSort=!0):"editSort"===t?(this.dialogClassificationTitle="编辑一级分类",this.dialogSortForm.primarySortName=e.name,this.showDialogClassificationRow=e,this.showDialogSort=!0):"addCategory"===t?(this.dialogClassificationTitle="新增二级分类",this.showDialogCategory=!0,this.dialogCategoryForm={primarySortId:"",categoryName:"",color:"#409EFF"}):"editCategory"===t&&(this.dialogClassificationTitle="编辑二级分类",this.showDialogCategory=!0,this.dialogCategoryForm={primarySortId:e.sort,categoryName:e.name,color:e.color},this.showDialogClassificationRow=e)},determineSortDialog:function(){var t=this;this.$refs.dialogSortForm.validate((function(e){if(!e)return!1;"addSort"===t.showDialogClassificationType?t.foodFoodSortAdd():"editSort"===t.showDialogClassificationType&&t.foodFoodSortModify()}))},determineCategoryDialog:function(){var t=this;this.$refs.dialogCategoryForm.validate((function(e){if(!e)return!1;"addCategory"===t.showDialogClassificationType?t.foodFoodCategoryAdd():"editCategory"===t.showDialogClassificationType&&t.foodFoodCategoryModify()}))},openImport:function(){this.$message.error("暂无导入")}}},v=w,S=(o("d30c"),o("2877")),F=Object(S["a"])(v,a,r,!1,null,"23f0d1ac",null);e["default"]=F.exports},"3dae":function(t,e,o){},d30c:function(t,e,o){"use strict";var a=o("3dae"),r=o.n(a);r.a}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-BasicInfo"],{3008:function(a,t,s){},3895:function(a,t,s){"use strict";s.r(t);var e=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"detail-basic-info"},[e("div",{staticClass:"basic-info records-wrapp-bg m-b-20"},[e("div",{staticClass:"p-b-10",staticStyle:{"font-weight":"bold"}},[a._v("基本属性")]),e("div",{staticClass:"ps-flex flex-align-c p-b-20"},[e("el-image",{staticStyle:{width:"50px",height:"50px","border-radius":"50px"},attrs:{fit:"fill",src:a.formData.head_image?a.formData.head_image:"男"===a.formData.gender?s("abc7"):s("89ce")}}),e("div",{staticClass:"p-l-50"},[e("div",[e("span",{staticClass:"p-r-20 info-name"},[a._v(a._s(a.formData.name))]),e("span",[a._v(a._s(a.formData.gender))])]),e("div",{staticClass:"info-id"},[e("span",{staticStyle:{color:"#23282d",opacity:"0.4"}},[a._v("用户ID：")]),e("span",[a._v(a._s(a.formData.user_id))])]),e("div",{staticClass:"info-id"},[e("span",{staticStyle:{color:"#23282d",opacity:"0.4"}},[a._v("档案ID：")]),e("span",[a._v(a._s(a.formData.id))])])])],1),e("el-form",{ref:"form",attrs:{size:"mini",model:a.formData,"label-position":"left","label-width":"80px"}},[e("el-form-item",{attrs:{label:"手机号：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.phone))])]),e("el-form-item",{attrs:{label:"出生日期：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.birthday))])]),e("el-form-item",{attrs:{label:"年龄：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.age)+"岁")])]),e("el-form-item",{attrs:{label:"身高：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.height)+"cm")])]),e("el-form-item",{attrs:{label:"体重：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.weight)+"kg")])]),e("el-form-item",{attrs:{label:"BMI：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.bmi))]),e("span",{staticClass:"info-bmi-status m-l-10"},[a._v(a._s(a.formData.bmi_text))])]),e("el-form-item",{attrs:{label:"体脂率：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.fat))]),e("span",{staticClass:"info-fat-status m-l-10"},[a._v(a._s(a.formData.fat_text))])]),e("el-form-item",{attrs:{label:"基础代谢率：","label-width":"100px"}},[e("span",[a._v(a._s(a.formData.base_energy)+"kcal")])])],1)],1)])},i=[],l={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(a){this.formData=a}},mounted:function(){console.log(this.formInfoData,222)},methods:{}},o=l,r=(s("de34"),s("2877")),n=Object(r["a"])(o,e,i,!1,null,null,null);t["default"]=n.exports},de34:function(a,t,s){"use strict";var e=s("3008"),i=s.n(e);i.a}}]);
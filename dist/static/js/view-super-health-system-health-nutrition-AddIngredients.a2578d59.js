(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-AddIngredients","view-super-health-system-components-selectLaber","view-super-health-system-health-nutrition-CommodityLibrary","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(e,t,a){"use strict";a.r(t),a.d(t,"DEFAULT_NUTRITION",(function(){return i})),a.d(t,"ELEMENT_NUTRITION",(function(){return n})),a.d(t,"VITAMIN_NUTRITION",(function(){return l})),a.d(t,"NUTRITION_LIST",(function(){return s})),a.d(t,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return r})),a.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return o})),a.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return c})),a.d(t,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return u})),a.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return d}));var i=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"}],n=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],l=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],s=[].concat(i,n,l),r={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},o={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},u={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},d={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"1a24":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("div",{staticClass:"healthTagDialog"},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:e.searchHandle},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}}),a("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v(" 已选 "),a("span",[e._v(e._s(e.selectLabelIdList.length))]),e._v(" 个标签 ")])]),e._l(e.tableData,(function(t,i){return a("div",{key:i},[a("el-collapse",{model:{value:e.activeLaberList,callback:function(t){e.activeLaberList=t},expression:"activeLaberList"}},[a("el-collapse-item",{attrs:{name:t.id}},[a("template",{slot:"title"},[a("span",[e._v(" "+e._s(t.name)+" "),a("span",[e._v("（"+e._s(t.label_list.length)+"）")])]),a("span",{staticClass:"tips-r"},[a("span",{staticClass:"open"},[e._v("展开")]),a("span",{staticClass:"close"},[e._v("收起")])])]),a("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[t.inputVisible?a("el-input",{ref:"saveTagInput"+t.id,refInFor:!0,staticClass:"ps-input w-100  p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(a){return e.handleInputConfirm(t)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputConfirm(t)}},model:{value:t.inputValue,callback:function(a){e.$set(t,"inputValue",a)},expression:"item.inputValue"}}):a("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(a){return e.showInput(t)}}},[e._v(" 添加标签 ")]),a("div",{staticStyle:{flex:"1"}},[a("el-checkbox-group",{attrs:{size:"mini"},model:{value:e.selectLabelIdList,callback:function(t){e.selectLabelIdList=t},expression:"selectLabelIdList"}},e._l(t.label_list,(function(i,n){return a("el-checkbox-button",{key:n,attrs:{label:i.id,disabled:i.disabled},on:{change:function(a){return e.checkboxChangge(i,t)}}},[e._v(" "+e._s(i.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},n=[],l=a("a34a"),s=a.n(l),r=a("ed08");function o(e,t){return p(e)||m(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return d(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=new Array(t);a<t;a++)i[a]=e[a];return i}function m(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],i=!0,n=!1,l=void 0;try{for(var s,r=e[Symbol.iterator]();!(i=(s=r.next()).done);i=!0)if(a.push(s.value),t&&a.length===t)break}catch(o){n=!0,l=o}finally{try{i||null==r["return"]||r["return"]()}finally{if(n)throw l}}return a}}function p(e){if(Array.isArray(e))return e}function f(e,t,a,i,n,l,s){try{var r=e[l](s),o=r.value}catch(c){return void a(c)}r.done?t(o):Promise.resolve(o).then(i,n)}function b(e){return function(){var t=this,a=arguments;return new Promise((function(i,n){var l=e.apply(t,a);function s(e){f(l,i,n,s,r,"next",e)}function r(e){f(l,i,n,s,r,"throw",e)}s(void 0)}))}}var g={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(r["c"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var e=this;return b(s.a.mark((function t(){var a,i,n,l,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a={type:e.labelType,page:e.currentPage,page_size:e.pageSize},e.name&&(a.name=e.name),t.next=5,Object(r["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(a));case 5:if(i=t.sent,n=o(i,2),l=n[0],c=n[1],e.isLoading=!1,!l){t.next=13;break}return e.$message.error(l.message),t.abrupt("return");case 13:0===c.code?(e.totalCount=c.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=c.data.results.map((function(t){return t.inputVisible=!1,t.inputValue="",t.label_list.forEach((function(a){a.label_group_name=t.name,e.ruleSingleInfo.selectLabelAllIds&&e.ruleSingleInfo.selectLabelAllIds.length&&e.ruleSingleInfo.selectLabelAllIds.includes(a.id)&&!e.selectLabelIdList.includes(a.id)?a.disabled=!0:a.disabled=!1})),e.activeLaberList.push(t.id),t})),console.log(e.tableData)):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},handleChange:function(){},checkboxChangge:function(e,t){var a=this,i=this.selectLabelIdList.indexOf(e.id);-1!==i?this.selectLabelListData.push(e):this.selectLabelListData.map((function(t,i){e.id===t.id&&a.selectLabelListData.splice(i,1)}))},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(a){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return b(s.a.mark((function a(){var i,n,l,c;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(r["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(i=a.sent,n=o(i,2),l=n[0],c=n[1],t.isLoading=!1,!l){a.next=11;break}return t.$message.error(l.message),a.abrupt("return");case 11:0===c.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},clickConfirmHandle:function(){var e={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",e),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()}}},h=g,y=(a("fa5f"),a("2877")),v=Object(y["a"])(h,i,n,!1,null,null,null);t["default"]=v.exports},"5c85":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"super-add-ingredients container-wrapper"},[i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:e.formRuls,model:e.formData,size:"small"}},[i("div",{staticClass:"table-wrapper"},[i("div",{staticClass:"table-header"},[i("div",{staticClass:"table-title"},[e._v("基本信息")])]),i("div",{staticStyle:{padding:"0 20px"}},[i("el-form-item",{staticClass:"block-label form-content-flex",attrs:{label:"食材图片",prop:""}},[i("div",{},[i("div",{staticClass:"inline-block upload-w"},[i("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.uploading,expression:"uploading"}],ref:"fileUpload",staticClass:"file-upload",attrs:{"element-loading-text":"上传中",drag:"",action:e.serverUrl,data:e.uploadParams,"file-list":e.fileLists,"on-success":e.uploadSuccess,"before-upload":e.beforeFoodImgUpload,limit:1,multiple:!1,"show-file-list":!1,headers:e.headersOpts,accept:".jpeg,.jpg,.png,.bmp"}},[e._t("default",[e.formData.imageList.length?e._e():i("div",{staticClass:"upload-t"},[i("i",{staticClass:"el-icon-circle-plus"}),i("div",{staticClass:"el-upload__text"},[i("span",{},[e._v("上传食材图片")])])]),e.formData.imageList.length?i("el-image",{staticClass:"el-upload-dragger",attrs:{src:e.formData.imageList[0],fit:"contain"},on:{click:e.removeFoodImg}}):e._e()])],2)],1),i("div",{staticClass:"inline-block upload-tips"},[i("span",{staticStyle:{"padding-left":"2px"}},[e._v("上传：食材图片。")]),i("br"),e._v(" 建议图片需清晰，图片内容与名称相符。"),i("br"),e._v(" 仅支持jpg、png、bmp格式，大小不超过5M ")])])])],1),i("div",{},[i("div",{staticStyle:{width:"48%",padding:"0 20px"}},[i("el-form-item",{staticClass:"block-label form-content-flex",attrs:{label:"食材名称",prop:"name"}},[i("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{placeholder:"请输入食材名称",maxlength:"30"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}}),i("el-tooltip",{attrs:{effect:"dark",content:"增加食材别名",placement:"top"}},[i("img",{staticClass:"add-btn-img",attrs:{src:a("a851"),alt:""},on:{click:e.addAliasName}})])],1),e.formData.aliasName.length?i("div",[i("el-form-item",{staticClass:"block-label",attrs:{label:"食材别名"}},e._l(e.formData.aliasName,(function(t,n){return i("el-form-item",{key:n,class:[n>0?"m-t-15":"","alias-name-form"],attrs:{rules:e.formRuls.aliasName,prop:"aliasName["+n+"]"}},[i("el-input",{staticClass:"ps-input",staticStyle:{width:"80%"},attrs:{placeholder:"请输入食材别名"},model:{value:e.formData.aliasName[n],callback:function(t){e.$set(e.formData.aliasName,n,t)},expression:"formData.aliasName[index]"}}),i("img",{attrs:{src:a("a851"),alt:""},on:{click:e.addAliasName}}),i("img",{attrs:{src:a("1597"),alt:""},on:{click:function(t){return e.delAliasName(n)}}})],1)})),1)],1):e._e(),i("el-form-item",{staticClass:"block-label",attrs:{label:"食材类别",prop:"sort_id"}},[i("el-cascader",{staticClass:"ps-select",staticStyle:{width:"80%"},attrs:{placeholder:"请选择或输入食材类别",options:e.categoryList,"show-all-levels":!1,props:e.cascaderProps},model:{value:e.formData.sort_id,callback:function(t){e.$set(e.formData,"sort_id",t)},expression:"formData.sort_id"}}),i("el-button",{staticClass:"m-l-10",attrs:{type:"text"},on:{click:e.gotoCategory}},[e._v("添加分类")])],1),i("el-form-item",{attrs:{label:"标签",prop:""}},[i("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.labelClick}},[e._v(" 选择标签 ")])],1),e._l(e.formData.labelGroupInfoList,(function(t,a,n){return i("el-form-item",{key:n,attrs:{label:a+":",prop:""}},e._l(t,(function(t,n){return i("el-tag",{key:n,staticClass:"m-r-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff",closable:""},on:{close:function(i){return e.closeTag(a,n,t)}}},[e._v(" "+e._s(t.name)+" ")])})),1)}))],2)])]),i("div",{staticClass:"table-wrapper"},[i("div",{staticClass:"table-header"},[i("div",{staticClass:"table-title"},[e._v("营养信息 "),i("span",{staticClass:"tip-o-7"},[e._v("（每100克所含的营养信息）")]),i("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.formData.is_enable_nutrition,callback:function(t){e.$set(e.formData,"is_enable_nutrition",t)},expression:"formData.is_enable_nutrition"}})],1)]),e.formData.is_enable_nutrition?i("div",{staticClass:"table-content"},[e._l(e.currentNutritionList,(function(t){return[i("div",{key:t.key,staticClass:"nutrition-item"},[i("div",{staticClass:"nutrition-label"},[e._v(e._s(t.name+"："))]),i("el-form-item",{attrs:{prop:t.key,rules:e.formRuls.nutrition}},[i("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},model:{value:e.formData[t.key],callback:function(a){e.$set(e.formData,t.key,a)},expression:"formData[nutrition.key]"}}),i("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(t.unit))])],1)],1)]})),i("div",{staticClass:"text-center pointer"},[i("span",{staticStyle:{color:"#027DB4"},on:{click:function(t){e.showAll=!e.showAll}}},[e._v(e._s(e.showAll?"收起":"查看更多营养信息"))])])],2):e._e()]),i("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[i("el-button",{staticStyle:{width:"120px"},on:{click:e.closeHandler}},[e._v("取消")]),i("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:e.submitHandler}},[e._v(" "+e._s("add"===e.type?"添加":"编辑")+" ")])],1)]),e.selectLaberDialogVisible?i("select-laber",{attrs:{isshow:e.selectLaberDialogVisible,width:"600px",ruleSingleInfo:e.ruleSingleInfo},on:{"update:isshow":function(t){e.selectLaberDialogVisible=t},selectLaberData:e.selectLaberData}}):e._e()],1)},n=[],l=a("a34a"),s=a.n(l),r=a("ed08"),o=a("015b"),c=a("1a24");function u(e,t){return b(e)||f(e,t)||m(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=new Array(t);a<t;a++)i[a]=e[a];return i}function f(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],i=!0,n=!1,l=void 0;try{for(var s,r=e[Symbol.iterator]();!(i=(s=r.next()).done);i=!0)if(a.push(s.value),t&&a.length===t)break}catch(o){n=!0,l=o}finally{try{i||null==r["return"]||r["return"]()}finally{if(n)throw l}}return a}}function b(e){if(Array.isArray(e))return e}function g(e,t,a,i,n,l,s){try{var r=e[l](s),o=r.value}catch(c){return void a(c)}r.done?t(o):Promise.resolve(o).then(i,n)}function h(e){return function(){var t=this,a=arguments;return new Promise((function(i,n){var l=e.apply(t,a);function s(e){g(l,i,n,s,r,"next",e)}function r(e){g(l,i,n,s,r,"throw",e)}s(void 0)}))}}var y={name:"SuperAddIngredients",data:function(){var e=function(e,t,a){if(t){var i=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;i.test(t)?a():a(new Error("营养数据有误，仅支持两位小数"))}else a()};return{isLoading:!1,type:"add",formData:{id:"",name:"",aliasName:[],sort_id:"",ingredient_id:"",is_enable_nutrition:!1,selectLabelListData:[],selectLabelIdList:[],labelGroupInfoList:{},imageList:[]},formRuls:{name:[{required:!0,message:"食材名称不能为空",trigger:"blur"}],aliasName:[{required:!0,message:"请输入食材别名",trigger:"blur"}],category:[{required:!0,message:"请选择食材类别",trigger:"blur"}],nutrition:[{validator:e,trigger:"change"}],sort_id:[{required:!0,message:"请选择食材分类",trigger:"blur"}]},nutritionList:o["NUTRITION_LIST"],categoryList:[],cascaderProps:{label:"name",value:"id",children:"sort_list",emitPath:!1},selectLaberDialogVisible:!1,ruleSingleInfo:{},serverUrl:"/api/background/file/upload",headersOpts:{TOKEN:Object(r["x"])()},fileLists:[],uploadParams:{prefix:"super_food_img"},showAll:!1,showDialog:!1,uploading:!1}},computed:{currentNutritionList:function(){var e=[];return e=this.showAll?this.nutritionList:this.nutritionList.slice(0,4),e}},components:{selectLaber:c["default"]},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){var e=this;return h(s.a.mark((function t(){var a;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getCategoryCategoryNameList();case 2:"modify"===e.type?(a=e.$decodeQuery(e.$route.query.data),e.formData.name=a.name,e.formData.aliasName=a.alias_name,e.formData.sort_id=a.sort,a.image&&(e.formData.imageList=[a.image],e.fileLists=[{url:a.image,name:a.image,status:"success",uid:a.image}]),e.categoryList.map((function(t){t.sort_list.length&&t.sort_list.map((function(t){t.id&&Number(t.id.split("-")[1])===a.sort&&(e.formData.sort_id=t.id)}))})),a.label.length&&e.initLabelGroup(a.label),e.formData.selectLabelListData=a.label,e.formData.selectLabelIdList=a.label.map((function(e){return e.id})),e.formData.id=a.id,e.formData.is_enable_nutrition=!!a.is_enable_nutrition,e.setNutritonData(a)):e.setNutritonData({});case 3:case"end":return t.stop()}}),t)})))()},searchHandle:Object(r["c"])((function(){this.currentPage=1}),300),setNutritonData:function(e){var t=this;e.nutrition_info||(e.nutrition_info={});var a=e.nutrition_info.element?JSON.parse(Object(r["J"])(e.nutrition_info.element)):{},i=e.nutrition_info.vitamin?JSON.parse(Object(r["J"])(e.nutrition_info.vitamin)):{};o["NUTRITION_LIST"].forEach((function(n){"default"===n.type&&t.$set(t.formData,n.key,e.nutrition_info[n.key]?e.nutrition_info[n.key]:0),"element"===n.type&&t.$set(t.formData,n.key,a[n.key]?a[n.key]:0),"vitamin"===n.type&&t.$set(t.formData,n.key,i[n.key]?i[n.key]:0)}))},getCategoryCategoryNameList:function(){var e=this;return h(s.a.mark((function t(){var a,i,n,l;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(r["Q"])(e.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost());case 2:if(a=t.sent,i=u(a,2),n=i[0],l=i[1],!n){t.next=9;break}return e.$message.error(n.message),t.abrupt("return");case 9:0===l.code?e.categoryList=l.data.map((function(e){return e.sort_list.length&&(e.sort_list=e.sort_list.map((function(t){return t.id=e.id+"-"+t.id,t}))),e})):e.$message.error(l.msg);case 10:case"end":return t.stop()}}),t)})))()},formatParams:function(){var e=this,t={name:this.formData.name,label_list:this.formData.selectLabelIdList,alias_name:this.formData.aliasName,is_enable_nutrition:this.formData.is_enable_nutrition?1:0};if(this.formData.ingredient_id&&"add"===this.type&&(t.ingredient_id=this.formData.ingredient_id),this.formData.sort_id){var a=this.formData.sort_id.split("-");t.sort_id=a[1]}if("modify"===this.type&&(t.id=this.formData.id),this.formData.is_enable_nutrition){var i={},n={};o["NUTRITION_LIST"].forEach((function(a){var l=e.formData[a.key]?e.formData[a.key]:0;"default"===a.type&&(t[a.key]=l),"element"===a.type&&(i[a.key]=l),"vitamin"===a.type&&(n[a.key]=l)})),t.element=JSON.stringify(i),t.vitamin=JSON.stringify(n)}return this.formData.imageList.length&&(t.image=this.formData.imageList[0]),t},submitHandler:function(){var e=this;this.$refs.formIngredients.validate((function(t){if(t){if(e.isLoading)return e.$message.error("请勿重复提交！");"modify"===e.type?e.modifyIngredients():e.addIngredients()}}))},addIngredients:function(){var e=this;return h(s.a.mark((function t(){var a,i,n,l;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(r["Q"])(e.$apis.apiBackgroundAdminIngredientAddPost(e.formatParams()));case 3:if(a=t.sent,i=u(a,2),n=i[0],l=i[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===l.code?(e.$message.success(l.msg),e.$closeCurrentTab(e.$route.path)):2===l.code?(e.formData.ingredient_id=l.data.ingredient_id,e.$confirm(l.msg,"提示",{confirmButtonText:"覆 盖",cancelButtonText:e.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var t=h(s.a.mark((function t(a,i,n){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==a){t.next=7;break}return i.confirmButtonLoading=!0,t.next=4,e.addIngredients();case 4:i.confirmButtonLoading=!1,t.next=8;break;case 7:i.confirmButtonLoading||n();case 8:case"end":return t.stop()}}),t)})));function a(e,a,i){return t.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(t){e.formData.ingredient_id=""}))):e.$message.error(l.msg);case 12:case"end":return t.stop()}}),t)})))()},labelClick:function(){this.ruleSingleInfo={labelType:"ingredient",selectLabelIdList:this.formData.selectLabelIdList,selectLabelListData:this.formData.selectLabelListData},this.selectLaberDialogVisible=!0},closeTag:function(e,t,a){var i=this.formData.selectLabelIdList.indexOf(a.id),n=this.formData.selectLabelListData.indexOf(a);this.formData.selectLabelIdList.splice(i,1),this.formData.selectLabelListData.splice(n,1),this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},selectLaberData:function(e){this.formData.selectLabelIdList=e.selectLabelIdList,this.formData.selectLabelListData=e.selectLabelListData,this.formData.labelGroupInfoList={},this.initLabelGroup(this.formData.selectLabelListData)},initLabelGroup:function(e){var t=this;e.forEach((function(e){t.formData.labelGroupInfoList[e.label_group_name]||(t.formData.labelGroupInfoList[e.label_group_name]=[]),t.formData.labelGroupInfoList[e.label_group_name]&&!t.formData.labelGroupInfoList[e.label_group_name].includes(e)&&t.formData.labelGroupInfoList[e.label_group_name].push(e)}))},modifyIngredients:function(){var e=this;return h(s.a.mark((function t(){var a,i,n,l;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(r["Q"])(e.$apis.apiBackgroundAdminIngredientModifyPost(e.formatParams()));case 3:if(a=t.sent,i=u(a,2),n=i[0],l=i[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===l.code?(e.$message.success(l.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(l.msg);case 12:case"end":return t.stop()}}),t)})))()},closeHandler:function(){var e=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(t,a,i){"confirm"===t?(a.confirmButtonLoading=!0,e.$closeCurrentTab(e.$route.path),a.confirmButtonLoading=!1):a.confirmButtonLoading||i()}}).then((function(e){})).catch((function(e){}))},gotoCategory:function(){this.$router.push({name:"SuperImportIngredientsCategory"})},addAliasName:function(){this.formData.aliasName.push("")},delAliasName:function(e){this.formData.aliasName.splice(e,1)},removeFoodImg:function(e){this.formData.imageList.splice(e,1),this.fileLists.splice(e,1)},uploadSuccess:function(e,t,a){this.uploading=!1,0===e.code?(this.fileLists=a,this.formData.imageList=[e.data.public_url],console.log(this.formData.imageList)):this.$message.error(e.msg)},beforeFoodImgUpload:function(e){var t=[".jpeg",".jpg",".png",".bmp"],a=e.size/1024/1024<5;return t.includes(Object(r["w"])(e.name))?a?void(this.uploading=!0):(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是 JPG 格式或者PNG格式!"),!1)},clickConfirmHandle:function(e){var t=this;return h(s.a.mark((function e(){return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.clickCancleHandle(),e.next=3,t.$sleep(100);case 3:t.$router.push({name:"MerchantCopyFoods"});case 4:case"end":return e.stop()}}),e)})))()},clickCancleHandle:function(e){this.$closeCurrentTab(this.$route.path),this.showDialog=!1}}},v=y,L=(a("8efa"),a("2877")),_=Object(L["a"])(v,i,n,!1,null,null,null);t["default"]=_.exports},"784d":function(e,t,a){},"7e85":function(e,t,a){},"8efa":function(e,t,a){"use strict";var i=a("784d"),n=a.n(i);n.a},fa5f:function(e,t,a){"use strict";var i=a("7e85"),n=a.n(i);n.a}}]);
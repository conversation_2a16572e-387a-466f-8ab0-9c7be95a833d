(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-ImportCommodity"],{"7d26":function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",[o("refresh-tool",{attrs:{title:t.title,"show-refresh":!1}}),o("import-page",{staticClass:"importPage",attrs:{initial:t.initial,url:t.url,"header-len":t.headerLen,"template-url":t.templateUrl}})],1)},r=[],a={name:"ImportIngredients",data:function(){return{type:"import",title:"批量导入菜品/商品",headerLen:2,initial:!0,url:"apiBackgroundAdminFoodFoodBatAddPost",templateUrl:"/api/temporary/template_excel/food_stock/super_foods.xlsx"}},computed:{},watch:{},created:function(){this.$route.params.type&&(this.type=this.$route.params.type),"import"===this.type?(this.title="批量导入菜品/商品",this.url="apiBackgroundAdminFoodFoodBatAddPost"):(this.title="导入编辑",this.url="apiBackgroundAdminFoodFoodBatModifyPost")},mounted:function(){},methods:{}},n=a,s=(o("b176"),o("2877")),l=Object(s["a"])(n,i,r,!1,null,null,null);e["default"]=l.exports},b176:function(t,e,o){"use strict";var i=o("bb1f"),r=o.n(i);r.a},bb1f:function(t,e,o){}}]);
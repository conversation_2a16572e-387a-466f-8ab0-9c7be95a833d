(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-article-push-article-admin-addEditArticle"],{"34e3":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"super-add-article container-wrapper"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small","label-width":"80px"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{padding:"0 20px"}},[a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入标题",maxlength:"40","show-word-limit":""},model:{value:t.formData.title,callback:function(e){t.$set(t.formData,"title",e)},expression:"formData.title"}})],1),a("el-form-item",{attrs:{label:"来源",prop:"source"}},[a("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.source,callback:function(e){t.$set(t.formData,"source",e)},expression:"formData.source"}},[a("el-option",{attrs:{label:"原创",value:1}}),a("el-option",{attrs:{label:"转载",value:2}})],1)],1),a("el-form-item",{attrs:{label:"是否推荐",prop:"is_recommend"}},[a("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:t.formData.is_recommend,callback:function(e){t.$set(t.formData,"is_recommend",e)},expression:"formData.is_recommend"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),a("el-form-item",{attrs:{label:"作者",prop:"author"}},[a("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入标题",maxlength:"10","show-word-limit":""},model:{value:t.formData.author,callback:function(e){t.$set(t.formData,"author",e)},expression:"formData.author"}})],1),a("el-form-item",{attrs:{label:"标签",prop:"tags"}},[a("el-select",{staticClass:"ps-select",attrs:{placeholder:"请下拉选择",multiple:"","popper-class":"ps-popper-select"},model:{value:t.formData.tags,callback:function(e){t.$set(t.formData,"tags",e)},expression:"formData.tags"}},t._l(t.tagsList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.id,disabled:"disable"==t.status}})})),1)],1),a("el-form-item",{attrs:{label:"封面",prop:"image"}},[a("el-upload",{ref:"uploadFood",staticClass:"avatar-uploader",attrs:{data:t.uploadParams,action:t.actionUrl,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[t.formData.image?a("img",{staticClass:"avatar",attrs:{src:t.formData.image}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),a("el-form-item",{attrs:{label:"正文",prop:"title"}},[a("TinymceUeditor",{attrs:{content:t.formData.content},on:{message:t.messageTinymceUeditor},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}})],1)],1)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"编辑")+" ")])],1)])],1)},s=[],o=a("a34a"),i=a.n(o),n=a("ed08"),l=a("56f9");function c(t,e){return f(t)||m(t,e)||d(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"===typeof t)return p(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function m(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],r=!0,s=!1,o=void 0;try{for(var i,n=t[Symbol.iterator]();!(r=(i=n.next()).done);r=!0)if(a.push(i.value),e&&a.length===e)break}catch(l){s=!0,o=l}finally{try{r||null==n["return"]||n["return"]()}finally{if(s)throw o}}return a}}function f(t){if(Array.isArray(t))return t}function g(t,e,a,r,s,o,i){try{var n=t[o](i),l=n.value}catch(c){return void a(c)}n.done?e(l):Promise.resolve(l).then(r,s)}function h(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var o=t.apply(e,a);function i(t){g(o,r,s,i,n,"next",t)}function n(t){g(o,r,s,i,n,"throw",t)}i(void 0)}))}}var b={name:"SuperAddEditArticle",components:{TinymceUeditor:l["a"]},data:function(){return{isLoading:!1,type:"add",formData:{title:"",tags:[],source:"",is_recommend:null,author:"",image:"",content:""},formRuls:{title:[{required:!0,message:"请输入文章标题",trigger:"blur"}],source:[{required:!0,message:"请选择来源",trigger:"blur"}],is_recommend:[{required:!0,message:"请选择是否推荐",trigger:"blur"}]},tagsList:[],actionUrl:"",uploadParams:{}}},created:function(){this.getUploadToken(),this.getArticleChildTagList(),this.type=this.$route.query.type},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,title:t.title,tags:t.tags.map((function(t){return t.id})),source:t.source,is_recommend:t.is_recommend,author:t.author,image:t.image,content:t.content?t.content:""},console.log(t)}},searchHandle:Object(n["c"])((function(){this.currentPage=1}),300),getUploadToken:function(){var t=this;return h(i.a.mark((function e(){var a;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$apis.getUploadToken({prefix:"jpeg/png"});case 2:a=e.sent,0===a.code?(t.actionUrl=a.data.host,t.uploadParams={key:a.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:a.data.prefix,policy:a.data.policy,OSSAccessKeyId:a.data.accessid,signature:a.data.signature,callback:a.data.callback,success_action_status:"200"}):t.$message.error(a.msg);case 4:case"end":return e.stop()}}),e)})))()},getArticleChildTagList:function(){var t=this;return h(i.a.mark((function e(){var a,r,s,o;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(n["Q"])(t.$apis.apiBackgroundArticleTagChildListPost({page:1,page_size:1e4}));case 2:if(a=e.sent,r=c(a,2),s=r[0],o=r[1],!s){e.next=9;break}return t.$message.error(s.message),e.abrupt("return");case 9:0===o.code?(t.tagsList=o.data.results,t.initLoad()):t.$message.error(o.msg);case 10:case"end":return e.stop()}}),e)})))()},handleAvatarSuccess:function(t,e){0===t.code?(this.$refs.uploadFood.clearFiles(),this.formData.image=t.data.public_url,this.getUploadToken()):this.$message.error(t.msg)},beforeAvatarUpload:function(t){var e="image/jpeg"===t.type||"image/png"===t.type,a=t.size/1024/1024<2;return e||this.$message.error("上传头像图片只能是 JPG 格式!"),a||this.$message.error("上传头像图片大小不能超过 2MB!"),e&&a},messageTinymceUeditor:function(t){this.formData.content=t},addModifyArticle:function(t){var e=this;return h(i.a.mark((function a(){var r,s,o,l,u,d,p,m;return i.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.isLoading=!0,r="",s=c(r,2),o=s[0],l=s[1],"add"!==e.type){a.next=12;break}return a.next=6,Object(n["Q"])(e.$apis.apiBackgroundArticleAddPost(t));case 6:u=a.sent,d=c(u,2),o=d[0],l=d[1],a.next=19;break;case 12:return a.next=15,Object(n["Q"])(e.$apis.apiBackgroundArticleModifyPost(t));case 15:p=a.sent,m=c(p,2),o=m[0],l=m[1];case 19:if(e.isLoading=!1,!o){a.next=23;break}return e.$message.error(o.message),a.abrupt("return");case 23:0===l.code?(e.$message.success(l.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(l.msg);case 24:case"end":return a.stop()}}),a)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifyArticle(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,r){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||r()}}).then((function(t){})).catch((function(t){}))}}},v=b,y=(a("7b8d"),a("2877")),w=Object(y["a"])(v,r,s,!1,null,null,null);e["default"]=w.exports},"7b8d":function(t,e,a){"use strict";var r=a("af13"),s=a.n(r);s.a},af13:function(t,e,a){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-IngredientsLibrary","view-super-health-system-components-selectLaber","view-super-health-system-health-nutrition-CommodityLibrary","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(e,t,a){"use strict";a.r(t),a.d(t,"DEFAULT_NUTRITION",(function(){return n})),a.d(t,"ELEMENT_NUTRITION",(function(){return i})),a.d(t,"VITAMIN_NUTRITION",(function(){return r})),a.d(t,"NUTRITION_LIST",(function(){return l})),a.d(t,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return s})),a.d(t,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return o})),a.d(t,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return c})),a.d(t,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return u})),a.d(t,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return d}));var n=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"}],i=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],r=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],l=[].concat(n,i,r),s={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},o={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},c={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},u={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},d={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"1a24":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("div",{staticClass:"healthTagDialog"},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:e.searchHandle},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}}),a("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v(" 已选 "),a("span",[e._v(e._s(e.selectLabelIdList.length))]),e._v(" 个标签 ")])]),e._l(e.tableData,(function(t,n){return a("div",{key:n},[a("el-collapse",{model:{value:e.activeLaberList,callback:function(t){e.activeLaberList=t},expression:"activeLaberList"}},[a("el-collapse-item",{attrs:{name:t.id}},[a("template",{slot:"title"},[a("span",[e._v(" "+e._s(t.name)+" "),a("span",[e._v("（"+e._s(t.label_list.length)+"）")])]),a("span",{staticClass:"tips-r"},[a("span",{staticClass:"open"},[e._v("展开")]),a("span",{staticClass:"close"},[e._v("收起")])])]),a("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[t.inputVisible?a("el-input",{ref:"saveTagInput"+t.id,refInFor:!0,staticClass:"ps-input w-100  p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(a){return e.handleInputConfirm(t)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputConfirm(t)}},model:{value:t.inputValue,callback:function(a){e.$set(t,"inputValue",a)},expression:"item.inputValue"}}):a("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(a){return e.showInput(t)}}},[e._v(" 添加标签 ")]),a("div",{staticStyle:{flex:"1"}},[a("el-checkbox-group",{attrs:{size:"mini"},model:{value:e.selectLabelIdList,callback:function(t){e.selectLabelIdList=t},expression:"selectLabelIdList"}},e._l(t.label_list,(function(n,i){return a("el-checkbox-button",{key:i,attrs:{label:n.id,disabled:n.disabled},on:{change:function(a){return e.checkboxChangge(n,t)}}},[e._v(" "+e._s(n.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},i=[],r=a("a34a"),l=a.n(r),s=a("ed08");function o(e,t){return m(e)||p(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return d(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function p(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],n=!0,i=!1,r=void 0;try{for(var l,s=e[Symbol.iterator]();!(n=(l=s.next()).done);n=!0)if(a.push(l.value),t&&a.length===t)break}catch(o){i=!0,r=o}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw r}}return a}}function m(e){if(Array.isArray(e))return e}function g(e,t,a,n,i,r,l){try{var s=e[r](l),o=s.value}catch(c){return void a(c)}s.done?t(o):Promise.resolve(o).then(n,i)}function b(e){return function(){var t=this,a=arguments;return new Promise((function(n,i){var r=e.apply(t,a);function l(e){g(r,n,i,l,s,"next",e)}function s(e){g(r,n,i,l,s,"throw",e)}l(void 0)}))}}var f={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(s["c"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var e=this;return b(l.a.mark((function t(){var a,n,i,r,c;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a={type:e.labelType,page:e.currentPage,page_size:e.pageSize},e.name&&(a.name=e.name),t.next=5,Object(s["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(a));case 5:if(n=t.sent,i=o(n,2),r=i[0],c=i[1],e.isLoading=!1,!r){t.next=13;break}return e.$message.error(r.message),t.abrupt("return");case 13:0===c.code?(e.totalCount=c.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=c.data.results.map((function(t){return t.inputVisible=!1,t.inputValue="",t.label_list.forEach((function(a){a.label_group_name=t.name,e.ruleSingleInfo.selectLabelAllIds&&e.ruleSingleInfo.selectLabelAllIds.length&&e.ruleSingleInfo.selectLabelAllIds.includes(a.id)&&!e.selectLabelIdList.includes(a.id)?a.disabled=!0:a.disabled=!1})),e.activeLaberList.push(t.id),t})),console.log(e.tableData)):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},handleChange:function(){},checkboxChangge:function(e,t){var a=this,n=this.selectLabelIdList.indexOf(e.id);-1!==n?this.selectLabelListData.push(e):this.selectLabelListData.map((function(t,n){e.id===t.id&&a.selectLabelListData.splice(n,1)}))},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(a){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return b(l.a.mark((function a(){var n,i,r,c;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(n=a.sent,i=o(n,2),r=i[0],c=i[1],t.isLoading=!1,!r){a.next=11;break}return t.$message.error(r.message),a.abrupt("return");case 11:0===c.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},clickConfirmHandle:function(){var e={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",e),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()}}},h=f,y=(a("fa5f"),a("2877")),v=Object(y["a"])(h,n,i,!1,null,null,null);t["default"]=v.exports},"2fbd":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"super-ingredients-library container-wrapper"},[n("refresh-tool",{on:{refreshPage:e.refreshHandle}}),n("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px"},on:{search:e.searchHandle}},[n("template",{slot:"perv"},[n("div",{staticClass:"tab"},[n("div",{class:["tab-item","system"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("system")}}},[e._v(" 系统食材 ")]),n("div",{class:["tab-item","merchant"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("merchant")}}},[e._v(" 商户食材 ")])])])],2),n("div",{staticClass:"table-wrapper"},[n("div",{staticClass:"table-header"},[n("div",{staticClass:"table-title"},[e._v("数据列表")]),n("div",{staticClass:"align-r"},["system"===e.tabType?n("button-icon",{attrs:{color:"plain",type:"del"},on:{click:function(t){return e.batchLabelClick("batchLabelDel")}}},[e._v(" 批量移除标签 ")]):e._e(),"system"===e.tabType?n("button-icon",{attrs:{color:"plain",type:"mul"},on:{click:function(t){return e.batchLabelClick("batchLabelAdd")}}},[e._v(" 批量打标签 ")]):e._e(),"system"===e.tabType?n("button-icon",{attrs:{color:"origin",type:"add"},on:{click:e.addIngredients}},[e._v(" 添加食材 ")]):e._e(),"system"===e.tabType?n("button-icon",{attrs:{color:"plain",type:"Import"},on:{click:function(t){return e.importHandler("modify_import")}}},[e._v(" 导入编辑 ")]):e._e(),"system"===e.tabType?n("button-icon",{attrs:{color:"plain",type:"Import"},on:{click:function(t){return e.importHandler("import")}}},[e._v(" 批量导入食材 ")]):e._e(),"system"===e.tabType?n("button-icon",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.ingredient.ingredient_image_bat_add"],expression:"['background.admin.ingredient.ingredient_image_bat_add']"}],attrs:{color:"plain",type:"Import"},on:{click:function(t){return e.importHandler("importImage")}}},[e._v(" 导入食材图片 ")]):e._e(),"system"===e.tabType?n("button-icon",{attrs:{color:"plain",type:"menu"},on:{click:e.gotoCategory}},[e._v(" 分类管理 ")]):e._e(),"merchant"===e.tabType?n("span",{staticStyle:{"font-size":"12px"}},[e._v(" 是否允许商户上传信息 "),n("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},on:{change:e.updateSettingHandler},model:{value:e.updateSetting,callback:function(t){e.updateSetting=t},expression:"updateSetting"}})],1):e._e(),n("button-icon",{attrs:{color:"origin",type:"export"},on:{click:e.gotoExport}},[e._v("导出EXCEL")])],1)]),n("div",{key:e.tabType,staticClass:"table-content"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","row-key":"id","header-row-class-name":"ps-table-header-row"},on:{"selection-change":e.handleSelectionChange}},["system"===e.tabType?n("el-table-column",{attrs:{type:"selection",width:"50",align:"center","class-name":"ps-checkbox","reserve-selection":!0}}):e._e(),n("el-table-column",{attrs:{type:"index",width:"80",label:"图片",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[n("el-image",{staticClass:"column-image",attrs:{lazy:!0,src:e.row.image?e.row.image:a("cb7d"),"preview-src-list":[e.row.image?e.row.image:a("cb7d")]}},[n("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[n("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),n("el-table-column",{attrs:{prop:"id",label:"食材ID",align:"center"}}),n("el-table-column",{attrs:{prop:"name",label:"食材名称",align:"center"}}),n("el-table-column",{attrs:{prop:"all_alias_name",label:"食材别名",align:"center",width:"120","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"category_name",label:"一级分类",align:"center"}}),n("el-table-column",{attrs:{prop:"sort_name",label:"二级分类",align:"center"}}),"merchant"===e.tabType?n("el-table-column",{attrs:{prop:"create_source_name",label:"来源",align:"center"}}):e._e(),n("el-table-column",{attrs:{prop:"nutrition",label:"营养信息",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.is_enable_nutrition?n("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.showDialogHandler("nutrition",t.row)}}},[e._v(" 查看 ")]):n("span",[e._v("--")])]}}])}),"system"===e.tabType?n("el-table-column",{attrs:{prop:"xx",label:"标签",align:"center",width:"220px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticClass:"collapse-wrapper"},[n("div",{staticClass:"collapse-list hide"},[e._l(t.row.label,(function(a,i){return n("el-tag",{key:i,staticClass:"m-r-5 m-t-5 collapse-data",attrs:{size:"medium",effect:"plain",type:"light",closable:""},on:{close:function(n){return e.closeTag(a,t.row)}}},[e._v(" "+e._s(a.name)+" ")])})),t.row.label&&t.row.label.length>3?[n("span",{staticClass:"collapse-more",on:{click:e.showMoreHandler}},[e._v(" 查看更多 "),n("i",{staticClass:"el-icon-arrow-down"})]),n("span",{staticClass:"collapse-hide",on:{click:e.hideMoreHandler}},[e._v(" 收起 "),n("i",{staticClass:"el-icon-arrow-up"})])]:e._e()],2)])]}}],null,!1,2657971599)}):e._e(),"merchant"===e.tabType?n("el-table-column",{attrs:{prop:"is_repeat",label:"已有食材",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.is_repeat?"是":"否")+" ")]}}],null,!1,3065278787)}):e._e(),n("el-table-column",{attrs:{prop:"operator_name",label:"操作人",align:"center"}}),n("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),"system"===e.tabType?n("el-table-column",{attrs:{prop:"update_time",label:"修改时间",align:"center"}}):e._e(),n("el-table-column",{attrs:{label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["merchant"===e.tabType?[n("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.addToSystem(t.row.id)}}},[e._v(" 创建到食材库 ")])]:[n("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.modifyIngredients(t.row)}}},[e._v(" 编辑 ")]),n("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[e._v("|")]),n("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHandler("single",t.row.id)}}},[e._v(" 删除 ")])]]}}])})],1)],1),n("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[n("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),n("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"700px",top:"20vh","custom-class":"ps-dialog","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t},closed:e.dialogHandleClose}},[n("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],attrs:{model:e.formData,size:"small"}},[n("div",{staticClass:"table-content"},[e._l(e.nutritionList,(function(t){return[n("div",{key:t.key,staticClass:"nutrition-item"},[n("div",{staticClass:"nutrition-label"},[e._v(e._s(t.name+"："))]),n("el-form-item",{attrs:{prop:t.key}},[n("el-input",{staticClass:"ps-input",staticStyle:{width:"120px"},attrs:{readonly:""},model:{value:e.formData[t.key],callback:function(a){e.$set(e.formData,t.key,a)},expression:"formData[nutrition.key]"}}),n("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(t.unit))])],1)],1)]}))],2)])],1),e.selectLaberDialogVisible?n("select-laber",{attrs:{isshow:e.selectLaberDialogVisible,title:e.titleSelectLaber,width:"600px",ruleSingleInfo:e.ruleSingleInfo},on:{"update:isshow":function(t){e.selectLaberDialogVisible=t},selectLaberData:e.selectLaberData}}):e._e()],1)},i=[],r=a("a34a"),l=a.n(r),s=a("f63a"),o=a("ed08"),c=a("015b"),u=a("1a24");function d(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function p(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?d(Object(a),!0).forEach((function(t){m(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function m(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function g(e,t){return v(e)||y(e,t)||f(e,t)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"===typeof e)return h(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function y(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],n=!0,i=!1,r=void 0;try{for(var l,s=e[Symbol.iterator]();!(n=(l=s.next()).done);n=!0)if(a.push(l.value),t&&a.length===t)break}catch(o){i=!0,r=o}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw r}}return a}}function v(e){if(Array.isArray(e))return e}function L(e,t,a,n,i,r,l){try{var s=e[r](l),o=s.value}catch(c){return void a(c)}s.done?t(o):Promise.resolve(o).then(n,i)}function _(e){return function(){var t=this,a=arguments;return new Promise((function(n,i){var r=e.apply(t,a);function l(e){L(r,n,i,l,s,"next",e)}function s(e){L(r,n,i,l,s,"throw",e)}l(void 0)}))}}var k={name:"SuperIngredientsLibrary",mixins:[s["a"]],data:function(){return{tabType:"system",isLoading:!1,pageSize:10,totalCount:0,currentPage:1,updateSetting:!1,tableData:[],searchFormSetting:c["LIBRARY_SEARCH_SETTING_SUPER"],dialogData:{},dialogTitle:"营养信息",dialogType:"",dialogVisible:!1,dialogLoading:!1,nutritionList:c["NUTRITION_LIST"],formData:{},checkList:[],selectLaberDialogVisible:!1,titleSelectLaber:"",batchLabelType:"",ruleSingleInfo:{}}},components:{selectLaber:u["default"]},created:function(){this.initLoad(),this.getCategoryCategoryNameList()},mounted:function(){},methods:{initLoad:function(){this.getListHandler(),this.getAllLabelGroupList()},searchHandle:Object(o["c"])((function(){this.currentPage=1,this.getListHandler()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},tabClick:function(e){this.tabType=e,this.currentPage=1,this.searchFormSetting="system"===e?c["LIBRARY_SEARCH_SETTING_SUPER"]:c["LIBRARY_SEARCH_SETTING_MERCHANT"],this.searchHandle()},formatQueryParams:function(e){var t={},a=function(a){(e[a].value||e[a].value&&e[a].value.length)&&("select_time"!==a?"sort_id"===a?e[a].dataList.map((function(n){"1"===e[a].value.split("_")[0]?t.category_id=Number(e[a].value.split("_")[1]):"2"===e[a].value.split("_")[0]&&(t.sort_id=Number(e[a].value.split("_")[1]))})):t[a]=e[a].value:e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]))};for(var n in e)a(n);return t},getIngredientslist:function(){var e=this;return _(l.a.mark((function t(){var a,n,i,r;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminIngredientListPost(p(p({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,n=g(a,2),i=n[0],r=n[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===r.code?(e.totalCount=r.data.count,e.tableData=r.data.results.map((function(e){return null!==e.alias_name?e.all_alias_name=e.alias_name.join(","):e.alias_name=[],e}))):e.$message.error(r.msg);case 12:case"end":return t.stop()}}),t)})))()},getIngredientsManchantlist:function(){var e=this;return _(l.a.mark((function t(){var a,n,i,r;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminIngredientMerchantIngredientPost(p(p({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,n=g(a,2),i=n[0],r=n[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===r.code?(e.totalCount=r.data.count,e.tableData=r.data.results.map((function(e){return null!==e.alias_name?e.all_alias_name=e.alias_name.join(","):e.alias_name=[],e})),e.updateSetting=r.data.ingredient_upload):e.$message.error(r.msg);case 12:case"end":return t.stop()}}),t)})))()},getCategoryCategoryNameList:function(){var e=this;return _(l.a.mark((function t(){var a,n,i,r;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost());case 3:if(a=t.sent,n=g(a,2),i=n[0],r=n[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===r.code?(c["LIBRARY_SEARCH_SETTING_MERCHANT"].sort_id.dataList=e.deleteEmptyGroup(r.data),c["LIBRARY_SEARCH_SETTING_SUPER"].sort_id.dataList=e.deleteEmptyGroup(r.data)):e.$message.error(r.msg);case 12:case"end":return t.stop()}}),t)})))()},deleteEmptyGroup:function(e){var t=this;function a(e){e.map((function(e){e.sort_list&&e.sort_list.length>0?a(e.sort_list):t.$delete(e,"sort_list")}))}return a(e),e},showDialogHandler:function(e,t){"nutrition"===e&&this.setDialogNutriton(t),this.dialogVisible=!0},setDialogNutriton:function(e){var t=this;this.formData={},e.nutrition_info||(e.nutrition_info={});var a=e.nutrition_info.element?JSON.parse(Object(o["J"])(e.nutrition_info.element)):{},n=e.nutrition_info.vitamin?JSON.parse(Object(o["J"])(e.nutrition_info.vitamin)):{};c["NUTRITION_LIST"].forEach((function(i){"default"===i.type&&t.$set(t.formData,i.key,e.nutrition_info[i.key]?e.nutrition_info[i.key]:0),"element"===i.type&&t.$set(t.formData,i.key,a[i.key]?a[i.key]:0),"vitamin"===i.type&&t.$set(t.formData,i.key,n[i.key]?n[i.key]:0)}))},distributeHandler:function(){var e=this;this.$confirm("是否更新食材到商户端食材库？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var t=_(l.a.mark((function t(a,n,i){var r,s,c,u;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==a){t.next=19;break}if(!e.isLoading){t.next=3;break}return t.abrupt("return",e.$message.error("请勿重复提交！"));case 3:return e.isLoading=!0,n.confirmButtonLoading=!0,t.next=7,Object(o["Q"])(e.$apis.apiBackgroundAdminIngredientSyncIngredientPost());case 7:if(r=t.sent,s=g(r,2),c=s[0],u=s[1],e.isLoading=!1,!c){t.next=15;break}return e.$message.error(c.message),t.abrupt("return");case 15:0===u.code?(i(),e.$message.success(u.msg),e.getListHandler()):e.$message.error(u.msg),n.confirmButtonLoading=!1,t.next=20;break;case 19:n.confirmButtonLoading||i();case 20:case"end":return t.stop()}}),t)})));function a(e,a,n){return t.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},deleteHandler:function(e,t){var a=this,n=[];if("single"===e)n=[t];else{if(!this.checkList.length)return this.$message.error("请先选择要删除的数据！");n=this.checkList}this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=_(l.a.mark((function e(t,i,r){var s,c,u,d;return l.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==t){e.next=19;break}if(!a.dialogLoading){e.next=3;break}return e.abrupt("return",a.$message.error("请勿重复提交！"));case 3:return a.dialogLoading=!0,i.confirmButtonLoading=!0,e.next=7,Object(o["Q"])(a.$apis.apiBackgroundAdminIngredientDeletePost({ids:n}));case 7:if(s=e.sent,c=g(s,2),u=c[0],d=c[1],a.dialogLoading=!1,!u){e.next=15;break}return a.$message.error(u.message),e.abrupt("return");case 15:0===d.code?(r(),a.$message.success(d.msg),a.getListHandler(),a.checkList=[]):a.$message.error(d.msg),i.confirmButtonLoading=!1,e.next=20;break;case 19:i.confirmButtonLoading||r();case 20:case"end":return e.stop()}}),e)})));function t(t,a,n){return e.apply(this,arguments)}return t}()}).then((function(e){})).catch((function(e){}))},updateSettingHandler:function(e){var t=this,a="是否允许商户上传信息?";this.updateSetting||(a="是否关闭用户上传信息?"),this.$confirm(a,{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var e=_(l.a.mark((function e(a,n,i){var r,s,c,u;return l.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==a){e.next=19;break}if(!t.isLoading){e.next=3;break}return e.abrupt("return",t.$message.error("请勿重复提交！"));case 3:return t.isLoading=!0,n.confirmButtonLoading=!0,e.next=7,Object(o["Q"])(t.$apis.apiBackgroundAdminIngredientChangeUploadPost({is_enable:t.updateSetting?1:0}));case 7:if(r=e.sent,s=g(r,2),c=s[0],u=s[1],t.isLoading=!1,!c){e.next=15;break}return t.$message.error(c.message),e.abrupt("return");case 15:0===u.code?(i(),t.$message.success(u.msg),t.getListHandler()):t.$message.error(u.msg),n.confirmButtonLoading=!1,e.next=20;break;case 19:n.confirmButtonLoading||(i(),t.updateSetting=!t.updateSetting);case 20:case"end":return e.stop()}}),e)})));function a(t,a,n){return e.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},addToSystem:function(e){var t=this;this.$confirm('是否创建到食材库?<p style="color:red;">注：食材重名自动覆盖！<p>',{dangerouslyUseHTMLString:!0,confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var a=_(l.a.mark((function a(n,i,r){var s,c,u,d;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==n){a.next=19;break}if(!t.isLoading){a.next=3;break}return a.abrupt("return",t.$message.error("请勿重复提交！"));case 3:return t.isLoading=!0,i.confirmButtonLoading=!0,a.next=7,Object(o["Q"])(t.$apis.apiBackgroundAdminIngredientAddToSystemPost({id:e}));case 7:if(s=a.sent,c=g(s,2),u=c[0],d=c[1],t.isLoading=!1,!u){a.next=15;break}return t.$message.error(u.message),a.abrupt("return");case 15:0===d.code?(r(),t.$message.success(d.msg),t.getListHandler()):t.$message.error(d.msg),i.confirmButtonLoading=!1,a.next=20;break;case 19:i.confirmButtonLoading||r();case 20:case"end":return a.stop()}}),a)})));function n(e,t,n){return a.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},addIngredients:function(){this.$router.push({name:"SuperAddIngredients",query:{type:"add"},params:{type:"add"}})},modifyIngredients:function(e){this.$router.push({name:"SuperAddIngredients",query:{type:"modify",data:this.$encodeQuery(e)},params:{type:"modify"}})},handleSelectionChange:function(e){var t=this;this.checkList=[],e.map((function(e){t.checkList.push(e.id)}))},showMoreHandler:function(e){e.target.parentNode.classList.remove("hide")},hideMoreHandler:function(e){e.target.parentNode.classList.add("hide")},batchLabelClick:function(e){if(this.batchLabelType=e,"batchLabelDel"===e?this.titleSelectLaber="批量移除标签":"batchLabelAdd"===e&&(this.titleSelectLaber="批量打标签"),!this.checkList.length)return this.$message.error("请先选择要".concat(this.titleSelectLaber,"的数据！"));this.ruleSingleInfo={labelType:"ingredient"},this.selectLaberDialogVisible=!0},getAllLabelGroupList:function(){var e=this;return _(l.a.mark((function t(){var a,n,i,r;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(o["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupAllLabelGroupListPost({page_size:999999,page:1,type:"ingredient"}));case 3:if(a=t.sent,n=g(a,2),i=n[0],r=n[1],e.isLoading=!1,!i){t.next=11;break}return e.$message.error(i.message),t.abrupt("return");case 11:0===r.code?(r.data.results.map((function(e){return e.label_list.length||(e.isDisabled=!0),e})),e.searchFormSetting.label_list.dataList=r.data.results):e.$message({type:"error",duration:1e3,message:r.msg});case 12:case"end":return t.stop()}}),t)})))()},selectLaberData:function(e){var t=this;this.$confirm("是否".concat(this.titleSelectLaber),{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var a=_(l.a.mark((function a(n,i,r){var s,c,u,d,p,m,b,f,h;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("confirm"!==n){a.next=32;break}if(!t.dialogLoading){a.next=3;break}return a.abrupt("return",t.$message.error("请勿重复提交！"));case 3:if(t.dialogLoading=!0,i.confirmButtonLoading=!0,s={ids:t.checkList,label_list:e.selectLabelIdList},c="",u=g(c,2),d=u[0],p=u[1],"batchLabelAdd"!==t.batchLabelType){a.next=17;break}return a.next=11,Object(o["Q"])(t.$apis.apiBackgroundAdminIngredientBatchAddLabelPost(s));case 11:m=a.sent,b=g(m,2),d=b[0],p=b[1],a.next=24;break;case 17:return a.next=20,Object(o["Q"])(t.$apis.apiBackgroundAdminIngredientBatchDeleteLabelPost(s));case 20:f=a.sent,h=g(f,2),d=h[0],p=h[1];case 24:if(t.dialogLoading=!1,!d){a.next=28;break}return t.$message.error(d.message),a.abrupt("return");case 28:0===p.code?(r(),t.$message.success(p.msg),t.$refs.tableData.clearSelection(),t.getListHandler(),t.checkList=[]):t.$message.error(p.msg),i.confirmButtonLoading=!1,a.next=33;break;case 32:i.confirmButtonLoading||r();case 33:case"end":return a.stop()}}),a)})));function n(e,t,n){return a.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},closeTag:function(e,t){this.batchLabelType="delSingleTag",this.titleSelectLaber="删除该标签";var a={selectLabelIdList:[e.id]};this.checkList=[t.id],this.selectLaberData(a)},getListHandler:function(){"system"===this.tabType?this.getIngredientslist():this.getIngredientsManchantlist()},handleSizeChange:function(e){this.pageSize=e,this.getListHandler()},handleCurrentChange:function(e){this.currentPage=e,this.getListHandler()},dialogHandleClose:function(){this.formData={}},importHandler:function(e){"import"===e?this.$router.push({name:"SuperImportIngredients",params:{type:e}}):"importImage"===e&&this.$router.push({name:"SuperImportIngredientImage",params:{type:e}})},gotoCategory:function(){this.$router.push({name:"SuperImportIngredientsCategory"})},gotoExport:function(){var e={type:"SuperIngredientsLibrary",params:p(p({},this.formatQueryParams(this.searchFormSetting)),{},{page:this.currentPage,page_size:this.pageSize})};"system"===this.tabType?e.type="SuperIngredientsLibrarySystem":e.type="SuperIngredientsLibraryMerchant",this.exportHandle(e)}}},C=k,S=(a("bdae"),a("2877")),I=Object(S["a"])(C,n,i,!1,null,"b486e208",null);t["default"]=I.exports},"7d0e":function(e,t,a){},"7e85":function(e,t,a){},bdae:function(e,t,a){"use strict";var n=a("7d0e"),i=a.n(n);i.a},fa5f:function(e,t,a){"use strict";var n=a("7e85"),i=a.n(n);i.a}}]);
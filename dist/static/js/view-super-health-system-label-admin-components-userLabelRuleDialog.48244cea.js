(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-label-admin-components-userLabelRuleDialog","view-super-health-system-components-selectLaber"],{"1a24":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[l("div",{staticClass:"healthTagDialog"},[l("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:e.searchHandle},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}}),l("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[l("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[l("span",[e._v(" 已选 "),l("span",[e._v(e._s(e.selectLabelIdList.length))]),e._v(" 个标签 ")])]),e._l(e.tableData,(function(t,a){return l("div",{key:a},[l("el-collapse",{model:{value:e.activeLaberList,callback:function(t){e.activeLaberList=t},expression:"activeLaberList"}},[l("el-collapse-item",{attrs:{name:t.id}},[l("template",{slot:"title"},[l("span",[e._v(" "+e._s(t.name)+" "),l("span",[e._v("（"+e._s(t.label_list.length)+"）")])]),l("span",{staticClass:"tips-r"},[l("span",{staticClass:"open"},[e._v("展开")]),l("span",{staticClass:"close"},[e._v("收起")])])]),l("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[t.inputVisible?l("el-input",{ref:"saveTagInput"+t.id,refInFor:!0,staticClass:"ps-input w-100  p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(l){return e.handleInputConfirm(t)}},nativeOn:{keyup:function(l){return!l.type.indexOf("key")&&e._k(l.keyCode,"enter",13,l.key,"Enter")?null:e.handleInputConfirm(t)}},model:{value:t.inputValue,callback:function(l){e.$set(t,"inputValue",l)},expression:"item.inputValue"}}):l("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(l){return e.showInput(t)}}},[e._v(" 添加标签 ")]),l("div",{staticStyle:{flex:"1"}},[l("el-checkbox-group",{attrs:{size:"mini"},model:{value:e.selectLabelIdList,callback:function(t){e.selectLabelIdList=t},expression:"selectLabelIdList"}},e._l(t.label_list,(function(a,i){return l("el-checkbox-button",{key:i,attrs:{label:a.id,disabled:a.disabled},on:{change:function(l){return e.checkboxChangge(a,t)}}},[e._v(" "+e._s(a.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),l("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[l("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),l("template",{slot:"tool"},[l("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[l("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),l("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},i=[],s=l("a34a"),n=l.n(s),r=l("ed08");function o(e,t){return p(e)||d(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return b(e,t);var l=Object.prototype.toString.call(e).slice(8,-1);return"Object"===l&&e.constructor&&(l=e.constructor.name),"Map"===l||"Set"===l?Array.from(e):"Arguments"===l||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var l=0,a=new Array(t);l<t;l++)a[l]=e[l];return a}function d(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var l=[],a=!0,i=!1,s=void 0;try{for(var n,r=e[Symbol.iterator]();!(a=(n=r.next()).done);a=!0)if(l.push(n.value),t&&l.length===t)break}catch(o){i=!0,s=o}finally{try{a||null==r["return"]||r["return"]()}finally{if(i)throw s}}return l}}function p(e){if(Array.isArray(e))return e}function f(e,t,l,a,i,s,n){try{var r=e[s](n),o=r.value}catch(c){return void l(c)}r.done?t(o):Promise.resolve(o).then(a,i)}function h(e){return function(){var t=this,l=arguments;return new Promise((function(a,i){var s=e.apply(t,l);function n(e){f(s,a,i,n,r,"next",e)}function r(e){f(s,a,i,n,r,"throw",e)}n(void 0)}))}}var L={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(r["c"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var e=this;return h(n.a.mark((function t(){var l,a,i,s,c;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,l={type:e.labelType,page:e.currentPage,page_size:e.pageSize},e.name&&(l.name=e.name),t.next=5,Object(r["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(l));case 5:if(a=t.sent,i=o(a,2),s=i[0],c=i[1],e.isLoading=!1,!s){t.next=13;break}return e.$message.error(s.message),t.abrupt("return");case 13:0===c.code?(e.totalCount=c.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=c.data.results.map((function(t){return t.inputVisible=!1,t.inputValue="",t.label_list.forEach((function(l){l.label_group_name=t.name,e.ruleSingleInfo.selectLabelAllIds&&e.ruleSingleInfo.selectLabelAllIds.length&&e.ruleSingleInfo.selectLabelAllIds.includes(l.id)&&!e.selectLabelIdList.includes(l.id)?l.disabled=!0:l.disabled=!1})),e.activeLaberList.push(t.id),t})),console.log(e.tableData)):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},handleChange:function(){},checkboxChangge:function(e,t){var l=this,a=this.selectLabelIdList.indexOf(e.id);-1!==a?this.selectLabelListData.push(e):this.selectLabelListData.map((function(t,a){e.id===t.id&&l.selectLabelListData.splice(a,1)}))},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(l){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return h(n.a.mark((function l(){var a,i,s,c;return n.a.wrap((function(l){while(1)switch(l.prev=l.next){case 0:return t.isLoading=!0,l.next=3,Object(r["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(a=l.sent,i=o(a,2),s=i[0],c=i[1],t.isLoading=!1,!s){l.next=11;break}return t.$message.error(s.message),l.abrupt("return");case 11:0===c.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(c.msg);case 12:case"end":return l.stop()}}),l)})))()},clickConfirmHandle:function(){var e={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",e),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()}}},m=L,g=(l("fa5f"),l("2877")),y=Object(g["a"])(m,a,i,!1,null,null,null);t["default"]=y.exports},"221e":function(e,t,l){},"383c":function(e,t,l){"use strict";var a=l("221e"),i=l.n(a);i.a},"7e85":function(e,t,l){},acbc:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",[l("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[l("div",{staticClass:"userLabelRuleDialog"},[l("el-radio-group",{staticClass:"p-b-20",model:{value:e.laberlUseType,callback:function(t){e.laberlUseType=t},expression:"laberlUseType"}},[l("el-radio",{staticClass:"ps-radio",attrs:{label:"label_group"}},[e._v("按标签组")]),l("el-radio",{staticClass:"ps-radio",attrs:{label:"label"}},[e._v("按标签")])],1),e._l(e.ruleForm[e.laberlUseType],(function(t,a){return l("div",{key:a,staticClass:"rule-wrapper"},[l("div",{staticClass:"laber-rule-content"},[l("span",{staticClass:"p-r-10"},[e._v("当集体被标记上该群体标签时")]),e.ruleForm[e.laberlUseType].length>1&&"label"===e.laberlUseType?l("i",{staticClass:"el-icon-error",staticStyle:{color:"red"},on:{click:function(t){return e.removeRuleClick(a)}}}):e._e()]),"label_group"===e.laberlUseType?l("div",{staticClass:"label-rroup-name"},[e._v(" "+e._s(e.labelDataInfo.name)+"标签组名称 ")]):e._e(),"label"===e.laberlUseType?l("el-checkbox-group",{attrs:{size:"mini"},model:{value:t.labelIdsList,callback:function(l){e.$set(t,"labelIdsList",l)},expression:"item.labelIdsList"}},e._l(t.ruleLabelDataList,(function(t,i){return l("el-checkbox-button",{key:i,attrs:{label:t.id,disabled:t.disabled},on:{change:function(t){return e.checkboxChangge(t,a)}}},[e._v(" "+e._s(t.name)+" ")])})),1):e._e(),l("div",{staticClass:"rule-content"},[l("div",{staticClass:"rule-title"},[l("span",[e._v("当前条件")]),l("i",{staticClass:"el-icon-circle-plus",on:{click:function(t){return e.conditionAddClick(a)}}})]),e._l(t.ruleLabelList,(function(i,s){return l("div",{key:s,staticClass:"laber-rule-wrapper"},[l("div",{staticClass:"laber-rule-content"},[l("div",[l("span",{staticClass:"m-r-10"},[e._v("对应包含以下")]),l("el-select",{staticClass:"ps-select m-r-10",staticStyle:{width:"120px"},attrs:{placeholder:"请下拉选择",size:"small","popper-class":"ps-popper-select"},on:{change:function(l){return e.changeLabelType(i,t,s)}},model:{value:i.labelType,callback:function(t){e.$set(i,"labelType",t)},expression:"conditionItem.labelType"}},e._l(i.labelTypeList,(function(e,t){return l("el-option",{key:t,attrs:{label:e.name,value:e.value,disabled:e.disabled}})})),1),l("span",{staticClass:"m-r-10"},[e._v("的菜品")]),l("el-select",{staticClass:"ps-select m-r-10",staticStyle:{width:"120px"},attrs:{placeholder:"请下拉选择",size:"small","popper-class":"ps-popper-select",disabled:!i.labelType},on:{change:function(l){return e.changeActionType(i,t)}},model:{value:i.action,callback:function(t){e.$set(i,"action",t)},expression:"conditionItem.action"}},e._l(i.actionList,(function(e,t){return l("el-option",{key:t,attrs:{label:e.name,value:e.value,disabled:e.disabled}})})),1),l("span",[e._v("标识")])],1),t.ruleLabelList.length>1?l("i",{staticClass:"el-icon-error",staticStyle:{color:"#d7e1ea"},on:{click:function(l){return e.removeConditionClick(a,s,i,t)}}}):e._e()]),l("div",{staticClass:"m-t-5 m-b-10"},[l("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(l){return e.addLabelClick(t,a,i,s)}}},[e._v(" 添加标签 ")]),e._l(i.selectLabelListData,(function(t,a){return l("el-tag",{key:a,staticClass:"m-r-5 m-t-5",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff"}},[e._v(" "+e._s(t.name)+" ")])}))],2)])}))],2)],1)})),"label"===e.laberlUseType?l("el-button",{staticClass:"ps-origin-plain-btn",staticStyle:{width:"100%",height:"35px"},attrs:{size:"mini",icon:"el-icon-plus"},on:{click:e.addRuleClick}},[e._v(" 添加规则 ")]):e._e()],2),l("template",{slot:"tool"},[l("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px"},attrs:{slot:"footer"},slot:"footer"},[l("div",[e.labelDataInfo.rule_label_list.length?l("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"medium",icon:"el-icon-delete"},on:{click:function(t){return e.ruleDelClick()}}},[e._v(" 删除该规则 ")]):e._e()],1),l("div",[l("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),l("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])])],2),e.selectLaberDialogVisible?l("select-laber",{attrs:{isshow:e.selectLaberDialogVisible,width:"600px",ruleSingleInfo:e.ruleSingleInfo},on:{"update:isshow":function(t){e.selectLaberDialogVisible=t},selectLaberData:e.selectLaberData}}):e._e()],1)},i=[],s=l("a34a"),n=l.n(s),r=l("1a24"),o=l("ed08");function c(e,t){return f(e)||p(e,t)||b(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(e,t){if(e){if("string"===typeof e)return d(e,t);var l=Object.prototype.toString.call(e).slice(8,-1);return"Object"===l&&e.constructor&&(l=e.constructor.name),"Map"===l||"Set"===l?Array.from(e):"Arguments"===l||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var l=0,a=new Array(t);l<t;l++)a[l]=e[l];return a}function p(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var l=[],a=!0,i=!1,s=void 0;try{for(var n,r=e[Symbol.iterator]();!(a=(n=r.next()).done);a=!0)if(l.push(n.value),t&&l.length===t)break}catch(o){i=!0,s=o}finally{try{a||null==r["return"]||r["return"]()}finally{if(i)throw s}}return l}}function f(e){if(Array.isArray(e))return e}function h(e,t,l,a,i,s,n){try{var r=e[s](n),o=r.value}catch(c){return void l(c)}r.done?t(o):Promise.resolve(o).then(a,i)}function L(e){return function(){var t=this,l=arguments;return new Promise((function(a,i){var s=e.apply(t,l);function n(e){h(s,a,i,n,r,"next",e)}function r(e){h(s,a,i,n,r,"throw",e)}n(void 0)}))}}var m={name:"userLabelRuleDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,labelDataInfo:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{isLoading:!1,laberlUseType:"label_group",ruleForm:{label_group:[{currentSelects:{},ruleLabelList:[{rule_id:-1,labelType:"food",action:"recommend",selectLabelIdList:[],selectLabelListData:[],labelTypeList:[{name:"菜品标签",value:"food",disabled:!1},{name:"食材标签",value:"ingredient",disabled:!1}],actionList:[{name:"建议",value:"recommend",disabled:!1},{name:"不建议",value:"not_recommend",disabled:!1},{name:"适量",value:"suitable",disabled:!1}]}]}],label:[]},ruleSingleInfo:{},selectLaberDialogVisible:!1,deductionAllSelectRule:[]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},components:{selectLaber:r["default"]},created:function(){this.labelDataInfo.rule_label_list.length?this.initLabel():this.initLabelAdd()},mounted:function(){},methods:{initLabel:function(){this.laberlUseType=this.labelDataInfo.use_type,this.ruleForm[this.laberlUseType]=[];for(var e=[],t=[],l=0;l<this.labelDataInfo.rule_label_list.length;l++)if(-1===e.indexOf(JSON.stringify(this.labelDataInfo.rule_label_list[l].label_id_list))){var a={rule_id:this.labelDataInfo.rule_label_list[l].rule_id,labelType:this.labelDataInfo.rule_label_list[l].label_type,action:this.labelDataInfo.rule_label_list[l].action,selectLabelIdList:this.labelDataInfo.rule_label_list[l].selected_label_id_list,selectLabelListData:this.labelDataInfo.rule_label_list[l].selected_label,labelTypeList:[{name:"菜品标签",value:"food",disabled:!1},{name:"食材标签",value:"ingredient",disabled:!1}],actionList:[{name:"建议",value:"recommend",disabled:!1},{name:"不建议",value:"not_recommend",disabled:!1},{name:"适量",value:"suitable",disabled:!1}]};t.push({currentSelects:{},ruleLabelDataList:Object(o["e"])(this.labelDataInfo.label_list),labelIdsList:this.labelDataInfo.rule_label_list[l].label_id_list,ruleLabelList:[a]}),e.push(JSON.stringify(this.labelDataInfo.rule_label_list[l].label_id_list))}else for(var i=0;i<t.length;i++)if(JSON.stringify(t[i].labelIdsList)===JSON.stringify(this.labelDataInfo.rule_label_list[l].label_id_list)){t[i].ruleLabelList.push({rule_id:this.labelDataInfo.rule_label_list[l].rule_id,labelType:this.labelDataInfo.rule_label_list[l].label_type,action:this.labelDataInfo.rule_label_list[l].action,selectLabelIdList:this.labelDataInfo.rule_label_list[l].selected_label_id_list,selectLabelListData:this.labelDataInfo.rule_label_list[l].selected_label,labelTypeList:[{name:"菜品标签",value:"food",disabled:!1},{name:"食材标签",value:"ingredient",disabled:!1}],actionList:[{name:"建议",value:"recommend",disabled:!1},{name:"不建议",value:"not_recommend",disabled:!1},{name:"适量",value:"suitable",disabled:!1}]});break}this.ruleForm[this.laberlUseType]=t,this.changeRuleLabelDatadisabled(),this.initLabelAdd()},initLabelAdd:function(){var e=this;this.ruleForm[this.laberlUseType].forEach((function(t,l){t.ruleLabelList.forEach((function(l){e.changeLabelTypedisabled(l,t)}))}))},checkboxChangge:function(e,t){this.changeRuleLabelDatadisabled()},changeRuleLabelDatadisabled:function(){var e=this;this.deductionAllSelectRule=[],this.ruleForm[this.laberlUseType].map((function(t,l){e.deductionAllSelectRule=e.deductionAllSelectRule.concat(t.labelIdsList)})),this.ruleForm[this.laberlUseType].forEach((function(t,l){t.ruleLabelDataList.forEach((function(l){e.deductionAllSelectRule.includes(l.id)&&!t.labelIdsList.includes(l.id)?l.disabled=!0:l.disabled=!1}))}))},changeLabelType:function(e,t){e.selectLabelIdList=[],e.selectLabelListData=[],e.action="",t.currentSelects[e.labelType]=[],this.initLabelAdd()},changeLabelTypedisabled:function(e,t){e.labelType&&!t.currentSelects[e.labelType]&&(t.currentSelects[e.labelType]=[]),this.changeActionTypedisabled(e,t)},changeActionType:function(e,t){this.changeActionTypedisabled(e,t)},changeActionTypedisabled:function(e,t){var l=this;e.action&&t.currentSelects[e.labelType]&&!t.currentSelects[e.labelType].includes(e.action)&&(t.currentSelects[e.labelType]=[],t.ruleLabelList.forEach((function(l){l.action&&l.labelType===e.labelType&&t.currentSelects[e.labelType].push(l.action)}))),this.ruleForm[this.laberlUseType].forEach((function(e,t){e.ruleLabelList.forEach((function(t){t.actionList.forEach((function(a){a.disabled=l.actionDisabledFun(a,t,e)}))}))}))},actionDisabledFun:function(e,t,l){var a=!1,i=l.currentSelects;return i[t.labelType]&&i[t.labelType].includes(e.value)&&e.value!==t.action&&(a=!0),a},conditionAddClick:function(e){this.ruleForm[this.laberlUseType][e].ruleLabelList.push({rule_id:-1,labelType:"",action:"",selectLabelIdList:[],selectLabelListData:[],labelTypeList:[{name:"菜品标签",value:"food",disabled:!1},{name:"食材标签",value:"ingredient",disabled:!1}],actionList:[{name:"建议",value:"recommend",disabled:!1},{name:"不建议",value:"not_recommend",disabled:!1},{name:"适量",value:"suitable",disabled:!1}]})},removeConditionClick:function(e,t,l,a){if(this.ruleForm[this.laberlUseType][e].ruleLabelList.splice(t,1),l.labelType){var i=a.currentSelects[l.labelType].indexOf(l.action);i>-1&&a.currentSelects[l.labelType].splice(i,1),this.initLabelAdd()}},selectLaberData:function(e){this.ruleForm[this.laberlUseType][this.ruleSingleInfo.fatherIndex].ruleLabelList[this.ruleSingleInfo.conditionIndex].selectLabelIdList=e.selectLabelIdList,this.ruleForm[this.laberlUseType][this.ruleSingleInfo.fatherIndex].ruleLabelList[this.ruleSingleInfo.conditionIndex].selectLabelListData=e.selectLabelListData},addRuleClick:function(){this.ruleForm[this.laberlUseType].push({ruleLabelDataList:Object(o["e"])(this.labelDataInfo.label_list),labelIdsList:[],currentSelects:{},ruleLabelList:[{rule_id:-1,labelType:"",action:"",selectLabelIdList:[],selectLabelListData:[],labelTypeList:[{name:"菜品标签",value:"food",disabled:!1},{name:"食材标签",value:"ingredient",disabled:!1}],actionList:[{name:"建议",value:"recommend",disabled:!1},{name:"不建议",value:"not_recommend",disabled:!1},{name:"适量",value:"suitable",disabled:!1}]}]}),this.changeRuleLabelDatadisabled()},removeRuleClick:function(e){this.ruleForm[this.laberlUseType].splice(e,1),this.changeRuleLabelDatadisabled()},addLabelClick:function(e,t,l,a){if(!l.labelType)return this.$message.error("请选标签");var i=[];e.ruleLabelList.forEach((function(e,t){i=i.concat(e.selectLabelIdList)})),this.ruleSingleInfo={fatherIndex:t,conditionIndex:a,selectLabelIdList:l.selectLabelIdList,selectLabelAllIds:i,selectLabelListData:l.selectLabelListData,labelType:l.labelType},this.selectLaberDialogVisible=!0},clickConfirmHandle:function(){for(var e={label_group_id:this.labelDataInfo.id,use_type:this.laberlUseType,rule_label_list:[]},t=0;t<this.ruleForm[this.laberlUseType].length;t++){if("label"===this.laberlUseType&&!this.ruleForm[this.laberlUseType][t].labelIdsList.length)return this.$message.error("第".concat(t+1,"个规则请选择集体标签"));for(var l=0;l<this.ruleForm[this.laberlUseType][t].ruleLabelList.length;l++){if(!this.ruleForm[this.laberlUseType][t].ruleLabelList[l].labelType)return this.$message.error("请选择标签");if(!this.ruleForm[this.laberlUseType][t].ruleLabelList[l].action)return this.$message.error("请选择菜品标识");if(this.ruleForm[this.laberlUseType][t].ruleLabelList[l].selectLabelIdList.length<=0)return this.$message.error("请添加标签");var a={rule_id:this.ruleForm[this.laberlUseType][t].ruleLabelList[l].rule_id,action:this.ruleForm[this.laberlUseType][t].ruleLabelList[l].action,label_type:this.ruleForm[this.laberlUseType][t].ruleLabelList[l].labelType,select_label_id_list:this.ruleForm[this.laberlUseType][t].ruleLabelList[l].selectLabelIdList,label_id_list:this.ruleForm[this.laberlUseType][t].labelIdsList};this.ruleForm[this.laberlUseType][t].rule_id&&(a.rule_id=this.ruleForm[this.laberlUseType][t].rule_id),e.rule_label_list.push(a)}}this.getRuleModify(e)},getRuleModify:function(e){var t=this;return L(n.a.mark((function l(){var a,i,s,r;return n.a.wrap((function(l){while(1)switch(l.prev=l.next){case 0:return t.isLoading=!0,l.next=3,Object(o["Q"])(t.$apis.apiBackgroundHealthyAdminLabelRuleModifyPost(e));case 3:if(a=l.sent,i=c(a,2),s=i[0],r=i[1],t.isLoading=!1,!s){l.next=11;break}return t.$message.error(s.message),l.abrupt("return");case 11:0===r.code?(t.visible=!1,t.confirm()):t.$message.error(r.msg);case 12:case"end":return l.stop()}}),l)})))()},ruleDelClick:function(e){var t=this;this.$confirm("确定删除该标签组规则？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var e=L(n.a.mark((function e(l,a,i){var s,r;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==l){e.next=10;break}return s=t.labelDataInfo.rule_label_list.map((function(e){return e.rule_id})),e.next=4,t.$apis.apiBackgroundHealthyAdminLabelRuleDeletePost({ids:s});case 4:r=e.sent,0===r.code?(t.$message.success("删除成功"),t.visible=!1,t.confirm()):t.$message.error(r.msg),i(),a.confirmButtonLoading=!1,e.next=11;break;case 10:a.confirmButtonLoading||i();case 11:case"end":return e.stop()}}),e)})));function l(t,l,a){return e.apply(this,arguments)}return l}()}).then((function(e){})).catch((function(e){}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1}}},g=m,y=(l("383c"),l("2877")),v=Object(y["a"])(g,a,i,!1,null,null,null);t["default"]=v.exports},fa5f:function(e,t,l){"use strict";var a=l("7e85"),i=l.n(a);i.a}}]);
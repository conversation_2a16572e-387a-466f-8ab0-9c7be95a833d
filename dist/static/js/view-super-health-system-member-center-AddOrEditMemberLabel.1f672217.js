(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-AddOrEditMemberLabel"],{"0513":function(e,t,r){},"68e8":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"AddOrEditMemberLabel container-wrapper"},[r("div",{staticClass:"table-wrapper"},[r("div",{staticClass:"table-header"},[r("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新建":"编辑")+"会员标签")])]),r("div",[r("el-form",{ref:"memberFormRef",attrs:{model:e.memberForm,rules:e.memberFormRules,"label-width":"120px"}},[r("el-form-item",{attrs:{label:"标签名称：",prop:"name"}},[r("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"15"},model:{value:e.memberForm.name,callback:function(t){e.$set(e.memberForm,"name",t)},expression:"memberForm.name"}})],1),r("el-form-item",{attrs:{label:"标签类型："}},[r("el-radio-group",{model:{value:e.memberForm.labelType,callback:function(t){e.$set(e.memberForm,"labelType",t)},expression:"memberForm.labelType"}},[r("el-radio",{staticClass:"ps-radio",attrs:{label:"auto"}},[e._v("自动")]),r("el-radio",{staticClass:"ps-radio",attrs:{label:"manual"}},[e._v("手动")])],1)],1),r("el-form-item",[r("el-radio-group",{staticClass:"ps-radio-btn",model:{value:e.memberForm.labelDirection,callback:function(t){e.$set(e.memberForm,"labelDirection",t)},expression:"memberForm.labelDirection"}},[r("el-radio-button",{staticClass:"ps-radio",attrs:{label:"positive"}},[e._v("正向标签")]),r("el-radio-button",{staticClass:"ps-radio",attrs:{label:"negative"}},[e._v("负面标签")])],1)],1),"auto"===e.memberForm.labelType?r("div",["positive"===e.memberForm.labelDirection?r("div",[r("el-form-item",{key:e.memberForm.isTotalCount+"totalCount",attrs:{prop:e.memberForm.isTotalCount?"totalCount":""}},[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isTotalCount,callback:function(t){e.$set(e.memberForm,"isTotalCount",t)},expression:"memberForm.isTotalCount"}},[e._v("累计签到天数")]),r("el-input",{staticClass:"ps-input w-180 m-l-5",model:{value:e.memberForm.totalCount,callback:function(t){e.$set(e.memberForm,"totalCount",t)},expression:"memberForm.totalCount"}})],1),r("el-form-item",{key:e.memberForm.isContinueCount+"continueCount",attrs:{prop:e.memberForm.isContinueCount?"continueCount":""}},[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isContinueCount,callback:function(t){e.$set(e.memberForm,"isContinueCount",t)},expression:"memberForm.isContinueCount"}},[e._v("连续签到天数")]),r("el-input",{staticClass:"ps-input w-180 m-l-5",model:{value:e.memberForm.continueCount,callback:function(t){e.$set(e.memberForm,"continueCount",t)},expression:"memberForm.continueCount"}})],1),r("el-form-item",{key:e.memberForm.isLevel+"level",attrs:{prop:e.memberForm.isLevel?"level":""}},[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isLevel,callback:function(t){e.$set(e.memberForm,"isLevel",t)},expression:"memberForm.isLevel"}},[e._v("会员等级")]),r("el-select",{staticClass:"ps-select w-180 m-l-5",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.memberForm.level,callback:function(t){e.$set(e.memberForm,"level",t)},expression:"memberForm.level"}},e._l(e.levelList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),r("el-form-item",{key:e.memberForm.isIntegral+"integral",attrs:{prop:e.memberForm.isIntegral?"integral":""}},[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isIntegral,callback:function(t){e.$set(e.memberForm,"isIntegral",t)},expression:"memberForm.isIntegral"}},[e._v("积分达到")]),r("el-input",{staticClass:"ps-input w-180 m-l-5",model:{value:e.memberForm.integral,callback:function(t){e.$set(e.memberForm,"integral",t)},expression:"memberForm.integral"}})],1)],1):e._e(),"negative"===e.memberForm.labelDirection?r("div",[r("el-form-item",{key:e.memberForm.isNotLoginCount+"notLoginCount",attrs:{prop:e.memberForm.isNotLoginCount?"notLoginCount":""}},[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isNotLoginCount,callback:function(t){e.$set(e.memberForm,"isNotLoginCount",t)},expression:"memberForm.isNotLoginCount"}},[e._v("连续未登录天数")]),r("el-input",{staticClass:"ps-input w-180 m-l-5",model:{value:e.memberForm.notLoginCount,callback:function(t){e.$set(e.memberForm,"notLoginCount",t)},expression:"memberForm.notLoginCount"}}),e._v(" （从未登陆过的用户不在此条件范围内） ")],1),r("el-form-item",{key:e.memberForm.isNotSignCount+"notSignCount",attrs:{prop:e.memberForm.isNotSignCount?"notSignCount":""}},[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isNotSignCount,callback:function(t){e.$set(e.memberForm,"isNotSignCount",t)},expression:"memberForm.isNotSignCount"}},[e._v("连续未签到天数")]),r("el-input",{staticClass:"ps-input w-180 m-l-5",model:{value:e.memberForm.notSignCount,callback:function(t){e.$set(e.memberForm,"notSignCount",t)},expression:"memberForm.notSignCount"}}),e._v(" （从未签到过的用户不在此条件范围内） ")],1),r("el-form-item",[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isNeverLogin,callback:function(t){e.$set(e.memberForm,"isNeverLogin",t)},expression:"memberForm.isNeverLogin"}},[e._v("从未登陆过")])],1),r("el-form-item",[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isNeverSign,callback:function(t){e.$set(e.memberForm,"isNeverSign",t)},expression:"memberForm.isNeverSign"}},[e._v("从未签到过")])],1),r("el-form-item",[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isNeverBuy,callback:function(t){e.$set(e.memberForm,"isNeverBuy",t)},expression:"memberForm.isNeverBuy"}},[e._v("从未购买过会员")])],1)],1):e._e(),r("el-form-item",{key:e.memberForm.isLabel+"label",attrs:{prop:e.memberForm.isLabel?"label":""}},[r("el-checkbox",{staticClass:"ps-checkbox",model:{value:e.memberForm.isLabel,callback:function(t){e.$set(e.memberForm,"isLabel",t)},expression:"memberForm.isLabel"}},[e._v("触发指定用户标签")]),r("el-select",{staticClass:"ps-select w-180 m-l-5",attrs:{multiple:!0,placeholder:"请下拉选择","popper-class":"ps-popper-select"},model:{value:e.memberForm.label,callback:function(t){e.$set(e.memberForm,"label",t)},expression:"memberForm.label"}},e._l(e.userLabelList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),r("el-form-item",[r("div",{staticClass:"m-l-20 label-list"},e._l(e.labelNameList,(function(t,i){return r("div",{key:t,staticClass:"label-list-item"},[r("span",{staticClass:"m-r-5"},[e._v("标签"+e._s(t))]),r("i",{staticClass:"el-icon-close del-icon",on:{click:function(t){return e.delLabel(i)}}})])})),0)])],1):e._e(),r("el-form-item",[r("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:e.saveSetting}},[e._v("保存")])],1)],1)],1)])])},o=[],m=r("a34a"),s=r.n(m);function n(e,t,r,i,o,m,s){try{var n=e[m](s),a=n.value}catch(l){return void r(l)}n.done?t(a):Promise.resolve(a).then(i,o)}function a(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var m=e.apply(t,r);function s(e){n(m,i,o,s,a,"next",e)}function a(e){n(m,i,o,s,a,"throw",e)}s(void 0)}))}}var l={name:"AddOrEditMemberLabel",components:{},props:{},data:function(){var e=function(e,t,r){if(""===t)return r(new Error("不能为空"));var i=/^\d+$/;i.test(t)?r():r(new Error("请输入正整数"))};return{isLoading:!1,type:"",settingData:{},memberForm:{name:"",labelType:"auto",labelDirection:"positive",isTotalCount:!1,totalCount:"",isContinueCount:!1,continueCount:"",isLevel:!1,level:"",isIntegral:!1,integral:"",isLabel:!1,label:[],isNotLoginCount:!1,notLoginCount:"",isNotSignCount:!1,notSignCount:"",isNeverLogin:!1,isNeverSign:!1,isNeverBuy:!1},memberFormRules:{name:[{required:!0,message:"请输入会员名称",trigger:"blur"}],totalCount:[{validator:e,trigger:"blur"}],continueCount:[{validator:e,trigger:"blur"}],level:[{required:!0,message:"请选择会员等级",trigger:"change"}],integral:[{validator:e,trigger:"blur"}]},levelList:[],userLabelList:[],labelNameList:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.query.data&&(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data))),this.$route.params.type&&(this.type=this.$route.params.type,"edit"===this.type&&this.initData()),this.getMemberLevel(),this.getUserLabelList()},initData:function(){this.memberForm.name=this.settingData.name,this.memberForm.labelType=this.settingData.type,this.memberForm.labelDirection=this.settingData.direction,this.memberForm.isTotalCount=!(-1===this.settingData.grand_total_sign_days),this.memberForm.totalCount=this.memberForm.isTotalCount?this.settingData.grand_total_sign_days:"",this.memberForm.isContinueCount=!(-1===this.settingData.continuous_sign_days),this.memberForm.continueCount=this.memberForm.isContinueCount?this.settingData.continuous_sign_days:"",this.memberForm.isLevel=!(null===this.settingData.member_grade),this.memberForm.level=this.memberForm.isLevel?this.settingData.member_grade:"",this.memberForm.isIntegral=!(-1===this.settingData.integrals),this.memberForm.integral=this.memberForm.isIntegral?this.settingData.integrals:"",this.memberForm.isLabel=!!this.settingData.labels.length,this.memberForm.label=this.memberForm.isLabel?this.settingData.labels:[],this.memberForm.isNotLoginCount=!(-1===this.settingData.not_login_days),this.memberForm.notLoginCount=this.memberForm.isNotLoginCount?this.settingData.not_login_days:"",this.memberForm.isNotSignCount=!(-1===this.settingData.not_sign_days),this.memberForm.notSignCount=this.memberForm.isNotSignCount?this.settingData.not_sign_days:"",this.memberForm.isNeverLogin=this.settingData.not_login,this.memberForm.isNeverSign=this.settingData.not_sign,this.memberForm.isNeverBuy=this.settingData.not_buy},saveSetting:function(){var e=this;if("auto"===this.memberForm.labelType&&"positive"===this.memberForm.labelDirection&&!this.memberForm.isTotalCount&&!this.memberForm.isContinueCount&&!this.memberForm.isLevel&&!this.memberForm.isIntegral&&!this.memberForm.isLabel||"auto"===this.memberForm.labelType&&"negative"===this.memberForm.labelDirection&&!this.memberForm.isNotLoginCount&&!this.memberForm.isNotSignCount&&!this.memberForm.isNeverLogin&&!this.memberForm.isNeverSign&&!this.memberForm.isNeverBuy&&!this.memberForm.isLevel)return this.$message.error("请选择标签条件");this.$refs.memberFormRef.validate((function(t){if(t){var r,i={name:e.memberForm.name,type:e.memberForm.labelType,direction:e.memberForm.labelDirection,grand_total_sign_days:e.memberForm.isTotalCount&&"positive"===e.memberForm.labelDirection?e.memberForm.totalCount:-1,continuous_sign_days:e.memberForm.isContinueCount&&"positive"===e.memberForm.labelDirection?e.memberForm.continueCount:-1,member_grade:e.memberForm.isLevel&&"positive"===e.memberForm.labelDirection?e.memberForm.level:null,integrals:e.memberForm.isIntegral&&"positive"===e.memberForm.labelDirection?e.memberForm.integral:-1,labels:e.memberForm.isLabel&&"positive"===e.memberForm.labelDirection?e.memberForm.label:[],not_login_days:e.memberForm.isNotLoginCount&&"negative"===e.memberForm.labelDirection?e.memberForm.notLoginCount:-1,not_sign_days:e.memberForm.isNotSignCount&&"negative"===e.memberForm.labelDirection?e.memberForm.notSignCount:-1,not_login:"negative"===e.memberForm.labelDirection&&e.memberForm.isNeverLogin,not_sign:"negative"===e.memberForm.labelDirection&&e.memberForm.isNeverSign,not_buy:"negative"===e.memberForm.labelDirection&&e.memberForm.isNeverBuy};switch(e.type){case"add":r=e.$apis.apiBackgroundMemberMemberLabelAddPost(i);break;case"edit":i.id=Number(e.settingData.id),r=e.$apis.apiBackgroundMemberMemberLabelModifyPost(i);break}e.confirmOperation(r)}}))},confirmOperation:function(e){var t=this;return a(s.a.mark((function r(){var i;return s.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!t.isLoading){r.next=2;break}return r.abrupt("return");case 2:return t.isLoading=!0,r.next=5,e;case 5:i=r.sent,t.isLoading=!1,0===i.code?(t.$message.success("成功"),t.$closeCurrentTab(t.$route.path)):t.$message.error(i.msg);case 8:case"end":return r.stop()}}),r)})))()},getMemberLevel:function(){var e=this;return a(s.a.mark((function t(){var r;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundMemberMemberGradeListPost({page:1,page_size:99999});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.levelList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},getUserLabelList:function(){var e=this;return a(s.a.mark((function t(){var r;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundHealthyLabelListPost({page:1,page_size:99999,type:"user"});case 3:r=t.sent,e.isLoading=!1,0===r.code?e.userLabelList=r.data.results:e.$message.error(r.msg);case 6:case"end":return t.stop()}}),t)})))()},addLabel:function(){this.memberForm.permissions.push("")},delLabel:function(e){this.memberForm.permissions.splice(e,1)}}},b=l,u=(r("86d8"),r("2877")),c=Object(u["a"])(b,i,o,!1,null,"1032bdc0",null);t["default"]=c.exports},"86d8":function(e,t,r){"use strict";var i=r("0513"),o=r.n(i);o.a}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-ImportCommodityImage"],{2102:function(t,a,e){},"3d48":function(t,a,e){"use strict";var r=e("2102"),o=e.n(r);o.a},b7b8:function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"mul-import-img"},[e("el-form",{staticClass:"import-form-wrapper",attrs:{"label-width":"140px"}},[e("el-form-item",{attrs:{label:"菜品图片导入模板"}},[e("el-link",{attrs:{type:"primary",href:"https://cashier-v4.debug.packertec.com/api/temporary/template_excel/food_extra_image.zip"}},[t._v(" 点击下载 ")])],1),e("el-form-item",{attrs:{label:"上传菜品图片模板"}},[e("file-upload",{attrs:{drag:"",data:t.uploadParams,limit:t.limit,"before-upload":t.beforeUpload,prefix:"food_img_zip",action:t.actionUrl,"on-remove":t.remove},on:{fileLists:t.getSuccessUploadRes}},[e("div",{},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v(" 将文件拖到此处，或 "),e("em",[t._v("点击上传")])])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传zip文件")])])],1),e("el-form-item",[e("el-button",{staticClass:"import-btn-wrapper",attrs:{type:"primary"},on:{click:t.mulImortFoodImg}},[t._v(" 确定 ")])],1)],1)],1)},o=[],i=e("a34a"),s=e.n(i),l=e("f63a");function n(t,a,e,r,o,i,s){try{var l=t[i](s),n=l.value}catch(p){return void e(p)}l.done?a(n):Promise.resolve(n).then(r,o)}function p(t){return function(){var a=this,e=arguments;return new Promise((function(r,o){var i=t.apply(a,e);function s(t){n(i,r,o,s,l,"next",t)}function l(t){n(i,r,o,s,l,"throw",t)}s(void 0)}))}}var c={mixins:[l["a"]],data:function(){return{limit:1,actionUrl:"",uploadParams:{},uploadUrl:""}},methods:{getUploadToken:function(){var t=this;return p(s.a.mark((function a(){var e;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.$apis.getUploadToken({prefix:"food_img_zip"});case 2:e=a.sent,0===e.code?(t.actionUrl=e.data.host,t.uploadParams={key:e.data.prefix+(new Date).getTime()+Math.floor(150*Math.random()),prefix:e.data.prefix,policy:e.data.policy,OSSAccessKeyId:e.data.accessid,signature:e.data.signature,callback:e.data.callback,success_action_status:"200"}):t.$message.error(e.msg);case 4:case"end":return a.stop()}}),a)})))()},beforeUpload:function(t){var a=/application\/\S*zip\S*/;if(!a.test(t.type))return this.$message.error("请上传后缀名为.zip的压缩包文件"),!1},remove:function(){this.uploadUrl=""},getSuccessUploadRes:function(t){this.uploadUrl=t[0].url},mulImortFoodImg:function(){if(this.uploadUrl){var t={type:"MulImportFoodImg",url:"apiBackgroundAdminFoodFoodExtraImageBatAddPost",message:"确定导入？",params:{oss_url:this.uploadUrl}};this.exportHandle(t)}else this.$message.error("压缩包还没上传完毕或未上传")}},created:function(){}},u=c,d=(e("3d48"),e("2877")),m=Object(d["a"])(u,r,o,!1,null,null,null);a["default"]=m.exports}}]);
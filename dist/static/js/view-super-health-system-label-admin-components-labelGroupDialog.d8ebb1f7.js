(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-label-admin-components-labelGroupDialog"],{"05ce":function(e,t,a){"use strict";var r=a("bfa4"),i=a.n(r);i.a},bfa4:function(e,t,a){},de5c:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("div",{staticClass:"labelGroupDialog"},[a("el-form",{ref:"labelGroupFormDataRef",attrs:{model:e.labelGroupFormData,"status-icon":"",rules:e.labelGroupFormDataRuls,"label-width":"125px"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{label:"标签组名称:",prop:"name"}},[a("el-input",{staticClass:"ps-input",staticStyle:{width:"250px"},attrs:{size:"small",maxlength:"15",placeholder:"请输入标签组名称"},model:{value:e.labelGroupFormData.name,callback:function(t){e.$set(e.labelGroupFormData,"name",t)},expression:"labelGroupFormData.name"}})],1),a("el-form-item",{attrs:{label:"可见范围:"}},[a("el-radio-group",{staticClass:"ps-radio",model:{value:e.labelGroupFormData.visible,callback:function(t){e.$set(e.labelGroupFormData,"visible",t)},expression:"labelGroupFormData.visible"}},[a("el-radio",{attrs:{label:"all"}},[e._v("全部可见")]),a("el-radio",{attrs:{label:"part"}},[e._v("商户可用")])],1)],1),"part"===e.labelGroupFormData.visible?a("el-form-item",{attrs:{label:"选择可用商户：",prop:"visible_organization"}},[a("organization-select",{staticClass:"search-item-w ps-input w-250",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!0,"check-strictly":!0,role:"super",size:"small","append-to-body":!0,filterable:!0},model:{value:e.labelGroupFormData.visible_organization,callback:function(t){e.$set(e.labelGroupFormData,"visible_organization",t)},expression:"labelGroupFormData.visible_organization"}})],1):e._e(),a("el-form-item",{attrs:{prop:"merchantId",label:"标签名称:"}},[e._l(e.labelGroupFormData.laberList,(function(t,r){return a("div",{key:r,staticClass:"ps-flex-align-c"},[a("el-input",{staticClass:"ps-input",staticStyle:{width:"250px"},attrs:{size:"small",maxlength:"15",placeholder:"请输入标签名称"},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"laberItem.name"}}),a("div",{staticClass:"p-l-20"},[0!=r?a("i",{staticClass:"el-icon-remove-outline p-r-10 ps-origin",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.removeFormLaber(r)}}}):e._e(),0!=r?a("i",{staticClass:"el-icon-top p-r-10 ps-green-text",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.riseClick(r)}}}):e._e(),e.labelGroupFormData.laberList.length!==r+1?a("i",{staticClass:"el-icon-bottom",staticStyle:{"font-size":"18px",color:"#2b8bfb"},on:{click:function(t){return e.declineClick(r)}}}):e._e()])],1)})),"add"==e.type?a("div",{staticClass:"p-b-20",staticStyle:{color:"#fda04d",cursor:"pointer",width:"80px"},on:{click:function(t){return e.addFormLaber()}}},[a("i",{staticClass:"el-icon-plus"}),e._v(" 添加标签 ")]):e._e()],2)],1)],1),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},i=[],l=a("a34a"),o=a.n(l),n=a("cbfb"),s=a("ed08");function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){b(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function b(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function p(e,t){return g(e)||h(e,t)||f(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"===typeof e)return d(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function h(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,i=!1,l=void 0;try{for(var o,n=e[Symbol.iterator]();!(r=(o=n.next()).done);r=!0)if(a.push(o.value),t&&a.length===t)break}catch(s){i=!0,l=s}finally{try{r||null==n["return"]||n["return"]()}finally{if(i)throw l}}return a}}function g(e){if(Array.isArray(e))return e}function v(e,t,a,r,i,l,o){try{var n=e[l](o),s=n.value}catch(u){return void a(u)}n.done?t(s):Promise.resolve(s).then(r,i)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var l=e.apply(t,a);function o(e){v(l,r,i,o,n,"next",e)}function n(e){v(l,r,i,o,n,"throw",e)}o(void 0)}))}}var D={name:"labelGroupDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},visibleType:{type:String,default:""},width:{type:String,default:"600px"},isshow:Boolean,labelRroupInfo:{type:Object,default:function(){return{}}},confirm:Function},data:function(){return{isLoading:!1,inputValue:"",labelGroupFormData:{name:"",visible:"all",visible_organization:[],laberList:[{name:""}]},labelGroupFormDataRuls:{name:[{required:!0,message:"请输入标签组名称",trigger:"blur"}],visible_organization:[{required:!0,message:"请选择商户",trigger:"change"}]}}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},components:{OrganizationSelect:n["a"]},created:function(){"modify"===this.type&&(this.labelGroupFormData={name:this.labelRroupInfo.name,visible:this.labelRroupInfo.visible,visible_organization:this.labelRroupInfo.visible_organization,laberList:[]},this.labelRroupInfo.label_list.length&&(this.labelGroupFormData.laberList=this.labelRroupInfo.label_list.map((function(e){return{name:e.name}}))))},mounted:function(){},methods:{removeFormLaber:function(e){this.labelGroupFormData.laberList.splice(e,1)},addFormLaber:function(){this.labelGroupFormData.laberList.push({name:""})},riseClick:function(e){this.labelGroupFormData.laberList[e]=this.labelGroupFormData.laberList.splice(e-1,1,this.labelGroupFormData.laberList[e])[0]},declineClick:function(e){this.labelGroupFormData.laberList[e]=this.labelGroupFormData.laberList.splice(e+1,1,this.labelGroupFormData.laberList[e])[0]},getParams:function(){var e={name:this.labelGroupFormData.name,visible:this.labelGroupFormData.visible,label_list:this.labelGroupFormData.laberList,type:this.visibleType};if("part"===this.labelGroupFormData.visible&&(e.visible_organization=this.labelGroupFormData.visible_organization),this.labelGroupFormData.laberList&&this.labelGroupFormData.laberList.length)for(var t=0;t<this.labelGroupFormData.laberList.length;t++)if(!this.labelGroupFormData.laberList[t].name)return this.$message.error("请输入标签名称");this.getLabelGroup(e)},getLabelGroup:function(e){var t=this;return y(o.a.mark((function a(){var r,i,l,n,u,b,m,f;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t.isLoading=!0,r="",i=p(r,2),l=i[0],n=i[1],"add"!==t.type){a.next=12;break}return a.next=6,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddPost(e));case 6:u=a.sent,b=p(u,2),l=b[0],n=b[1],a.next=19;break;case 12:return a.next=15,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupModifyPost(c({id:t.labelRroupInfo.id},e)));case 15:m=a.sent,f=p(m,2),l=f[0],n=f[1];case 19:if(t.isLoading=!1,!l){a.next=23;break}return t.$message.error(l.message),a.abrupt("return");case 23:0===n.code?(t.visible=!1,t.confirm()):t.$message.error(n.msg);case 24:case"end":return a.stop()}}),a)})))()},handleChange:function(){},clickConfirmHandle:function(){var e=this;this.$refs.labelGroupFormDataRef.validate((function(t){t&&e.getParams()}))},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1}}},G=D,F=(a("05ce"),a("2877")),w=Object(F["a"])(G,r,i,!1,null,null,null);t["default"]=w.exports}}]);
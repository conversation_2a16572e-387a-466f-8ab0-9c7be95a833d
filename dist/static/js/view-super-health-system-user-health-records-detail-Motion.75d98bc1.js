(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-Motion"],{7256:function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"motion-wrapp records-wrapp-bg"},[e("div",{staticClass:"ps-flex-bw"},[e("div",[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("运动数据")]),e("span",{staticClass:"last-update-time p-l-20"},[t._v("更新时间："+t._s(t.formData.last_update_time))])])]),e("div",{staticClass:"ps-flex flex-wrap m-t-10"},[e("div",{staticClass:"motion-title m-r-10 m-b-10"},[t._m(0),e("div",{staticClass:"p-l-20"},[e("span",{staticClass:"number"},[t._v(t._s(t.formData.total_use_energy_kcal))]),e("span",{staticClass:"number"},[t._v("kacal")])])]),e("div",{staticClass:"motion-title m-r-10  m-b-10"},[t._m(1),e("div",{staticClass:"p-l-20"},[e("span",{staticClass:"number"},[t._v(t._s(t.formData.total_minute))])])]),e("div",{staticClass:"motion-title m-r-10  m-b-10"},[t._m(2),e("div",{staticClass:"p-l-20"},[e("span",{staticClass:"number"},[t._v(t._s(t.formData.total_max_minute))]),e("span",{staticClass:"number"},[t._v("分钟")])])]),e("div",{staticClass:"motion-title m-r-10  m-b-10"},[t._m(3),e("div",{staticClass:"p-l-20"},[e("span",{staticClass:"number"},[t._v(t._s(t.formData.total_count))]),e("span",{staticClass:"number"},[t._v("次")])])])]),e("div",[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"},{name:"tableLoadmore",rawName:"v-tableLoadmore",value:t.tableLoadmore,expression:"tableLoadmore"}],ref:"tableData",staticStyle:{width:"1200px"},attrs:{data:t.tableData.slice((t.currentPage-1)*t.pageSize,t.currentPage*t.pageSize),height:"288px",stripe:"","header-row-class-name":"ps-table-header-row"}},[e("el-table-column",{attrs:{type:"index",label:"序号",align:"center"}}),e("el-table-column",{attrs:{prop:"name",label:"运动名称",align:"center"}}),e("el-table-column",{attrs:{prop:"max_minute",label:"最高耗时",align:"center"}}),e("el-table-column",{attrs:{prop:"max_use_energy_kcal",label:"最高消耗热量",align:"center",width:"120px"}}),e("el-table-column",{attrs:{prop:"count",label:"累计次数",align:"center"}}),e("el-table-column",{attrs:{prop:"count_scale",label:"次数占比",align:"center"}}),e("el-table-column",{attrs:{prop:"use_energy_kcal",label:"累计消耗热量",align:"center",width:"120px"}}),e("el-table-column",{attrs:{prop:"use_energy_kcal_scale",label:"消耗占比",align:"center"}}),e("el-table-column",{attrs:{prop:"last_update_time",label:"最近一次记录",align:"center"}})],1)],1)])},l=[function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("累计消耗")])])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("累计时长")])])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("最高耗时")])])},function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[t._v("累计次数")])])}],n={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,formData:{},tableData:[],currentPage:1,pageSize:6,totalCount:0}},watch:{formInfoData:function(t){this.formData=t,this.tableData=this.formData.sport_list}},mounted:function(){},methods:{tableLoadmore:function(){var t=this;setTimeout((function(){t.pageSize+=10}),100)}}},i=n,r=(e("f82b"),e("2877")),c=Object(r["a"])(i,s,l,!1,null,"c44795ac",null);a["default"]=c.exports},e98c:function(t,a,e){},f82b:function(t,a,e){"use strict";var s=e("e98c"),l=e.n(s);l.a}}]);
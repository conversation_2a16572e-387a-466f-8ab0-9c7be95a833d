(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-AddOrEditMemberLevel"],{"45a2":function(e,r,t){"use strict";var s=t("e179"),m=t.n(s);m.a},"47da":function(e,r,t){"use strict";t.r(r);var s=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"AddOrEditMemberLevel container-wrapper"},[t("div",{staticClass:"table-wrapper"},[t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[e._v(e._s("add"===e.type?"新建":"编辑")+"会员等级")])]),t("div",[t("el-form",{ref:"memberFormRef",attrs:{model:e.memberForm,rules:e.memberFormRules,"label-width":"180px"}},[t("el-form-item",{attrs:{label:"会员等级："}},[e._v(e._s(e.memberForm.grade))]),t("el-form-item",{attrs:{label:"等级名称：",prop:"name"}},[t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"5"},model:{value:e.memberForm.name,callback:function(r){e.$set(e.memberForm,"name",r)},expression:"memberForm.name"}})],1),t("el-form-item",{attrs:{label:"会员等级成长分数区间：",prop:"growthScore"}},[e._v(" "+e._s(e.memberForm.startScore)+" ~ "),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.growthScore,callback:function(r){e.$set(e.memberForm,"growthScore",r)},expression:"memberForm.growthScore"}}),e._v(" 分 ")],1),"edit"===e.type?t("div",{staticClass:"tips"},[e._v("说明：成长分上限必须小于下一等级的成长分区间上限")]):e._e(),t("el-form-item",{attrs:{label:"购买/续费成长分数："}},[t("el-form-item",{staticClass:"m-b-10",attrs:{prop:"monthScore"}},[e._v(" 月卡（30天）"),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.monthScore,callback:function(r){e.$set(e.memberForm,"monthScore",r)},expression:"memberForm.monthScore"}}),e._v(" 分 ")],1),t("el-form-item",{staticClass:"m-b-10",attrs:{prop:"seasonScore"}},[e._v(" 季卡（90天）"),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.seasonScore,callback:function(r){e.$set(e.memberForm,"seasonScore",r)},expression:"memberForm.seasonScore"}}),e._v(" 分 ")],1),t("el-form-item",{staticClass:"m-b-10",attrs:{prop:"yearScore"}},[e._v(" 年卡（365天）"),t("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.memberForm.yearScore,callback:function(r){e.$set(e.memberForm,"yearScore",r)},expression:"memberForm.yearScore"}}),e._v(" 分 ")],1)],1),t("el-form-item",{attrs:{label:"会员权限：",prop:"memberPermission"}},[t("el-select",{staticClass:"ps-select w-250 m-l-5",attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",multiple:"","collapse-tags":""},on:{change:function(r){return e.changeMemberPermission()}},model:{value:e.memberForm.memberPermission,callback:function(r){e.$set(e.memberForm,"memberPermission",r)},expression:"memberForm.memberPermission"}},e._l(e.memberPermissionList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",[t("div",{staticClass:"label-list"},e._l(e.permissionNameList,(function(r,s){return t("div",{key:s,staticClass:"label-list-item"},[t("span",{staticClass:"m-r-5"},[e._v(e._s(r))]),t("i",{staticClass:"el-icon-close del-icon",on:{click:function(r){return e.delPermission(s)}}})])})),0)]),t("el-form-item",[t("el-button",{staticClass:"ps-origin-btn w-150",attrs:{size:"small",type:"primary"},on:{click:e.saveSetting}},[e._v("保存")])],1)],1)],1)])])},m=[],i=t("a34a"),o=t.n(i);function a(e,r,t,s,m,i,o){try{var a=e[i](o),n=a.value}catch(c){return void t(c)}a.done?r(n):Promise.resolve(n).then(s,m)}function n(e){return function(){var r=this,t=arguments;return new Promise((function(s,m){var i=e.apply(r,t);function o(e){a(i,s,m,o,n,"next",e)}function n(e){a(i,s,m,o,n,"throw",e)}o(void 0)}))}}var c={name:"AddOrEditMemberLevel",props:{},data:function(){var e=this,r=function(r,t,s){var m=/^\d+$/;if(""===t)return s(new Error("不能为空"));m.test(t)?t<e.memberForm.startScore?s(new Error("必须大于等于起始分")):e.maxScore&&t>=e.maxScore?s(new Error("必须小于下一等级成长分区间上限")):s():s(new Error("请输入正整数"))},t=function(e,r,t){var s=/^\d+$/;r&&!s.test(r)?t(new Error("请输入正整数")):t()};return{isLoading:!1,type:"",settingData:{},memberForm:{grade:1,name:"",startScore:0,growthScore:"",monthScore:"",seasonScore:"",yearScore:"",memberPermission:[]},memberFormRules:{name:[{required:!0,message:"请输入等级名称",trigger:"blur"}],growthScore:[{required:!0,validator:r,trigger:"blur"}],monthScore:[{validator:t,trigger:"blur"}],seasonScore:[{validator:t,trigger:"blur"}],yearScore:[{validator:t,trigger:"blur"}],memberPermission:[{required:!0,message:"请选择权限",trigger:"blur"}]},maxScore:null,memberPermissionList:[],permissionNameList:[]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.$route.params.type&&(this.type=this.$route.params.type),"add"===this.type?(this.memberForm.grade=this.$route.query.grade,this.memberForm.startScore=this.$route.query.startScore):(this.settingData=JSON.parse(decodeURIComponent(this.$route.query.data)),this.memberForm.grade=this.settingData.grade,this.memberForm.name=this.settingData.name,this.memberForm.startScore=this.settingData.start_growth_value,this.memberForm.growthScore=this.settingData.end_growth_value,this.memberForm.monthScore=this.settingData.month_growth_points,this.memberForm.seasonScore=this.settingData.season_growth_points,this.memberForm.yearScore=this.settingData.year_growth_points,this.memberForm.memberPermission=this.settingData.member_permissions_list.map((function(e){return e.id})),this.permissionNameList=this.settingData.member_permissions_list.map((function(e){return e.name})),this.maxScore=this.$route.query.maxScore),this.getMemberPermission()},saveSetting:function(){var e=this;this.$refs.memberFormRef.validate((function(r){if(r){var t,s={grade:e.memberForm.grade,name:e.memberForm.name,start_growth_value:e.memberForm.startScore,end_growth_value:e.memberForm.growthScore,member_permissions:e.memberForm.memberPermission};switch(e.memberForm.monthScore&&(s.month_growth_points=e.memberForm.monthScore),e.memberForm.seasonScore&&(s.season_growth_points=e.memberForm.seasonScore),e.memberForm.yearScore&&(s.year_growth_points=e.memberForm.yearScore),e.type){case"add":t=e.$apis.apiBackgroundMemberMemberGradeAddPost(s);break;case"edit":s.id=Number(e.settingData.id),t=e.$apis.apiBackgroundMemberMemberGradeModifyPost(s);break}e.confirmOperation(t)}}))},confirmOperation:function(e){var r=this;return n(o.a.mark((function t(){var s;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r.isLoading){t.next=2;break}return t.abrupt("return");case 2:return r.isLoading=!0,t.next=5,e;case 5:s=t.sent,r.isLoading=!1,0===s.code?(r.$message.success("成功"),r.$closeCurrentTab(r.$route.path)):r.$message.error(s.msg);case 8:case"end":return t.stop()}}),t)})))()},getMemberPermission:function(){var e=this;return n(o.a.mark((function r(){var t;return o.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,e.$apis.apiBackgroundMemberMemberPermissionListPost({page:1,page_size:99999});case 3:t=r.sent,e.isLoading=!1,0===t.code?e.memberPermissionList=t.data.results:e.$message.error(t.msg);case 6:case"end":return r.stop()}}),r)})))()},delPermission:function(e){this.memberForm.memberPermission.splice(e,1),this.permissionNameList.splice(e,1)},changeMemberPermission:function(){var e=this;this.permissionNameList=[],this.memberPermissionList.map((function(r){-1!==e.memberForm.memberPermission.indexOf(r.id)&&e.permissionNameList.push(r.name)}))}}},l=c,u=(t("45a2"),t("2877")),b=Object(u["a"])(l,s,m,!1,null,"73dd6e1a",null);r["default"]=b.exports},e179:function(e,r,t){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-BodyViewReport"],{"06fc":function(t,e,r){"use strict";var a=r("2fad"),n=r.n(a);n.a},"2fad":function(t,e,r){},9501:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"BodyViewReport"},[r("div",{staticClass:"table-wrapper"},[r("div",{staticClass:"table-content"},[r("div",{staticClass:"p-t-20"},[r("div",{staticClass:"title p-l-40"},[t._v("体检一体机")]),t._l(t.formData,(function(e,a,n){return r("div",{key:n,staticClass:"p-l-10 p-t-20"},[r("div",{staticClass:"title-content p-b-5"},[t._v(t._s(e.name))]),t._l(e.children,(function(e,a,n){return r("div",{key:n,staticClass:"content p-b-5"},[r("span",{staticClass:"text"},[t._v(t._s(e.name)+"：")]),r("span",[t._v(t._s(e.value)+" "+t._s(e.unit))])])}))],2)}))],2),r("div",{staticClass:"m-t-20 p-l-20"},[r("el-button",{staticClass:"ps-btn",attrs:{type:"primary",size:"small"},on:{click:t.handleExport}},[t._v(" 下载报告 ")])],1)])])])},n=[],i=r("a34a"),s=r.n(i),o=r("ed08"),c=r("f63a");function u(t,e){return v(t)||f(t,e)||d(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"===typeof t)return p(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}function f(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var r=[],a=!0,n=!1,i=void 0;try{for(var s,o=t[Symbol.iterator]();!(a=(s=o.next()).done);a=!0)if(r.push(s.value),e&&r.length===e)break}catch(c){n=!0,i=c}finally{try{a||null==o["return"]||o["return"]()}finally{if(n)throw i}}return r}}function v(t){if(Array.isArray(t))return t}function m(t,e,r,a,n,i,s){try{var o=t[i](s),c=o.value}catch(u){return void r(u)}o.done?e(c):Promise.resolve(c).then(a,n)}function y(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function s(t){m(i,a,n,s,o,"next",t)}function o(t){m(i,a,n,s,o,"throw",t)}s(void 0)}))}}var h={mixins:[c["a"]],data:function(){return{params:{},formData:{}}},created:function(){this.params=this.$route.query,this.getBodyDataDetail(this.params)},mounted:function(){},methods:{getBodyDataDetail:function(t){var e=this;return y(s.a.mark((function r(){var a,n,i,c;return s.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return e.isLoading=!0,r.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminHealthyInfoBodyDataDetailPost(t));case 3:if(a=r.sent,n=u(a,2),i=n[0],c=n[1],e.isLoading=!1,!i){r.next=11;break}return e.$message.error(i.message),r.abrupt("return");case 11:0===c.code?(e.formData=c.data.results,console.log(123)):e.$message.error(c.msg);case 12:case"end":return r.stop()}}),r)})))()},handleExport:function(){var t={type:"ExportHealthyInfoBodyData",params:{ids:this.params.id,type:this.params.type,user_id:this.params.user_id}};this.exportHandle(t)}}},b=h,w=(r("06fc"),r("2877")),g=Object(w["a"])(b,a,n,!1,null,"20958879",null);e["default"]=g.exports}}]);
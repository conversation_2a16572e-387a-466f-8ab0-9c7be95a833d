(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-HealthyLabel"],{6570:function(t,e,a){},d504:function(t,e,a){"use strict";var l=a("6570"),s=a.n(l);s.a},efcf:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"label-wrapp records-wrapp-bg m-r-20 m-b-20"},[a("div",{staticClass:"p-b-10",staticStyle:{"font-weight":"bold"}},[t._v("标签属性")]),a("div",{staticClass:"label-form"},[a("el-form",{ref:"form",attrs:{size:"mini",model:t.formData,"label-position":"left","label-width":"90px"}},t._l(t.labelList,(function(e,l){return a("el-form-item",{key:l,staticClass:"p-b-10",attrs:{label:e.name}},["ingredient_taboo"===e.key?a("div",t._l(e.label_name,(function(e,l){return a("el-tag",{key:l,staticClass:"m-r-10",attrs:{size:"small",effect:"plain",type:"warning",color:"#fff"}},[a("i",{staticClass:"el-icon-warning  ps-i"}),t._v(" "+t._s(e)+" ")])})),1):"taste"===e.key?a("div",t._l(e.label_name,(function(e,l){return a("el-tag",{key:l,staticClass:"m-r-10",attrs:{size:"small",effect:e.is_have?"plain":"dark",type:"info"}},[a("div",{style:{color:e.is_have?"":"#fff"}},[a("span",[t._v(t._s(e.name))]),e.count?a("span",[t._v("*"+t._s(e.count))]):t._e()])])})),1):a("div",t._l(e.label_name,(function(e,l){return a("el-tag",{key:l,staticClass:"m-r-10",attrs:{size:"small",effect:"plain",type:"info",color:"#fff"}},[t._v(" "+t._s(e)+" ")])})),1)])})),1)],1)])},s=[],n={props:{formInfoData:{type:Array,default:function(){return[]}}},data:function(){return{labelList:[],formData:{}}},watch:{formInfoData:function(t){var e=this;t.length&&(this.labelList=[],t.forEach((function(t){t.label_name.length&&e.labelList.push(t)})))}},mounted:function(){},methods:{}},i=n,r=(a("d504"),a("2877")),o=Object(r["a"])(i,l,s,!1,null,"540c5486",null);e["default"]=o.exports}}]);
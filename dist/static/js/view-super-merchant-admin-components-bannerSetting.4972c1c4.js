(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-bannerSetting"],{"0691":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"container-wrapper bannersetting"},[n("div",{staticClass:"l-title clearfix"},[n("span",{staticClass:"float-l min-title-h"},[e._v("首页轮播图")]),"detail"===e.formOperate?n("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:e.changeOperate}},[e._v("编辑")]):e._e()],1),n("div",{staticClass:"appid-box"},[n("el-form",{ref:"appidRef",attrs:{rules:e.formDataRuls,model:e.formData,size:"small","label-width":"100px"}},[n("el-form-item",{attrs:{label:"首页轮播",prop:"banner"}},[n("el-select",{staticClass:"ps-select w-300",attrs:{disabled:!e.checkIsFormStatus,multiple:!0},model:{value:e.formData.banner,callback:function(t){e.$set(e.formData,"banner",t)},expression:"formData.banner"}},e._l(e.bannerList,(function(e){return n("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),e.checkIsFormStatus?n("div",{staticClass:"add-wrapper"},[n("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.saveSettingHandle}},[e._v("保存")])],1):e._e()],1)])},r=[],i=n("a34a"),s=n.n(i),o=n("ed08");function c(e,t){return m(e)||f(e,t)||d(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"===typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function f(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],a=!0,r=!1,i=void 0;try{for(var s,o=e[Symbol.iterator]();!(a=(s=o.next()).done);a=!0)if(n.push(s.value),t&&n.length===t)break}catch(c){r=!0,i=c}finally{try{a||null==o["return"]||o["return"]()}finally{if(r)throw i}}return n}}function m(e){if(Array.isArray(e))return e}function p(e,t,n,a,r,i,s){try{var o=e[i](s),c=o.value}catch(u){return void n(u)}o.done?t(c):Promise.resolve(c).then(a,r)}function g(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var i=e.apply(t,n);function s(e){p(i,a,r,s,o,"next",e)}function o(e){p(i,a,r,s,o,"throw",e)}s(void 0)}))}}var b={name:"SuperBannerSetting",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},components:{},data:function(){return{formOperate:"detail",isLoading:!1,formData:{banner:[]},formDataRuls:{banner:[{required:!0,message:"请先选择",trigger:"blur"}]},bannerList:[]}},computed:{checkIsFormStatus:function(){var e=!1;switch(this.formOperate){case"detail":e=!1;break;case"modify":e=!0;break}return e}},watch:{},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getSelectBannerList()},refreshHandle:function(){this.initLoad()},searchHandle:Object(o["c"])((function(){}),300),changeOperate:function(){"modify"!==this.formOperate?this.formOperate="modify":this.formOperate="detail"},getBannerList:function(){var e=this;return g(s.a.mark((function t(){var n,a,r,i,o;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,n={org_id:e.organizationData.id,page:1,page_size:999999},t.next=4,e.$to(e.$apis.apiBackgroundAdminMarketingBannerListPost(n));case 4:if(a=t.sent,r=c(a,2),i=r[0],o=r[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===o.code&&(e.bannerList=o.data.results);case 13:case"end":return t.stop()}}),t)})))()},getSelectBannerList:function(){var e=this;return g(s.a.mark((function t(){var n,a,r,i,o;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,n={org_id:e.organizationData.id,page:1,page_size:999999},t.next=4,e.$to(e.$apis.apiBackgroundAdminMarketingBannerGetOrgBannerListPost(n));case 4:if(a=t.sent,r=c(a,2),i=r[0],o=r[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===o.code?(e.bannerList=o.data,o.data.map((function(t){t.is_select&&e.formData.banner.push(t.id)}))):e.$message.error(o.msg);case 13:case"end":return t.stop()}}),t)})))()},saveSettingHandle:function(){var e=this;return g(s.a.mark((function t(){return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:e.$refs.appidRef.validate((function(t){t&&e.modifySetting()}));case 3:case"end":return t.stop()}}),t)})))()},modifySetting:function(){var e=this;return g(s.a.mark((function t(){var n,a,r,i,u;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n={org_id:e.organizationData.id,banner_ids:e.formData.banner},e.isLoading=!0,t.next=4,Object(o["Q"])(e.$apis.apiBackgroundAdminMarketingBannerSetOrgsPost(n));case 4:if(a=t.sent,r=c(a,2),i=r[0],u=r[1],e.isLoading=!1,!i){t.next=12;break}return e.$message.error(i.message),t.abrupt("return");case 12:0===u.code?(e.$message.success("修改成功"),e.formOperate="detail",e.restoreHandle(e.type,e.formOperate)):e.$message.error(u.msg);case 13:case"end":return t.stop()}}),t)})))()}}},v=b,h=(n("a072"),n("2877")),w=Object(h["a"])(v,a,r,!1,null,null,null);t["default"]=w.exports},5361:function(e,t,n){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},a072:function(e,t,n){"use strict";var a=n("5361"),r=n.n(a);r.a}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-AddMerchantCommodityToSuper","view-super-health-system-health-nutrition-IngredientsCategory","view-super-health-system-health-nutrition-constants"],{"015b":function(e,l,t){"use strict";t.r(l),t.d(l,"DEFAULT_NUTRITION",(function(){return a})),t.d(l,"ELEMENT_NUTRITION",(function(){return n})),t.d(l,"VITAMIN_NUTRITION",(function(){return i})),t.d(l,"NUTRITION_LIST",(function(){return u})),t.d(l,"LIBRARY_SEARCH_SETTING_SUPER",(function(){return m})),t.d(l,"LIBRARY_SEARCH_SETTING_MERCHANT",(function(){return r})),t.d(l,"LIBRARY_SEARCH_SETTING_CATEORY",(function(){return s})),t.d(l,"COMMODITY_SEARCH_SETTING_SUPER",(function(){return y})),t.d(l,"COMMODITY_SEARCH_SETTING_MERCHANT",(function(){return d}));var a=[{name:"千卡",key:"energy_kcal",unit:"Kcal",type:"default"},{name:"碳水化合物",key:"carbohydrate",unit:"g",type:"default"},{name:"蛋白质",key:"protein",unit:"g",type:"default"},{name:"脂肪",key:"axunge",unit:"g",type:"default"}],n=[{key:"Ca",name:"钙",unit:"mg",type:"element"},{key:"P",name:"磷",unit:"mg",type:"element"},{key:"K",name:"钾",unit:"mg",type:"element"},{key:"Na",name:"钠",unit:"mg",type:"element"},{name:"镁",key:"Mg",unit:"mg",type:"element"},{key:"Fe",name:"铁",unit:"mg",type:"element"},{key:"I",name:"碘",unit:"μg",type:"element"},{key:"Se",name:"硒",unit:"μg",type:"element"},{key:"Zn",name:"锌",unit:"mg",type:"element"},{key:"Cu",name:"铜",unit:"mg",type:"element"},{key:"F",name:"氟",unit:"mg",type:"element"},{key:"Cr",name:"铬",unit:"μg",type:"element"},{key:"Mo",name:"钼",unit:"μg",type:"element"},{key:"Mn",name:"锰",unit:"mg",type:"element"}],i=[{key:"VA",name:"维生素A",unit:"μg",type:"vitamin"},{key:"VD",name:"维生素D",unit:"μg",type:"vitamin"},{key:"VE",name:"维生素E",unit:"mg",type:"vitamin"},{key:"VK",name:"维生素K",unit:"μg",type:"vitamin"},{key:"VB1",name:"维生素B1",unit:"mg",type:"vitamin"},{key:"VB2",name:"维生素B2",unit:"mg",type:"vitamin"},{key:"VB6",name:"维生素B6",unit:"mg",type:"vitamin"},{key:"VB12",name:"维生素B12",unit:"μg",type:"vitamin"},{key:"VC",name:"维生素C",unit:"mg",type:"vitamin"},{key:"VB5",name:"泛酸",unit:"mg",type:"vitamin"},{key:"VM",name:"叶酸",unit:"μg",type:"vitamin"},{key:"VB3",name:"烟酸",unit:"mg",type:"vitamin"},{key:"Choline",name:" 胆碱",unit:"mg",type:"vitamin"},{key:"Nicotinamide",name:"烟酰胺",unit:"mg",type:"vitamin"},{key:"VH",name:"生物素",unit:"mg",type:"vitamin"}],u=[].concat(a,n,i),m={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"",clearable:!0,value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},operator:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},r={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},is_repeat:{type:"select",label:"已有食材",value:"",placeholder:"请选择是否已有食材",dataList:[{label:"是",value:1},{label:"否",value:0}]},is_enable_nutrition:{type:"select",label:"营养信息",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"有",value:"1"},{label:"无",value:"0"}]},ingredient_name:{type:"input",label:"食材名称",value:"",placeholder:"请输入食材名称"},is_entering:{type:"select",label:"录入图片",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"已录入",value:"1"},{label:"未录入",value:"0"}]}},s={select_time:{type:"datetimerange",label:"修改时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},sort_id:{type:"treeselect",multiple:!1,flat:!1,label:"分类",value:null,placeholder:"请选择分类",dataList:[],limit:1,level:1,normalizer:function(e){return{id:e.level+"_"+e.id,label:e.name,children:e.sort_list}}},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"}},y={date_type:{type:"select",label:"",value:1,maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:1},{label:"修改时间",value:2}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},user_name:{type:"input",label:"操作人",value:"",placeholder:"请输入操作人"},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]},label_filter:{type:"select",label:"",value:"",placeholder:"",dataList:[{label:"请选择",value:"select",disabled:!0},{label:"包含",value:"Include"},{label:"不包含",value:"Exclude"}]},label_list:{type:"treeselect",label:"",value:[],placeholder:"选择标签",multiple:!0,limit:1,level:1,valueConsistsOf:"LEAF_PRIORITY",normalizer:function(e){return{id:e.id,label:e.name,children:e.label_list}},dataList:[]}},d={select_time:{type:"datetimerange",label:"创建时间",format:"yyyy-MM-dd HH:mm:ss",value:[]},food_name:{type:"input",label:"菜品/商品名称",value:"",placeholder:"请输入菜品/商品名称"},is_repeat:{type:"select",label:"已有菜品/商品",value:"",placeholder:"请选择",dataList:[{label:"是",value:1},{label:"否",value:0}]},attributes:{type:"select",label:"属性",value:"",placeholder:"请选择",dataList:[{label:"全部",value:""},{label:"商品",value:"goods"},{label:"菜品",value:"foods"}]}}},"3fa5":function(e,l,t){"use strict";t.d(l,"a",(function(){return n}));var a=t("5c96");function n(e,l){return new Promise((function(t,n){a["MessageBox"].confirm(e.content?e.content:"",e.title?e.title:"提示",{dangerouslyUseHTMLString:e.useHTML,distinguishCancelAndClose:!0,closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",confirmButtonText:e.confirmButtonText,cancelButtonText:e.cancelButtonText,center:""===e.center||e.center}).then((function(e){l?t(l()):t()})).catch((function(e){n(e)}))}))}}}]);
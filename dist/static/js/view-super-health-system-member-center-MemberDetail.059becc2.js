(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-MemberDetail"],{"0b79":function(e,t,a){},3948:function(e,t,a){"use strict";var s=a("0b79"),i=a.n(s);i.a},"526d":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"SuperMemberDetail container-wrapper"},[a("div",{staticClass:"table-wrapper"},[e._m(0),a("div",{staticClass:"user-info-wrapper"},[a("div",{staticClass:"user-img"},[a("el-avatar",{attrs:{size:120,fit:"cover",src:e.userInfo.headimgurl}})],1),a("div",{staticClass:"user-info-r"},[a("div",{staticClass:"float-l clearfixt"},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("姓名：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.nickname))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("性别：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.gender_alias))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("用户ID：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.user_id))])])]),a("div",{staticClass:"float-l"},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("来源渠道：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.source_alias))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("openid：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.openid))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("注册时间：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.create_time))])])]),a("div",{staticClass:"float-l"},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("微信号：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.nickname))])]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"label"},[e._v("手机号码：")]),a("div",{staticClass:"value"},[e._v(e._s(e.userInfo.phone))])]),a("div",{staticClass:"info-item"},[a("span",{staticStyle:{"margin-right":"20px"}},[a("span",{staticClass:"label"},[e._v("关联用户：")]),a("span",{staticClass:"value"},[e._v(e._s(e.userInfo.nickname))])]),a("span",[a("span",{staticClass:"label"},[e._v("关联企业:")]),a("span",{staticClass:"value"},[e._v(e._s(e.userInfo.company.length))])])])])])])]),a("div",{staticClass:"table-wrapper"},[e._m(1),a("div",{staticClass:"member-info-wrapper"},[a("div",{staticClass:"info-item"},[a("div",{staticClass:"title"},[e._v("会员标签：")]),a("div",{staticClass:"info-item-label"},e._l(e.userInfo.member_labels_list,(function(t){return a("div",{key:t.id,staticClass:"info-item-label-item"},[e._v(e._s(t.name))])})),0)]),a("div",{staticClass:"info-item"},[a("div",{staticClass:"title"},[e._v("会员信息：")]),a("ul",{staticClass:"member-info"},[a("li",{staticClass:"member-info-title"},[e._v("会员等级")]),a("li",[e._v(e._s(e.userInfo.member_grade_name))])]),a("ul",{staticClass:"member-info"},[a("li",{staticClass:"member-info-title"},[e._v("会员有效期")]),a("li",[e._v(e._s(e.userInfo.end_time))])]),a("ul",{staticClass:"member-info"},[a("li",{staticClass:"member-info-title"},[e._v("积分")]),a("li",[e._v(e._s(e.userInfo.integral))])])])])]),a("div",{staticClass:"table-wrapper"},[e._m(2),a("div",{staticClass:"table-content"},[a("el-radio-group",{staticClass:"ps-radio-btn m-b-20",attrs:{size:"mini"},on:{change:e.changTableType},model:{value:e.tableType,callback:function(t){e.tableType=t},expression:"tableType"}},[a("el-radio-button",{attrs:{label:"receive"}},[e._v("会员领取记录")]),a("el-radio-button",{attrs:{label:"growth"}},[e._v("会员等级成长记录")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},["receive"===e.tableType?[a("el-table-column",{attrs:{prop:"trade_no",label:"订单号",align:"center",width:"180"}}),a("el-table-column",{attrs:{prop:"receive_time",label:"领取时间",align:"center"}}),a("el-table-column",{attrs:{prop:"days",label:"领取天数",align:"center"}}),a("el-table-column",{attrs:{prop:"end_time",label:"到期时间",align:"center"}}),a("el-table-column",{attrs:{prop:"receive_type_alias",label:"领取方式",align:"center"}})]:e._e(),"growth"===e.tableType?[a("el-table-column",{attrs:{prop:"create_time",label:"获取时间",align:"center"}}),a("el-table-column",{attrs:{prop:"add_growth_value",label:"分值",align:"center"}}),a("el-table-column",{attrs:{prop:"obtain_type_alias",label:"获取方式",align:"center"}})]:e._e()],2)],1)])])},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("个人信息")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("会员信息")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("个人信息")])])}],r=a("a34a"),l=a.n(r),n=a("ed08");function c(e,t,a,s,i,r,l){try{var n=e[r](l),c=n.value}catch(o){return void a(o)}n.done?t(c):Promise.resolve(c).then(s,i)}function o(e){return function(){var t=this,a=arguments;return new Promise((function(s,i){var r=e.apply(t,a);function l(e){c(r,s,i,l,n,"next",e)}function n(e){c(r,s,i,l,n,"throw",e)}l(void 0)}))}}var v={name:"SuperMemberDetail",props:{},data:function(){return{userInfo:{},isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],tableType:"receive"}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.userInfo=JSON.parse(this.$route.query.data),"receive"===this.tableType?this.getMemberReceive():this.getMemberGradeGrowth()},changTableType:function(){this.currentPage=1,"receive"===this.tableType?this.getMemberReceive():this.getMemberGradeGrowth()},searchHandle:Object(n["c"])((function(){this.currentPage=1,"receive"===this.tableType?this.getMemberReceive():this.getMemberGradeGrowth()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},getMemberReceive:function(){var e=this;return o(l.a.mark((function t(){var a;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundMemberMemberReceiveListPost({user_id:e.userInfo.id,page:e.currentPage,page_size:e.pageSize});case 3:a=t.sent,e.isLoading=!1,0===a.code?(e.tableData=a.data.results,e.totalCount=a.data.count):e.$message.error(a.msg);case 6:case"end":return t.stop()}}),t)})))()},getMemberGradeGrowth:function(){var e=this;return o(l.a.mark((function t(){var a;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$apis.apiBackgroundMemberMemberGradeGrowthListPost({user_id:e.userInfo.id,page:e.currentPage,page_size:e.pageSize});case 3:a=t.sent,e.isLoading=!1,0===a.code?(e.tableData=a.data.results,e.totalCount=a.data.count):e.$message.error(a.msg);case 6:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,"receive"===this.tableType?this.getMemberReceive():this.getMemberGradeGrowth()},handleCurrentChange:function(e){this.currentPage=e,"receive"===this.tableType?this.getMemberReceive():this.getMemberGradeGrowth()}}},u=v,d=(a("3948"),a("2877")),b=Object(d["a"])(u,s,i,!1,null,"50bc79d2",null);t["default"]=b.exports}}]);
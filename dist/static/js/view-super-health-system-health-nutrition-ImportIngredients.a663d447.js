(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-nutrition-ImportIngredients"],{"04121":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("refresh-tool",{attrs:{title:t.title,"show-refresh":!1}}),i("import-page",{staticClass:"importPage",attrs:{initial:t.initial,url:t.url,"header-len":t.headerLen,"template-url":t.templateUrl}})],1)},r=[],a={name:"ImportIngredients",data:function(){return{type:"import",title:"批量导入食材",headerLen:1,initial:!0,url:"apiBackgroundAdminIngredientIngredientBatAddPost",templateUrl:"/api/temporary/template_excel/food_stock/super_ingredients.xlsx"}},computed:{},watch:{},created:function(){this.$route.params.type&&(this.type=this.$route.params.type),"import"===this.type?(this.title="批量导入食材",this.url="apiBackgroundAdminIngredientIngredientBatAddPost"):(this.title="导入编辑",this.url="apiBackgroundAdminIngredientIngredientBatModifyPost")},mounted:function(){},methods:{}},s=a,o=(i("8dde"),i("2877")),d=Object(o["a"])(s,n,r,!1,null,null,null);e["default"]=d.exports},"8dde":function(t,e,i){"use strict";var n=i("bc9b"),r=i.n(n);r.a},bc9b:function(t,e,i){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-components-selectLaber"],{"1a24":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("div",{staticClass:"healthTagDialog"},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入要查询的标签组/标签名称名称"},on:{input:e.searchHandle},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}}),a("el-card",{staticClass:"m-t-20",attrs:{shadow:"never"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v(" 已选 "),a("span",[e._v(e._s(e.selectLabelIdList.length))]),e._v(" 个标签 ")])]),e._l(e.tableData,(function(t,n){return a("div",{key:n},[a("el-collapse",{model:{value:e.activeLaberList,callback:function(t){e.activeLaberList=t},expression:"activeLaberList"}},[a("el-collapse-item",{attrs:{name:t.id}},[a("template",{slot:"title"},[a("span",[e._v(" "+e._s(t.name)+" "),a("span",[e._v("（"+e._s(t.label_list.length)+"）")])]),a("span",{staticClass:"tips-r"},[a("span",{staticClass:"open"},[e._v("展开")]),a("span",{staticClass:"close"},[e._v("收起")])])]),a("div",{staticClass:"ps-flex-align-c flex-align-c flex-wrap"},[t.inputVisible?a("el-input",{ref:"saveTagInput"+t.id,refInFor:!0,staticClass:"ps-input w-100  p-r-10 p-t-5",attrs:{size:"mini",autofocus:"",maxlength:"15"},on:{blur:function(a){return e.handleInputConfirm(t)}},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputConfirm(t)}},model:{value:t.inputValue,callback:function(a){e.$set(t,"inputValue",a)},expression:"item.inputValue"}}):a("button-icon",{staticClass:"p-t-5",attrs:{color:"origin",type:"add"},on:{click:function(a){return e.showInput(t)}}},[e._v(" 添加标签 ")]),a("div",{staticStyle:{flex:"1"}},[a("el-checkbox-group",{attrs:{size:"mini"},model:{value:e.selectLabelIdList,callback:function(t){e.selectLabelIdList=t},expression:"selectLabelIdList"}},e._l(t.label_list,(function(n,i){return a("el-checkbox-button",{key:i,attrs:{label:n.id,disabled:n.disabled},on:{change:function(a){return e.checkboxChangge(n,t)}}},[e._v(" "+e._s(n.name)+" ")])})),1)],1)],1)],2)],1)],1)}))],2)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 保存 ")])],1)])],2)},i=[],s=a("a34a"),l=a.n(s),r=a("ed08");function o(e,t){return b(e)||d(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function d(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],n=!0,i=!1,s=void 0;try{for(var l,r=e[Symbol.iterator]();!(n=(l=r.next()).done);n=!0)if(a.push(l.value),t&&a.length===t)break}catch(o){i=!0,s=o}finally{try{n||null==r["return"]||r["return"]()}finally{if(i)throw s}}return a}}function b(e){if(Array.isArray(e))return e}function f(e,t,a,n,i,s,l){try{var r=e[s](l),o=r.value}catch(c){return void a(c)}r.done?t(o):Promise.resolve(o).then(n,i)}function g(e){return function(){var t=this,a=arguments;return new Promise((function(n,i){var s=e.apply(t,a);function l(e){f(s,n,i,l,r,"next",e)}function r(e){f(s,n,i,l,r,"throw",e)}l(void 0)}))}}var h={name:"selectLaber",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:"选择标签"},width:{type:String,default:"600px"},isshow:Boolean,ruleSingleInfo:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,name:"",activeLaberList:[],selectLabelIdList:[],selectLabelListData:[],tableData:[],labelType:"",pageSize:3,totalCount:0,currentPage:1}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},created:function(){this.ruleSingleInfo.selectLabelIdList&&this.ruleSingleInfo.selectLabelIdList.length&&(this.selectLabelIdList=this.ruleSingleInfo.selectLabelIdList),this.ruleSingleInfo.selectLabelListData&&this.ruleSingleInfo.selectLabelListData.length&&(this.selectLabelListData=this.ruleSingleInfo.selectLabelListData),this.labelType=this.ruleSingleInfo.labelType,this.getLabelGroupList()},mounted:function(){},methods:{searchHandle:Object(r["c"])((function(){this.currentPage=1,this.getLabelGroupList()}),300),getLabelGroupList:function(){var e=this;return g(l.a.mark((function t(){var a,n,i,s,c;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a={type:e.labelType,page:e.currentPage,page_size:e.pageSize},e.name&&(a.name=e.name),t.next=5,Object(r["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupListPost(a));case 5:if(n=t.sent,i=o(n,2),s=i[0],c=i[1],e.isLoading=!1,!s){t.next=13;break}return e.$message.error(s.message),t.abrupt("return");case 13:0===c.code?(e.totalCount=c.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=c.data.results.map((function(t){return t.inputVisible=!1,t.inputValue="",t.label_list.forEach((function(a){a.label_group_name=t.name,e.ruleSingleInfo.selectLabelAllIds&&e.ruleSingleInfo.selectLabelAllIds.length&&e.ruleSingleInfo.selectLabelAllIds.includes(a.id)&&!e.selectLabelIdList.includes(a.id)?a.disabled=!0:a.disabled=!1})),e.activeLaberList.push(t.id),t})),console.log(e.tableData)):e.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},handleChange:function(){},checkboxChangge:function(e,t){var a=this,n=this.selectLabelIdList.indexOf(e.id);-1!==n?this.selectLabelListData.push(e):this.selectLabelListData.map((function(t,n){e.id===t.id&&a.selectLabelListData.splice(n,1)}))},handleInputConfirm:function(e){e.inputValue?this.getLabelGroupAddLabel(e):e.inputVisible=!1},showInput:function(e){var t=this;e.inputVisible=!0,this.$nextTick((function(a){t.$refs["saveTagInput"+e.id][0].$refs.input.focus()}))},getLabelGroupAddLabel:function(e){var t=this;return g(l.a.mark((function a(){var n,i,s,c;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(r["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupAddLabelPost({name:e.inputValue,label_group_id:e.id}));case 3:if(n=a.sent,i=o(n,2),s=i[0],c=i[1],t.isLoading=!1,!s){a.next=11;break}return t.$message.error(s.message),a.abrupt("return");case 11:0===c.code?(e.inputValue="",e.inputVisible=!1,t.getLabelGroupList()):t.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},clickConfirmHandle:function(){var e={selectLabelIdList:this.selectLabelIdList,selectLabelListData:this.selectLabelListData};this.$emit("selectLaberData",e),this.visible=!1},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.visible=!1},handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupList()}}},L=h,m=(a("fa5f"),a("2877")),v=Object(m["a"])(L,n,i,!1,null,null,null);t["default"]=v.exports},"7e85":function(e,t,a){},fa5f:function(e,t,a){"use strict";var n=a("7e85"),i=a.n(n);i.a}}]);
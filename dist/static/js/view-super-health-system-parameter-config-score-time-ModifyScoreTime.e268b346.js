(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-score-time-ModifyScoreTime"],{"0fab":function(t,e,r){"use strict";var a=r("f428"),i=r.n(a);i.a},"73ff":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"modify-scoreTime container-wrapper"},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[r("div",{staticClass:"table-wrapper"},[r("div",{staticClass:"table-header"},[r("div",{staticClass:"table-title"},[t._v("基本信息")])]),r("div",{staticStyle:{padding:"0 20px"}},[r("div",{staticClass:"font-size-20 p-b-20"},[t._v("计分时间段设置")]),r("div",{staticStyle:{"max-width":"620px",padding:"0 20px"}},[r("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[r("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入提示语",type:"textarea",autosize:{minRows:6,maxRows:10}},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.mealList,(function(e,a){return r("el-form-item",{key:e.id,staticClass:"block-label",attrs:{error:e.error}},[r("div",[t._v(t._s(e.text)+"：")]),a<4?r("el-time-picker",{attrs:{"is-range":"",format:"HH:mm:ss","value-format":"HH:mm:ss","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围",clearable:!1},on:{change:function(r){return t.changeTimePicker(r,e,a)}},model:{value:e.value,callback:function(r){t.$set(e,"value",r)},expression:"item.value"}}):t._e()],1)}))],2)])]),r("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[r("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),r("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"编辑")+" ")])],1)])],1)},i=[],n=r("a34a"),o=r.n(n),s=r("ed08");function c(t,e){return d(t)||f(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return m(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?m(t,e):void 0}}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}function f(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var r=[],a=!0,i=!1,n=void 0;try{for(var o,s=t[Symbol.iterator]();!(a=(o=s.next()).done);a=!0)if(r.push(o.value),e&&r.length===e)break}catch(c){i=!0,n=c}finally{try{a||null==s["return"]||s["return"]()}finally{if(i)throw n}}return r}}function d(t){if(Array.isArray(t))return t}function p(t,e,r,a,i,n,o){try{var s=t[n](o),c=s.value}catch(l){return void r(l)}s.done?e(c):Promise.resolve(c).then(a,i)}function v(t){return function(){var e=this,r=arguments;return new Promise((function(a,i){var n=t.apply(e,r);function o(t){p(n,a,i,o,s,"next",t)}function s(t){p(n,a,i,o,s,"throw",t)}o(void 0)}))}}var h={name:"SuperAddEditArticle",data:function(){return{isLoading:!1,type:"add",formData:{tips:""},formRuls:{tips:[{required:!0,message:"请输入文章标题",trigger:"blur"}]},timeOverlay:!1,mealList:[{value:["00:00:00","09:59:00"],text:"早餐",id:"breakfast",error:"",mealStartTime:"",mealEndTime:""},{value:["10:00:00","15:59:00"],text:"午餐",id:"lunch",error:"",mealStartTime:"",mealEndTime:""},{value:["16:00:00","19:59:00"],text:"晚餐",id:"dinner",error:"",mealStartTime:"",mealEndTime:""}]}},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){this.formData.tips=this.$route.query.tips;var t=this.$decodeQuery(this.$route.query.data);this.mealList.map((function(e,r){var a="",i="";a=t[r].start,i=t[r].end;try{e.value=[a,i]}catch(n){}return e}))}},searchHandle:Object(s["c"])((function(){this.currentPage=1}),300),changeTimePicker:function(){var t=this;this.timeOverlay=!1,this.mealList.forEach((function(t){t.error=""})),this.mealList.forEach((function(e,r){if(t.mealList[r+1]){var a=t.compareWidth(e.value[0],e.value[1],t.mealList[r+1].value[0]);1===a&&(t.mealList[r].error="餐段时间重叠，请检查！",t.mealList[r+1].error="餐段时间重叠，请检查！",t.timeOverlay=!0)}}))},addModifyArticle:function(){var t=this;return v(o.a.mark((function e(){var r,a,i,n,l,u,m;return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r={tips:t.formData.tips},t.mealList.forEach((function(t){r[t.id+"_start"]=t.value[0],r[t.id+"_end"]=t.value[1]})),!t.timeOverlay){e.next=4;break}return e.abrupt("return");case 4:if(t.isLoading=!0,a="",i=c(a,2),n=i[0],l=i[1],"modify"!==t.type){e.next=14;break}return e.next=10,Object(s["Q"])(t.$apis.apiBackgroundAdminHealthyInfoHealthyMealTimeModifyPost(r));case 10:u=e.sent,m=c(u,2),n=m[0],l=m[1];case 14:if(t.isLoading=!1,!n){e.next=18;break}return t.$message.error(n.message),e.abrupt("return");case 18:0===l.code?(t.$message.success(l.msg),t.$closeCurrentTab(t.$route.path)):t.$message.error(l.msg);case 19:case"end":return e.stop()}}),e)})))()},compareWidth:function(t,e,r){var a="2022/03/03",i=1e3,n=new Date("".concat(a," ").concat(t)).getTime(),o=new Date("".concat(a," ").concat(e)).getTime(),s=new Date("".concat(a," ").concat(r)).getTime();s<n&&s<o&&o>n&&(s+=864e5);var c=s-o;return c<i?1:c>i?-1:0},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifyArticle(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,a){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||a()}}).then((function(t){})).catch((function(t){}))}}},y=h,b=(r("0fab"),r("2877")),g=Object(b["a"])(y,a,i,!1,null,null,null);e["default"]=g.exports},f428:function(t,e,r){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-device-admin-components-DeviceDialog"],{"70e5":function(e,t,i){"use strict";var a=i("deae"),o=i.n(a);o.a},deae:function(e,t,i){},e899:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,showFooter:e.showFooter,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("el-form",{ref:"deviceForm",staticClass:"dialog-form",attrs:{model:e.deviceForm,"status-icon":"",rules:e.deviceFormRules,"label-width":"125px",inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},["add"===e.type?a("div",[a("el-form-item",{attrs:{label:"激活码：",prop:"activationCode"}},[a("div",{staticClass:"code-form-item-wrapper"},[a("el-input",{staticStyle:{width:"260px"},attrs:{disabled:""},model:{value:e.deviceForm.activationCode,callback:function(t){e.$set(e.deviceForm,"activationCode",t)},expression:"deviceForm.activationCode"}}),a("el-button",{staticClass:"code-btn-wrapper",attrs:{type:"primary"},on:{click:e.getActivationCode}},[e._v("生成")])],1)]),a("el-form-item",{attrs:{label:"设备名："}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入设备名",maxlength:"32"},model:{value:e.deviceForm.deviceName,callback:function(t){e.$set(e.deviceForm,"deviceName",t)},expression:"deviceForm.deviceName"}})],1),a("el-form-item",{attrs:{label:"所属组织：",prop:"organization"}},[a("organization-select",{staticClass:"search-item-w ps-input w-350",attrs:{placeholder:"请选择所属组织",isLazy:!1,multiple:!1,"check-strictly":!0,role:"super","append-to-body":!0,filterable:!1},model:{value:e.deviceForm.organization,callback:function(t){e.$set(e.deviceForm,"organization",t)},expression:"deviceForm.organization"}})],1),a("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[a("el-select",{staticClass:"ps-select w-350",attrs:{placeholder:"请选择设备类型","popper-class":"ps-popper-select"},on:{change:e.deviceTypeChange},model:{value:e.deviceForm.deviceType,callback:function(t){e.$set(e.deviceForm,"deviceType",t)},expression:"deviceForm.deviceType"}},e._l(e.deviceTypeList,(function(e){return a("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1),"RLZJ"===e.deviceForm.deviceType?a("div",[a("el-form-item",{attrs:{label:"水控模式",prop:"shuikong"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.shuikong,callback:function(t){e.$set(e.deviceForm,"shuikong",t)},expression:"deviceForm.shuikong"}})],1),a("el-form-item",{attrs:{label:"考勤功能",prop:"attendance"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.attendance,callback:function(t){e.$set(e.deviceForm,"attendance",t)},expression:"deviceForm.attendance"}})],1),a("el-form-item",{attrs:{label:"健康码识别功能",prop:"healthyCode"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.code,callback:function(t){e.$set(e.deviceForm,"code",t)},expression:"deviceForm.code"}})],1),a("el-form-item",{attrs:{label:"体温检测",prop:"temperature"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.temperature,callback:function(t){e.$set(e.deviceForm,"temperature",t)},expression:"deviceForm.temperature"}})],1),a("el-form-item",{attrs:{label:"门禁功能",prop:"control"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.deviceForm.control,callback:function(t){e.$set(e.deviceForm,"control",t)},expression:"deviceForm.control"}})],1)],1):e._e(),a("el-form-item",{attrs:{label:"设备型号：",prop:"deviceModel"}},[a("el-select",{staticClass:"ps-select w-350",attrs:{placeholder:"请选择设备型号","popper-class":"ps-popper-select"},model:{value:e.deviceForm.deviceModel,callback:function(t){e.$set(e.deviceForm,"deviceModel",t)},expression:"deviceForm.deviceModel"}},e._l(e.deviceModelList,(function(e){return a("el-option",{key:e.key,attrs:{label:e.key,value:e.key}})})),1)],1),"PS-C1050-1"===e.deviceForm.deviceModel||"PS-C1050"===e.deviceForm.deviceModel?a("div",[a("el-form-item",{attrs:{label:"传感器版本-秤",prop:"balanceSensorVersion"}},[a("el-radio-group",{staticClass:"ps-radio",model:{value:e.deviceForm.balanceSensorVersion,callback:function(t){e.$set(e.deviceForm,"balanceSensorVersion",t)},expression:"deviceForm.balanceSensorVersion"}},[a("el-radio",{attrs:{label:"v1"}},[e._v("v1版本")]),a("el-radio",{attrs:{label:"v2"}},[e._v("v2版本")])],1)],1),a("el-form-item",{attrs:{label:"传感器版本-托盘",prop:"traySensorVersion"}},[a("el-radio-group",{staticClass:"ps-radio",model:{value:e.deviceForm.traySensorVersion,callback:function(t){e.$set(e.deviceForm,"traySensorVersion",t)},expression:"deviceForm.traySensorVersion"}},[a("el-radio",{attrs:{label:"v1"}},[e._v("v1版本")]),a("el-radio",{attrs:{label:"v2"}},[e._v("v2版本")]),a("el-radio",{attrs:{label:"v3"}},[e._v("v3版本")])],1)],1),a("el-form-item",{attrs:{label:"离线人脸激活码"}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入"},model:{value:e.deviceForm.offlineFaceActivationCode,callback:function(t){e.$set(e.deviceForm,"offlineFaceActivationCode",t)},expression:"deviceForm.offlineFaceActivationCode"}})],1)],1):e._e(),"QCG"===e.deviceForm.deviceType?a("div",[a("el-form-item",{attrs:{label:"格子",required:""}},e._l(e.deviceForm.cupboardCeil,(function(t,o){return a("el-form-item",{key:"option"+o,class:[o>0?"m-t-25":"","cupboard-ceil-form"],attrs:{prop:"cupboardCeil["+o+"]",rules:e.deviceFormRules.cupboardCeil}},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入格子数"},model:{value:e.deviceForm.cupboardCeil[o],callback:function(t){e.$set(e.deviceForm.cupboardCeil,o,e._n(t))},expression:"deviceForm.cupboardCeil[index]"}}),a("img",{attrs:{src:i("a851"),alt:""},on:{click:function(t){return e.addCupboardCeil()}}}),e.deviceForm.cupboardCeil.length>1?a("img",{attrs:{src:i("1597"),alt:""},on:{click:function(t){return e.delCupboardCeil(o)}}}):e._e()],1)})),1),a("el-form-item",{attrs:{label:"appid：",prop:"cupboardAppid"}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入appid"},model:{value:e.deviceForm.cupboardAppid,callback:function(t){e.$set(e.deviceForm,"cupboardAppid",t)},expression:"deviceForm.cupboardAppid"}})],1),a("el-form-item",{attrs:{label:"deviceName：",prop:"cupboardDeviceName"}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入deviceName"},model:{value:e.deviceForm.cupboardDeviceName,callback:function(t){e.$set(e.deviceForm,"cupboardDeviceName",t)},expression:"deviceForm.cupboardDeviceName"}})],1),a("el-form-item",{attrs:{label:"deviceSecret：",prop:"cupboardDeviceSecret"}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入deviceSecret"},model:{value:e.deviceForm.cupboardDeviceSecret,callback:function(t){e.$set(e.deviceForm,"cupboardDeviceSecret",t)},expression:"deviceForm.cupboardDeviceSecret"}})],1),a("el-form-item",{attrs:{label:"serialport：",prop:"cupboardSerialport"}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入serialport（串口号）"},model:{value:e.deviceForm.cupboardSerialport,callback:function(t){e.$set(e.deviceForm,"cupboardSerialport",t)},expression:"deviceForm.cupboardSerialport"}})],1),a("el-form-item",{attrs:{label:"服务器地址：",prop:"cupboardUrl"}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入服务器地址"},model:{value:e.deviceForm.cupboardUrl,callback:function(t){e.$set(e.deviceForm,"cupboardUrl",t)},expression:"deviceForm.cupboardUrl"}})],1)],1):e._e(),a("el-form-item",{attrs:{label:"SN码：",prop:"SNCode"}},[a("el-input",{staticClass:"ps-input w-350",attrs:{placeholder:"请输入SN码"},model:{value:e.deviceForm.SNCode,callback:function(t){e.$set(e.deviceForm,"SNCode",t)},expression:"deviceForm.SNCode"}})],1),a("el-form-item",{attrs:{label:"有效期：",prop:"validityDate"}},[a("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"生效时间","end-placeholder":"失效时间","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:""},model:{value:e.deviceForm.validityDate,callback:function(t){e.$set(e.deviceForm,"validityDate",t)},expression:"deviceForm.validityDate"}})],1)],1):e._e(),"detail"===e.type?a("div",{staticClass:"detail"},[a("el-form-item",{attrs:{label:"所属消费点："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.consumer_name))])]),e._e(),a("el-form-item",{attrs:{label:"设备名："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.device_name))])]),a("el-form-item",{attrs:{label:"设备类型："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.device_type_alias))])]),a("el-form-item",{attrs:{label:"设备型号："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.deviceInfo.device_model_alias))])]),"QCG"===e.deviceInfo.device_type?a("div",[a("el-form-item",{attrs:{label:"deviceName："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.deviceName))])]),a("el-form-item",{attrs:{label:"appid："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.appId))])]),a("el-form-item",{attrs:{label:"deviceSecret："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.deviceSecret))])]),a("el-form-item",{attrs:{label:"串口号："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.serialport))])]),a("el-form-item",{attrs:{label:"柜子数量："}},[a("span",{staticClass:"detail__span"},[e._v(e._s(e.cupboardJson.ceil_list))])])],1):e._e(),"RLZJ"===e.deviceInfo.device_type?a("div",[a("el-form-item",{attrs:{label:"水控模式:"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.shuikong,callback:function(t){e.$set(e.zjJson,"shuikong",t)},expression:"zjJson.shuikong"}})],1),a("el-form-item",{attrs:{label:"考勤功能:"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.attendance,callback:function(t){e.$set(e.zjJson,"attendance",t)},expression:"zjJson.attendance"}})],1),a("el-form-item",{attrs:{label:"健康码识别功能:"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.healthyCode,callback:function(t){e.$set(e.zjJson,"healthyCode",t)},expression:"zjJson.healthyCode"}})],1),a("el-form-item",{attrs:{label:"体温检测:"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.temperature,callback:function(t){e.$set(e.zjJson,"temperature",t)},expression:"zjJson.temperature"}})],1),a("el-form-item",{attrs:{label:"门禁功能:"}},[a("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.zjJson.control,callback:function(t){e.$set(e.zjJson,"control",t)},expression:"zjJson.control"}})],1)],1):e._e()],1):e._e(),"getlog"===e.type?a("div",[a("el-form-item",{attrs:{label:"拉取日期",prop:"logDate"}},[a("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间","picker-options":e.logPickerOptions,"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd"},model:{value:e.deviceForm.logDate,callback:function(t){e.$set(e.deviceForm,"logDate",t)},expression:"deviceForm.logDate"}})],1)],1):e._e(),"effective"===e.type||"mul_activate_time"===e.type?a("div",[a("el-form-item",{attrs:{label:"有效期：",prop:"validityDate"}},[a("el-date-picker",{staticClass:"ps-poper-picker",attrs:{type:"daterange",align:"left","unlink-panels":"","range-separator":"至","start-placeholder":"生效时间","end-placeholder":"失效时间","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",clearable:""},model:{value:e.deviceForm.validityDate,callback:function(t){e.$set(e.deviceForm,"validityDate",t)},expression:"deviceForm.validityDate"}})],1)],1):e._e(),"name"===e.type?a("div",[a("el-form-item",{attrs:{label:"设备名：",prop:"deviceName"}},[a("el-input",{staticClass:"ps-input w-250",attrs:{placeholder:"请输入设备名",maxlength:"32"},model:{value:e.deviceForm.deviceName,callback:function(t){e.$set(e.deviceForm,"deviceName",t)},expression:"deviceForm.deviceName"}})],1)],1):e._e(),"code"===e.type?a("div",{staticClass:"code-box"},[a("div",{staticClass:"code"},[a("qrcode",{staticClass:"face-img",attrs:{value:"QCG"===e.deviceInfo.device_type?e.deviceInfo.cupboard_json:e.deviceInfo.activation_code,options:{width:280},margin:10,alt:""}}),a("div",{staticClass:"code-info-box"},[a("p",[e._v("设备名："+e._s(e.deviceInfo.device_name))]),a("p",[e._v("设备类型："+e._s(e.deviceInfo.device_type_alias))]),a("p",[e._v("所属组织："+e._s(e.deviceInfo.consumer_name))])])],1)]):e._e(),"weigh"===e.type?a("div",[a("el-form-item",{attrs:{label:"请选择所属商户",prop:"companyId"}},[a("company-select",{staticClass:"search-item-w ps-select",attrs:{clearable:!0,filterable:!0,options:e.companyOptions},on:{getselect:e.changeWeighHandle},model:{value:e.deviceForm.companyId,callback:function(t){e.$set(e.deviceForm,"companyId",t)},expression:"deviceForm.companyId"}})],1),a("div",{staticClass:"origin-text"},[e._v("该激活码支持该商户所有组织使用，仅适用激活双屏结算秤/点餐机“称重模块”")])],1):e._e()]),a("template",{slot:"tool"},[e.showFooter?a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),"weigh"===e.type?a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 生成并下载 ")]):a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1):e._e()]),a("div",{staticClass:"weigh-code-wrapper"},[a("div",{ref:"weighCodeRef",staticClass:"weigh-code p-t-20"},[e.selectWeighCompany.name?a("div",[e._v(e._s(e.selectWeighCompany.name)+"-称重模块激活码")]):e._e(),e.weighCode?a("qrcode",{staticClass:"face-img",attrs:{value:e.weighCode,options:e.weighOption,margin:5,alt:""}}):e._e()],1)])],2)},o=[],r=i("a34a"),c=i.n(r),n=i("cbfb"),s=i("b2e5"),l=i.n(s),d=i("6e71"),p=i("c0e9"),v=i.n(p),m=i("21a6"),u=i.n(m);function f(e,t,i,a,o,r,c){try{var n=e[r](c),s=n.value}catch(l){return void i(l)}n.done?t(s):Promise.resolve(s).then(a,o)}function g(e){return function(){var t=this,i=arguments;return new Promise((function(a,o){var r=e.apply(t,i);function c(e){f(r,a,o,c,n,"next",e)}function n(e){f(r,a,o,c,n,"throw",e)}c(void 0)}))}}var b={name:"trayDialog",components:{OrganizationSelect:n["a"],qrcode:l.a,CompanySelect:d["a"]},props:{loading:Boolean,isshow:Boolean,showFooter:{type:Boolean,default:!0},type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"600px"},deviceInfo:{type:Object,default:function(){return{}}},organizationList:{type:Array,default:function(){return[]}},deviceTypeList:{type:Array,default:function(){return[]}},selectList:{type:Array,default:function(){return[]}},minTime:{type:[Number,String],default:""},confirm:Function},data:function(){var e=function(e,t,i){t.length?t[1].split("-")[2]-t[0].split("-")[2]>7?i(new Error("您所选的日期超过7天，请重新选择")):i():i(new Error("请选择日期"))};return{isLoading:!1,pickerOptions:{shortcuts:[{text:"一个月",onClick:function(e){var t=new Date,i=new Date;t.setTime(t.getTime()+2592e6),e.$emit("pick",[i,t])}},{text:"三个月",onClick:function(e){var t=new Date,i=new Date;t.setTime(t.getTime()+7776e6),e.$emit("pick",[i,t])}},{text:"一年",onClick:function(e){var t=new Date,i=new Date;t.setTime(t.getTime()+31536e6),e.$emit("pick",[i,t])}}]},logPickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()},shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,i=new Date;i.setTime(i.getTime()-5184e5),e.$emit("pick",[i,t])}}]},deviceForm:{activationCode:"",organization:"",deviceName:"",deviceType:"",deviceModel:"",SNCode:"",validityDate:[],logDate:[],cupboardCeil:[32],cupboardAppid:"",cupboardDeviceName:"",cupboardDeviceSecret:"",cupboardSerialport:"",cupboardUrl:"",shuikong:!1,attendance:!1,healthyCode:!1,temperature:!1,control:!1,balanceSensorVersion:"v1",traySensorVersion:"v1",offlineFaceActivationCode:""},deviceFormRules:{deviceName:[{required:!0,message:"请输入设备名",trigger:"blur"}],organization:[{required:!0,message:"请选择组织",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"change"}],deviceModel:[{required:!0,message:"请选择设备型号",trigger:"change"}],logDate:[{required:!0,validator:e,trigger:"blur"}],validityDate:[{required:!0,message:"请选择有效期",trigger:"change"}],cupboardCeil:[{required:!0,message:"格子数不能为空"},{type:"number",message:"格子数必须为数字值"}],cupboardAppid:[{required:!0,message:"请输入appid",trigger:"blur"}],cupboardDeviceName:[{required:!0,message:"请输入deviceName",trigger:"blur"}],cupboardDeviceSecret:[{required:!0,message:"请输入deviceSecret",trigger:"blur"}],cupboardSerialport:[{required:!0,message:"请输入serialport（串口号）",trigger:"blur"}],cupboardUrl:[{required:!0,message:"请输入服务器地址",trigger:"blur"}]},deviceModelList:[],cupboardJson:{},zjJson:{shuikong:!1,attendance:!1,healthyCode:!1,temperature:!1,control:!1},companyOptions:{label:"name",value:"company"},selectWeighCompany:{},weighCode:"",weighOption:{errorCorrectionLevel:"H",width:280}}},computed:{visible:{get:function(){return"add"===this.type&&this.isshow&&this.getActivationCode(),this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(e){var t=this;if(this.visible){if(this.weighCode="",this.selectWeighCompany={},"name"===this.type&&(this.deviceForm.deviceName=this.deviceInfo.device_name),this.deviceInfo.cupboard_json&&(this.cupboardJson=JSON.parse(this.deviceInfo.cupboard_json)),this.deviceInfo.zj_json){var i=JSON.parse(this.deviceInfo.zj_json);this.zjJson.shuikong=!!i.sk_on,this.zjJson.attendance=!!i.kq_on,this.zjJson.healthyCode=!!i.jkm_on,this.zjJson.temperature=!!i.tw_on,this.zjJson.control=!!i.mj_on}e&&(this.deviceForm.validityDate=this.getNextYear()),"mul_activate_time"===this.type&&(this.pickerOptions.disabledDate=function(e){return t.minTime>e.getTime()})}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getActivationCode()},clickConfirmHandle:function(){var e=this;this.$refs.deviceForm.validate((function(t){var i;if(t)switch(e.type){case"add":i={activation_code:e.deviceForm.activationCode,device_name:e.deviceForm.deviceName,organization_id:e.deviceForm.organization,device_type:e.deviceForm.deviceType,device_model:e.deviceForm.deviceModel,start_date:e.deviceForm.validityDate[0],end_date:e.deviceForm.validityDate[1],balance_sensor_version:e.deviceForm.balanceSensorVersion,tray_sensor_version:e.deviceForm.traySensorVersion},"QCG"===e.deviceForm.deviceType&&(i.cupboard_json={appId:e.deviceForm.cupboardAppid,deviceName:e.deviceForm.cupboardDeviceName,deviceSecret:e.deviceForm.cupboardDeviceSecret,serialport:e.deviceForm.cupboardSerialport,url:e.deviceForm.cupboardUrl,ceil_list:e.deviceForm.cupboardCeil,activation_code:e.deviceForm.activationCode}),"RLZJ"===e.deviceForm.deviceType&&(i.zj_json={sk_on:e.deviceForm.shuikong?1:0,kq_on:e.deviceForm.attendance?1:0,jkm_on:e.deviceForm.healthyCode?1:0,tw_on:e.deviceForm.temperature?1:0,mj_on:e.deviceForm.control?1:0}),e.deviceForm.SNCode&&(i.serial_no=e.deviceForm.SNCode),e.deviceForm.offlineFaceActivationCode&&(i.offline_face_activation_code=e.deviceForm.offlineFaceActivationCode),e.addDevice(i);break;case"detail":"RLZJ"===e.deviceInfo.device_type&&(i={device_no:e.deviceInfo.device_no,zj_json:{sk_on:e.zjJson.shuikong?1:0,kq_on:e.zjJson.attendance?1:0,jkm_on:e.zjJson.healthyCode?1:0,tw_on:e.zjJson.temperature?1:0,mj_on:e.zjJson.control?1:0}},e.modifyDevice(i));break;case"name":i={device_no:e.deviceInfo.device_no,device_name:e.deviceForm.deviceName},e.modifyDevice(i);break;case"effective":i={device_no:e.deviceInfo.device_no,time_range:{start_date:e.deviceForm.validityDate[0],end_date:e.deviceForm.validityDate[1]}},e.modifyDevice(i);break;case"mul_activate_time":i={device_nos:e.selectList,choices:6,valid_day:{start_time:e.deviceForm.validityDate[0]+" 00:00:00",end_time:e.deviceForm.validityDate[1]+" 23:59:59"}},e.modifyMulDevice(i);break;case"getlog":i={device_no:e.deviceInfo.device_no,start_date:e.deviceForm.logDate[0],end_date:e.deviceForm.logDate[1]},e.deviceLogPull(i);break;case"weigh":i={company_id:e.deviceForm.companyId},e.addWeighCode(i);break}}))},addDevice:function(e){var t=this;return g(c.a.mark((function i(){var a;return c.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceAddPost(e);case 5:a=i.sent,t.isLoading=!1,0===a.code?(t.$message.success("新建成功"),t.confirm()):t.$message.error(a.msg);case 8:case"end":return i.stop()}}),i)})))()},modifyMulDevice:function(e){var t=this;return g(c.a.mark((function i(){var a;return c.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceBatchModifyPost(e);case 5:a=i.sent,t.isLoading=!1,0===a.code?(t.$message.success("修改成功"),t.confirm()):t.$message.error(a.msg);case 8:case"end":return i.stop()}}),i)})))()},modifyDevice:function(e){var t=this;return g(c.a.mark((function i(){var a;return c.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceModifyPost(e);case 5:a=i.sent,t.isLoading=!1,0===a.code?(t.$message.success("修改成功"),t.confirm()):t.$message.error(a.msg);case 8:case"end":return i.stop()}}),i)})))()},deviceLogPull:function(e){var t=this;return g(c.a.mark((function i(){var a;return c.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,t.$apis.apiBackgroundAdminDevicePullLogPost(e);case 2:a=i.sent,0===a.code?(t.$message.success("拉取日志成功"),t.confirm()):t.$message.error(a.msg);case 4:case"end":return i.stop()}}),i)})))()},addWeighCode:function(e){var t=this;return g(c.a.mark((function i(){var a;return c.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,t.$apis.apiBackgroundAdminDeviceGenerateActiveCodePost(e);case 5:a=i.sent,0===a.code?(t.weighCode=a.data,t.$message.success(a.msg),t.$nextTick((function(e){t.downWeighCode()}))):(t.isLoading=!1,t.$message.error(a.msg));case 7:case"end":return i.stop()}}),i)})))()},changeWeighHandle:function(e){this.selectWeighCompany=e.item},downWeighCode:function(){var e=this,t=this.$refs.weighCodeRef;v()(t).then((function(t){var i=t.toDataURL();u.a.saveAs(i,e.selectWeighCompany.name+"-称重模块激活码.png"),e.confirm()})),this.isLoading=!1},clickCancleHandle:function(){this.visible=!1,this.deviceForm.deviceName=""},handleClose:function(e){this.$refs.deviceForm.resetFields(),this.isLoading=!1,this.visible=!1,this.deviceForm.deviceName=""},normalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}},getActivationCode:function(){var e=this;return g(c.a.mark((function t(){var i;return c.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundAdminDeviceGenerateActivationPost();case 2:i=t.sent,0===i.code?e.deviceForm.activationCode=i.data.activation_code:e.$message.error(i.msg);case 4:case"end":return t.stop()}}),t)})))()},deviceTypeChange:function(){this.deviceForm.deviceModel="",this.getDeviceModel()},getDeviceModel:function(){var e=this;return g(c.a.mark((function t(){var i;return c.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundAdminDeviceDeviceModelPost({device_type:e.deviceForm.deviceType});case 2:i=t.sent,0===i.code?e.deviceModelList=i.data:e.$message.error(i.msg);case 4:case"end":return t.stop()}}),t)})))()},getNextYear:function(){var e,t;"mul_activate_time"===this.type?(console.log(11112332323),e=this.minTime+31536e6,t=[new Date(this.minTime).getFullYear(),(new Date(this.minTime).getMonth()+1).toString().padStart(2,"0"),new Date(this.minTime).getDate().toString().padStart(2,"0")].join("-")):(e=(new Date).getTime()+31536e6,t=[(new Date).getFullYear(),((new Date).getMonth()+1).toString().padStart(2,"0"),(new Date).getDate().toString().padStart(2,"0")].join("-"));var i=[new Date(e).getFullYear(),(new Date(e).getMonth()+1).toString().padStart(2,"0"),new Date(e).getDate().toString().padStart(2,"0")].join("-");return[t,i]},addCupboardCeil:function(){this.deviceForm.cupboardCeil.push("")},delCupboardCeil:function(e){this.deviceForm.cupboardCeil.splice(e,1)}}},h=b,_=(i("70e5"),i("2877")),y=Object(_["a"])(h,a,o,!1,null,"53de9d19",null);t["default"]=y.exports}}]);
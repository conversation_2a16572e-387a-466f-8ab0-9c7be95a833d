(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-rule-AddModifyHealthFractionRule","view-super-health-system-parameter-config-health-rule-components-BmiConfig","view-super-health-system-parameter-config-health-rule-components-EnergyConfig","view-super-health-system-parameter-config-health-rule-components-FoodDiversityConfig","view-super-health-system-parameter-config-health-rule-components-NutritionConfig","view-super-health-system-parameter-config-health-rule-components-SportConfig"],{"0eab":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"energy-config"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[a("template",{slot:"append"},[t._v("分")])],2)],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),a("div",{staticClass:"form-content-box m-b-20"},[a("div",[t._v("变量值")]),t._l(t.formData.listText,(function(e,s){return a("div",{key:s,staticClass:"p-t-10"},[t._v(" "+t._s(e.text)+" "),a("span",{staticStyle:{color:"red"}},[t._v(t._s(e.tips))])])})),a("div",{staticClass:"p-t-10 p-b-10 flex-between"},[a("div",[t._v("规则配置")]),t.disabled?t._e():a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),a("div",{staticClass:"ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),a("div",t._l(t.formData.config,(function(e,s){return a("div",{key:s,staticClass:"ps-flex-align-c flex-wrap"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_one,callback:function(a){t.$set(e,"comparison_one",a)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.comparison_one_score,callback:function(a){t.$set(e,"comparison_one_score",a)},expression:"contentItem.comparison_one_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_two,callback:function(a){t.$set(e,"comparison_two",a)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.comparison_two_score,callback:function(a){t.$set(e,"comparison_two_score",a)},expression:"contentItem.comparison_two_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.operation,callback:function(a){t.$set(e,"operation",a)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.operation_score,callback:function(a){t.$set(e,"operation_score",a)},expression:"contentItem.operation_score"}})],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),a("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(s)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)],1)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",listText:[{text:"x千卡",tips:"（用户每餐摄入量）"},{text:"y千卡",tips:"（查表法计算得出每餐建议摄入量）"}],config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data,console.log(this.type)},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type};a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:t.formData.config},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,s){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||s()}}).then((function(t){})).catch((function(t){}))}}},l=i,r=(a("68f0"),a("2877")),n=Object(r["a"])(l,s,o,!1,null,"49e09af8",null);e["default"]=n.exports},"0f73":function(t,e,a){},"12d5":function(t,e,a){},"140d":function(t,e,a){},"68f0":function(t,e,a){"use strict";var s=a("6d19"),o=a.n(s);o.a},"6d19":function(t,e,a){},"77f9":function(t,e,a){},"78a0":function(t,e,a){"use strict";var s=a("12d5"),o=a.n(s);o.a},"86fa":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bmi-config"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[a("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,placeholder:"请输入维度分值"},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[a("template",{slot:"append"},[t._v("分")])],2)],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[a("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语"},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),a("div",{staticClass:"form-content-box m-b-20"},[a("div",[t._v("变量值")]),t._l(t.formData.listText,(function(e,s){return a("div",{key:s,staticClass:"p-t-10"},[t._v(" "+t._s(e.text)+" "),a("span",{staticStyle:{color:"red"}},[t._v(t._s(e.tips))])])})),a("div",{staticClass:"p-t-10 p-b-10 flex-between"},[a("div",[t._v("规则配置")]),t.disabled?t._e():a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),a("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),a("div",{staticClass:"ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),a("div",t._l(t.formData.config,(function(e,s){return a("div",{key:s,staticClass:"ps-flex-align-c flex-wrap"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_one,callback:function(a){t.$set(e,"comparison_one",a)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{disabled:t.disabled,"show-word-limit":""},model:{value:e.comparison_one_score,callback:function(a){t.$set(e,"comparison_one_score",a)},expression:"contentItem.comparison_one_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_two,callback:function(a){t.$set(e,"comparison_two",a)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.comparison_two_score,callback:function(a){t.$set(e,"comparison_two_score",a)},expression:"contentItem.comparison_two_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.operation,callback:function(a){t.$set(e,"operation",a)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.operation_score,callback:function(a){t.$set(e,"operation_score",a)},expression:"contentItem.operation_score"}})],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),a("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(s)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)],1)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",listText:[{text:"x BMI",tips:"（此值代表人体BMI）"}],config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data,console.log(this.type)},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type};a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:t.formData.config},console.log(a),t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,s){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||s()}}).then((function(t){})).catch((function(t){}))}}},l=i,r=(a("9a9b"),a("2877")),n=Object(r["a"])(l,s,o,!1,null,"5a446438",null);e["default"]=n.exports},"8d88":function(t,e,a){"use strict";var s=a("140d"),o=a.n(s);o.a},"9a9b":function(t,e,a){"use strict";var s=a("77f9"),o=a.n(s);o.a},a947:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"sport-config"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[a("template",{slot:"append"},[t._v("分")])],2)],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),a("div",{staticClass:"form-content-box m-b-20"},[a("div",[t._v("变量值")]),a("div",{staticClass:"p-t-10"},[a("span",[t._v("x千卡")]),a("span",{staticStyle:{color:"red"}},[t._v("（用户每天运动量）")])]),a("div",{staticClass:"p-t-10 ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("y")]),a("el-form-item",{attrs:{label:""}},[a("el-input",{staticStyle:{width:"150px"},attrs:{disabled:t.disabled,"show-word-limit":""},model:{value:t.formData.kcal,callback:function(e){t.$set(t.formData,"kcal",e)},expression:"formData.kcal"}},[a("template",{slot:"append"},[t._v("千卡")])],2)],1),a("span",{staticClass:"p-r-10 p-t-5",staticStyle:{color:"red"}},[t._v("（用户每天运动量）")])],1),a("div",{staticClass:"p-t-10 p-b-10 flex-between"},[a("div",[t._v("规则配置")]),t.disabled?t._e():a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),a("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),a("div",{staticClass:"ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),a("div",t._l(t.formData.config,(function(e,s){return a("div",{key:s,staticClass:"ps-flex-align-c flex-wrap"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_one,callback:function(a){t.$set(e,"comparison_one",a)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.comparison_one_score,callback:function(a){t.$set(e,"comparison_one_score",a)},expression:"contentItem.comparison_one_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_two,callback:function(a){t.$set(e,"comparison_two",a)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.comparison_two_score,callback:function(a){t.$set(e,"comparison_two_score",a)},expression:"contentItem.comparison_two_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.operation,callback:function(a){t.$set(e,"operation",a)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.operation_score,callback:function(a){t.$set(e,"operation_score",a)},expression:"contentItem.operation_score"}})],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),a("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(s)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])])],1)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",kcal:"",config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data,console.log(this.type)},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type};a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,kcal:t.formData.kcal,config:t.formData.config},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,s){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||s()}}).then((function(t){})).catch((function(t){}))}}},l=i,r=(a("de7f"),a("2877")),n=Object(r["a"])(l,s,o,!1,null,"963d4e68",null);e["default"]=n.exports},cfb2:function(t,e,a){},d8bf:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"super_add_modify_health_fraction_rule container-wrapper"},["food"===t.dataType?a("food-diversity-config",{attrs:{data:t.data,type:t.type,disabled:t.disabled},on:{submitHandler:t.submitHandler}}):t._e(),"sport"===t.dataType?a("sport-config",{attrs:{disabled:t.disabled,data:t.data,type:t.type},on:{submitHandler:t.submitHandler}}):t._e(),"bmi"===t.dataType?a("bmi-config",{attrs:{disabled:t.disabled,data:t.data,type:t.type},on:{submitHandler:t.submitHandler}}):t._e(),"energy"===t.dataType?a("energy-config",{attrs:{disabled:t.disabled,data:t.data,type:t.type},on:{submitHandler:t.submitHandler}}):t._e(),"nutrition"===t.dataType?a("nutrition-config",{attrs:{data:t.data,type:t.type,disabled:t.disabled},on:{submitHandler:t.submitHandler}}):t._e()],1)},o=[],i=a("a34a"),l=a.n(i),r=a("86fa"),n=a("e989"),c=a("a947"),p=a("0eab"),d=a("dfaf"),u=a("ed08");function m(t,e){return g(t)||_(t,e)||b(t,e)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(t,e){if(t){if("string"===typeof t)return v(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?v(t,e):void 0}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,s=new Array(e);a<e;a++)s[a]=t[a];return s}function _(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],s=!0,o=!1,i=void 0;try{for(var l,r=t[Symbol.iterator]();!(s=(l=r.next()).done);s=!0)if(a.push(l.value),e&&a.length===e)break}catch(n){o=!0,i=n}finally{try{s||null==r["return"]||r["return"]()}finally{if(o)throw i}}return a}}function g(t){if(Array.isArray(t))return t}function h(t,e,a,s,o,i,l){try{var r=t[i](l),n=r.value}catch(c){return void a(c)}r.done?e(n):Promise.resolve(n).then(s,o)}function x(t){return function(){var e=this,a=arguments;return new Promise((function(s,o){var i=t.apply(e,a);function l(t){h(i,s,o,l,r,"next",t)}function r(t){h(i,s,o,l,r,"throw",t)}l(void 0)}))}}var y={components:{foodDiversityConfig:n["default"],nutritionConfig:d["default"],sportConfig:c["default"],bmiConfig:r["default"],energyConfig:p["default"]},data:function(){return{type:"",dataType:"",disabled:!1,data:{}}},created:function(){this.type=this.$route.query.type,this.dataType=this.$route.query.dataType,Number(this.$route.query.disabled)?this.disabled=!0:this.disabled=!1,this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.data=this.$decodeQuery(this.$route.query.data),console.log(33333,this.data)},setHealthyModify:function(t){var e=this;return x(l.a.mark((function a(){var s,o,i,r;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(u["Q"])(e.$apis.apiBackgroundAdminHealthyInfoHealthyModifyPost(t));case 2:if(s=a.sent,o=m(s,2),i=o[0],r=o[1],!i){a.next=9;break}return e.$message.error(i.message),a.abrupt("return");case 9:0===r.code?(e.$message.success(r.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(r.msg);case 10:case"end":return a.stop()}}),a)})))()},submitHandler:function(t){if("see"===this.type)return this.$closeCurrentTab(this.$route.path);this.setHealthyModify(t)}}},C=y,w=(a("e2b1"),a("2877")),k=Object(w["a"])(C,s,o,!1,null,"849bb19a",null);e["default"]=k.exports},de7f:function(t,e,a){"use strict";var s=a("0f73"),o=a.n(s);o.a},dfaf:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"nutrition"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[a("template",{slot:"append"},[t._v("分")])],2)],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.formData.config,(function(e,s,o){return a("div",{key:o,staticClass:"form-content-box m-b-20"},[a("div",[t._v("变量值")]),t._l(e.listText,(function(e,s){return a("div",{key:s,staticClass:"p-t-10"},[t._v(" "+t._s(e.text)+" "),a("span",{staticStyle:{color:"red"}},[t._v(t._s(e.tips))])])})),a("div",{staticClass:"p-t-10 p-b-10 flex-between"},[a("div",[t._v("规则配置")]),t.disabled?t._e():a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(a){return t.addRule(e,s)}}},[t._v(" 新增规则 ")])],1)]),a("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),a("div",{staticClass:"ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),a("div",t._l(e.content,(function(o,i){return a("div",{key:i,staticClass:"ps-flex-align-c flex-wrap"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v(t._s(e.unitText))]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:o.comparison_one,callback:function(e){t.$set(o,"comparison_one",e)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:o.comparison_one_score,callback:function(e){t.$set(o,"comparison_one_score",e)},expression:"contentItem.comparison_one_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:o.comparison_two,callback:function(e){t.$set(o,"comparison_two",e)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:o.comparison_two_score,callback:function(e){t.$set(o,"comparison_two_score",e)},expression:"contentItem.comparison_two_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:o.operation,callback:function(e){t.$set(o,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:o.operation_score,callback:function(e){t.$set(o,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),a("div",{staticClass:"m-b-30"},[e.content.length>1&&!t.disabled?a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(o,s,i)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)}))],2)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",config:{protein:{listText:[{text:"x克",tips:"（用户每餐蛋白质摄入量）"},{text:"y克",tips:"（查表法计算得出用户每餐蛋白质推荐摄入量）"}],unitText:"x完成度在y正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},carbohydrate:{listText:[{text:"x1克",tips:"（用户每餐碳水化合物摄入量）"},{text:"y1克",tips:"（查表法计算得出用户每餐碳水化合物推荐摄入量）"}],unitText:"x1完成度在y1正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},axunge:{listText:[{text:"x2克",tips:"（用户每餐脂肪摄入量）"},{text:"y2克/餐",tips:"（查表法计算得出用户每餐脂肪推荐摄入量）"}],unitText:"x2完成度在y2正负",content:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]}}},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){for(var t in console.log(this.data),this.formData.score=this.data.score,this.formData.tips=this.data.tips,this.formData.type=this.data.type,this.data.config)this.formData.config[t].content=this.data.config[t]},addRule:function(t,e){console.log(this.formData.config[e]),this.formData.config[e].content.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t,e,a){this.formData.config[e].content.splice(a,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type},s={};for(var o in t.formData.config)s[o]=t.formData.config[o].content;a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:s},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,s){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||s()}}).then((function(t){})).catch((function(t){}))}}},l=i,r=(a("8d88"),a("2877")),n=Object(r["a"])(l,s,o,!1,null,"12e29f82",null);e["default"]=n.exports},e2b1:function(t,e,a){"use strict";var s=a("cfb2"),o=a.n(s);o.a},e989:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"food-diversity"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[a("el-input",{staticClass:"ps-input",attrs:{disabled:t.disabled,placeholder:"请输入维度分值"},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[a("template",{slot:"append"},[t._v("分")])],2)],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),t._l(t.formData.config,(function(e,s,o){return a("div",{key:o,staticClass:"form-content-box m-b-20"},[a("div",[t._v("变量值")]),t._l(e.listText,(function(e,s){return a("div",{key:s,staticClass:"p-t-10"},[t._v(" "+t._s(e.text)+" "),a("span",{staticStyle:{color:"red"}},[t._v(t._s(e.tips))])])})),a("div",{staticClass:"p-t-10 p-b-10 flex-between"},[a("div",[t._v("规则配置")]),t.disabled?t._e():a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(a){return t.addRule(e,s)}}},[t._v(" 新增规则 ")])],1)]),a("div",{staticClass:"ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),a("div",t._l(e.content,(function(o,i){return a("div",{key:i,staticClass:"ps-flex-align-c flex-wrap"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v(t._s(e.unitText))]),a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".comparison",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:o.comparison,callback:function(e){t.$set(o,"comparison",e)},expression:"contentItem.comparison"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".comparison_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:o.comparison_score,callback:function(e){t.$set(o,"comparison_score",e)},expression:"contentItem.comparison_score"}})],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("执行")]),a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:o.operation,callback:function(e){t.$set(o,"operation",e)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".content."+i+".operation_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:o.operation_score,callback:function(e){t.$set(o,"operation_score",e)},expression:"contentItem.operation_score"}})],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),a("div",{staticClass:"m-b-30"},[e.content.length>1&&!t.disabled?a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(o,s,i)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])],2)}))],2)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],i={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",config:{diversity:{listText:[{text:"x种",tips:"（用户每餐食物多样摄入种类）"}],unitText:"x",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},cereals:{listText:[{text:"x1克/餐",tips:"（用户每餐谷物摄入量）"},{text:"y1克/餐",tips:"（查表法计算得出每餐谷物推荐摄入量）"}],unitText:"x1正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},eggsandmeat:{listText:[{text:"x2克/餐",tips:"（用户每餐鱼禽蛋肉摄入量）"},{text:"y2克/餐",tips:"（查表法计算得出用户每餐鱼禽蛋肉摄入量）"}],unitText:"x2正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""}]},fruit:{listText:[{text:"x3克/餐",tips:"（用户每餐水果摄入量）"},{text:"y3克/餐",tips:"（查表计算得出用户每餐水果摄入量）"}],unitText:"x3正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]},vegetable:{listText:[{text:"x4克/餐",tips:"（用户每餐蔬菜摄入量）"},{text:"y4克/餐",tips:"（查表计算得出用户每餐蔬菜摄入量）"}],unitText:"x4正负",content:[{comparison:"",comparison_score:"",operation:"",operation_score:""},{comparison:"",comparison_score:"",operation:"",operation_score:""}]}}},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){for(var t in this.formData.score=this.data.score,this.formData.tips=this.data.tips,this.formData.type=this.data.type,this.data.config)this.formData.config[t].content=this.data.config[t];console.log(this.data)},addRule:function(t,e){console.log(this.formData.config[e]),this.formData.config[e].content.push({comparison:"",comparison_score:"",operation:"",operation_score:""})},removeRule:function(t,e,a){this.formData.config[e].content.splice(a,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type},s={};for(var o in t.formData.config)s[o]=t.formData.config[o].content;a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,config:s},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,s){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||s()}}).then((function(t){})).catch((function(t){}))}}},l=i,r=(a("78a0"),a("2877")),n=Object(r["a"])(l,s,o,!1,null,"cfeeba88",null);e["default"]=n.exports}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-msgSetting"],{4053:function(e,t,n){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},9497:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"container-wrapper msgSetting"},[n("el-radio-group",{staticClass:"ps-radio-btn m-t-20",attrs:{size:"mini",prop:"couponType"},model:{value:e.senderType,callback:function(t){e.senderType=t},expression:"senderType"}},e._l(e.templateList,(function(t){return n("el-radio-button",{key:t.sender_type,attrs:{label:t.sender_type}},[e._v(e._s(t.sender_type_alias))])})),1),e.dataLoading?n("div",{staticClass:"m-b-20 m-t-20"},[n("el-switch",{attrs:{"active-color":"#ff9b45","inactive-color":"#ffcda2"},model:{value:e.formData[e.senderType].common.enable,callback:function(t){e.$set(e.formData[e.senderType].common,"enable",t)},expression:"formData[senderType].common.enable"}})],1):e._e(),e.dataLoading?n("el-form",{ref:"msgSetting",attrs:{rules:e.formDataRuls,model:e.formData,size:"small"}},[n("el-form-item",{attrs:{label:"适用组织",prop:"orgIds","label-width":"70px"}},[n("organization-select",{staticClass:"ps-input w-250",attrs:{placeholder:"请选择适用组织",isLazy:!1,multiple:!0,"check-strictly":!0,"append-to-body":!0,company:e.organizationData.company,role:"super"},model:{value:e.formData[e.senderType].organizations,callback:function(t){e.$set(e.formData[e.senderType],"organizations",t)},expression:"formData[senderType].organizations"}})],1),e._l(e.formSettingList,(function(t){return["common"===t.setting_type?n("div",{key:t.setting_type,staticClass:"l-title"},[e._v(e._s(t.setting_type_alias))]):n("div",{key:t.setting_type,staticClass:"checkbox-title"},[n("el-checkbox",{staticClass:"ps-checkbox m-r-10",model:{value:e.formData[e.senderType][t.setting_type].enable,callback:function(n){e.$set(e.formData[e.senderType][t.setting_type],"enable",n)},expression:"formData[senderType][tempItem.setting_type].enable"}}),e._v(" "+e._s(t.setting_type_alias)+" ")],1),e._l(t.template,(function(a){return[n("el-form-item",{key:a.key+t.setting_type,staticClass:"m-l-25",attrs:{prop:a.key,label:a.name}},[a.type&&"input"!==a.type?e._e():n("el-input",{staticClass:"ps-input w-250",attrs:{size:"small",type:a.type},model:{value:e.formData[e.senderType][t.setting_type][a.key],callback:function(n){e.$set(e.formData[e.senderType][t.setting_type],a.key,n)},expression:"formData[senderType][tempItem.setting_type][item.key]"}}),"textarea"===a.type?n("el-input",{staticClass:"ps-input w-250",attrs:{size:"small",type:"textarea",rows:3},model:{value:e.formData[e.senderType][t.setting_type][a.key],callback:function(n){e.$set(e.formData[e.senderType][t.setting_type],a.key,n)},expression:"formData[senderType][tempItem.setting_type][item.key]"}}):e._e(),"select"===a.type?n("el-select",{ref:"forRef",refInFor:!0,staticClass:"search-item-w ps-input w-250",attrs:{size:"small",placeholder:""},model:{value:e.formData[e.senderType][t.setting_type][a.key],callback:function(n){e.$set(e.formData[e.senderType][t.setting_type],a.key,n)},expression:"formData[senderType][tempItem.setting_type][item.key]"}},e._l(a.value,(function(e){return n("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1):e._e(),"radio"===a.type?n("el-radio-group",{staticClass:"ps-radio",model:{value:e.formData[e.senderType][t.setting_type][a.key],callback:function(n){e.$set(e.formData[e.senderType][t.setting_type],a.key,n)},expression:"formData[senderType][tempItem.setting_type][item.key]"}},e._l(a.value,(function(t){return n("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.name))])})),1):e._e(),"checkbox"===a.type?n("el-checkbox-group",{model:{value:e.formData[e.senderType][t.setting_type][a.key],callback:function(n){e.$set(e.formData[e.senderType][t.setting_type],a.key,n)},expression:"formData[senderType][tempItem.setting_type][item.key]"}},e._l(a.value,(function(t){return n("el-checkbox",{key:t.value,staticClass:"ps-checkbox",attrs:{label:t.value}},[e._v(e._s(t.name))])})),1):e._e(),"number"===a.type?n("el-input-number",{staticClass:"ps-input-number",attrs:{"step-strictly":"",label:""},model:{value:e.formData[e.senderType][t.setting_type][a.key],callback:function(n){e.$set(e.formData[e.senderType][t.setting_type],a.key,n)},expression:"formData[senderType][tempItem.setting_type][item.key]"}}):e._e(),a.help_text?n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.help_text,placement:"top-start"}},[n("i",{staticClass:"el-icon-info"})]):e._e()],1)]}))]}))],2):e._e(),n("div",{staticClass:"add-wrapper"},[n("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.saveSettingHandle}},[e._v("保存")])],1)],1)},r=[],s=n("a34a"),i=n.n(s),o=n("ed08"),l=n("cbfb"),p=n("bbd5");function c(e,t){return f(e)||d(e,t)||m(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?y(e,t):void 0}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}function d(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],a=!0,r=!1,s=void 0;try{for(var i,o=e[Symbol.iterator]();!(a=(i=o.next()).done);a=!0)if(n.push(i.value),t&&n.length===t)break}catch(l){r=!0,s=l}finally{try{a||null==o["return"]||o["return"]()}finally{if(r)throw s}}return n}}function f(e){if(Array.isArray(e))return e}function g(e,t,n,a,r,s,i){try{var o=e[s](i),l=o.value}catch(p){return void n(p)}o.done?t(l):Promise.resolve(l).then(a,r)}function b(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var s=e.apply(t,n);function i(e){g(s,a,r,i,o,"next",e)}function o(e){g(s,a,r,i,o,"throw",e)}i(void 0)}))}}var v={name:"SuperMsgSetting",components:{OrganizationSelect:l["a"]},props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{isLoading:!1,dataLoading:!1,templateList:[],settingList:[],senderType:"abc",formData:{},formDataRuls:{}}},created:function(){},mounted:function(){this.initLoad()},computed:{formSettingList:function(){var e=this,t=this.templateList.filter((function(t){return t.sender_type===e.senderType}));return t.length?t[0].settings_template:[]}},methods:{initLoad:function(){this.getMessagestemplateList()},refreshHandle:function(){this.initLoad()},getMessagestemplateList:function(){var e=this;return b(i.a.mark((function t(){var n,a,r,s;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,e.$to(e.$apis.apiBackgroundAdminThirdMessagesSettingsTemplateListPost());case 3:if(n=t.sent,a=c(n,2),r=a[0],s=a[1],e.isLoading=!1,!r){t.next=11;break}return e.$message.error(r.message),t.abrupt("return");case 11:0===s.code&&(e.templateList=s.data,e.templateList.length&&(e.senderType=e.templateList[0].sender_type),e.getMessagesSettingsList());case 12:case"end":return t.stop()}}),t)})))()},getMessagesSettingsList:function(){var e=this;return b(i.a.mark((function t(){var n,a,r,s,o;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,n={company:e.organizationData.company},t.next=4,e.$to(e.$apis.apiBackgroundAdminThirdMessagesSettingsListPost(n));case 4:if(a=t.sent,r=c(a,2),s=r[0],o=r[1],e.isLoading=!1,!s){t.next=12;break}return e.$message.error(s.message),t.abrupt("return");case 12:0===o.code&&(e.settingList=o.data,e.loadFormData());case 13:case"end":return t.stop()}}),t)})))()},loadFormData:function(){var e=this;this.templateList.length&&(this.templateList.map((function(t){var n=e.settingList.filter((function(e){return null!==e.id&&e.sender_type===t.sender_type}));e.$set(e.formData,t.sender_type,{}),e.$set(e.formData[t.sender_type],"organizations",n.length?n[0].organizations:[]),t.settings_template.map((function(a){var r,s;if(e.$set(e.formData[t.sender_type],a.setting_type,{}),a.template){var i=(null===(r=n[0])||void 0===r||null===(s=r.event_msg_config)||void 0===s?void 0:s[a.setting_type])?n[0].event_msg_config[a.setting_type]:{};a.template.map((function(n){if("checkbox"===n.type){var r=JSON.parse(n.default).map((function(e){return String(e)}));e.$set(e.formData[t.sender_type][a.setting_type],n.key,i[n.key]?i[n.key]:r)}else e.$set(e.formData[t.sender_type][a.setting_type],n.key,i[n.key]?i[n.key]:n.default)})),e.$set(e.formData[t.sender_type][a.setting_type],"enable",!!i.enable&&i.enable)}}))})),this.dataLoading=!0)},saveSettingHandle:function(){var e=this;return b(i.a.mark((function t(){return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:e.$refs.msgSetting.validate((function(t){t&&e.modifySetting()}));case 3:case"end":return t.stop()}}),t)})))()},modifySetting:function(){var e=this;return b(i.a.mark((function t(){var n,a,r,s,l,u;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=Object(p["a"])(e.formData[e.senderType]),delete n.organizations,a={sender_type:e.senderType,organizations:e.formData[e.senderType].organizations,event_msg_config:n,company_id:e.organizationData.company},e.isLoading=!0,t.next=6,Object(o["Q"])(e.$apis.apiBackgroundAdminThirdMessagesSettingsModifyPost(a));case 6:if(r=t.sent,s=c(r,2),l=s[0],u=s[1],e.isLoading=!1,!l){t.next=14;break}return e.$message.error(l.message),t.abrupt("return");case 14:0===u.code?(e.$message.success("修改成功"),e.formOperate="detail",e.restoreHandle(e.type,e.formOperate)):e.$message.error(u.msg);case 15:case"end":return t.stop()}}),t)})))()}}},_=v,k=(n("a9db"),n("2877")),h=Object(k["a"])(_,a,r,!1,null,null,null);t["default"]=h.exports},a9db:function(e,t,n){"use strict";var a=n("4053"),r=n.n(a);r.a},bbd5:function(e,t,n){"use strict";function a(e){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}n.d(t,"a",(function(){return r}));var r=function e(t){if(!t&&"object"!==a(t))throw new Error("error arguments","deepClone");var n=t.constructor===Array?[]:{};return Object.keys(t).forEach((function(r){t[r]&&"object"===a(t[r])?n[r]=e(t[r]):n[r]=t[r]})),n}}}]);
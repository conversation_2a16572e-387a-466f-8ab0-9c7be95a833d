(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-nutrition-rules-core-GuidanceRules"],{"08d4":function(t,e,n){},"0dd9":function(t,e,n){"use strict";var a=n("08d4"),r=n.n(a);r.a},f6f9:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"guidance_rules"},[n("refresh-tool",{on:{refreshPage:t.refreshHandle}}),n("div",{staticClass:"table-wrapper"},[t._m(0),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"table-content ps-flex"},t._l(t.tableData,(function(e,a){return n("div",{key:a,staticClass:"model m-r-30"},[n("div",{staticClass:"model-content"},[n("div",[n("div",{staticClass:"title"},[t._v(t._s(e.name))]),n("div",{staticClass:"content"},[t._v(" "+t._s(e.remark)+" ")])]),n("div",{staticClass:"update-time"},[t._v("更新时间："+t._s(e.update_time))])]),n("div",{staticClass:"bottom-state"},[n("div",[n("span",{staticClass:"p-r-10",staticStyle:{"font-size":"13px"}},[t._v("状态")]),n("el-switch",{attrs:{"active-color":"#ff9b45","active-value":"enable","inactive-value":"disable"},on:{change:function(n){return t.getNutritionRuleModify(e)}},model:{value:e.status,callback:function(n){t.$set(e,"status",n)},expression:"item.status"}})],1),n("el-button",{staticClass:"ps-origin-plain-btn",attrs:{size:"mini"},on:{click:function(n){return t.modifyClick("modify",e)}}},[t._v(" 编辑 ")])],1)])})),0)]),n("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[n("el-pagination",{staticClass:"ps-text",attrs:{"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":t.handleCurrentChange}})],1)],1)},r=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"table-header"},[n("div",{staticClass:"table-title"},[t._v("数据列表")])])}],i=n("a34a"),s=n.n(i),o=n("ed08");function u(t,e){return p(t)||f(t,e)||l(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(t,e){if(t){if("string"===typeof t)return d(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=new Array(e);n<e;n++)a[n]=t[n];return a}function f(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var n=[],a=!0,r=!1,i=void 0;try{for(var s,o=t[Symbol.iterator]();!(a=(s=o.next()).done);a=!0)if(n.push(s.value),e&&n.length===e)break}catch(u){r=!0,i=u}finally{try{a||null==o["return"]||o["return"]()}finally{if(r)throw i}}return n}}function p(t){if(Array.isArray(t))return t}function v(t,e,n,a,r,i,s){try{var o=t[i](s),u=o.value}catch(c){return void n(c)}o.done?e(u):Promise.resolve(u).then(a,r)}function g(t){return function(){var e=this,n=arguments;return new Promise((function(a,r){var i=t.apply(e,n);function s(t){v(i,a,r,s,o,"next",t)}function o(t){v(i,a,r,s,o,"throw",t)}s(void 0)}))}}var m={data:function(){return{isLoading:!1,tableData:[],pageSize:10,totalCount:0,currentPage:1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getNutritionRuleList()},refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getNutritionRuleList:function(){var t=this;return g(s.a.mark((function e(){var n,a,r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(o["Q"])(t.$apis.apiBackgroundHealthyAdminNutritionRuleListPost({type:"nutrition_guide",page:t.currentPage,page_size:t.pageSize}));case 3:if(n=e.sent,a=u(n,2),r=a[0],i=a[1],t.isLoading=!1,!r){e.next=11;break}return t.$message.error(r.message),e.abrupt("return");case 11:0===i.code?(t.totalCount=i.data.count,t.tableData=i.data.results):t.$message.error(i.msg);case 12:case"end":return e.stop()}}),e)})))()},getNutritionRuleModify:function(t){var e=this;return g(s.a.mark((function n(){var a,r,i,c;return s.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.isLoading=!0,n.next=3,Object(o["Q"])(e.$apis.apiBackgroundHealthyAdminNutritionRuleModifyPost({id:t.id,status:t.status}));case 3:if(a=n.sent,r=u(a,2),i=r[0],c=r[1],e.isLoading=!1,!i){n.next=11;break}return e.$message.error(i.message),n.abrupt("return");case 11:0===c.code?e.getNutritionRuleList():e.$message.error(c.msg);case 12:case"end":return n.stop()}}),n)})))()},handleCurrentChange:function(t){this.currentPage=t,this.getNutritionRuleList()},formatQueryParams:function(t){var e={};for(var n in t)""!==t[n].value&&("select_time"!==n?e[n]=t[n].value:t[n].value&&t[n].value.length>0&&(e.start_time=t[n].value[0],e.end_time=t[n].value[1]));return e},modifyClick:function(t,e){this.$router.push({name:"SuperModifyGuidanceRules",query:{type:t,data:"modify"===t?this.$encodeQuery(e):""}})}}},h=m,y=(n("0dd9"),n("2877")),b=Object(y["a"])(h,a,r,!1,null,"23b150d8",null);e["default"]=b.exports}}]);
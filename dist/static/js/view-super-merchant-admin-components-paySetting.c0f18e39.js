(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-paySetting"],{3079:function(e,a,t){"use strict";t.r(a);var n=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"paysetting-wrapper"},["root"===e.type?t("div",{staticClass:"paysetting-container"},[t("div",{staticClass:"tree-wrapper paysetting-l"},[t("el-input",{staticClass:"tree-search ps-input",attrs:{type:"primary",placeholder:"请输入",clearable:"",size:"small"},model:{value:e.treeFilterText,callback:function(a){e.treeFilterText=a},expression:"treeFilterText"}}),e.treeFilterText?e._e():t("div",{class:["all-tree",e.selectKey?"":"is-current"],on:{click:function(a){return e.treeHandleNodeClick("","all")}}},[t("span",[e._v(" 全部 ")])]),t("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.treeLoading,expression:"treeLoading"}],ref:"treeRef",class:{"tree-box":e.selectKey},attrs:{data:e.paySettingList,props:e.treeProps,"check-on-click-node":!0,"expand-on-click-node":!1,"highlight-current":!0,"filter-node-method":e.filterTreeNode,"current-node-key":e.selectKey,"node-key":"key"},on:{"node-click":function(a){return e.treeHandleNodeClick(a,"tree")}}})],1),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"paysetting-r"},[t("div",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.add"],expression:"['background.admin.pay_info.add']"}],staticStyle:{"margin-bottom":"10px"}},[t("el-button",{staticClass:"add-paysetting-btn",attrs:{size:"small"},on:{click:function(a){return e.openDialogHandle("add")}}},[e._v("添加支付渠道")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"payInfoListRef",attrs:{width:"100%",data:e.queryPayInfoList,"tooltip-effect":"dark","header-row-class-name":"ps-table-header-row",stripe:""},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{label:"商户名称",prop:"merchant_name",align:"center"}}),t("el-table-column",{attrs:{label:"商户号",prop:"merchant_id",align:"center"}}),t("el-table-column",{attrs:{label:"支付类型",prop:"payway_alias",align:"center"}}),t("el-table-column",{attrs:{label:"支付方式",prop:"sub_payway_alias",align:"center"}}),t("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"适用层级",prop:"",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e.showOrganizationsText(a.row.organizations)))])]}}],null,!1,1512553625)}),t("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"备注",prop:"remark",align:"center"}}),t("el-table-column",{attrs:{label:"操作",prop:"",align:"center",width:"150px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.modify"],expression:"['background.admin.pay_info.modify']"}],attrs:{type:"text",size:"small"},on:{click:function(t){return e.openDialogHandle("modify",a.row)}}},[e._v("编辑")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.delete"],expression:"['background.admin.pay_info.delete']"}],staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(t){return e.deletePayInfo("one",a.row.id)}}},[e._v("删除")]),t("el-switch",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.modify"],expression:"['background.admin.pay_info.modify']"}],staticStyle:{"margin-left":"10px"},attrs:{"active-color":"#ff9b45"},on:{change:function(t){return e.enablePayInfo(a.row)}},model:{value:a.row.enable,callback:function(t){e.$set(a.row,"enable",t)},expression:"scope.row.enable"}})]}}],null,!1,2667121349)})],1),e.totalCount>e.pageSize?t("div",{staticStyle:{"text-align":"right","margin-top":"20px"}},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next","popper-class":"ps-popper-select",total:e.totalCount},on:{"current-change":e.handleCurrentChange}})],1):e._e()],1),t("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,top:"20vh","custom-class":"ps-dialog ps-paysetting-dialog","close-on-click-modal":!1,"before-close":e.beforeCloseDialogHandle,width:"520px"},on:{"update:visible":function(a){e.dialogVisible=a},closed:e.closeDialogHandle}},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.dialogIsLoading,expression:"dialogIsLoading"}],ref:"payFormDataRef",staticClass:"paysetting-dialog",attrs:{model:e.payFormData,"status-icon":"",rules:e.payFormDataRuls,"label-width":"110px"},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-form-item",{attrs:{prop:"merchantId",label:"商户号"}},[t("el-input",{attrs:{size:"small"},model:{value:e.payFormData.merchantId,callback:function(a){e.$set(e.payFormData,"merchantId",a)},expression:"payFormData.merchantId"}})],1),t("el-form-item",{attrs:{prop:"merchantName",label:"商户名称"}},[t("el-input",{attrs:{size:"small"},model:{value:e.payFormData.merchantName,callback:function(a){e.$set(e.payFormData,"merchantName",a)},expression:"payFormData.merchantName"}})],1),t("div",[t("el-form-item",{staticClass:"tree-item",attrs:{label:"支付类型",prop:"payway"}},[t("tree-select",{attrs:{multiple:!1,options:e.paywayList,normalizer:e.paySettingNormalizer,placeholder:"请选择","default-expand-level":1,"disable-branch-nodes":!0,"show-count":!0,disabled:"add"!==e.formOperate,"append-to-body":!0,"z-index":3e3,"no-results-text":"暂无数据"},on:{input:e.changePayway,open:e.openTreeHandle},model:{value:e.payFormData.payway,callback:function(a){e.$set(e.payFormData,"payway",a)},expression:"payFormData.payway"}})],1)],1),e.payFormData.payway?t("el-form-item",{attrs:{label:"支付方式",prop:"subPayway"}},[t("el-select",{ref:"subPayway",attrs:{disabled:"add"!==e.formOperate,size:"small",placeholder:""},on:{change:e.changeSubPayway},model:{value:e.payFormData.subPayway,callback:function(a){e.$set(e.payFormData,"subPayway",a)},expression:"payFormData.subPayway"}},e._l(e.subPaywayList,(function(e){return t("el-option",{key:e.key,attrs:{label:e.name,value:e.key}})})),1)],1):e._e(),e._l(e.formSettingList,(function(a){return[a.hidden||"abc_subinfo"==a.key?e._e():t("el-form-item",{key:a.key,attrs:{prop:a.key,label:a.name}},[a.type&&"input"!==a.type?e._e():t("el-input",{attrs:{size:"small",disabled:a.disabled},model:{value:e.payFormData[a.key],callback:function(t){e.$set(e.payFormData,a.key,t)},expression:"payFormData[item.key]"}}),"textarea"===a.type?t("el-input",{attrs:{size:"small",type:"textarea",rows:3,disabled:a.disabled},model:{value:e.payFormData[a.key],callback:function(t){e.$set(e.payFormData,a.key,t)},expression:"payFormData[item.key]"}}):e._e(),"select"===a.type?t("el-select",{ref:"forRef",refInFor:!0,staticClass:"search-item-w",attrs:{size:"small",disabled:a.disabled,placeholder:""},model:{value:e.payFormData[a.key],callback:function(t){e.$set(e.payFormData,a.key,t)},expression:"payFormData[item.key]"}},e._l(a.value,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1):e._e(),"switch"===a.type?t("el-switch",{attrs:{disabled:a.disabled},model:{value:e.payFormData[a.key],callback:function(t){e.$set(e.payFormData,a.key,t)},expression:"payFormData[item.key]"}}):e._e(),"checkbox"===a.type?t("el-checkbox-group",{attrs:{disabled:a.disabled},model:{value:e.payFormData[a.key],callback:function(t){e.$set(e.payFormData,a.key,t)},expression:"payFormData[item.key]"}},e._l(a.value,(function(n,i){return t("el-checkbox",{key:i,attrs:{label:n.value,name:a.name}},[e._v(e._s(n.name))])})),1):e._e(),"radio"===a.type?t("el-radio-group",{attrs:{disabled:a.disabled},model:{value:a.value,callback:function(t){e.$set(a,"value",t)},expression:"item.value"}},e._l(a.value,(function(n){return t("el-radio",{key:n.value,attrs:{label:n.value,name:a.radio}},[e._v(e._s(n.name))])})),1):e._e(),a.help_text?t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.help_text,placement:"top-start"}},[t("i",{staticClass:"el-icon-info"})]):e._e()],1),a.hidden||"abc_subinfo"!==a.key||"1"!==e.payFormData["abc_type"]?e._e():e._l(a.value,(function(n){return t("el-form-item",{key:n.key,attrs:{prop:n.key,label:n.name}},[n.type&&"input"!==n.type?e._e():t("el-input",{attrs:{size:"small",disabled:n.disabled},model:{value:e.payFormData[n.key],callback:function(a){e.$set(e.payFormData,n.key,a)},expression:"payFormData[subinfo.key]"}}),"textarea"===n.type?t("el-input",{attrs:{size:"small",type:"textarea",rows:3,disabled:n.disabled},model:{value:e.payFormData[n.key],callback:function(a){e.$set(e.payFormData,n.key,a)},expression:"payFormData[subinfo.key]"}}):e._e(),"select"===n.type?t("el-select",{ref:"forRef",refInFor:!0,staticClass:"search-item-w",attrs:{size:"small",disabled:n.disabled,placeholder:""},model:{value:e.payFormData[n.key],callback:function(a){e.$set(e.payFormData,n.key,a)},expression:"payFormData[subinfo.key]"}},e._l(n.value,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1):e._e(),a.help_text?t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.help_text,placement:"top-start"}},[t("i",{staticClass:"el-icon-info"})]):e._e()],1)}))]})),t("el-form-item",{staticClass:"remark-item",attrs:{label:"适用组织",prop:"organizations"}},[t("tree-select",{attrs:{multiple:!0,options:e.organizationList,normalizer:e.organizationNormalizer,placeholder:"",limit:2,limitText:function(e){return"+"+e},"default-expand-level":6,"value-consists-of":"ALL",flat:!0,"append-to-body":!0,"z-index":3e3,"no-results-text":"暂无数据"},model:{value:e.payFormData.organizations,callback:function(a){e.$set(e.payFormData,"organizations",a)},expression:"payFormData.organizations"}})],1),t("el-form-item",{staticClass:"remark-item",attrs:{label:"备注",prop:"remark"}},[t("el-input",{staticClass:"ps-input",staticStyle:{width:"100%"},attrs:{type:"textarea",rows:3},model:{value:e.payFormData.remark,callback:function(a){e.$set(e.payFormData,"remark",a)},expression:"payFormData.remark"}})],1)],2),t("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.dialogIsLoading,size:"small"},on:{click:e.clickCancleHandle}},[e._v("取消")]),t("el-button",{staticClass:"ps-btn",attrs:{disabled:e.dialogIsLoading,type:"primary",size:"small"},on:{click:e.clickConfirmHandle}},[e._v("确定")])],1)],1)],1):t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.subIsLoading,expression:"subIsLoading"}],staticClass:"paysetting-sub"},e._l(e.collapseInfo,(function(a,n){return t("div",{key:n,staticClass:"sub-wrapper"},[t("div",{staticClass:"l-title"},[t("span",[e._v(e._s(a.name))]),t("el-switch",{staticStyle:{"margin-left":"15px"},attrs:{"active-color":"#ff9b45"},on:{change:function(t){return e.changeSceneHandle(t,a.key)}},model:{value:a.isOpen,callback:function(t){e.$set(a,"isOpen",t)},expression:"info.isOpen"}}),e.showBindBtnHandle(a.key)?t("el-button",{staticClass:"ps-origin-btn float-r save-m-r",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.clickBindOrgsHandle(a.key)}}},[e._v("保存")]):e._e()],1),a.payways.length>0?t("el-collapse",{model:{value:a.activePayCollapse,callback:function(t){e.$set(a,"activePayCollapse",t)},expression:"info.activePayCollapse"}},e._l(a.payways,(function(n){return t("el-collapse-item",{key:n.key,attrs:{title:n.name,name:n.key}},[t("template",{slot:"title"},[t("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!a.isOpen},on:{change:function(t){return e.changePaywayHandle(t,n.key,a)}},model:{value:n.isOpen,callback:function(a){e.$set(n,"isOpen",a)},expression:"payway.isOpen"}},[e._v(e._s(n.name))]),t("span",{staticClass:"tips-r"},[t("span",{staticClass:"open"},[e._v("展开")]),t("span",{staticClass:"close"},[e._v("收起")])])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"subPayInfoListRef"+a.key+"-"+n.key,refInFor:!0,attrs:{width:"100%",data:n.sub_payways,"tooltip-effect":"dark"}},[t("el-table-column",{attrs:{"class-name":"ps-checkbox",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!(a.isOpen&&n.isOpen)},on:{change:function(t){return e.changeSubPayHandle(t,i.row,n.sub_payways,a.key+"-"+n.key)}},model:{value:i.row.binded,callback:function(a){e.$set(i.row,"binded",a)},expression:"scope.row.binded"}})]}}],null,!0)}),t("el-table-column",{attrs:{label:"商户名称",prop:"merchant_name",align:"center"}}),t("el-table-column",{attrs:{label:"商户号",prop:"merchant_id",align:"center"}}),t("el-table-column",{attrs:{label:"支付类型",prop:"payway_alias",align:"center"}}),t("el-table-column",{attrs:{label:"支付方式",prop:"sub_payway_alias",align:"center"}}),t("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"适用层级",prop:"",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v(e._s(e.showOrganizationsText(a.row.organizations)))])]}}],null,!0)}),t("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"备注",prop:"remark",align:"center"}})],1)],2)})),1):t("div",{staticClass:"empty-collapse-text"},[e._v("暂无更多数据")])],1)})),0)])},i=[],r=t("a34a"),s=t.n(r),o=t("ed08");function l(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);a&&(n=n.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,n)}return t}function c(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?l(Object(t),!0).forEach((function(a){u(e,a,t[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}))}return e}function u(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function p(e,a){return g(e)||m(e,a)||d(e,a)||y()}function y(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,a){if(e){if("string"===typeof e)return f(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?f(e,a):void 0}}function f(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,n=new Array(a);t<a;t++)n[t]=e[t];return n}function m(e,a){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var t=[],n=!0,i=!1,r=void 0;try{for(var s,o=e[Symbol.iterator]();!(n=(s=o.next()).done);n=!0)if(t.push(s.value),a&&t.length===a)break}catch(l){i=!0,r=l}finally{try{n||null==o["return"]||o["return"]()}finally{if(i)throw r}}return t}}function g(e){if(Array.isArray(e))return e}function h(e,a,t,n,i,r,s){try{var o=e[r](s),l=o.value}catch(c){return void t(c)}o.done?a(l):Promise.resolve(l).then(n,i)}function b(e){return function(){var a=this,t=arguments;return new Promise((function(n,i){var r=e.apply(a,t);function s(e){h(r,n,i,s,o,"next",e)}function o(e){h(r,n,i,s,o,"throw",e)}s(void 0)}))}}var k={name:"SuperPaySetting",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{treeLoading:!1,treeProps:{children:"children",label:"name"},treeFilterText:"",selectKey:"",selectData:null,isLoading:!1,formOperate:"detail",formSettingList:[],payFormData:{organizations:[],merchantId:"",merchantName:"",remark:"",payScene:"",payway:null,subPayway:""},payFormDataRuls:{merchantId:[{required:!0,message:"商户号不能为空",trigger:"blur"}],merchantName:[{required:!0,message:"商户名称不能为空",trigger:"blur"}],payway:[{required:!0,message:"请选择支付渠道",trigger:"blur"}],subPayway:[{required:!0,message:"请选择支付方式",trigger:"blur"}],organizations:[{required:!0,message:"请选择适用组织",trigger:"blur"}]},payTemplateList:{},paySettingList:[],payInfoList:[],queryPayInfoList:[],pageSize:10,currentPage:1,totalCount:0,dialogVisible:!1,dialogTitle:"",dialogData:null,dialogIsLoading:!1,paywayList:[],subPaywayList:[],organizationList:[],selectTableCoumn:[],activePayCollapse:[],subIsLoading:!1,subPayInfoList:[],collapseInfo:{},selectSubInfo:{}}},computed:{checkIsFormStatus:function(){var e=!1;switch(this.formOperate){case"detail":e=!1;break;case"add":e=!0;break}return e}},watch:{type:function(e){},organizationData:function(e){var a=this;setTimeout((function(){a.searchHandle()}),50)},treeFilterText:function(e){this.$refs.treeRef.filter(e)}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){"root"===this.type?(this.getPaySettingTemplate(),this.getPayInfoList()):this.getSubOrgsAllList()},refreshHandle:function(){this.currentPage=1,this.initLoad()},searchHandle:Object(o["c"])((function(){this.initLoad()}),300),getPaySettingTemplate:function(e){var a=this;return b(s.a.mark((function e(){var t,n,i,r,l;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.treeLoading=!0,e.next=3,Object(o["Q"])(a.$apis.apiBackgroundAdminPayInfoTemplateListPost({pay_scenes:["instore","online"],company:a.organizationData.company}));case 3:if(t=e.sent,n=p(t,2),i=n[0],r=n[1],a.treeLoading=!1,!i){e.next=11;break}return a.$message.error(i.message),e.abrupt("return");case 11:0===r.code?(a.payTemplateList=r.data,l=r.data.scene.sort((function(e,a){return a.key.charCodeAt(0)-e.key.charCodeAt(0)})),a.paySettingList=a.setTemplatePrefix(l),a.selectKey||(a.paywayList=a.paySettingList)):a.$message.error(r.msg);case 12:case"end":return e.stop()}}),e)})))()},filterTreeNode:function(e,a){return!e||-1!==a.name.indexOf(e)},setTemplatePrefix:function(e){var a=Object(o["e"])(e);return a.forEach((function(e){e.children&&e.children.length>0&&e.children.forEach((function(a){a.parent=e.key,a.key=e.key+"-"+a.key}))})),a},getPayInfoList:function(e){var a=this;return b(s.a.mark((function t(){var n,i,r,l,c;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a.isLoading=!0,n={company:a.organizationData.company,organizations:[a.organizationData.id],page:a.currentPage,page_size:a.pageSize},a.selectData?a.selectData.parent?n.pay_scene=a.selectData.parent:n.pay_scene=a.selectData.key:n.pay_scenes=["instore","online"],t.next=5,Object(o["Q"])(a.$apis.apiBackgroundAdminPayInfoListPost(n));case 5:if(i=t.sent,r=p(i,2),l=r[0],c=r[1],a.isLoading=!1,!l){t.next=13;break}return a.$message.error(l.message),t.abrupt("return");case 13:0===c.code?(a.totalCount=c.data.count,a.payInfoList=c.data.results.map((function(e){return e.enable=!!e.enable,e})),a.queryPayInfoList=e?a.payInfoList.filter((function(a){return a.payway===e})):a.payInfoList):a.$message.error(c.msg);case 14:case"end":return t.stop()}}),t)})))()},handleCurrentChange:function(e){this.currentPage=e,this.getPayInfoList()},showOrganizationsText:function(e){var a="";return e.forEach((function(e){a?a+="，".concat(e.name):a=e.name})),a},treeHandleNodeClick:function(e,a){var t=this;this.$nextTick((function(){var a=!1;e&&e.key===t.selectKey&&(a=!0),e&&e.parent===t.selectKey&&(a=!0);var n=e?e.key.indexOf("-"):-1,i="";n>-1&&(i=e.key.substring(n+1)),e?(t.selectKey=n>-1?e.key.substring(0,n):e.key,t.selectData=e):(t.selectKey="",t.selectData=null),a?i?(t.queryPayInfoList=[],t.queryPayInfoList=t.payInfoList.filter((function(e){return e.payway===i}))):t.queryPayInfoList=t.payInfoList:(t.payInfoList=[],t.getPayInfoList(i))}))},initPayawyList:function(e){var a=this;if(this.subPaywayList=[],this.selectKey){for(var t=this.paySettingList.length,n=[],i=0;i<t;i++)if(n.push(this.paySettingList[i].key),e.parent){if(this.paySettingList[i].key!==e.parent)continue;this.paySettingList[i].children&&this.paySettingList[i].children.length&&this.paySettingList[i].children.forEach((function(t){e.key===t.key&&(a.payFormData.payScene=t.parent,a.subPaywayList=t.sub_payway)}))}else{if(this.paySettingList[i].key!==this.selectKey)continue;this.payFormData.payScene=this.selectKey}n.includes(this.selectKey)?this.payFormData.payway=null:this.payFormData.payway=this.selectKey}},changePayway:function(e){var a=this;if("add"===this.formOperate&&(this.formSettingList=[],this.payFormData.subPayway=""),e&&this.payFormData.payway){var t=e.split("-");this.payFormData.payScene!==t[0]&&(this.payFormData.payScene=t[0]);for(var n=this.paySettingList.length,i=0;i<n;i++)this.paySettingList[i].children&&this.paySettingList[i].children.length&&this.paySettingList[i].children.forEach((function(e){a.payFormData.payway===e.key&&(a.subPaywayList=e.sub_payway)}))}},changeSubPayway:function(e){var a=this.payTemplateList.template[this.payFormData.payway.substring(this.payFormData.payway.indexOf("-")+1)];this.initFormSettingList(a)},initFormSettingList:function(e){this.formSettingList=[];var a=[];e.defaults&&e.defaults.length>0&&(this.setDynamicParams(this.formOperate,this.payFormData,e.defaults),a=Object(o["e"])(e.defaults));var t=e[this.payFormData.subPayway];t&&t.length&&(this.setDynamicParams(this.formOperate,this.payFormData,t),a=a.concat(Object(o["e"])(t))),this.formSettingList=a},setDynamicParams:function(e,a,t){var n=this;"add"===e?t.forEach((function(e){switch(e.type){case"checkbox":if(e.default){var t=JSON.parse(e.default);n.$set(a,e.key,t)}else n.$set(a,e.key,[]);break;default:"abc_subinfo"===e.key?e.value.forEach((function(e){e.default?n.$set(a,e.key,e.default):n.$set(a,e.key,"")})):e.default?n.$set(a,e.key,e.default):n.$set(a,e.key,"");break}})):t.forEach((function(e){switch(e.type){case"checkbox":n.$set(a,e.key,n.dialogData.extra[e.key]);break;default:"abc_subinfo"===e.key?e.value.forEach((function(e){n.$set(a,e.key,n.dialogData.extra[e.key])})):n.$set(a,e.key,n.dialogData.extra[e.key]);break}}))},paySettingNormalizer:function(e){if(e)return{id:e.key,label:e.name,children:e.children}},organizationNormalizer:function(e){return{id:e.id,label:e.name,children:e.children_list}},loadCurrentLevelOrganization:function(){var e=this;return b(s.a.mark((function a(){var t,n,i,r;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return"modify"===e.formOperate&&(e.dialogIsLoading=!0),a.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminOrganizationTreeListPost({company_id:e.organizationData.company}));case 3:if(t=a.sent,n=p(t,2),i=n[0],r=n[1],e.dialogIsLoading=!1,!i){a.next=11;break}return e.$message.error(i.message),a.abrupt("return");case 11:0===r.code?(console.log(e.findKeyTreeList(r.data,"company",e.organizationData.company)),e.organizationList=e.deleteEmptyChildren(e.findKeyTreeList(r.data,"company",e.organizationData.company)),console.log("organizationList",e.organizationList),"add"===e.formOperate&&(e.payFormData.organizations=Object(o["z"])(e.organizationList,"id","children_list"))):e.$message.error(r.msg);case 12:case"end":return a.stop()}}),a)})))()},findKeyTreeList:function(e,a,t){var n=this,i=[];return e.forEach((function(e){if(e[a]===t)i.push(e);else if(e.children_list&&e.children_list.length>0){var r=n.findKeyTreeList(e.children_list,a,t);r&&i.push(r)}})),[i[0]]},loadOrganization:function(e){var a=this;return b(s.a.mark((function t(){var n,i,r,l,c,u,y,d;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.action,n=e.parentNode,i=e.callback,r={status__in:["enable","disable"],page:1,page_size:99999,company:a.organizationData.company},n&&n.id?r.parent__in=n.id:(r.parent__is_null="1",a.treeLoading=!0),t.next=5,Object(o["Q"])(a.$apis.apiBackgroundAdminOrganizationListPost(r));case 5:if(l=t.sent,c=p(l,2),u=c[0],y=c[1],a.treeLoading=!1,!u){t.next=14;break}return i(),a.$message.error(u.message),t.abrupt("return");case 14:0===y.code?(d=y.data.results.map((function(e){return e.has_children&&(e.children=null),e})),a.organizationList?n.children=d:a.organizationList=d,i()):(i(),a.$message.error(y.msg));case 15:case"end":return t.stop()}}),t)})))()},deleteEmptyChildren:function(e,a){a=a||"children_list";var t=this;function n(e){e.map((function(e){e[a]&&e[a].length>0?n(e[a]):t.$delete(e,a)}))}return n(e),e},handleSelectionChange:function(e){this.selectTableCoumn=e.map((function(e){return e.id}))},openDialogHandle:function(e,a){this.formOperate=e,this.dialogData=a,this.dialogVisible=!0,this.initPayawyList(this.selectData),"add"===e?(this.dialogTitle="添加支付渠道",this.changePayway(this.payFormData.payway)):(this.dialogTitle="修改支付渠道",this.payFormData.merchantId=a.merchant_id,this.payFormData.merchantName=a.merchant_name,this.payFormData.payScene=a.pay_scene,this.payFormData.payway=a.pay_scene+"-"+a.payway,this.payFormData.subPayway=a.sub_payway,this.payFormData.remark=a.remark,this.payFormData.organizations=a.organizations.map((function(e){return e.id})),this.payFormData.company=a.company,this.changePayway(this.payFormData.payway),this.payFormData.subPayway=a.sub_payway,this.changeSubPayway(a.sub_payway)),this.loadCurrentLevelOrganization()},clickCancleHandle:function(){this.$refs.payFormDataRef.resetFields(),this.dialogVisible=!1},clickConfirmHandle:function(){var e=this;return b(s.a.mark((function a(){return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!e.dialogIsLoading){a.next=2;break}return a.abrupt("return",e.$message.error("请勿重复提交！"));case 2:e.$refs.payFormDataRef.validate((function(a){a&&("add"===e.formOperate?e.addPayInfo(e.formatData()):e.modifyPayInfo(e.formatData()))}));case 3:case"end":return a.stop()}}),a)})))()},beforeCloseDialogHandle:function(e){this.$refs.payFormDataRef.resetFields(),e()},closeDialogHandle:function(){this.formOperate="",this.dialogTitle="",this.dialogData=null,this.formSettingList=[]},formatData:function(){var e=this,a={extra:{},organization:this.organizationData.id,organizations:this.payFormData.organizations,merchant_id:this.payFormData.merchantId,merchant_name:this.payFormData.merchantName,remark:this.payFormData.remark,pay_scene:this.payFormData.payScene,payway:this.payFormData.payway.substring(this.payFormData.payway.indexOf("-")+1),sub_payway:this.payFormData.subPayway};return"modify"===this.formOperate?(a.id=this.dialogData.id,a.company=this.dialogData.company):a.company=this.organizationData.company,this.formSettingList.forEach((function(t){"abc_subinfo"===t.key?t.value.forEach((function(t){a.extra[t.key]=e.payFormData[t.key]})):a.extra[t.key]=e.payFormData[t.key]})),a},addPayInfo:function(e){var a=this;return b(s.a.mark((function t(){var n,i,r,l;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a.dialogIsLoading=!0,t.next=3,Object(o["Q"])(a.$apis.apiBackgroundAdminPayInfoAddPost(e));case 3:if(n=t.sent,i=p(n,2),r=i[0],l=i[1],a.dialogIsLoading=!1,!r){t.next=11;break}return a.$message.error(r.message),t.abrupt("return");case 11:0===l.code?(a.payInfoList=l.data.results,a.$refs.payFormDataRef.resetFields(),a.dialogVisible=!1,a.$message.success(l.msg),a.getPayInfoList()):a.$message.error(l.msg);case 12:case"end":return t.stop()}}),t)})))()},modifyPayInfo:function(e){var a=this;return b(s.a.mark((function t(){var n,i,r,l;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a.dialogIsLoading=!0,t.next=3,Object(o["Q"])(a.$apis.apiBackgroundAdminPayInfoModifyPost(e));case 3:if(n=t.sent,i=p(n,2),r=i[0],l=i[1],a.dialogIsLoading=!1,!r){t.next=11;break}return a.$message.error(r.message),t.abrupt("return");case 11:0===l.code?(a.payInfoList=l.data.results,a.$refs.payFormDataRef.resetFields(),a.dialogVisible=!1,a.$message.success(l.msg),a.getPayInfoList()):a.$message.error(l.msg);case 12:case"end":return t.stop()}}),t)})))()},deletePayInfo:function(e,a){var t=this;return b(s.a.mark((function n(){var i;return s.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=[],i="one"===e?[a]:t.selectTableCoumn,i.length){n.next=5;break}return t.$message.error("请选择要删除的数据！"),n.abrupt("return");case 5:t.$confirm("确定删除？","提示",{confirmButtonText:t.$t("dialog.confirm_btn"),cancelButtonText:t.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=b(s.a.mark((function e(a,n,r){var l,c,u,y;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==a){e.next=18;break}return n.confirmButtonLoading=!0,t.isLoading=!0,e.next=5,Object(o["Q"])(t.$apis.apiBackgroundAdminPayInfoDeletePost({ids:i,organization:t.organizationData.id,company:t.organizationData.company}));case 5:if(l=e.sent,c=p(l,2),u=c[0],y=c[1],t.isLoading=!1,n.confirmButtonLoading=!1,r(),!u){e.next=15;break}return t.$message.error(u.message),e.abrupt("return");case 15:0===y.code?(t.$message.success(y.msg),t.getPayInfoList()):t.$message.error(y.msg),e.next=19;break;case 18:n.confirmButtonLoading||r();case 19:case"end":return e.stop()}}),e)})));function a(a,t,n){return e.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}));case 6:case"end":return n.stop()}}),n)})))()},enablePayInfo:function(e){var a=this;this.$confirm("确定".concat(e.enable?"启用":"关闭","？"),"提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var t=b(s.a.mark((function t(n,i,r){var l,c,u,y;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("confirm"!==n){t.next=19;break}return i.confirmButtonLoading=!0,a.isLoading=!0,t.next=5,Object(o["Q"])(a.$apis.apiBackgroundAdminPayInfoModifyPost({id:e.id,organization:a.organizationData.id,company:a.organizationData.company,enable:e.enable?1:0}));case 5:if(l=t.sent,c=p(l,2),u=c[0],y=c[1],a.isLoading=!1,i.confirmButtonLoading=!1,r(),!u){t.next=16;break}return e.enable=!e.enable,a.$message.error(u.message),t.abrupt("return");case 16:0===y.code?(a.$message.success(y.msg),a.getPayInfoList()):(e.enable=!e.enable,a.$message.error(y.msg)),t.next=21;break;case 19:console.log(111,e),i.confirmButtonLoading||(e.enable=!e.enable,r());case 21:case"end":return t.stop()}}),t)})));function n(e,a,n){return t.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},getSubOrgsAllList:function(){var e=this;return b(s.a.mark((function a(){var t,n,i,r,l;return s.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.subIsLoading=!0,a.next=3,Object(o["Q"])(e.$apis.apiBackgroundAdminPayInfoSubOrgsAllListPost({organizations:[e.organizationData.id],pay_scenes:["instore","online"],company:e.organizationData.company}));case 3:if(t=a.sent,n=p(t,2),i=n[0],r=n[1],e.subIsLoading=!1,!i){a.next=11;break}return e.$message.error(i.message),a.abrupt("return");case 11:0===r.code?(e.collapseInfo={},e.selectSubInfo={},e.subPayInfoList=r.data.sort((function(e,a){return a.key.charCodeAt(0)-e.key.charCodeAt(0)})),l=[],Object(o["e"])(r.data).map((function(a){var t=!1,n=[];a.payways=a.payways.map((function(i){var r=!1;return i.sub_payways.forEach((function(s){s.binded&&(t=!0,r=!0,e.selectSubInfo["".concat(a.key,"-").concat(i.key)]?e.selectSubInfo["".concat(a.key,"-").concat(i.key)].push(s.id):e.$set(e.selectSubInfo,"".concat(a.key,"-").concat(i.key),[s.id]),n.includes(i.key)||n.push(i.key),l.push({type:a.key+"-"+i.key,list:s}))})),i.isOpen=r,i})),e.$set(e.collapseInfo,a.key,c(c({},a),{},{activePayCollapse:n,isOpen:t}))}))):e.$message.error(r.msg);case 12:case"end":return a.stop()}}),a)})))()},setDefaultTableSelect:function(e){var a=this;e.forEach((function(e){var t=a.$refs["subPayInfoListRef".concat(e.type)][0];t.toggleRowSelection(e.list,!0)}))},changeSceneHandle:function(e,a){},selectableHandle:function(e,a){var t=!0;return this.collapseInfo[e.pay_scene].isOpen||(t=!1),this.collapseInfo[e.pay_scene].isOpen&&this.collapseInfo[e.pay_scene].payways.forEach((function(a){a.isOpen||e.payway!==a.key||(t=!1)})),t},changePaywayHandle:function(e,a,t){e&&!t.activePayCollapse.includes(a)&&t.activePayCollapse.push(a)},showBindBtnHandle:function(e){var a=!1;for(var t in this.selectSubInfo)if(t.indexOf(e)>-1&&(a=!0),a)break;return a},clickBindOrgsHandle:function(e){var a=this,t=[];this.collapseInfo[e].payways.forEach((function(n){if(a.collapseInfo[e].isOpen&&n.isOpen){var i=a.selectSubInfo[e+"-"+n.key];n.sub_payways.forEach((function(e){i.includes(e.id)&&t.push({id:e.id})}))}})),this.setSubOrgsBind(e,t)},setSubOrgsBind:function(e,a){var t=this;return b(s.a.mark((function n(){var i,r,l,c,u;return s.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.subIsLoading=!0,i={pay_scene:e,organizations:[t.organizationData.id],payinfo:a,company:t.organizationData.company},n.next=4,Object(o["Q"])(t.$apis.apiBackgroundAdminPayInfoSubOrgsBindPost(i));case 4:if(r=n.sent,l=p(r,2),c=l[0],u=l[1],t.subIsLoading=!1,!c){n.next=12;break}return t.$message.error(c.message),n.abrupt("return");case 12:0===u.code?(t.$message.success(u.msg),t.getSubOrgsAllList()):t.$message.error(u.msg);case 13:case"end":return n.stop()}}),n)})))()},openTreeHandle:function(e){},changeSubPayHandle:function(e,a,t,n){var i=this,r=[];t.forEach((function(e){e.binded&&e.id!==a.id&&r.push(e.sub_payway)})),t.forEach((function(t){if(e)r.includes(a.sub_payway)?(t.id===a.id&&i.$nextTick((function(){t.binded=!1;var e=i.selectSubInfo[n].indexOf(a.id);e>-1&&i.selectSubInfo[n].splice(e,1)})),i.$message.error("请勿选择相同支付类型！")):i.selectSubInfo[n]&&i.selectSubInfo[n].length?i.selectSubInfo[n].includes(a.id)||i.selectSubInfo[n].push(a.id):i.$set(i.selectSubInfo,n,[a.id]);else{var s=i.selectSubInfo[n].indexOf(a.id);s>-1&&i.selectSubInfo[n].splice(s,1)}}))}}},v=k,w=(t("9eec"),t("2877")),x=Object(w["a"])(v,n,i,!1,null,null,null);a["default"]=x.exports},3912:function(e,a,t){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},"9eec":function(e,a,t){"use strict";var n=t("3912"),i=t.n(n);i.a}}]);
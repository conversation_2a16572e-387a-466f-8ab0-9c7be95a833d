(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-article-push-article-admin-articleAdmin"],{"6b4a":function(e,t,r){"use strict";var a=r("cba8"),n=r.n(a);n.a},"97dd":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"ArticleAdmin"},[r("refresh-tool",{on:{refreshPage:e.refreshHandle}}),r("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),r("div",{staticClass:"table-wrapper"},[r("div",{staticClass:"table-header"},[r("div",{staticClass:"table-title"},[e._v("数据列表")]),r("div",{staticClass:"align-r"},[r("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.gotoArticle("add")}}},[e._v("添加文章")])],1)]),r("div",{staticClass:"table-content"},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row","tree-props":{children:"children_list",hasChildren:"has_children"}}},[r("el-table-column",{attrs:{prop:"release_time",label:"发布时间",align:"center"}}),r("el-table-column",{attrs:{prop:"title",label:"文章标题",align:"center"}}),r("el-table-column",{attrs:{prop:"person_no",label:"封面",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:e.row.image,"preview-src-list":[e.row.image]}})]}}])}),r("el-table-column",{attrs:{prop:"person_no",label:"标签",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.tags,(function(t,a){return r("div",{key:a,staticClass:"label-article"},[e._v(" "+e._s(t.name)+"、 ")])}))}}])}),r("el-table-column",{attrs:{prop:"source_alias",label:"来源",align:"center"}}),r("el-table-column",{attrs:{prop:"is_recommend",label:"是否推荐",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s(t.row.is_recommend?"是":"否")+" ")])]}}])}),r("el-table-column",{attrs:{prop:"author",label:"作者",align:"center"}}),r("el-table-column",{attrs:{prop:"operator_name",label:"创建人",align:"center"}}),r("el-table-column",{attrs:{prop:"read_number",label:"阅读量",align:"center"}}),r("el-table-column",{attrs:{fixed:"right",label:"操作人",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(r){return e.gotoArticle("modify",t.row)}}},[e._v(" 编辑 ")]),t.row.is_release?e._e():r("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(r){return e.clickIsRelease(!0,t.row)}}},[e._v(" 发布 ")]),t.row.is_release?r("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(r){return e.clickIsRelease(!1,t.row)}}},[e._v(" 取消发布 ")]):e._e(),r("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[e._v("|")]),r("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(r){return e.deleteHandler("single",t.row.id)}}},[e._v(" 删除 ")])]}}])})],1)],1),r("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[r("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)])],1)},n=[],i=r("a34a"),s=r.n(i),l=r("ed08");function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e,t){return b(e)||m(e,t)||f(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"===typeof e)return g(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}function m(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var r=[],a=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(a=(s=l.next()).done);a=!0)if(r.push(s.value),t&&r.length===t)break}catch(o){n=!0,i=o}finally{try{a||null==l["return"]||l["return"]()}finally{if(n)throw i}}return r}}function b(e){if(Array.isArray(e))return e}function h(e,t,r,a,n,i,s){try{var l=e[i](s),o=l.value}catch(c){return void r(c)}l.done?t(o):Promise.resolve(o).then(a,n)}function v(e){return function(){var t=this,r=arguments;return new Promise((function(a,n){var i=e.apply(t,r);function s(e){h(i,a,n,s,l,"next",e)}function l(e){h(i,a,n,s,l,"throw",e)}s(void 0)}))}}var y={name:"ArticleAdmin",components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{title:{type:"input",label:"标题",value:"",placeholder:"请输入标题"},operator:{type:"input",label:"创建人",value:"",placeholder:"请输入创建人"},tags:{type:"select",value:[],label:"标签",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!0,collapseTags:!0,dataList:[]},source:{type:"select",value:"",label:"来源",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!1,collapseTags:!0,dataList:[{name:"原创",id:"1"},{name:"转载",id:"2"}]},is_recommend:{type:"select",value:"",label:"是否推荐",placeholder:"请选择",listNameKey:"name",listValueKey:"id",multiple:!1,collapseTags:!0,dataList:[{name:"是",id:!0},{name:"否",id:!1}]}}}},created:function(){this.getArticleChildTagList(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getArticleList()},searchHandle:Object(l["c"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.initLoad()},getArticleChildTagList:function(){var e=this;return v(s.a.mark((function t(){var r,a,n,i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(l["Q"])(e.$apis.apiBackgroundArticleTagListPost({level:1}));case 2:if(r=t.sent,a=p(r,2),n=a[0],i=a[1],!n){t.next=9;break}return e.$message.error(n.message),t.abrupt("return");case 9:0===i.code?e.searchFormSetting.tags.dataList=i.data.results:e.$message.error(i.msg);case 10:case"end":return t.stop()}}),t)})))()},getArticleList:function(){var e=this;return v(s.a.mark((function t(){var r,a,n,i;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(l["Q"])(e.$apis.apiBackgroundArticleListPost(c(c({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(r=t.sent,a=p(r,2),n=a[0],i=a[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.tableData=i.data.results):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},getArticleChangeRelease:function(e){var t=this;return v(s.a.mark((function r(){var a,n,i,o;return s.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return t.isLoading=!0,r.next=3,Object(l["Q"])(t.$apis.apiBackgroundArticleChangeReleasePost(e));case 3:if(a=r.sent,n=p(a,2),i=n[0],o=n[1],t.isLoading=!1,!i){r.next=11;break}return t.$message.error(i.message),r.abrupt("return");case 11:0===o.code?t.getArticleList():t.$message.error(o.msg);case 12:case"end":return r.stop()}}),r)})))()},clickIsRelease:function(e,t){var r={is_release:e,id:t.id};this.getArticleChangeRelease(r)},gotoArticle:function(e,t){this.$router.push({name:"SuperAddEditArticle",query:{type:e,data:this.$encodeQuery(t)}})},deleteHandler:function(e,t){var r=this;this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=v(s.a.mark((function e(a,n,i){var o,c,u,d;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==a){e.next=19;break}if(!r.dialogLoading){e.next=3;break}return e.abrupt("return",r.$message.error("请勿重复提交！"));case 3:return r.dialogLoading=!0,n.confirmButtonLoading=!0,e.next=7,Object(l["Q"])(r.$apis.apiBackgroundArticleDeletePost({ids:[t]}));case 7:if(o=e.sent,c=p(o,2),u=c[0],d=c[1],r.dialogLoading=!1,!u){e.next=15;break}return r.$message.error(u.message),e.abrupt("return");case 15:0===d.code?(i(),r.$message.success(d.msg),r.getArticleList()):r.$message.error(d.msg),n.confirmButtonLoading=!1,e.next=20;break;case 19:n.confirmButtonLoading||i();case 20:case"end":return e.stop()}}),e)})));function a(t,r,a){return e.apply(this,arguments)}return a}()}).then((function(e){})).catch((function(e){}))},handleSizeChange:function(e){this.pageSize=e,this.getAccountList()},handleCurrentChange:function(e){this.currentPage=e,this.getArticleList()},formatQueryParams:function(e){var t={};for(var r in e)""!==e[r].value&&("select_date"!==r?t[r]=e[r].value:e[r].value.length>0&&(t.start_date=e[r].value[0],t.end_date=e[r].value[1]));return t}}},w=y,x=(r("6b4a"),r("2877")),_=Object(x["a"])(w,a,n,!1,null,null,null);t["default"]=_.exports},cba8:function(e,t,r){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);
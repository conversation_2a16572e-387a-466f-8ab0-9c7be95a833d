(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-health-rule-components-SportConfig"],{"0f73":function(t,e,a){},a947:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"sport-config"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formRef",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[t._v("基本信息")])]),a("div",{staticStyle:{"max-width":"50%",padding:"0 20px"}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"维度分值",prop:"score"}},[a("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入维度分值",disabled:t.disabled},model:{value:t.formData.score,callback:function(e){t.$set(t.formData,"score",e)},expression:"formData.score"}},[a("template",{slot:"append"},[t._v("分")])],2)],1),a("el-form-item",{staticClass:"block-label",attrs:{label:"提示语",prop:"tips"}},[a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入提示语",disabled:t.disabled},model:{value:t.formData.tips,callback:function(e){t.$set(t.formData,"tips",e)},expression:"formData.tips"}})],1),a("div",{staticClass:"form-content-box m-b-20"},[a("div",[t._v("变量值")]),a("div",{staticClass:"p-t-10"},[a("span",[t._v("x千卡")]),a("span",{staticStyle:{color:"red"}},[t._v("（用户每天运动量）")])]),a("div",{staticClass:"p-t-10 ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("y")]),a("el-form-item",{attrs:{label:""}},[a("el-input",{staticStyle:{width:"150px"},attrs:{disabled:t.disabled,"show-word-limit":""},model:{value:t.formData.kcal,callback:function(e){t.$set(t.formData,"kcal",e)},expression:"formData.kcal"}},[a("template",{slot:"append"},[t._v("千卡")])],2)],1),a("span",{staticClass:"p-r-10 p-t-5",staticStyle:{color:"red"}},[t._v("（用户每天运动量）")])],1),a("div",{staticClass:"p-t-10 p-b-10 flex-between"},[a("div",[t._v("规则配置")]),t.disabled?t._e():a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addRule()}}},[t._v("新增规则")])],1)]),a("div",{staticClass:"p-b-20",staticStyle:{color:"red"}},[t._v("提示：选择不限制请选填0或其他")]),a("div",{staticClass:"ps-flex-align-c"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("如果")]),a("div",t._l(t.formData.config,(function(e,s){return a("div",{key:s,staticClass:"ps-flex-align-c flex-wrap"},[a("span",{staticClass:"p-r-10 p-t-5"},[t._v("x完成度在y正负")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_one,callback:function(a){t.$set(e,"comparison_one",a)},expression:"contentItem.comparison_one"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_one_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.comparison_one_score,callback:function(a){t.$set(e,"comparison_one_score",a)},expression:"contentItem.comparison_one_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("到")]),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.comparison_two,callback:function(a){t.$set(e,"comparison_two",a)},expression:"contentItem.comparison_two"}},t._l(t.comparisonList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".comparison_two_score",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"150px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.comparison_two_score,callback:function(a){t.$set(e,"comparison_two_score",a)},expression:"contentItem.comparison_two_score"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1),a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100px"},attrs:{placeholder:"请下拉选择","popper-class":"ps-popper-select",disabled:t.disabled},model:{value:e.operation,callback:function(a){t.$set(e,"operation",a)},expression:"contentItem.operation"}},t._l(t.operationList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1),a("div",{staticClass:"p-l-10"},[a("el-form-item",{attrs:{label:"",prop:"config."+s+".operation_score",rules:{required:!0,message:"请选择",trigger:"blur"}}},[a("el-input",{staticClass:"ps-input p-r-20",staticStyle:{width:"100px"},attrs:{"show-word-limit":"",disabled:t.disabled},model:{value:e.operation_score,callback:function(a){t.$set(e,"operation_score",a)},expression:"contentItem.operation_score"}})],1)],1),a("div",{staticClass:"p-t-5 p-r-10"},[t._v("分")]),a("div",{staticClass:"m-b-30"},[t.formData.config.length>1&&!t.disabled?a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeRule(s)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),0)])])],1)]),a("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[a("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),a("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("see"===t.type?"返回":"编辑")+" ")])],1)])],1)},o=[],l={props:{type:String,data:Object,disabled:Boolean},data:function(){return{isLoading:!1,formData:{score:"",tips:"",kcal:"",config:[{comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""}]},formRuls:{},comparisonList:[{label:"等于",value:"=="},{label:"大于",value:">"},{label:"大于等于",value:">="},{label:"小于",value:"<"},{label:"小于等于",value:"<="},{label:"不限制",value:""}],operationList:[{label:"加",value:"+"},{label:"减",value:"-"}]}},created:function(){this.formData=this.data,console.log(this.type)},mounted:function(){},methods:{addRule:function(){this.formData.config.push({comparison_one:"",comparison_one_score:"",comparison_two:"",comparison_two_score:"",operation:"",operation_score:""})},removeRule:function(t){this.formData.config.splice(t,1)},submitHandler:function(){var t=this;this.$refs.formRef.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");var a={key:t.formData.type};a[t.formData.type]={score:t.formData.score,tips:t.formData.tips,kcal:t.formData.kcal,config:t.formData.config},t.$emit("submitHandler",a)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,a,s){"confirm"===e?t.$closeCurrentTab(t.$route.path):a.confirmButtonLoading||s()}}).then((function(t){})).catch((function(t){}))}}},i=l,r=(a("de7f"),a("2877")),n=Object(r["a"])(i,s,o,!1,null,"963d4e68",null);e["default"]=n.exports},de7f:function(t,e,a){"use strict";var s=a("0f73"),o=a.n(s);o.a}}]);
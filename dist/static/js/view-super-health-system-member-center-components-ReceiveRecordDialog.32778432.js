(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-components-ReceiveRecordDialog"],{b15a:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[i("el-form",{ref:"memberFormRef",staticClass:"member-form",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},["grant"===e.type?i("div",[i("el-form-item",{attrs:{label:"会员手机号：",prop:"userid"}},[i("el-select",{staticClass:"ps-input w-250",attrs:{filterable:"",remote:"",placeholder:"请选择","remote-method":e.getMemberList,loading:e.loadingMemberList},on:{change:e.changeMemberPhone},model:{value:e.dialogForm.userid,callback:function(t){e.$set(e.dialogForm,"userid",t)},expression:"dialogForm.userid"}},e._l(e.memberList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.phone,value:e.id}})})),1)],1),i("el-form-item",{attrs:{label:"会员姓名："}},[e._v(e._s(e.userInfo.nickname))]),i("el-form-item",{attrs:{label:"会员ID："}},[e._v(e._s(e.userInfo.user_id))]),i("el-form-item",{attrs:{label:"发放天数：",prop:"days"}},[i("el-input",{staticClass:"ps-input w-250",attrs:{maxlength:"20"},model:{value:e.dialogForm.days,callback:function(t){e.$set(e.dialogForm,"days",t)},expression:"dialogForm.days"}}),e._v("天 ")],1)],1):e._e()]),i("template",{slot:"tool"},[i("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[i("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),i("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},n=[],s=i("a34a"),a=i.n(s);function o(e,t,i,r,n,s,a){try{var o=e[s](a),l=o.value}catch(c){return void i(c)}o.done?t(l):Promise.resolve(l).then(r,n)}function l(e){return function(){var t=this,i=arguments;return new Promise((function(r,n){var s=e.apply(t,i);function a(e){o(s,r,n,a,l,"next",e)}function l(e){o(s,r,n,a,l,"throw",e)}a(void 0)}))}}var c={name:"ForLeaveRuleDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"450px"},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){return{isLoading:!1,loadingMemberList:!1,dialogForm:{userid:"",days:""},dialogFormRules:{userid:[{required:!0,message:"请输入搜索手机号",trigger:"blur"}],days:[{required:!0,message:"请输入发放天数",trigger:"change"}]},userInfo:{},memberList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){this.visible||this.$refs.memberFormRef.resetFields()}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var e=this;this.$refs.memberFormRef.validate((function(t){if(t){var i,r={user_id:e.dialogForm.userid,days:e.dialogForm.days,receive_type:"manual_release"};switch(e.type){case"grant":i=e.$apis.apiBackgroundMemberMemberReceiveAddPost(r);break}e.confirmOperation(i)}}))},confirmOperation:function(e){var t=this;return l(a.a.mark((function i(){var r;return a.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.isLoading){i.next=2;break}return i.abrupt("return");case 2:return t.isLoading=!0,i.next=5,e;case 5:r=i.sent,t.isLoading=!1,0===r.code?(t.$message.success("成功"),t.confirm()):t.$message.error(r.msg);case 8:case"end":return i.stop()}}),i)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.memberFormRef.resetFields()},getMemberList:function(e){var t=this;return l(a.a.mark((function i(){var r;return a.a.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e){i.next=2;break}return i.abrupt("return");case 2:return t.loadingMemberList=!0,i.next=5,t.$apis.apiBackgroundMemberMemberUserListPost({phone:e,page:1,page_size:99999});case 5:r=i.sent,t.loadingMemberList=!1,0===r.code?t.memberList=r.data.results:t.$message.error(r.msg);case 8:case"end":return i.stop()}}),i)})))()},changeMemberPhone:function(e){var t=this.memberList.filter((function(t){return t.id===e}));t.length&&(this.userInfo=t[0])}}},u=c,d=(i("de72"),i("2877")),m=Object(d["a"])(u,r,n,!1,null,"ec07eff2",null);t["default"]=m.exports},bdb4:function(e,t,i){},de72:function(e,t,i){"use strict";var r=i("bdb4"),n=i.n(r);n.a}}]);
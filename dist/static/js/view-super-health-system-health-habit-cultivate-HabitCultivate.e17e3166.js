(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-habit-cultivate-HabitCultivate"],{1892:function(e,t,a){},4787:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"HabitCultivate"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")]),a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.gotoHabitCultivate("add")}}},[e._v(" 新增 ")])],1)]),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticClass:"ps-table-tree",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row","tree-props":{children:"children_list",hasChildren:"has_children"}}},[a("el-table-column",{attrs:{prop:"icon",label:"图标",align:"center",width:"170px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.image?a("el-image",{staticStyle:{width:"50px",height:"50px"},attrs:{src:t.row.image}}):e._e()]}}])}),a("el-table-column",{attrs:{prop:"name",label:"习惯名称",align:"center"}}),a("el-table-column",{attrs:{prop:"users",label:"使用用户数",align:"center"}}),a("el-table-column",{attrs:{prop:"clocks",label:"打卡总数",align:"center"}}),a("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{prop:"update_time",label:"修改时间",align:"center"}}),a("el-table-column",{attrs:{prop:"operator_name",label:"操作人",align:"center"}}),a("el-table-column",{attrs:{prop:"",label:"使用状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-color":"#f59933"},on:{change:function(a){return e.modifyStatus(a,t.row.id)}},model:{value:t.row.is_open,callback:function(a){e.$set(t.row,"is_open",a)},expression:"scope.row.is_open"}})]}}])}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.gotoHabitCultivate("modify",t.row)}}},[e._v(" 编辑 ")]),a("span",{staticStyle:{margin:"0 10px",color:"#e2e8f0"}},[e._v("|")]),a("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHandler("single",t.row.id)}}},[e._v(" 删除 ")])]}}])})],1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)])],1)},r=[],i=a("a34a"),o=a.n(i),s=a("ed08");function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){u(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function u(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function p(e,t){return m(e)||b(e,t)||f(e,t)||d()}function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"===typeof e)return g(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function b(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],n=!0,r=!1,i=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(a.push(o.value),t&&a.length===t)break}catch(l){r=!0,i=l}finally{try{n||null==s["return"]||s["return"]()}finally{if(r)throw i}}return a}}function m(e){if(Array.isArray(e))return e}function h(e,t,a,n,r,i,o){try{var s=e[i](o),l=s.value}catch(c){return void a(c)}s.done?t(l):Promise.resolve(l).then(n,r)}function v(e){return function(){var t=this,a=arguments;return new Promise((function(n,r){var i=e.apply(t,a);function o(e){h(i,n,r,o,s,"next",e)}function s(e){h(i,n,r,o,s,"throw",e)}o(void 0)}))}}var y={name:"HealthAssessment",components:{},props:{},data:function(){return{isLoading:!1,pageSize:10,currentPage:1,totalCount:0,totalPageSize:0,tableData:[],searchFormSetting:{date_type:{type:"select",label:"",value:"create_time",maxWidth:"130px",placeholder:"请选择",dataList:[{label:"创建时间",value:"create_time"},{label:"修改时间",value:"update_time"}]},select_time:{type:"datetimerange",label:"",format:"yyyy-MM-dd HH:mm:ss",value:[]},name:{type:"input",label:"习惯名称",value:"",placeholder:"请输入习惯名称"},status:{type:"select",label:"状态",value:"",maxWidth:"130px",placeholder:"请选择",dataList:[{label:"全部",value:"all"},{label:"启用",value:"enable"},{label:"禁用",value:"disable"}]}},dialogLoading:!1}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getHabitList()},searchHandle:Object(s["c"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getHabitList:function(){var e=this;return v(o.a.mark((function t(){var a,n,r,i;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Q"])(e.$apis.apiBackgroundHealthyAdminHabitListPost(c(c({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,n=p(a,2),r=n[0],i=n[1],e.isLoading=!1,!r){t.next=11;break}return e.$message.error(r.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.totalPageSize=e.$computedTotalPageSize(e.totalCount,e.pageSize),e.tableData=i.data.results.map((function(e){return e.is_open="enable"===e.status,e}))):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},deleteHandler:function(e,t){var a=this,n=[];"single"===e&&(n=[t]),this.$confirm("确定删除？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var r=v(o.a.mark((function r(i,l,c){var u,d,f,g;return o.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("confirm"!==i){r.next=20;break}if(!a.dialogLoading){r.next=3;break}return r.abrupt("return",a.$message.error("请勿重复提交！"));case 3:return a.dialogLoading=!0,l.confirmButtonLoading=!0,r.next=7,Object(s["Q"])(a.$apis.apiBackgroundHealthyAdminHabitDeletePost({ids:[t]}));case 7:if(u=r.sent,d=p(u,2),f=d[0],g=d[1],a.dialogLoading=!1,!f){r.next=15;break}return a.$message.error(f.message),r.abrupt("return");case 15:0===g.code?(a.$message.success(g.msg),a.currentPage>1&&(1===a.tableData.length&&"one"===e||a.currentPage===a.totalPageSize&&n.length===a.tableData.length)&&a.currentPage--,a.getHabitList()):a.$message.error(g.msg),c(),l.confirmButtonLoading=!1,r.next=21;break;case 20:l.confirmButtonLoading||c();case 21:case"end":return r.stop()}}),r)})));function i(e,t,a){return r.apply(this,arguments)}return i}()}).then((function(e){})).catch((function(e){}))},modifyStatus:function(e,t){var a=this;return v(o.a.mark((function n(){var r;return o.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a.isLoading=!0,n.next=3,a.$apis.apiBackgroundHealthyAdminHabitModifyPost({id:t,status:e?"enable":"disable"});case 3:r=n.sent,a.isLoading=!1,0===r.code?(a.$message.success("操作成功！"),a.getHabitList()):a.$message.error(r.msg);case 6:case"end":return n.stop()}}),n)})))()},gotoHabitCultivate:function(e,t){this.$router.push({name:"SuperAddModifyHabit",params:{type:e},query:{type:e,data:"modify"===e?this.$encodeQuery(t):""}})},handleSizeChange:function(e){this.pageSize=e,this.getHabitList()},handleCurrentChange:function(e){this.currentPage=e,this.getHabitList()},formatQueryParams:function(e){var t={};for(var a in e)""!==e[a].value&&null!==e[a].value&&0!==e[a].value.length&&("select_time"!==a?t[a]=e[a].value:e[a].value.length>0&&(t.start_date=e[a].value[0],t.end_date=e[a].value[1]));return t}}},w=y,x=(a("a4ef"),a("2877")),C=Object(x["a"])(w,n,r,!1,null,"b95c7d6a",null);t["default"]=C.exports},a4ef:function(e,t,a){"use strict";var n=a("1892"),r=a.n(n);r.a}}]);
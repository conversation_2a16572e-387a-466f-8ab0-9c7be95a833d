(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-FaceTraceback"],{a427:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container-wrapper"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px",isShowSearchBtn:e.isShowSearchBtn,isShowResetBtn:e.isShowResetBtn,isShowCollapse:e.isShowCollapse}},[a("div",{attrs:{slot:"customBtn"},slot:"customBtn"},[a("el-button",{staticClass:"ps-origin-btn search-h-r-btn",attrs:{disabled:e.isLoading,type:"primary",size:"mini"},on:{click:e.clickConfirmHandle}},[e._v("开始查询")]),a("el-button",{staticClass:"search-h-r-btn ps-plain-btn",attrs:{disabled:e.isLoading,size:"mini"},on:{click:e.resetFormFace}},[e._v("重置")])],1),a("div",{staticClass:"face_trace",attrs:{slot:"append"},slot:"append"},[a("el-form-item",{attrs:{label:"查询人脸"}},[a("file-upload",{directives:[{name:"loading",rawName:"v-loading",value:e.isUploading,expression:"isUploading"}],ref:"faceFileRef",class:["avatar-uploader",0==e.fileListsUpload.length?"border-gray-1":""],attrs:{fileList:e.fileListsUpload,type:"enclosure",limit:1,"before-upload":e.beforeUpload,"show-file-list":!1,prefix:"tmp"},on:{fileLists:e.getFileLists,uploadError:e.uploadError}},[e.fileListsUpload.length?a("img",{staticClass:"avatar",attrs:{src:e.fileListsUpload[0].url},on:{click:e.clearFileHandle}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),a("el-form-item",[a("div",[a("div",{staticClass:"m-t-25 color-gray"},[e._v(" "+e._s(e.uploadTipRightTop)+" ")]),a("div",{staticClass:"color-gray"},[e._v(" "+e._s(e.uploadTipRightBottom)+" ")])])])],1)]),e.isShowPopImg?a("el-card",{directives:[{name:"drag",rawName:"v-drag"}],staticClass:"box-card"},[a("div",{staticClass:"flex-between m-b-10"},[a("span",[e._v("人脸照片")]),a("i",{staticClass:"el-icon-close",staticStyle:{float:"right"},on:{click:e.closeCard}})]),a("img",{staticClass:"face-img-big",attrs:{src:e.faceImgUrl}})]):e._e(),a("div",{staticClass:"table-wrapper"},[e._m(0),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"460",stripe:"","header-row-class-name":"ps-table-header-row"}},e._l(e.tableSettings,(function(t){return a("table-column",{key:t.key,attrs:{col:t},scopedSlots:e._u([{key:"face",fn:function(t){var r=t.row;return[a("div",[a("img",{staticClass:"face-img",attrs:{src:r.face_url,alt:""},on:{click:function(t){return e.handlerImgClick(r.face_url)}}})])]}},{key:"sex",fn:function(t){var a=t.row;return[e._v(" "+e._s(e.getSexName(a.gender))+" ")]}},{key:"score",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("formatScoreNotRounding")(a.score))+" ")]}},{key:"operation",fn:function(t){var r=t.row;return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.face_traceback"],expression:"['background.admin.face_traceback']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.showDialogHandle(r)}}},[e._v("查看")])]}}],null,!0)})})),1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("pagination",{attrs:{onPaginationChange:e.onPaginationChange,"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next, jumper",total:e.totalCount},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t}}})],1)])],1)},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")])])}],n=a("a34a"),s=a.n(n),o=a("ed08");function l(e,t){return f(e)||g(e,t)||u(e,t)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e,t){if(e){if("string"===typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function g(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,i=!1,n=void 0;try{for(var s,o=e[Symbol.iterator]();!(r=(s=o.next()).done);r=!0)if(a.push(s.value),t&&a.length===t)break}catch(l){i=!0,n=l}finally{try{r||null==o["return"]||o["return"]()}finally{if(i)throw n}}return a}}function f(e){if(Array.isArray(e))return e}function h(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?h(Object(a),!0).forEach((function(t){m(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):h(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function m(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function b(e,t,a,r,i,n,s){try{var o=e[n](s),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(r,i)}function v(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){b(n,r,i,s,o,"next",e)}function o(e){b(n,r,i,s,o,"throw",e)}s(void 0)}))}}var y={name:"FaceTraceback",data:function(){return{isLoading:!1,pageSize:5,totalCount:0,currentPage:1,tableData:[],tableDataClone:[],tableSettings:[{label:"排序",key:"index"},{label:"近似人脸",key:"face_url",type:"slot",slotName:"face"},{label:"人员编号",key:"member_card"},{label:"姓名",key:"card_name"},{label:"手机号",key:"card_phone"},{label:"性别",key:"gender",type:"slot",slotName:"sex"},{label:"识别分数",key:"score",type:"slot",slotName:"score"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],searchFormSetting:{company_id:{type:"CompanySelect",value:[],label:"组织名称",dataList:[],multiple:!1,checkStrictly:!0,clearable:!0,companyOpts:{label:"name",value:"company"},role:"super"},count:{type:"input",value:"",label:"人脸回溯数量",placeholder:"请输入查询数量（上限50条）"}},fileListsUpload:[],isShowSearchBtn:!1,isShowResetBtn:!1,uploadTipRightTop:"上传：查询人脸",uploadTipRightBottom:"仅支持jpg、png、bmp格式，大小不超过10M",isShowPopImg:!1,faceImgUrl:"",isUploading:!1,isShowCollapse:!1}},created:function(){this.initLoad()},methods:{refreshHandle:function(){this.currentPage=1,this.resetFormFace(),this.tableData=[],this.initLoad()},initLoad:function(){console.log("faceTrace初始化")},clickConfirmHandle:function(){if(console.log("searchHandle",this.searchFormSetting,parseInt(this.searchFormSetting.count.value)),0!==this.searchFormSetting.company_id.value.length)if(0!==this.searchFormSetting.count.value.length&&"0"!==this.searchFormSetting.count.value){var e=/^\d+$/;this.searchFormSetting.count.value.length>0&&!e.test(this.searchFormSetting.count.value)?this.$message.error("人脸回溯数量必须为整数"):parseInt(this.searchFormSetting.count.value)>50?this.$message.error("人脸回溯数量一次查询的数量必须低于50"):0!==this.fileListsUpload.length?this.getFaceList():this.$message.error("请选择查询人脸")}else this.$message.error("请输入人脸回溯数量");else this.$message.error("请选择组织名称")},showDialogHandle:function(e){var t=e.member_card;this.$router.push({name:"FaceTracebackDetail",query:{id:t}})},onPaginationChange:function(e){this.currentPage=e.current,this.pageSize=e.pageSize;var t=this.tableDataClone.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize);this.tableData=Object(o["e"])(t)},beforeUpload:function(e){var t=[".jpg",".png",".bmp"];if(!t.includes(this.getSuffix(e.name)))return this.$message.error("上传图片只能是 jpg/png 格式"),!1;var a=e.size/1024/1024<10;if(!a)return this.$message.error("上传图片大小不能超过 10MB!"),!1;this.isUploading=!0},getSuffix:function(e){var t=e.lastIndexOf("."),a="";return-1!==t&&(a=e.substring(t)),a},getFileLists:function(e){this.isUploading=!1,this.fileListsUpload=Object(o["e"])(e),console.log("fileLists",e)},getFaceList:function(){var e=this;return v(s.a.mark((function t(){var a,r,i,n,c,u;return s.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isLoading){t.next=2;break}return t.abrupt("return");case 2:return e.isLoading=!0,a=d(d({},e.formatQueryParams(e.searchFormSetting)),{},{face_url:e.fileListsUpload[0].url}),t.next=6,Object(o["Q"])(e.$apis.apiBackgroundAdminFaceSearchFaceTracebackPost(a));case 6:if(r=t.sent,i=l(r,2),n=i[0],c=i[1],console.log("apiBackgroundAdminFaceSearchFaceTracebackPost",n,c),e.isLoading=!1,!n){t.next=15;break}return e.$message.error(n.message),t.abrupt("return");case 15:0===c.code?(u=c.data||[],Array.isArray(u)&&u.length>0&&u.map((function(e,t){return e.index=t+1,e})),e.tableData=Object(o["e"])(u.slice(0,e.pageSize)),e.tableDataClone=Object(o["e"])(u),e.totalCount=u.length):e.$message.error(c.msg);case 16:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){console.log("data",e);var t={};for(var a in e)""!==e[a].value&&null!==e[a].value&&0!==e[a].value.length&&("count"===a?t[a]=parseInt(e[a].value):"select_time"!==a?t[a]=e[a].value:e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]));return t},clearFileHandle:function(){console.log("clear"),this.$refs.faceFileRef.clearHandle(),this.fileListsUpload=[]},getSexName:function(e){switch(e){case"MAN":return"男";case"WOMEN":return"女";case"OTHER":return"其他";default:break}},resetFormFace:function(){this.$refs.searchRef&&Reflect.has(this.$refs.searchRef,"resetForm")&&this.$refs.searchRef.resetForm(),this.clearFileHandle()},handlerImgClick:function(e){console.log("handlerImgClick",this.isShowPopImg,e),this.faceImgUrl!==e?(this.faceImgUrl=e,this.isShowPopImg=!0):this.isShowPopImg=!this.isShowPopImg},uploadError:function(e){console.log("error",e),this.isUploading=!1},closeCard:function(){console.log("closeCard"),this.isShowPopImg=!1}}},S=y,w=(a("cb0e"),a("2877")),k=Object(w["a"])(S,r,i,!1,null,"9447ad76",null);t["default"]=k.exports},bbee:function(e,t,a){},cb0e:function(e,t,a){"use strict";var r=a("bbee"),i=a.n(r);i.a}}]);
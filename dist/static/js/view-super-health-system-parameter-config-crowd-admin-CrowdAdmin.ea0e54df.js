(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-crowd-admin-CrowdAdmin"],{"2a3d":function(e,t,a){"use strict";var n=a("589c"),r=a.n(n);r.a},"589c":function(e,t,a){},"800b":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"crowd-admin container-wrapper"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"105px"},on:{search:e.searchHandle}}),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title",staticStyle:{width:"900px"}},[e._v(" 中国居民膳食营养素参考摄入量（孕妇与哺乳期在同龄人群参考值基础上额外增加量） ")]),a("div",{staticClass:"align-r"},[a("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(t){return e.addAndEditCrowd("add")}}},[e._v(" 创建人群 ")])],1)]),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[a("el-table-column",{attrs:{prop:"id",label:"编号",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"名称",align:"center",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.name))])]}}])}),a("el-table-column",{attrs:{prop:"group",label:"人群",align:"center"}}),a("el-table-column",{attrs:{prop:"",label:"年龄(岁)",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.min_age)+"~"+e._s(t.row.max_age))])]}}])}),a("el-table-column",{attrs:{prop:"",label:"标签规则",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.seeRule(t.row)}}},[e._v(" 查看 ")])]}}])}),a("el-table-column",{attrs:{prop:"operator_name",label:"创建人",align:"center"}}),a("el-table-column",{attrs:{prop:"create_time",label:"创建时间",align:"center"}}),a("el-table-column",{attrs:{fixed:"right",label:"操作",width:"180",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.addAndEditCrowd("modify",t.row)}}},[e._v(" 编辑 ")]),a("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(a){return e.deleteHaldler("del",t.row)}}},[e._v(" 删除 ")])]}}])})],1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-sizes":[5,10,20,30,40],"page-size":e.pageSize,layout:"total, prev, pager, next, sizes, jumper",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),a("el-dialog",{attrs:{title:"查看标签规则",visible:e.ruleDialog,"custom-class":"ps-dialog",width:"600"},on:{"update:visible":function(t){e.ruleDialog=t},close:e.handleClose}},[a("div",[a("span",[e._v("人群名称：")]),a("span",[e._v(e._s(e.labelRuleInfo.group))])]),a("div",{staticClass:"p-t-10 p-b-10"},[a("div",[e._v("推荐菜品标签：")]),e.labelRuleInfo.recommend_label&&e.labelRuleInfo.recommend_label.length?a("div",{staticClass:"p-t-10 p-b-10"},e._l(e.labelRuleInfo.recommend_label,(function(t,n){return a("el-tag",{key:n,staticClass:"m-r-10 m-b-5",attrs:{size:"small",effect:"plain",type:"info",color:"#fff"}},[a("i",{staticClass:"el-icon-success ps-green-text"}),e._v(" "+e._s(t.name)+" ")])})),1):e._e(),e.labelRuleInfo.recommend_tips?a("div",[a("span",[e._v("说明文案：")]),a("span",[e._v(e._s(e.labelRuleInfo.recommend_tips))])]):e._e()]),a("div",{staticClass:"p-t-10 p-b-10"},[a("div",[e._v("不建议菜品标签：")]),e.labelRuleInfo.not_recommend_label&&e.labelRuleInfo.not_recommend_label.length?a("div",{staticClass:"p-t-10 p-b-10"},e._l(e.labelRuleInfo.not_recommend_label,(function(t,n){return a("el-tag",{key:n,staticClass:"m-r-10 m-b-5",attrs:{size:"small",effect:"plain",type:"info",color:"#fff"}},[a("i",{staticClass:"el-icon-warning  ps-i"}),e._v(" "+e._s(t.name)+" ")])})),1):e._e(),e.labelRuleInfo.not_recommend_tips?a("div",[a("span",[e._v("说明文案：")]),a("span",[e._v(e._s(e.labelRuleInfo.not_recommend_tips))])]):e._e()])])],1)},r=[],i=a("a34a"),o=a.n(i),l=a("ed08");function s(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?s(Object(a),!0).forEach((function(t){u(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function u(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t){return b(e)||g(e,t)||f(e,t)||p()}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}function g(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],n=!0,r=!1,i=void 0;try{for(var o,l=e[Symbol.iterator]();!(n=(o=l.next()).done);n=!0)if(a.push(o.value),t&&a.length===t)break}catch(s){r=!0,i=s}finally{try{n||null==l["return"]||l["return"]()}finally{if(r)throw i}}return a}}function b(e){if(Array.isArray(e))return e}function h(e,t,a,n,r,i,o){try{var l=e[i](o),s=l.value}catch(c){return void a(c)}l.done?t(s):Promise.resolve(s).then(n,r)}function v(e){return function(){var t=this,a=arguments;return new Promise((function(n,r){var i=e.apply(t,a);function o(e){h(i,n,r,o,l,"next",e)}function l(e){h(i,n,r,o,l,"throw",e)}o(void 0)}))}}var y={name:"DietNutrition",data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{name:{type:"input",label:"名字",value:"",maxlength:20,placeholder:"请输入名字"},group:{type:"input",label:"人群",value:"",maxlength:20,placeholder:"请输入人群"},min_age:{type:"input",label:"最小年龄",value:"",maxlength:20,placeholder:"请输入最小年龄"},max_age:{type:"input",label:"最大年龄",value:"",maxlength:20,placeholder:"请输入最大年龄"}},ruleDialog:!1,labelRuleInfo:{}}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.currentPage=1,this.getAdminCrowdList()},searchHandle:Object(l["c"])((function(){this.currentPage=1,this.initLoad()}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),this.currentPage=1,this.initLoad()},getAdminCrowdList:function(){var e=this;return v(o.a.mark((function t(){var a,n,r,i,s,u;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!Object.keys(e.formatQueryParams(e.searchFormSetting))||!Object.keys(e.formatQueryParams(e.searchFormSetting)).length){t.next=5;break}if(a=/^\d+$/,n=e.formatQueryParams(e.searchFormSetting),!(n.min_age&&!a.test(n.min_age)||n.max_age&&!a.test(n.max_age))){t.next=5;break}return t.abrupt("return",e.$message.error("年龄需要输入整数"));case 5:return e.isLoading=!0,t.next=8,Object(l["Q"])(e.$apis.apiBackgroundHealthyAdminCrowdListPost(c(c({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 8:if(r=t.sent,i=d(r,2),s=i[0],u=i[1],e.isLoading=!1,!s){t.next=16;break}return e.$message.error(s.message),t.abrupt("return");case 16:0===u.code?(e.totalCount=u.data.count,e.tableData=u.data.results):e.$message.error(u.msg);case 17:case"end":return t.stop()}}),t)})))()},formatQueryParams:function(e){var t={};for(var a in e)e[a].value&&("select_date"!==a?t[a]=e[a].value:e[a].value.length>0&&(t.start_date=e[a].value[0],t.end_date=e[a].value[1]));return t},deleteHaldler:function(e,t){var a=this;this.$confirm("是否删除该人群？","删除",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-warn",center:!0,beforeClose:function(){var e=v(o.a.mark((function e(n,r,i){var l;return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("confirm"!==n){e.next=10;break}return r.confirmButtonLoading=!0,e.next=4,a.$apis.apiBackgroundHealthyAdminCrowdDeletePost({ids:[t.id]});case 4:l=e.sent,0===l.code?(a.$message.success("删除成功"),a.currentPage>1&&1===a.tableData.length&&a.currentPage--,a.getAdminCrowdList()):a.$message.error(l.msg),i(),r.confirmButtonLoading=!1,e.next=11;break;case 10:r.confirmButtonLoading||i();case 11:case"end":return e.stop()}}),e)})));function n(t,a,n){return e.apply(this,arguments)}return n}()}).then((function(e){})).catch((function(e){}))},seeRule:function(e){this.ruleDialog=!0,this.labelRuleInfo=e},handleClose:function(){},handleSizeChange:function(e){this.pageSize=e,this.getAdminCrowdList()},handleCurrentChange:function(e){this.currentPage=e,this.getAdminCrowdList()},gotoExport:function(){this.$message.error("暂无导出")},addAndEditCrowd:function(e,t){this.$router.push({name:"SuperAddOrModifyCrowd",params:{type:e},query:{type:e,data:"modify"===e?this.$encodeQuery(t):""}})}}},_=y,w=(a("2a3d"),a("2877")),C=Object(w["a"])(_,n,r,!1,null,"72a768e0",null);t["default"]=C.exports}}]);
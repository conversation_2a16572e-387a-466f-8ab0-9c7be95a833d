(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-merchant-admin-components-addOrganization"],{2057:function(t,e,a){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},7046:function(t,e,a){"use strict";var r=a("2057"),i=a.n(r);i.a},8237:function(module,exports,__webpack_require__){(function(process,global){var __WEBPACK_AMD_DEFINE_RESULT__;
/**
 * [js-md5]{@link https://github.com/emn178/js-md5}
 *
 * @namespace md5
 * @version 0.7.3
 * <AUTHOR> <PERSON><PERSON><PERSON> [<EMAIL>]
 * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2014-2017
 * @license MIT
 */(function(){"use strict";var ERROR="input is invalid type",WINDOW="object"===typeof window,root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"===typeof self,NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"===typeof process&&process.versions&&process.versions.node;NODE_JS?root=global:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&"object"===typeof module&&module.exports,AMD=__webpack_require__("3c35"),ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!==typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-**********],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(t){return"object"===typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});var createOutputMethod=function(t){return function(e){return new Md5(!0).update(e)[t]()}},createMethod=function(){var t=createOutputMethod("hex");NODE_JS&&(t=nodeWrap(t)),t.create=function(){return new Md5},t.update=function(e){return t.create().update(e)};for(var e=0;e<OUTPUT_TYPES.length;++e){var a=OUTPUT_TYPES[e];t[a]=createOutputMethod(a)}return t},nodeWrap=function(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(t){if("string"===typeof t)return crypto.createHash("md5").update(t,"utf8").digest("hex");if(null===t||void 0===t)throw ERROR;return t.constructor===ArrayBuffer&&(t=new Uint8Array(t)),Array.isArray(t)||ArrayBuffer.isView(t)||t.constructor===Buffer?crypto.createHash("md5").update(new Buffer(t)).digest("hex"):method(t)};return nodeMethod};function Md5(t){if(t)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var e=new ArrayBuffer(68);this.buffer8=new Uint8Array(e),this.blocks=new Uint32Array(e)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(t){if(!this.finalized){var e,a=typeof t;if("string"!==a){if("object"!==a)throw ERROR;if(null===t)throw ERROR;if(ARRAY_BUFFER&&t.constructor===ArrayBuffer)t=new Uint8Array(t);else if(!Array.isArray(t)&&(!ARRAY_BUFFER||!ArrayBuffer.isView(t)))throw ERROR;e=!0}var r,i,s=0,o=t.length,n=this.blocks,l=this.buffer8;while(s<o){if(this.hashed&&(this.hashed=!1,n[0]=n[16],n[16]=n[1]=n[2]=n[3]=n[4]=n[5]=n[6]=n[7]=n[8]=n[9]=n[10]=n[11]=n[12]=n[13]=n[14]=n[15]=0),e)if(ARRAY_BUFFER)for(i=this.start;s<o&&i<64;++s)l[i++]=t[s];else for(i=this.start;s<o&&i<64;++s)n[i>>2]|=t[s]<<SHIFT[3&i++];else if(ARRAY_BUFFER)for(i=this.start;s<o&&i<64;++s)r=t.charCodeAt(s),r<128?l[i++]=r:r<2048?(l[i++]=192|r>>6,l[i++]=128|63&r):r<55296||r>=57344?(l[i++]=224|r>>12,l[i++]=128|r>>6&63,l[i++]=128|63&r):(r=65536+((1023&r)<<10|1023&t.charCodeAt(++s)),l[i++]=240|r>>18,l[i++]=128|r>>12&63,l[i++]=128|r>>6&63,l[i++]=128|63&r);else for(i=this.start;s<o&&i<64;++s)r=t.charCodeAt(s),r<128?n[i>>2]|=r<<SHIFT[3&i++]:r<2048?(n[i>>2]|=(192|r>>6)<<SHIFT[3&i++],n[i>>2]|=(128|63&r)<<SHIFT[3&i++]):r<55296||r>=57344?(n[i>>2]|=(224|r>>12)<<SHIFT[3&i++],n[i>>2]|=(128|r>>6&63)<<SHIFT[3&i++],n[i>>2]|=(128|63&r)<<SHIFT[3&i++]):(r=65536+((1023&r)<<10|1023&t.charCodeAt(++s)),n[i>>2]|=(240|r>>18)<<SHIFT[3&i++],n[i>>2]|=(128|r>>12&63)<<SHIFT[3&i++],n[i>>2]|=(128|r>>6&63)<<SHIFT[3&i++],n[i>>2]|=(128|63&r)<<SHIFT[3&i++]);this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,e=this.lastByteIndex;t[e>>2]|=EXTRA[3&e],e>=56&&(this.hashed||this.hash(),t[0]=t[16],t[16]=t[1]=t[2]=t[3]=t[4]=t[5]=t[6]=t[7]=t[8]=t[9]=t[10]=t[11]=t[12]=t[13]=t[14]=t[15]=0),t[14]=this.bytes<<3,t[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var t,e,a,r,i,s,o=this.blocks;this.first?(t=o[0]-680876937,t=(t<<7|t>>>25)-271733879<<0,r=(-1732584194^2004318071&t)+o[1]-117830708,r=(r<<12|r>>>20)+t<<0,a=(-271733879^r&(-271733879^t))+o[2]-1126478375,a=(a<<17|a>>>15)+r<<0,e=(t^a&(r^t))+o[3]-1316259209,e=(e<<22|e>>>10)+a<<0):(t=this.h0,e=this.h1,a=this.h2,r=this.h3,t+=(r^e&(a^r))+o[0]-680876936,t=(t<<7|t>>>25)+e<<0,r+=(a^t&(e^a))+o[1]-389564586,r=(r<<12|r>>>20)+t<<0,a+=(e^r&(t^e))+o[2]+606105819,a=(a<<17|a>>>15)+r<<0,e+=(t^a&(r^t))+o[3]-1044525330,e=(e<<22|e>>>10)+a<<0),t+=(r^e&(a^r))+o[4]-176418897,t=(t<<7|t>>>25)+e<<0,r+=(a^t&(e^a))+o[5]+1200080426,r=(r<<12|r>>>20)+t<<0,a+=(e^r&(t^e))+o[6]-1473231341,a=(a<<17|a>>>15)+r<<0,e+=(t^a&(r^t))+o[7]-45705983,e=(e<<22|e>>>10)+a<<0,t+=(r^e&(a^r))+o[8]+1770035416,t=(t<<7|t>>>25)+e<<0,r+=(a^t&(e^a))+o[9]-1958414417,r=(r<<12|r>>>20)+t<<0,a+=(e^r&(t^e))+o[10]-42063,a=(a<<17|a>>>15)+r<<0,e+=(t^a&(r^t))+o[11]-1990404162,e=(e<<22|e>>>10)+a<<0,t+=(r^e&(a^r))+o[12]+1804603682,t=(t<<7|t>>>25)+e<<0,r+=(a^t&(e^a))+o[13]-40341101,r=(r<<12|r>>>20)+t<<0,a+=(e^r&(t^e))+o[14]-1502002290,a=(a<<17|a>>>15)+r<<0,e+=(t^a&(r^t))+o[15]+1236535329,e=(e<<22|e>>>10)+a<<0,t+=(a^r&(e^a))+o[1]-165796510,t=(t<<5|t>>>27)+e<<0,r+=(e^a&(t^e))+o[6]-1069501632,r=(r<<9|r>>>23)+t<<0,a+=(t^e&(r^t))+o[11]+643717713,a=(a<<14|a>>>18)+r<<0,e+=(r^t&(a^r))+o[0]-373897302,e=(e<<20|e>>>12)+a<<0,t+=(a^r&(e^a))+o[5]-701558691,t=(t<<5|t>>>27)+e<<0,r+=(e^a&(t^e))+o[10]+38016083,r=(r<<9|r>>>23)+t<<0,a+=(t^e&(r^t))+o[15]-660478335,a=(a<<14|a>>>18)+r<<0,e+=(r^t&(a^r))+o[4]-405537848,e=(e<<20|e>>>12)+a<<0,t+=(a^r&(e^a))+o[9]+568446438,t=(t<<5|t>>>27)+e<<0,r+=(e^a&(t^e))+o[14]-1019803690,r=(r<<9|r>>>23)+t<<0,a+=(t^e&(r^t))+o[3]-187363961,a=(a<<14|a>>>18)+r<<0,e+=(r^t&(a^r))+o[8]+1163531501,e=(e<<20|e>>>12)+a<<0,t+=(a^r&(e^a))+o[13]-1444681467,t=(t<<5|t>>>27)+e<<0,r+=(e^a&(t^e))+o[2]-51403784,r=(r<<9|r>>>23)+t<<0,a+=(t^e&(r^t))+o[7]+1735328473,a=(a<<14|a>>>18)+r<<0,e+=(r^t&(a^r))+o[12]-1926607734,e=(e<<20|e>>>12)+a<<0,i=e^a,t+=(i^r)+o[5]-378558,t=(t<<4|t>>>28)+e<<0,r+=(i^t)+o[8]-2022574463,r=(r<<11|r>>>21)+t<<0,s=r^t,a+=(s^e)+o[11]+1839030562,a=(a<<16|a>>>16)+r<<0,e+=(s^a)+o[14]-35309556,e=(e<<23|e>>>9)+a<<0,i=e^a,t+=(i^r)+o[1]-1530992060,t=(t<<4|t>>>28)+e<<0,r+=(i^t)+o[4]+1272893353,r=(r<<11|r>>>21)+t<<0,s=r^t,a+=(s^e)+o[7]-155497632,a=(a<<16|a>>>16)+r<<0,e+=(s^a)+o[10]-1094730640,e=(e<<23|e>>>9)+a<<0,i=e^a,t+=(i^r)+o[13]+681279174,t=(t<<4|t>>>28)+e<<0,r+=(i^t)+o[0]-358537222,r=(r<<11|r>>>21)+t<<0,s=r^t,a+=(s^e)+o[3]-722521979,a=(a<<16|a>>>16)+r<<0,e+=(s^a)+o[6]+76029189,e=(e<<23|e>>>9)+a<<0,i=e^a,t+=(i^r)+o[9]-640364487,t=(t<<4|t>>>28)+e<<0,r+=(i^t)+o[12]-421815835,r=(r<<11|r>>>21)+t<<0,s=r^t,a+=(s^e)+o[15]+530742520,a=(a<<16|a>>>16)+r<<0,e+=(s^a)+o[2]-995338651,e=(e<<23|e>>>9)+a<<0,t+=(a^(e|~r))+o[0]-198630844,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~a))+o[7]+1126891415,r=(r<<10|r>>>22)+t<<0,a+=(t^(r|~e))+o[14]-1416354905,a=(a<<15|a>>>17)+r<<0,e+=(r^(a|~t))+o[5]-57434055,e=(e<<21|e>>>11)+a<<0,t+=(a^(e|~r))+o[12]+1700485571,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~a))+o[3]-1894986606,r=(r<<10|r>>>22)+t<<0,a+=(t^(r|~e))+o[10]-1051523,a=(a<<15|a>>>17)+r<<0,e+=(r^(a|~t))+o[1]-2054922799,e=(e<<21|e>>>11)+a<<0,t+=(a^(e|~r))+o[8]+1873313359,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~a))+o[15]-30611744,r=(r<<10|r>>>22)+t<<0,a+=(t^(r|~e))+o[6]-1560198380,a=(a<<15|a>>>17)+r<<0,e+=(r^(a|~t))+o[13]+1309151649,e=(e<<21|e>>>11)+a<<0,t+=(a^(e|~r))+o[4]-145523070,t=(t<<6|t>>>26)+e<<0,r+=(e^(t|~a))+o[11]-1120210379,r=(r<<10|r>>>22)+t<<0,a+=(t^(r|~e))+o[2]+718787259,a=(a<<15|a>>>17)+r<<0,e+=(r^(a|~t))+o[9]-343485551,e=(e<<21|e>>>11)+a<<0,this.first?(this.h0=t+1732584193<<0,this.h1=e-271733879<<0,this.h2=a-1732584194<<0,this.h3=r+271733878<<0,this.first=!1):(this.h0=this.h0+t<<0,this.h1=this.h1+e<<0,this.h2=this.h2+a<<0,this.h3=this.h3+r<<0)},Md5.prototype.hex=function(){this.finalize();var t=this.h0,e=this.h1,a=this.h2,r=this.h3;return HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[a>>4&15]+HEX_CHARS[15&a]+HEX_CHARS[a>>12&15]+HEX_CHARS[a>>8&15]+HEX_CHARS[a>>20&15]+HEX_CHARS[a>>16&15]+HEX_CHARS[a>>28&15]+HEX_CHARS[a>>24&15]+HEX_CHARS[r>>4&15]+HEX_CHARS[15&r]+HEX_CHARS[r>>12&15]+HEX_CHARS[r>>8&15]+HEX_CHARS[r>>20&15]+HEX_CHARS[r>>16&15]+HEX_CHARS[r>>28&15]+HEX_CHARS[r>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(){this.finalize();var t=this.h0,e=this.h1,a=this.h2,r=this.h3;return[255&t,t>>8&255,t>>16&255,t>>24&255,255&e,e>>8&255,e>>16&255,e>>24&255,255&a,a>>8&255,a>>16&255,a>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var t=new ArrayBuffer(16),e=new Uint32Array(t);return e[0]=this.h0,e[1]=this.h1,e[2]=this.h2,e[3]=this.h3,t},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var t,e,a,r="",i=this.array(),s=0;s<15;)t=i[s++],e=i[s++],a=i[s++],r+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[63&(t<<4|e>>>4)]+BASE64_ENCODE_CHAR[63&(e<<2|a>>>6)]+BASE64_ENCODE_CHAR[63&a];return t=i[s],r+=BASE64_ENCODE_CHAR[t>>>2]+BASE64_ENCODE_CHAR[t<<4&63]+"==",r};var exports=createMethod();COMMON_JS?module.exports=exports:(root.md5=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))})()}).call(this,__webpack_require__("4362"),__webpack_require__("c8ba"))},c938:function(t){t.exports=JSON.parse('[{"id":"1","name":"互联网/电子商务"},{"id":"2","name":"IT软件与服务"},{"id":"3","name":"IT硬件与设备"},{"id":"4","name":"电子技术"},{"id":"5","name":"通信与运营商"},{"id":"6","name":"网络游戏"},{"id":"7","name":"银行"},{"id":"8","name":"基金|理财|信托"},{"id":"9","name":"保险"},{"id":"10","name":"餐饮"},{"id":"11","name":"酒店"},{"id":"12","name":"旅游"},{"id":"13","name":"快递"},{"id":"14","name":"物流"},{"id":"15","name":"仓储"},{"id":"16","name":"培训"},{"id":"17","name":"院校"},{"id":"18","name":"学术科研"},{"id":"19","name":"交警"},{"id":"20","name":"博物馆"},{"id":"21","name":"公共事业|非盈利机构"},{"id":"22","name":"医药医疗"},{"id":"23","name":"护理美容"},{"id":"24","name":"保健与卫生"},{"id":"25","name":"汽车相关"},{"id":"26","name":"摩托车相关"},{"id":"27","name":"火车相关"},{"id":"28","name":"飞机相关"},{"id":"29","name":"建筑"},{"id":"30","name":"物业"},{"id":"31","name":"消费品"},{"id":"32","name":"法律"},{"id":"33","name":"会展"},{"id":"34","name":"中介服务"},{"id":"35","name":"认证"},{"id":"36","name":"审计"},{"id":"37","name":"传媒"},{"id":"38","name":"体育"},{"id":"39","name":"娱乐休闲"},{"id":"40","name":"印刷"},{"id":"41","name":"其它"}]')},d0dd:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return i})),a.d(e,"g",(function(){return s})),a.d(e,"c",(function(){return o})),a.d(e,"f",(function(){return n})),a.d(e,"d",(function(){return l})),a.d(e,"e",(function(){return c}));var r=function(t,e,a){if(e){var r=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(e)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},i=function(t,e,a){if(e){var r=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(e)?a():a(new Error("金额格式有误"))}else a()},s=function(t,e,a){if(!e)return a(new Error("手机号不能为空"));var r=/^1[3456789]\d{9}$/;r.test(e)?a():a(new Error("请输入正确手机号"))},o=function(t,e,a){if(!e)return a(new Error("金额有误"));var r=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;r.test(e)?a():a(new Error("金额格式有误"))},n=function(t,e,a){if(""===e)return a(new Error("不能为空"));var r=/^\d+$/;r.test(e)?a():a(new Error("请输入正确数字"))},l=function(t,e,a){if(""!==e){var r=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;console.log(e,r.test(e)),r.test(e)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},c=function(t,e,a){var r=/^[\u4E00-\u9FA5\w-]+$/;r.test(e)?a():a(new Error("格式不正确，不能包含特殊字符"))}},fb6f:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container-wrapper super-add-organization is-fixed-footer"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"organizationFormRef",staticClass:"organization-form-wrapper",attrs:{rules:t.formDataRuls,model:t.formData,size:"small"}},["add"===t.operate?a("div",{staticClass:"add-title"},[t._v("添加组织层级")]):t._e(),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("基本信息")]),t.checkIsFormStatus?t._e():a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:t.changeOperate}},[t._v("编辑")])],1),a("div",{staticClass:"item-box clearfix"},[t.labelName?a("div",{staticClass:"item-b-l"},[t._v(t._s(t.labelName))]):t._e(),a("div",{class:{"item-b-r":t.labelName}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"组织名称：",prop:"name"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.name))])],1)],1)]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"当前组织层次：",prop:"levelName"}},[t.checkIsFormStatus?a("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{placeholder:""},model:{value:t.formData.levelTag,callback:function(e){t.$set(t.formData,"levelTag",e)},expression:"formData.levelTag"}},t._l(t.levelList,(function(t){return a("el-option",{key:t.level,attrs:{label:t.name,value:t.level}})})),1):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.levelName))])],1),a("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[a("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[a("el-form-item",{attrs:{label:"行业性质：",prop:"industry"}},[a("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{placeholder:"请选择行业性质",size:"small",disabled:!t.checkIsFormStatus},model:{value:t.formData.industry,callback:function(e){t.$set(t.formData,"industry",e)},expression:"formData.industry"}},t._l(t.industryTypeList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),a("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[a("el-form-item",{attrs:{label:"所在地址：",prop:"district"}},[a("el-cascader",{staticStyle:{display:"block"},attrs:{size:"small",options:t.addrOptions,disabled:!t.checkIsFormStatus},model:{value:t.formData.district,callback:function(e){t.$set(t.formData,"district",e)},expression:"formData.district"}})],1)],1)],1),a("div",{staticClass:"form-line"}),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("联系方式")])]),a("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"联系人：",prop:"contact"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.contact,callback:function(e){t.$set(t.formData,"contact",e)},expression:"formData.contact"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.contact))])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"block-label",attrs:{label:"手机号码：",prop:"mobile"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:t.formData.mobile,callback:function(e){t.$set(t.formData,"mobile",e)},expression:"formData.mobile"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.mobile))])],1)],1)],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"备注：",prop:"remark"}},[t.checkIsFormStatus?a("el-input",{staticClass:"ps-input",attrs:{type:"textarea",rows:3},model:{value:t.formData.remark,callback:function(e){t.$set(t.formData,"remark",e)},expression:"formData.remark"}}):a("div",{staticClass:"item-form-text"},[t._v(t._s(t.formData.remark))])],1),a("div",{staticClass:"form-line"}),a("div",{staticClass:"l-title clearfix"},[a("span",{staticClass:"float-l min-title-h"},[t._v("其它设置")])]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"钱包设置",prop:""}},[a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.storeWalletOn,callback:function(e){t.$set(t.formData,"storeWalletOn",e)},expression:"formData.storeWalletOn"}},[t._v("储值钱包")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.electronicWalletOn,callback:function(e){t.$set(t.formData,"electronicWalletOn",e)},expression:"formData.electronicWalletOn"}},[t._v("电子钱包")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.subsidyWalletOn,callback:function(e){t.$set(t.formData,"subsidyWalletOn",e)},expression:"formData.subsidyWalletOn"}},[t._v("补贴钱包")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.complimentaryWalletOn,callback:function(e){t.$set(t.formData,"complimentaryWalletOn",e)},expression:"formData.complimentaryWalletOn"}},[t._v("优惠钱包")]),a("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.otherWalletOn,callback:function(e){t.$set(t.formData,"otherWalletOn",e)},expression:"formData.otherWalletOn"}},[t._v("第三方钱包")])],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[t._v(" 钱包扣款规则 "),a("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:!t.checkIsFormStatus},model:{value:t.formData.isWalletPayOrderAsc,callback:function(e){t.$set(t.formData,"isWalletPayOrderAsc",e)},expression:"formData.isWalletPayOrderAsc"}},[a("el-radio",{attrs:{label:!1}},[t._v("扣上级钱包余额")]),a("el-radio",{attrs:{label:!0}},[t._v("扣下级钱包余额")])],1),a("div",{staticStyle:{"margin-left":"88px",color:"#ff9b45"}},[t._v("当订单所属组织余额不足时将扣取余额充足的上级/下级钱包金额;该规则仅适合线下消费")])],1),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[a("span",[t._v(" 组合支付 "),a("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.combineWalletOn,callback:function(e){t.$set(t.formData,"combineWalletOn",e)},expression:"formData.combineWalletOn"}})],1)]),a("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[a("span",[t._v(" 是否农行项目点展示 "),a("el-switch",{attrs:{disabled:!t.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:t.formData.isAbcProject,callback:function(e){t.$set(t.formData,"isAbcProject",e)},expression:"formData.isAbcProject"}})],1)]),t.checkIsFormStatus?a("div",{staticClass:"form-footer"},[a("el-button",{attrs:{size:"small"},on:{click:t.cancelFormHandle}},[t._v("取消")]),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.add","background.admin.organization.modify"],expression:"['background.admin.organization.add', 'background.admin.organization.modify']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.sendFormdataHandle}},[t._v("保存")])],1):t._e()],1)],1)},i=[],s=a("a34a"),o=a.n(s),n=a("ed08"),l=a("ef6c"),c=a("c938"),d=a("d0dd"),f=a("8237"),m=a.n(f);function u(t,e){return v(t)||_(t,e)||p(t,e)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(t,e){if(t){if("string"===typeof t)return b(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?b(t,e):void 0}}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function _(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],r=!0,i=!1,s=void 0;try{for(var o,n=t[Symbol.iterator]();!(r=(o=n.next()).done);r=!0)if(a.push(o.value),e&&a.length===e)break}catch(l){i=!0,s=l}finally{try{r||null==n["return"]||n["return"]()}finally{if(i)throw s}}return a}}function v(t){if(Array.isArray(t))return t}function y(t,e,a,r,i,s,o){try{var n=t[s](o),l=n.value}catch(c){return void a(c)}n.done?e(l):Promise.resolve(l).then(r,i)}function A(t){return function(){var e=this,a=arguments;return new Promise((function(r,i){var s=t.apply(e,a);function o(t){y(s,r,i,o,n,"next",t)}function n(t){y(s,r,i,o,n,"throw",t)}o(void 0)}))}}var k={name:"SuperAddOrganization",props:{type:String,infoData:{type:Object,default:function(){return{}}},parentData:Object,treeData:Object,id:[String,Number],operate:String,restoreHandle:Function},data:function(){var t=function(t,e,a){var r=/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/;e&&!r.test(e)?a(new Error("退款密码为数字与字母的组合，长度8到20位")):a()};return{labelName:"",formOperate:"detail",isLoading:!1,industryTypeList:c,addrOptions:l["regionData"],formData:{id:"",name:"",levelName:"",levelTag:"",permission:[],url:"",district:[],contact:"",mobile:"",mailAddress:"",tel:"",industry:"",remark:"",storeWalletOn:!1,electronicWalletOn:!1,subsidyWalletOn:!1,complimentaryWalletOn:!1,combineWalletOn:!1,otherWalletOn:!1,smsTemplateId:"",isAbcProject:!1,isWalletPayOrderAsc:!1},formDataRuls:{name:[{required:!0,message:"组织名称不能为空",trigger:"blur"},{validator:d["e"],trigger:"blur"}],level_name:[{required:!0,message:"层级名称不能为空",trigger:"blur"}],refundPassword:[{validator:t,trigger:"blur"}]},levelList:[],permissionTree:[],loadingThirdInfo:!1}},computed:{checkIsFormStatus:function(){var t=!1;switch(this.operate){case"add":t=!0;break;case"detail":t="detail"!==this.formOperate;break;default:t="detail"!==this.formOperate;break}return t}},watch:{operate:function(t,e){t||(this.formOperate="detail"),this.initLoad()}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.operate&&(this.formOperate=this.operate),"add"===this.operate?(this.getLevelList(this.parentData.company),this.formData.parent=this.parentData.id,this.formData.company=this.parentData.company):(this.getLevelList(this.treeData.company),this.labelName=this.treeData.name.substring(0,1),this.initInfoHandle())},refreshHandle:function(){this.initLoad()},searchHandle:Object(n["c"])((function(){}),300),initInfoHandle:function(){for(var t in this.formData){var e=this.infoData[Object(n["b"])(t)];if(e)switch(t){case"industry":this.formData[t]=e.toString();break;case"district":this.formData[t]=e?JSON.parse(e):[];break;case"refundPassword":this.formData[t]=e;break;default:this.formData[t]=e;break}}},deleteEmptyChildren:function(t,e){e=e||"children_list";var a=this;function r(t){t.map((function(t){a.checkIsFormStatus?t.isDisabled=!1:t.isDisabled=!0,t[e]&&t[e].length>0?r(t[e]):a.$delete(t,e)}))}return r(t),t},getLevelList:function(t){var e=this;return A(o.a.mark((function a(){var r,i,s,l,c;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r={},t&&(r.company_id=t),a.next=4,Object(n["Q"])(e.$apis.apiBackgroundAdminOrganizationGetLevelNameMapPost(r));case 4:if(i=a.sent,s=u(i,2),l=s[0],c=s[1],!l){a.next=11;break}return e.$message.error(l.message),a.abrupt("return");case 11:0===c.code?(e.levelList=[],c.data.length>0&&c.data.forEach((function(t){"add"===e.formOperate?(t.level===e.parentData.level_tag+1&&(e.formData.levelName=t.name,e.formData.levelTag=t.level),t.level>e.parentData.level_tag&&e.levelList.push(t)):t.level>=e.treeData.level_tag&&e.levelList.push(t)}))):e.$message.error(c.msg);case 12:case"end":return a.stop()}}),a)})))()},changeOperate:function(){switch(this.operate){case"add":break;default:"detail"===this.formOperate?this.formOperate="modify":this.formOperate="detail";break}this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children")},cancelFormHandle:function(){"add"===this.operate?this.$refs.organizationFormRef.resetFields():(this.$refs.organizationFormRef.clearValidate(),this.formOperate="detail",this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children")),this.restoreHandle(this.type,this.formOperate)},generateThirdAppinfo:function(){var t=this;return A(o.a.mark((function e(){var a,r,i,s;return o.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loadingThirdInfo=!0,e.next=3,Object(n["Q"])(t.$apis.apiBackgroundAdminOrganizationGenerateThirdAppinfoPost({id:t.id}));case 3:if(a=e.sent,r=u(a,2),i=r[0],s=r[1],t.loadingThirdInfo=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===s.code?(t.formData.thirdAppKey=s.data.third_app_key,t.formData.thirdSecretKey=s.data.third_secret_key):t.$message.error(s.msg);case 12:case"end":return e.stop()}}),e)})))()},sendFormdataHandle:function(){var t=this;this.$refs.organizationFormRef.validate((function(e){e&&("add"===t.operate?t.addRootOrganization(t.formatData()):t.modifyOrganization(t.formatData()))}))},formatData:function(){var t={status:"enable"};for(var e in this.formData){var a=this.formData[e];if(""!==a){switch(e){case"district":a=JSON.stringify(a);break;case"password":break;case"refundPassword":a=m()(a);break;case"thirdAppUrl":a=encodeURIComponent(a);break}"levelName"!==e&&(t[Object(n["b"])(e)]=a)}}return"modify"===this.formOperate&&(t.company=this.treeData.company),t},addRootOrganization:function(t){var e=this;return A(o.a.mark((function a(){var r,i,s,l;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(n["Q"])(e.$apis.apiBackgroundAdminOrganizationAddPost(t));case 3:if(r=a.sent,i=u(r,2),s=i[0],l=i[1],e.isLoading=!1,!s){a.next=11;break}return e.$message.error(s.message),a.abrupt("return");case 11:0===l.code?(e.formOperate="detail",e.$message.success("添加成功"),e.$refs.organizationFormRef.clearValidate(),e.restoreHandle(e.type,e.formOperate)):e.$message.error(l.msg);case 12:case"end":return a.stop()}}),a)})))()},modifyOrganization:function(t){var e=this;return A(o.a.mark((function a(){var r,i,s,l;return o.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.isLoading=!0,a.next=3,Object(n["Q"])(e.$apis.apiBackgroundAdminOrganizationModifyPost(t));case 3:if(r=a.sent,i=u(r,2),s=i[0],l=i[1],e.isLoading=!1,!s){a.next=11;break}return e.$message.error(s.message),a.abrupt("return");case 11:0===l.code?(e.$message.success("修改成功"),e.formOperate="detail",e.restoreHandle(e.type,e.formOperate)):e.$message.error(l.msg);case 12:case"end":return a.stop()}}),a)})))()}}},g=k,E=(a("7046"),a("2877")),D=Object(E["a"])(g,r,i,!1,null,null,null);e["default"]=D.exports}}]);
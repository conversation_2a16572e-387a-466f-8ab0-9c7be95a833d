(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-member-center-MemberList","view-super-health-system-member-center-components-MemberListDialog","view-super-health-system-member-center-constants"],{"1bf5":function(e,t,a){"use strict";var i=a("777d"),r=a.n(i);r.a},"5f6f":function(e,t,a){},7192:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"SuperMemberList container-wrapper"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting},on:{search:e.searchHandle}}),a("div",{staticClass:"table-wrapper"},[e._m(0),a("div",{staticClass:"table-content"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[a("el-table-column",{attrs:{prop:"user_id",label:"用户ID",align:"center",width:"180"}}),a("el-table-column",{attrs:{prop:"nickname",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"手机号",align:"center"}}),a("el-table-column",{attrs:{prop:"member_grade_name",label:"会员等级",align:"center"}}),a("el-table-column",{attrs:{prop:"member_labels",label:"会员标签",align:"center",width:"250"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.member_labels_list,(function(t){return a("el-tag",{key:t.id,staticStyle:{"margin-right":"8px"}},[e._v(" "+e._s(t.name)+" ")])}))}}])}),a("el-table-column",{attrs:{prop:"integral",label:"积分",align:"center"}}),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(a){return e.gotoMemberDetail(t.row)}}},[e._v("详情")]),a("el-button",{staticClass:"ps-green",attrs:{type:"text",size:"small"},on:{click:function(a){return e.openDialog("label",t.row)}}},[e._v("编辑标签")]),a("el-button",{staticClass:"ps-red",attrs:{type:"text",size:"small"},on:{click:function(a){return e.openDialog("growthScore",t.row)}}},[e._v("修改成长分")])]}}])})],1)],1),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"current-page":e.currentPage,"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange}})],1)]),a("member-list-dialog",{attrs:{isshow:e.dialogVisible,title:e.dialogTitle,type:e.dialogType,"select-info":e.selectInfo,confirm:e.searchHandle},on:{"update:isshow":function(t){e.dialogVisible=t}}})],1)},r=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")])])}],s=a("a34a"),n=a.n(s),l=a("ed08"),o=a("d337"),c=a("c8c2");function u(e,t,a,i,r,s,n){try{var l=e[s](n),o=l.value}catch(c){return void a(c)}l.done?t(o):Promise.resolve(o).then(i,r)}function d(e){return function(){var t=this,a=arguments;return new Promise((function(i,r){var s=e.apply(t,a);function n(e){u(s,i,r,n,l,"next",e)}function l(e){u(s,i,r,n,l,"throw",e)}n(void 0)}))}}var m={name:"SuperMemberList",components:{MemberListDialog:o["default"]},props:{},data:function(){return{isLoading:!1,pageSize:10,totalCount:0,currentPage:1,tableData:[],searchFormSetting:{nickname:{type:"input",label:"姓名",value:"",placeholder:"请输入姓名"},phone:{type:"input",label:"手机号",value:"",placeholder:"请输入手机号"},member_grade_ids:{type:"select",value:[],label:"会员等级",dataList:[],multiple:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0},member_labels:{type:"select",value:[],label:"会员标签",dataList:[],multiple:!0,collapseTags:!0,listNameKey:"name",listValueKey:"id",clearable:!0}},dialogVisible:!1,dialogTitle:"",dialogType:"",selectInfo:{}}},created:function(){this.getMemberLabel(),this.getMemberGrade(),this.initLoad()},mounted:function(){},methods:{initLoad:function(){this.getMemberList()},searchHandle:Object(l["c"])((function(){this.dialogVisible=!1,this.currentPage=1,this.getMemberList()}),300),refreshHandle:function(){this.currentPage=1,this.tableData=[],this.initLoad()},getMemberList:function(){var e=this;return d(n.a.mark((function t(){var a,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,a=Object(c["getRequestParams"])(e.searchFormSetting,e.currentPage,e.pageSize),t.next=4,e.$apis.apiBackgroundMemberMemberUserListPost(a);case 4:i=t.sent,e.isLoading=!1,0===i.code?(e.tableData=i.data.results,e.totalCount=i.data.count):e.$message.error(i.msg);case 7:case"end":return t.stop()}}),t)})))()},handleSizeChange:function(e){this.pageSize=e,this.getMemberList()},handleCurrentChange:function(e){this.currentPage=e,this.getMemberList()},openDialog:function(e,t){this.dialogType=e,this.selectInfo=t,"label"===e?this.dialogTitle="编辑标签":"integral"===e?this.dialogTitle="修改积分":"growthScore"===e&&(this.dialogTitle="修改成长分"),this.dialogVisible=!0},gotoMemberDetail:function(e){this.$router.push({name:"SuperMemberDetail",query:{data:JSON.stringify(e)}})},getMemberGrade:function(){var e=this;return d(n.a.mark((function t(){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberGradeListPost({page:1,page_size:99999});case 2:a=t.sent,0===a.code?e.searchFormSetting.member_grade_ids.dataList=a.data.results:e.$message.error(a.msg);case 4:case"end":return t.stop()}}),t)})))()},getMemberLabel:function(){var e=this;return d(n.a.mark((function t(){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberLabelListPost({page:1,page_size:99999});case 2:a=t.sent,0===a.code?e.searchFormSetting.member_labels.dataList=a.data.results:e.$message.error(a.msg);case 4:case"end":return t.stop()}}),t)})))()}}},p=m,b=(a("1bf5"),a("2877")),g=Object(b["a"])(p,i,r,!1,null,"2c81b6d6",null);t["default"]=g.exports},"777d":function(e,t,a){},"8fa2":function(e,t,a){"use strict";var i=a("5f6f"),r=a.n(i);r.a},c8c2:function(e,t,a){"use strict";a.r(t),a.d(t,"getRequestParams",(function(){return l})),a.d(t,"RECENTSEVEN",(function(){return o}));var i=a("5a0c");function r(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,i)}return a}function s(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?r(Object(a),!0).forEach((function(t){n(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):r(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function n(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var l=function(e,t,a){var i,r,n={};Object.keys(e).forEach((function(t){("select_time"!==t&&""!==e[t].value&&e[t].value&&0!==e[t].value.length||"boolean"===typeof e[t].value)&&(n[t]=e[t].value)}));var l=s({page:t,page_size:a},n);return 2===(null===(i=e.select_time)||void 0===i||null===(r=i.value)||void 0===r?void 0:r.length)&&(l.start_date=e.select_time.value[0],l.end_date=e.select_time.value[1]),l},o=[i().subtract(7,"day").format("YYYY-MM-DD"),i().format("YYYY-MM-DD")]},d337:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialog-message",{attrs:{show:e.visible,title:e.title,loading:e.isLoading,customClass:"ps-dialog",width:e.width},on:{"update:show":function(t){e.visible=t},"update:loading":function(t){e.isLoading=t},close:e.handleClose}},[a("el-form",{ref:"memberFormRef",staticClass:"member-form",attrs:{model:e.dialogForm,"status-icon":"",rules:e.dialogFormRules,"label-width":"120px"},nativeOn:{submit:function(e){e.preventDefault()}}},["label"===e.type?a("div",[a("el-form-item",{attrs:{label:"自动标签："}},[e._v(" "+e._s(e.autoLabelNameList.join("，"))+" ")]),a("el-form-item",{attrs:{label:"手动标签："}},[a("div",{staticClass:"label-list"},e._l(e.labelNameList,(function(t,i){return a("div",{key:t,staticClass:"label-list-item"},[a("span",{staticClass:"m-r-5"},[e._v(e._s(t))]),a("i",{staticClass:"el-icon-close del-icon",on:{click:function(t){return e.delLabel(i)}}})])})),0)]),a("el-form-item",{attrs:{label:"新增手动标签："}},[a("el-select",{staticClass:"ps-input",attrs:{placeholder:"请选择手动标签",multiple:"","collapse-tags":""},on:{change:e.changeSelectLabel},model:{value:e.dialogForm.selectLabelList,callback:function(t){e.$set(e.dialogForm,"selectLabelList",t)},expression:"dialogForm.selectLabelList"}},e._l(e.labelList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1):e._e(),"integral"===e.type?a("div",[a("el-form-item",{attrs:{label:"当前积分："}},[e._v(e._s(e.selectInfo.integral))]),a("el-form-item",{attrs:{label:"修改后积分：",prop:"score"}},[a("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.dialogForm.score,callback:function(t){e.$set(e.dialogForm,"score",t)},expression:"dialogForm.score"}})],1)],1):e._e(),"growthScore"===e.type?a("div",[a("el-form-item",{attrs:{label:"当前成长分："}},[e._v(e._s(e.selectInfo.growth_points))]),a("el-form-item",{attrs:{label:"添加成长分：",prop:"score"}},[a("el-input",{staticClass:"ps-input w-180",attrs:{maxlength:"7"},model:{value:e.dialogForm.score,callback:function(t){e.$set(e.dialogForm,"score",t)},expression:"dialogForm.score"}})],1),a("el-form-item",{attrs:{label:"修改后成长分："}},[e._v(e._s(Number(e.selectInfo.growth_points)+Number(e.dialogForm.score)))])],1):e._e()]),a("template",{slot:"tool"},[a("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"20px","text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"ps-cancel-btn",attrs:{disabled:e.isLoading},on:{click:e.clickCancleHandle}},[e._v(" 取消 ")]),a("el-button",{staticClass:"ps-btn",attrs:{disabled:e.isLoading,type:"primary"},on:{click:e.clickConfirmHandle}},[e._v(" 确定 ")])],1)])],2)},r=[],s=a("a34a"),n=a.n(s);function l(e,t,a,i,r,s,n){try{var l=e[s](n),o=l.value}catch(c){return void a(c)}l.done?t(o):Promise.resolve(o).then(i,r)}function o(e){return function(){var t=this,a=arguments;return new Promise((function(i,r){var s=e.apply(t,a);function n(e){l(s,i,r,n,o,"next",e)}function o(e){l(s,i,r,n,o,"throw",e)}n(void 0)}))}}var c={name:"ForLeaveRuleDialog",props:{loading:Boolean,type:{type:String,default:""},title:{type:String,default:""},width:{type:String,default:"450px"},selectInfo:{type:Object,default:function(){return{}}},isshow:Boolean,confirm:Function},data:function(){var e=function(e,t,a){if(""===t)return a(new Error("不能为空"));var i=/^\d+$/;i.test(t)?a():a(new Error("请输入正整数"))};return{isLoading:!1,dialogForm:{selectLabelList:[],score:""},dialogFormRules:{score:[{required:!0,validator:e,trigger:"blur"}]},autoLabelNameList:[],autoLabelIdList:[],labelNameList:[],labelList:[]}},computed:{visible:{get:function(){return this.isshow},set:function(e){this.$emit("update:isshow",e)}}},watch:{visible:function(){var e=this;this.visible?(this.labelNameList=[],this.autoLabelNameList=[],this.autoLabelIdList=[],this.dialogForm.selectLabelList=[],this.selectInfo.member_labels_list.map((function(t){"auto"===t.type?(e.autoLabelNameList.push(t.name),e.autoLabelIdList.push(t.id)):e.dialogForm.selectLabelList.push(t.id)})),this.getMemberLabel()):this.$refs.memberFormRef.resetFields()}},created:function(){this.initLoad()},mounted:function(){},methods:{initLoad:function(){},clickConfirmHandle:function(){var e=this;this.$refs.memberFormRef.validate((function(t){if(t){var a,i={};switch(e.type){case"growthScore":i={user_id:e.selectInfo.id,add_growth_value:e.dialogForm.score,obtain_type:"background_add"},a=e.$apis.apiBackgroundMemberMemberGradeGrowthAddPost(i);break;case"label":i={id:e.selectInfo.id,member_labels:e.autoLabelIdList.concat(e.dialogForm.selectLabelList)},a=e.$apis.apiBackgroundMemberMemberUserModifyPost(i);break}e.confirmOperation(a)}}))},confirmOperation:function(e){var t=this;return o(n.a.mark((function a(){var i;return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.isLoading){a.next=2;break}return a.abrupt("return");case 2:return t.isLoading=!0,a.next=5,e;case 5:i=a.sent,t.isLoading=!1,0===i.code?(t.$message.success("成功"),t.confirm()):t.$message.error(i.msg);case 8:case"end":return a.stop()}}),a)})))()},clickCancleHandle:function(){this.visible=!1},handleClose:function(e){this.isLoading=!1,this.visible=!1,this.$refs.memberFormRef.resetFields()},delLabel:function(e){this.dialogForm.selectLabelList.splice(e,1),this.labelNameList.splice(e,1)},getMemberLabel:function(){var e=this;return o(n.a.mark((function t(){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$apis.apiBackgroundMemberMemberLabelListPost({page:1,page_size:99999,type:"manual"});case 2:a=t.sent,0===a.code?(e.labelList=a.data.results,e.labelList.map((function(t){-1!==e.dialogForm.selectLabelList.indexOf(t.id)&&e.labelNameList.push(t.name)}))):e.$message.error(a.msg);case 4:case"end":return t.stop()}}),t)})))()},changeSelectLabel:function(){var e=this;this.labelNameList=[],this.labelList.map((function(t){-1!==e.dialogForm.selectLabelList.indexOf(t.id)&&e.labelNameList.push(t.name)}))}}},u=c,d=(a("8fa2"),a("2877")),m=Object(d["a"])(u,i,r,!1,null,"3d32e7a6",null);t["default"]=m.exports}}]);
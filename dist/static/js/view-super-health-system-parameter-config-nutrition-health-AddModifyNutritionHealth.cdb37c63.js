(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-parameter-config-nutrition-health-AddModifyNutritionHealth"],{af92:function(t,e,r){"use strict";var a=r("e7d5"),n=r.n(a);n.a},d0dd:function(t,e,r){"use strict";r.d(e,"a",(function(){return a})),r.d(e,"b",(function(){return n})),r.d(e,"g",(function(){return i})),r.d(e,"c",(function(){return o})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return l})),r.d(e,"e",(function(){return c}));var a=function(t,e,r){if(e){var a=/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},n=function(t,e,r){if(e){var a=/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(e)?r():r(new Error("金额格式有误"))}else r()},i=function(t,e,r){if(!e)return r(new Error("手机号不能为空"));var a=/^1[3456789]\d{9}$/;a.test(e)?r():r(new Error("请输入正确手机号"))},o=function(t,e,r){if(!e)return r(new Error("金额有误"));var a=/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;a.test(e)?r():r(new Error("金额格式有误"))},s=function(t,e,r){if(""===e)return r(new Error("不能为空"));var a=/^\d+$/;a.test(e)?r():r(new Error("请输入正确数字"))},l=function(t,e,r){if(""!==e){var a=/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;console.log(e,a.test(e)),a.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},c=function(t,e,r){var a=/^[\u4E00-\u9FA5\w-]+$/;a.test(e)?r():r(new Error("格式不正确，不能包含特殊字符"))}},da93:function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"add_modify_nutrition_health container-wrapper"},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small","label-width":"100px"}},[r("div",{staticClass:"table-wrapper"},[r("div",{staticClass:"table-header"},[r("div",{staticClass:"table-title"},[t._v("基本信息")])]),r("div",{staticStyle:{padding:"0 20px"}},[r("div",{staticClass:"font-size-20 p-b-20"},[t._v("分类")]),r("el-form-item",{attrs:{label:"分类名称",prop:"category"}},[r("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入运动名称","show-word-limit":""},model:{value:t.formData.category,callback:function(e){t.$set(t.formData,"category",e)},expression:"formData.category"}})],1),r("div",{staticClass:"font-size-20 p-b-20"},[t._v("三大营养素供能比")]),r("el-form-item",{attrs:{label:"碳水化合物",prop:"carbohydrate"}},[r("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入碳水化合物","show-word-limit":""},model:{value:t.formData.carbohydrate,callback:function(e){t.$set(t.formData,"carbohydrate",e)},expression:"formData.carbohydrate"}},[r("template",{slot:"append"},[t._v("%")])],2)],1),r("el-form-item",{attrs:{label:"蛋白质",prop:"protein"}},[r("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入蛋白质","show-word-limit":""},model:{value:t.formData.protein,callback:function(e){t.$set(t.formData,"protein",e)},expression:"formData.protein"}},[r("template",{slot:"append"},[t._v("%")])],2)],1),r("el-form-item",{attrs:{label:"脂肪",prop:"axunge"}},[r("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入脂肪","show-word-limit":""},model:{value:t.formData.axunge,callback:function(e){t.$set(t.formData,"axunge",e)},expression:"formData.axunge"}},[r("template",{slot:"append"},[t._v("%")])],2)],1),r("el-form-item",{attrs:{label:"",prop:""}},[r("span",{staticStyle:{color:"red"}},[t._v("注：占比相加必须等于100%")])]),r("div",{staticClass:"font-size-20 p-b-20"},[t._v("三餐能量分配")]),r("el-form-item",{attrs:{label:"早餐",prop:"breakfast"}},[r("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入早餐","show-word-limit":""},model:{value:t.formData.breakfast,callback:function(e){t.$set(t.formData,"breakfast",e)},expression:"formData.breakfast"}},[r("template",{slot:"append"},[t._v("%")])],2)],1),r("el-form-item",{attrs:{label:"午餐",prop:"lunch"}},[r("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入午餐","show-word-limit":""},model:{value:t.formData.lunch,callback:function(e){t.$set(t.formData,"lunch",e)},expression:"formData.lunch"}},[r("template",{slot:"append"},[t._v("%")])],2)],1),r("el-form-item",{attrs:{label:"晚餐",prop:"dinner"}},[r("el-input",{staticClass:"ps-input p-r-48",staticStyle:{width:"240px"},attrs:{placeholder:"请输入晚餐","show-word-limit":""},model:{value:t.formData.dinner,callback:function(e){t.$set(t.formData,"dinner",e)},expression:"formData.dinner"}},[r("template",{slot:"append"},[t._v("%")])],2)],1),r("el-form-item",{attrs:{label:"",prop:""}},[r("span",{staticStyle:{color:"red"}},[t._v("注：占比相加必须等于100%")])])],1)]),r("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[r("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),r("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"编辑")+" ")])],1)])],1)},n=[],i=r("a34a"),o=r.n(i),s=r("ed08"),l=r("d0dd");function c(t,e){return m(t)||p(t,e)||d(t,e)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"===typeof t)return f(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}function p(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var r=[],a=!0,n=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(a=(o=s.next()).done);a=!0)if(r.push(o.value),e&&r.length===e)break}catch(l){n=!0,i=l}finally{try{a||null==s["return"]||s["return"]()}finally{if(n)throw i}}return r}}function m(t){if(Array.isArray(t))return t}function h(t,e,r,a,n,i,o){try{var s=t[i](o),l=s.value}catch(c){return void r(c)}s.done?e(l):Promise.resolve(l).then(a,n)}function b(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var i=t.apply(e,r);function o(t){h(i,a,n,o,s,"next",t)}function s(t){h(i,a,n,o,s,"throw",t)}o(void 0)}))}}var v={name:"SuperAddEditArticle",data:function(){return{isLoading:!1,type:"add",formData:{category:"",carbohydrate:"",protein:"",axunge:"",breakfast:"",lunch:"",dinner:""},formRuls:{category:[{required:!0,message:"请输入文章标题",trigger:"blur"}],carbohydrate:[{required:!0,validator:l["f"],trigger:"blur"}],protein:[{required:!0,validator:l["f"],trigger:"blur"}],axunge:[{required:!0,validator:l["f"],trigger:"blur"}],breakfast:[{required:!0,validator:l["f"],trigger:"blur"}],lunch:[{required:!0,validator:l["f"],trigger:"blur"}],dinner:[{required:!0,validator:l["f"],trigger:"blur"}]}}},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,category:t.category,carbohydrate:t.carbohydrate,protein:t.protein,axunge:t.axunge,breakfast:t.breakfast,lunch:t.lunch,dinner:t.dinner}}},searchHandle:Object(s["c"])((function(){this.currentPage=1}),300),addModifyArticle:function(t){var e=this;return b(o.a.mark((function r(){var a,n,i,l,u,d,f,p;return o.a.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e.isLoading=!0,a="",n=c(a,2),i=n[0],l=n[1],"add"!==e.type){r.next=12;break}return r.next=6,Object(s["Q"])(e.$apis.apiBackgroundAdminHealthyInfoHealthyNutritionAddPost(t));case 6:u=r.sent,d=c(u,2),i=d[0],l=d[1],r.next=19;break;case 12:return r.next=15,Object(s["Q"])(e.$apis.apiBackgroundAdminHealthyInfoHealthyNutritionModifyPost(t));case 15:f=r.sent,p=c(f,2),i=p[0],l=p[1];case 19:if(e.isLoading=!1,!i){r.next=23;break}return e.$message.error(i.message),r.abrupt("return");case 23:0===l.code?(e.$message.success(l.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(l.msg);case 24:case"end":return r.stop()}}),r)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");console.log(t.formData),t.addModifyArticle(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,r,a){"confirm"===e?t.$closeCurrentTab(t.$route.path):r.confirmButtonLoading||a()}}).then((function(t){})).catch((function(t){}))}}},y=v,g=(r("af92"),r("2877")),w=Object(g["a"])(y,a,n,!1,null,null,null);e["default"]=w.exports},e7d5:function(t,e,r){}}]);
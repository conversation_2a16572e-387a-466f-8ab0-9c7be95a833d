(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-user-health-records-detail-HealthyOrg"],{"473c":function(t,e,a){"use strict";var n=a("db25"),r=a.n(n);r.a},6361:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"healthy-org records-wrapp-bg m-b-20"},[t._m(0),a("div",{staticClass:"text"},[t._v(" "+t._s(t.formData.org_name)+" ")])])},r=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"p-b-10"},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("所属组织")])])}],s={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(t){this.formData=t}},mounted:function(){},methods:{}},o=s,c=(a("473c"),a("2877")),i=Object(c["a"])(o,n,r,!1,null,"812fef84",null);e["default"]=i.exports},db25:function(t,e,a){}}]);
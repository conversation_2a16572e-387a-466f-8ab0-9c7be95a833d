(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-device-admin-ImportThirdPartyEquipment"],{"664f4":function(e,t,i){},"7f9c":function(e,t,i){"use strict";var r=i("664f4"),n=i.n(r);n.a},d22a:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("refresh-tool",{attrs:{title:e.title,"show-refresh":!1}}),i("import-page",{staticClass:"importPage",attrs:{initial:e.initial,url:e.url,"header-len":e.headerLen,"template-url":e.templateUrl}})],1)},n=[],a={name:"ImportIngredients",data:function(){return{type:"import",title:"批量第三方设备列表",headerLen:1,initial:!0,url:"apiBackgroundDeviceAdminThirdDeviceThirdDeviceBatchAddPost",templateUrl:"/api/temporary/template_excel/third_device_import.xlsx"}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{}},l=a,c=(i("7f9c"),i("2877")),o=Object(c["a"])(l,r,n,!1,null,null,null);t["default"]=o.exports}}]);
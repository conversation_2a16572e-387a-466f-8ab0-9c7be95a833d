(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-label-admin-FoodLabel","view-super-health-system-label-admin-components-merchantTag","view-super-health-system-label-admin-constants"],{"294f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._m(0),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[e._l(e.tableSetting,(function(t){return["label_list"===t.key?[a("el-table-column",{key:t.key,attrs:{label:t.label_list,prop:t.key,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.label_list,(function(t,r){return a("el-tag",{key:r,staticClass:"m-r-5 m-t-5",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff"}},[e._v(" "+e._s(t.name)+" ")])}))}}],null,!0)})]:[a("el-table-column",{key:t.key,attrs:{label:t.label,prop:t.key,align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(" "+e._s(a.row[t.key]||0===a.row[t.key]?a.row[t.key]:"--")+" ")]}}],null,!0)})]]})),a("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.sync_status?a("el-button",{staticClass:"ps-origin",attrs:{type:"text",size:"small",disabled:""}},[e._v(" 已同步 ")]):a("el-button",{staticClass:"ps-origin",attrs:{type:"text",size:"small"},on:{click:function(a){return e.syncClick(t.row)}}},[e._v(" 同步 ")])]}}])})],2),a("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[a("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1)],1)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-header"},[a("div",{staticClass:"table-title"},[e._v("数据列表")])])}],i=a("a34a"),l=a.n(i),s=a("ed08");function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach((function(t){u(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function u(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function p(e,t){return h(e)||d(e,t)||m(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return b(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?b(e,t):void 0}}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=new Array(t);a<t;a++)r[a]=e[a];return r}function d(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],r=!0,n=!1,i=void 0;try{for(var l,s=e[Symbol.iterator]();!(r=(l=s.next()).done);r=!0)if(a.push(l.value),t&&a.length===t)break}catch(o){n=!0,i=o}finally{try{r||null==s["return"]||s["return"]()}finally{if(n)throw i}}return a}}function h(e){if(Array.isArray(e))return e}function g(e,t,a,r,n,i,l){try{var s=e[i](l),o=s.value}catch(c){return void a(c)}s.done?t(o):Promise.resolve(o).then(r,n)}function y(e){return function(){var t=this,a=arguments;return new Promise((function(r,n){var i=e.apply(t,a);function l(e){g(i,r,n,l,s,"next",e)}function s(e){g(i,r,n,l,s,"throw",e)}l(void 0)}))}}var v={props:{searchFormSetting:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,tableData:[],tableSetting:[{key:"name",label:"标签组名称"},{key:"label_list",label:"标签"},{key:"organization_name",label:"来源"},{key:"sync_operator_name",label:"操作人"},{key:"sync_status_name",label:"是否同步"},{key:"create_time",label:"创建时间"},{key:"sync_time",label:"同步时间"}],pageSize:10,totalCount:0,currentPage:1}},created:function(){this.getLabelGroupMerchantList()},mounted:function(){},methods:{handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupMerchantList()},getLabelGroupMerchantList:function(){var e=this;return y(l.a.mark((function t(){var a,r,n,i;return l.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupMerchantListPost(c(c({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(a=t.sent,r=p(a,2),n=r[0],i=r[1],e.isLoading=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===i.code?(e.totalCount=i.data.count,e.tableData=i.data.results.map((function(e){return e.sync_status_name=e.sync_status?"是":"否",e}))):e.$message.error(i.msg);case 12:case"end":return t.stop()}}),t)})))()},syncClick:function(e){var t=this;this.$confirm("确定同步该数据？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var a=y(l.a.mark((function a(r,n,i){return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:"confirm"===r?(t.getSyncLabelGroup(e.id),i(),n.confirmButtonLoading=!1):n.confirmButtonLoading||i();case 1:case"end":return a.stop()}}),a)})));function r(e,t,r){return a.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}))},getSyncLabelGroup:function(e){var t=this;return y(l.a.mark((function a(){var r,n,i,o;return l.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupSyncLabelGroupPost({id:e}));case 3:if(r=a.sent,n=p(r,2),i=n[0],o=n[1],t.isLoading=!1,!i){a.next=11;break}return t.$message.error(i.message),a.abrupt("return");case 11:0===o.code?(t.$message.success("同步成功"),t.tableData=[],t.getLabelGroupMerchantList()):t.$message.error(o.msg);case 12:case"end":return a.stop()}}),a)})))()},formatQueryParams:function(e){var t={};for(var a in e)""!==e[a].value&&("select_time"!==a?t[a]=e[a].value:e[a].value&&e[a].value.length>0&&(t.start_time=e[a].value[0],t.end_time=e[a].value[1]));return t}}},_=v,L=a("2877"),k=Object(L["a"])(_,r,n,!1,null,"46eca2b5",null);t["default"]=k.exports},"3dbb":function(e,t,a){},4846:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"food-label container-wrapper"},[a("refresh-tool",{on:{refreshPage:e.refreshHandle}}),a("search-form",{ref:"searchRef",attrs:{"form-setting":e.searchFormSetting,"label-width":"125px"},on:{search:e.searchHandle}},[a("template",{slot:"perv"},[a("div",{staticClass:"tab"},[a("div",{class:["tab-item","platform"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("platform")}}},[e._v(" 平台标签 ")]),a("div",{class:["tab-item","merchant"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("merchant")}}},[e._v(" 商户标签 ")])])])],2),a("div",{staticClass:"table-wrapper"},[a("div",{staticClass:"table-content"},["platform"===e.tabType?a("div",[a("platform-tag",{ref:"platform",attrs:{searchFormSetting:e.searchFormSetting}})],1):"merchant"===e.tabType?a("div",[a("merchant-tag",{ref:"merchant",attrs:{searchFormSetting:e.searchFormSetting}})],1):e._e()])])],1)},n=[],i=a("ed08"),l=a("f5d9"),s=a("843f"),o=a("294f"),c={data:function(){return{tabType:"platform",searchFormSetting:l["PLATFORM_LABEL"],isLoading:!1}},components:{platformTag:s["default"],merchantTag:o["default"]},created:function(){},mounted:function(){},methods:{searchHandle:Object(i["c"])((function(){console.log("123123123"),"platform"===this.tabType?(this.$refs.platform.currentPage=1,this.$refs.platform.getLabelGroupList()):(this.$refs.merchant.currentPage=1,this.$refs.merchant.getLabelGroupMerchantList())}),300),refreshHandle:function(){this.$refs.searchRef.resetForm(),"platform"===this.tabType?(this.$refs.platform.currentPage=1,this.$refs.platform.getLabelGroupList()):(this.$refs.merchant.currentPage=1,this.$refs.merchant.getLabelGroupMerchantList())},tabClick:function(e){this.tabType=e,this.searchFormSetting="platform"===e?l["PLATFORM_LABEL"]:l["MERCHANT_LABEL"]}}},u=c,p=(a("76b4"),a("2877")),f=Object(p["a"])(u,r,n,!1,null,"0ae22aec",null);t["default"]=f.exports},"76b4":function(e,t,a){"use strict";var r=a("3dbb"),n=a.n(r);n.a},f5d9:function(e,t,a){"use strict";a.r(t),a.d(t,"recentSevenDay",(function(){return n})),a.d(t,"PLATFORM_LABEL",(function(){return i})),a.d(t,"INGREDIENTS_LABEL",(function(){return l})),a.d(t,"MERCHANT_LABEL",(function(){return s})),a.d(t,"USER_LABEL",(function(){return o}));var r=a("5a0c"),n=[r().subtract(7,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD")],i={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称"},consume_organization_ids:{type:"organizationSelect",value:[],label:"可见范围",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,role:"super"}},l={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称",maxlength:15},consume_organization_ids:{type:"organizationSelect",value:[],label:"可见范围",listNameKey:"name",listValueKey:"id",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,role:"super"}},s={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称",maxlength:15},select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"同步时间",value:"",clearable:!0},sync_status:{type:"select",value:"",label:"是否同步",clearable:!1,dataList:[{label:"是",value:!0},{label:"否",value:!1}]}},o={name:{type:"input",label:"标签组/标签名称",value:"",placeholder:"请输入标签组/标签名称"}}}}]);
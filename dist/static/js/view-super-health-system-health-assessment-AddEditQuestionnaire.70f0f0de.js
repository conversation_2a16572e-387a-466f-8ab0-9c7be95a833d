(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-health-assessment-AddEditQuestionnaire"],{"0345":function(t,e,s){"use strict";var a=s("f31f6"),i=s.n(a);i.a},"14df":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"super-add-ingredients container-wrapper"},[s("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"formIngredients",attrs:{rules:t.formRuls,model:t.formData,size:"small"}},[s("div",{staticClass:"table-wrapper"},[s("div",{staticClass:"table-header"},[s("div",{staticClass:"table-title"},[t._v("基本信息")])]),s("div",{staticStyle:{"max-width":"300px",padding:"0 20px"}},[s("el-form-item",{staticClass:"block-label",attrs:{label:"测评名称",prop:"name"}},[s("el-input",{staticClass:"p-r-48 p-b-10",attrs:{placeholder:"请输入测评名称",type:"textarea",autosize:{minRows:0,maxRows:2},maxlength:"25","show-word-limit":""},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1)],1),s("div",{staticStyle:{"max-width":"350px",padding:"0 20px"}},[s("el-form-item",{attrs:{label:"测评介绍",prop:"content"}},[s("el-input",{staticClass:"p-r-48 p-b-10",attrs:{type:"textarea",placeholder:"请输入测评介绍",autosize:{minRows:4,maxRows:10},maxlength:"200","show-word-limit":""},model:{value:t.formData.content,callback:function(e){t.$set(t.formData,"content",e)},expression:"formData.content"}})],1)],1)]),s("div",{staticClass:"table-wrapper"},[s("div",{staticClass:"table-header"},[s("div",{staticClass:"table-title"},[t._v(" 测评题编写 "),s("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addQuestionnaire()}}},[t._v(" 添加题目 ")])],1)]),s("div",{staticClass:"table-content"},t._l(t.formData.topic_list,(function(e,a){return s("div",{key:a,staticStyle:{"max-width":"600px",padding:"0 20px"}},[s("el-form-item",{attrs:{label:a+1+"、",prop:"topic_list."+a+".content",rules:{required:!0,message:"请输入问题",trigger:"blur"},"label-width":"30px"}},[s("el-input",{staticClass:"p-r-48 p-b-10",attrs:{type:"textarea",placeholder:"请输入问题",autosize:{minRows:4,maxRows:10},maxlength:"200","show-word-limit":""},model:{value:e.content,callback:function(s){t.$set(e,"content",s)},expression:"questionnaireItem.content"}})],1),s("div",{staticClass:"ps-flex-align-c flex-align-c p-l-20"},[s("el-radio-group",{staticClass:"ps-radio",model:{value:e.choice_type,callback:function(s){t.$set(e,"choice_type",s)},expression:"questionnaireItem.choice_type"}},[s("el-radio",{attrs:{label:"single"}},[t._v("单选")]),s("el-radio",{attrs:{label:"multiple"}},[t._v("多选")])],1),s("el-select",{staticStyle:{"margin-left":"20px"},attrs:{size:"small"},model:{value:e.is_required,callback:function(s){t.$set(e,"is_required",s)},expression:"questionnaireItem.is_required"}},[s("el-option",{attrs:{label:"必填",value:"required"}}),s("el-option",{attrs:{label:"非必填",value:"not_required"}})],1),t.formData.topic_list.length>1?s("div",{staticClass:"ps-flex-align-c flex-align-c cursor: pointer"},[0!=a?s("div",{staticClass:"p-l-20",on:{click:function(e){return t.handleMove(a,"up",t.formData.topic_list)}}},[t._v(" 上移 "),s("i",{staticClass:"el-icon-top"})]):t._e(),s("div",{staticClass:"p-l-20",on:{click:function(e){return t.handleMove(a,"down",t.formData.topic_list)}}},[t._v(" 下移 "),s("i",{staticClass:"el-icon-bottom"})]),s("i",{staticClass:"el-icon-circle-close p-l-20 font-size-16",on:{click:function(e){return t.questionnaireRemove(a)}}})]):t._e()],1),t._l(e.options_text,(function(i,n){return s("div",{key:n,staticClass:"ps-flex-align-c p-t-20"},[s("el-form-item",{attrs:{label:t.chooseNumStr(n)+"、",prop:"topic_list."+a+".options_text."+n+".options_text",rules:{required:!0,message:"请输入选项",trigger:"blur"},"label-width":"30px"}},[s("el-input",{staticClass:"p-r-10",staticStyle:{width:"300px"},attrs:{type:"textarea",autosize:{minRows:0,maxRows:2},maxlength:"32"},model:{value:i.options_text,callback:function(e){t.$set(i,"options_text",e)},expression:"optionItem.options_text"}})],1),s("el-form-item",{attrs:{"label-width":"20px",prop:"topic_list."+a+".options_text."+n+".options_number",rules:{required:!0,message:"请输入分数",trigger:"blur"}}},[s("el-input",{staticClass:"p-r-10",staticStyle:{width:"120px"},model:{value:i.options_number,callback:function(e){t.$set(i,"options_number",e)},expression:"optionItem.options_number"}}),s("span",[t._v("分")])],1),s("div",{staticClass:"p-l-20"},[e.options_text.length>1?s("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.optionRemove(a,n)}}},[t._v(" 删除 ")]):t._e()],1)],1)})),s("div",{staticClass:"p-b-20 p-l-20",staticStyle:{color:"#fda04d",cursor:"pointer",width:"110px"},on:{click:function(s){return t.addOption(a,e.options_text)}}},[s("i",{staticClass:"el-icon-plus"}),t._v(" 添加选项 ")])],2)})),0)]),s("div",{staticClass:"table-wrapper"},[s("div",{staticClass:"table-header"},[s("div",{staticClass:"table-title"},[t._v(" 评价编写 "),s("button-icon",{attrs:{color:"origin",type:"add"},on:{click:function(e){return t.addEvaluate()}}},[t._v("添加评价")])],1)]),s("div",{staticClass:"p-l-20",staticStyle:{color:"#a5a5a5"}},[t._v(' 注：填写分数时，符号表示：“小于”=“>”、"大于"=“>”、“小于等于”=“>=”、“大于等于”=“>=”\' ')]),s("div",{staticClass:"table-content"},t._l(t.formData.assess_list,(function(e,a){return s("div",{key:a,staticStyle:{"max-width":"600px",padding:"0 20px"}},[s("div",{staticClass:"ps-flex-align-c p-t-20"},[s("el-form-item",{attrs:{label:a+1+"、","label-width":"30px"}},[s("span",{staticClass:"p-r-10"},[t._v("分数")]),s("el-select",{staticStyle:{width:"120px"},attrs:{size:"small"},model:{value:e.comparison_operator,callback:function(s){t.$set(e,"comparison_operator",s)},expression:"evaluateItem.comparison_operator"}},[s("el-option",{attrs:{label:"等于",value:"="}}),s("el-option",{attrs:{label:"大于",value:">"}}),s("el-option",{attrs:{label:"小于",value:"<"}}),s("el-option",{attrs:{label:"大于等于",value:">="}}),s("el-option",{attrs:{label:"小于等于",value:"<="}})],1)],1),s("el-form-item",{attrs:{prop:"assess_list."+a+".assess_number",rules:{required:!0,message:"请输入分数",trigger:"blur"},"label-width":"30px"}},[s("el-input",{staticStyle:{width:"120px"},attrs:{placeholder:"分数"},model:{value:e.assess_number,callback:function(s){t.$set(e,"assess_number",s)},expression:"evaluateItem.assess_number"}})],1)],1),s("el-form-item",{attrs:{"label-width":"30px",prop:"assess_list."+a+".assess_name",rules:{required:!0,message:"请输入标题",trigger:"blur"}}},[s("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入标题",type:"textarea",autosize:{minRows:0,maxRows:2},maxlength:"32"},model:{value:e.assess_name,callback:function(s){t.$set(e,"assess_name",s)},expression:"evaluateItem.assess_name"}}),s("span",{staticClass:"p-l-10"},[t._v("标题")])],1),s("el-form-item",{attrs:{"label-width":"30px",prop:"assess_list."+a+".assess_content",rules:{required:!0,message:"请输入问题",trigger:"blur"}}},[s("div",{staticClass:"ps-flex-align-c flex-align-c"},[s("el-input",{staticClass:"p-r-48 p-b-10",attrs:{type:"textarea",placeholder:"请输入问题",autosize:{minRows:4,maxRows:10},maxlength:"200","show-word-limit":""},model:{value:e.assess_content,callback:function(s){t.$set(e,"assess_content",s)},expression:"evaluateItem.assess_content"}}),t.formData.assess_list.length>1?s("div",{staticClass:"p-l-20"},[s("el-button",{staticClass:"ps-warn-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.evaluateRemove(a)}}},[t._v(" 删除 ")])],1):t._e()],1)])],1)})),0)]),s("div",{staticClass:"footer",staticStyle:{"margin-top":"20px"}},[s("el-button",{staticStyle:{width:"120px"},on:{click:t.closeHandler}},[t._v("取消")]),s("el-button",{staticClass:"ps-origin-btn",staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:t.submitHandler}},[t._v(" "+t._s("add"===t.type?"添加":"编辑")+" ")])],1)])],1)},i=[],n=s("a34a"),o=s.n(n),r=s("ed08");function l(t,e){return m(t)||d(t,e)||u(t,e)||c()}function c(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return p(t,e);var s=Object.prototype.toString.call(t).slice(8,-1);return"Object"===s&&t.constructor&&(s=t.constructor.name),"Map"===s||"Set"===s?Array.from(t):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?p(t,e):void 0}}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var s=0,a=new Array(e);s<e;s++)a[s]=t[s];return a}function d(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var s=[],a=!0,i=!1,n=void 0;try{for(var o,r=t[Symbol.iterator]();!(a=(o=r.next()).done);a=!0)if(s.push(o.value),e&&s.length===e)break}catch(l){i=!0,n=l}finally{try{a||null==r["return"]||r["return"]()}finally{if(i)throw n}}return s}}function m(t){if(Array.isArray(t))return t}function f(t,e,s,a,i,n,o){try{var r=t[n](o),l=r.value}catch(c){return void s(c)}r.done?e(l):Promise.resolve(l).then(a,i)}function v(t){return function(){var e=this,s=arguments;return new Promise((function(a,i){var n=t.apply(e,s);function o(t){f(n,a,i,o,r,"next",t)}function r(t){f(n,a,i,o,r,"throw",t)}o(void 0)}))}}var _={name:"SuperAddIngredients",data:function(){return{isLoading:!1,type:"add",formData:{name:"",content:"",topic_list:[{content:"",choice_type:"single",is_required:"not_required",options_text:[{options_text:"",options_number:""}]}],assess_list:[{comparison_operator:"=",assess_number:"",assess_name:"",assess_content:""}]},formRuls:{name:[{required:!0,message:"请输入测评名称",trigger:"blur"}],content:[{required:!0,message:"请输入测评介绍",trigger:"blur"}]},categoryList:[]}},created:function(){this.type=this.$route.query.type,this.initLoad()},mounted:function(){},methods:{initLoad:function(){if("modify"===this.type){var t=this.$decodeQuery(this.$route.query.data);this.formData={id:t.id,name:t.name,content:t.content,topic_list:t.topic,assess_list:t.assess},console.log(t)}},searchHandle:Object(r["c"])((function(){this.currentPage=1}),300),chooseNumStr:function(t){var e=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];return e[t]},addQuestionnaire:function(){this.formData.topic_list.push({content:"",choice_type:"single",is_required:"not_required",options_text:[{options_text:"",options_number:""}]})},questionnaireRemove:function(t){this.formData.topic_list.splice(t,1)},addOption:function(t,e){this.formData.topic_list[t].options_text.push({options_text:"",options_number:""})},optionRemove:function(t,e){this.formData.topic_list[t].options_text.splice(e,1)},handleMove:function(t,e,s){if("up"===e){if(0===t)return;var a=s[t-1];s.splice(t-1,1),s.splice(t,0,a)}else{if(t===s.length-1)return;var i=s[t+1];s.splice(t+1,1),s.splice(t,0,i)}},addEvaluate:function(){this.formData.assess_list.push({comparison_operator:"=",assess_number:"",assess_name:"",assess_content:""})},evaluateRemove:function(t){this.formData.assess_list.splice(t,1)},addModifyHealthyQuestion:function(t){var e=this;return v(o.a.mark((function s(){var a,i,n,c,u,p,d,m;return o.a.wrap((function(s){while(1)switch(s.prev=s.next){case 0:if(e.isLoading=!0,a="",i=l(a,2),n=i[0],c=i[1],"add"!==e.type){s.next=12;break}return s.next=6,Object(r["Q"])(e.$apis.apiBackgroundHealthyQuestionAddPost(t));case 6:u=s.sent,p=l(u,2),n=p[0],c=p[1],s.next=19;break;case 12:return s.next=15,Object(r["Q"])(e.$apis.apiBackgroundHealthyQuestionModifyPost(t));case 15:d=s.sent,m=l(d,2),n=m[0],c=m[1];case 19:if(e.isLoading=!1,!n){s.next=23;break}return e.$message.error(n.message),s.abrupt("return");case 23:0===c.code?(e.$message.success(c.msg),e.$closeCurrentTab(e.$route.path)):e.$message.error(c.msg);case 24:case"end":return s.stop()}}),s)})))()},submitHandler:function(){var t=this;this.$refs.formIngredients.validate((function(e){if(e){if(t.isLoading)return t.$message.error("请勿重复提交！");t.addModifyHealthyQuestion(t.formData)}}))},closeHandler:function(){var t=this;this.$confirm("当前信息还没保存，是否退出？",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(e,s,a){"confirm"===e?t.$closeCurrentTab(t.$route.path):s.confirmButtonLoading||a()}}).then((function(t){})).catch((function(t){}))}}},b=_,h=(s("0345"),s("2877")),x=Object(h["a"])(b,a,i,!1,null,null,null);e["default"]=x.exports},f31f6:function(t,e,s){}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["view-super-health-system-label-admin-components-merchantTag"],{"294f":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e._m(0),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","header-row-class-name":"ps-table-header-row"}},[e._l(e.tableSetting,(function(t){return["label_list"===t.key?[n("el-table-column",{key:t.key,attrs:{label:t.label_list,prop:t.key,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.label_list,(function(t,r){return n("el-tag",{key:r,staticClass:"m-r-5 m-t-5",attrs:{size:"medium",effect:"plain",type:"info",color:"#fff"}},[e._v(" "+e._s(t.name)+" ")])}))}}],null,!0)})]:[n("el-table-column",{key:t.key,attrs:{label:t.label,prop:t.key,align:"center"},scopedSlots:e._u([{key:"default",fn:function(n){return[e._v(" "+e._s(n.row[t.key]||0===n.row[t.key]?n.row[t.key]:"--")+" ")]}}],null,!0)})]]})),n("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.sync_status?n("el-button",{staticClass:"ps-origin",attrs:{type:"text",size:"small",disabled:""}},[e._v(" 已同步 ")]):n("el-button",{staticClass:"ps-origin",attrs:{type:"text",size:"small"},on:{click:function(n){return e.syncClick(t.row)}}},[e._v(" 同步 ")])]}}])})],2),n("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[n("el-pagination",{staticClass:"ps-text",attrs:{"page-size":e.pageSize,"current-page":e.currentPage,layout:"total, prev, pager, next",total:e.totalCount,background:"","popper-class":"ps-popper-select"},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t}}})],1)],1)},a=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"table-header"},[n("div",{staticClass:"table-title"},[e._v("数据列表")])])}],o=n("a34a"),i=n.n(o),s=n("ed08");function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){return m(e)||d(e,t)||b(e,t)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(e,t){if(e){if("string"===typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function d(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,a=!1,o=void 0;try{for(var i,s=e[Symbol.iterator]();!(r=(i=s.next()).done);r=!0)if(n.push(i.value),t&&n.length===t)break}catch(c){a=!0,o=c}finally{try{r||null==s["return"]||s["return"]()}finally{if(a)throw o}}return n}}function m(e){if(Array.isArray(e))return e}function y(e,t,n,r,a,o,i){try{var s=e[o](i),c=s.value}catch(l){return void n(l)}s.done?t(c):Promise.resolve(c).then(r,a)}function h(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function i(e){y(o,r,a,i,s,"next",e)}function s(e){y(o,r,a,i,s,"throw",e)}i(void 0)}))}}var v={props:{searchFormSetting:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,tableData:[],tableSetting:[{key:"name",label:"标签组名称"},{key:"label_list",label:"标签"},{key:"organization_name",label:"来源"},{key:"sync_operator_name",label:"操作人"},{key:"sync_status_name",label:"是否同步"},{key:"create_time",label:"创建时间"},{key:"sync_time",label:"同步时间"}],pageSize:10,totalCount:0,currentPage:1}},created:function(){this.getLabelGroupMerchantList()},mounted:function(){},methods:{handleCurrentChange:function(e){this.currentPage=e,this.getLabelGroupMerchantList()},getLabelGroupMerchantList:function(){var e=this;return h(i.a.mark((function t(){var n,r,a,o;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.isLoading=!0,t.next=3,Object(s["Q"])(e.$apis.apiBackgroundHealthyAdminLabelGroupMerchantListPost(l(l({},e.formatQueryParams(e.searchFormSetting)),{},{page:e.currentPage,page_size:e.pageSize})));case 3:if(n=t.sent,r=p(n,2),a=r[0],o=r[1],e.isLoading=!1,!a){t.next=11;break}return e.$message.error(a.message),t.abrupt("return");case 11:0===o.code?(e.totalCount=o.data.count,e.tableData=o.data.results.map((function(e){return e.sync_status_name=e.sync_status?"是":"否",e}))):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},syncClick:function(e){var t=this;this.$confirm("确定同步该数据？","提示",{confirmButtonText:this.$t("dialog.confirm_btn"),cancelButtonText:this.$t("dialog.cancel_btn"),closeOnClickModal:!1,customClass:"ps-confirm",cancelButtonClass:"ps-cancel-btn",confirmButtonClass:"ps-btn",center:!0,beforeClose:function(){var n=h(i.a.mark((function n(r,a,o){return i.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:"confirm"===r?(t.getSyncLabelGroup(e.id),o(),a.confirmButtonLoading=!1):a.confirmButtonLoading||o();case 1:case"end":return n.stop()}}),n)})));function r(e,t,r){return n.apply(this,arguments)}return r}()}).then((function(e){})).catch((function(e){}))},getSyncLabelGroup:function(e){var t=this;return h(i.a.mark((function n(){var r,a,o,c;return i.a.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.isLoading=!0,n.next=3,Object(s["Q"])(t.$apis.apiBackgroundHealthyAdminLabelGroupSyncLabelGroupPost({id:e}));case 3:if(r=n.sent,a=p(r,2),o=a[0],c=a[1],t.isLoading=!1,!o){n.next=11;break}return t.$message.error(o.message),n.abrupt("return");case 11:0===c.code?(t.$message.success("同步成功"),t.tableData=[],t.getLabelGroupMerchantList()):t.$message.error(c.msg);case 12:case"end":return n.stop()}}),n)})))()},formatQueryParams:function(e){var t={};for(var n in e)""!==e[n].value&&("select_time"!==n?t[n]=e[n].value:e[n].value&&e[n].value.length>0&&(t.start_time=e[n].value[0],t.end_time=e[n].value[1]));return t}}},w=v,k=n("2877"),_=Object(k["a"])(w,r,a,!1,null,"46eca2b5",null);t["default"]=_.exports}}]);
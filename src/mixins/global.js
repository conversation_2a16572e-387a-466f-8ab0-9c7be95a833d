import { mapGetters } from 'vuex'
// 权限控制
export default {
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['allPermissions'])
  },
  mounted () {
  },
  methods: {
    /**
     * @description 判断是否有权限
     * @param {Array} keys
     * @returns Boolean
     */
    hasPermission(keys) {
      let has = false
      if (keys && keys instanceof Array) {
        if (keys.length > 0) {
          const permissionVals = keys
          has = this.allPermissions.some(role => {
            return permissionVals.includes(role)
          })
        }
      }
      return has
    }
  },
  beforeDestroy() {}
}

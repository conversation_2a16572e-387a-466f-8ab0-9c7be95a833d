// 手动引入把，这样编辑器会有代码提示
import global from './global'

// 自动引入？这样编辑去不会有代码提示。。。
// let mixins = {}

// const modulesFiles = require.context('./modules', true, /\.js$/)

// modulesFiles.keys().forEach(path => {
//   // set './app.js' => 'app'
//   const mixinsName = path.replace(/^\.\/(.*)\.\w+$/, '$1')
//   const modules = modulesFiles(path)
//   mixins[mixinsName] = modules.default
// })

// export default mixins

export default {
  install(Vue) {
    Vue.mixin(global)
  }
}

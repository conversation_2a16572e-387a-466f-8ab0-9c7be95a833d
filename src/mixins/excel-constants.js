export const EXCEL_XHR_LIST = {
  // 超管
  // 导出账号
  SuperAccountList: 'apiBackgroundAdminAccountListExportPost',
  // 导出角色
  SuperRoleList: 'apiBackgroundAdminRoleListExportPost',
  // 导出用户
  SuperUserAdminList: 'apiBackgroundAdminUserUserListExportPost',
  // 导出食材分类
  SuperIngredientsCategory: 'apiBackgroundAdminIngredientSortListExportPost',
  // 食材库导出 系统
  SuperIngredientsLibrarySystem: 'apiBackgroundAdminIngredientListExportPost',
  // 食材库导出 商户
  SuperIngredientsLibraryMerchant: 'apiBackgroundAdminIngredientMerchantListExportPost',
  // 菜品导出 系统
  SuperCommodityLibrarySystem: 'apiBackgroundAdminFoodListExportPost',
  // 菜品导出 商户
  SuperCommodityLibraryMerchant: 'apiBackgroundAdminFoodMerchantListExportPost',
  // 导出用户健康档案
  SuperUserHealthRecords: 'apiBackgroundAdminHealthyInfoListExportPost',
  // 超管批量导出激活码
  SuperActiveCodeAdmin: 'apiBackgroundAdminDeviceBulkExportActivationCodePost',
  // 超管批量导出激活码
  SuperImportIcCard: 'apiBackgroundAdminCardNoImportModifyCardUserPost',

  // 商户端
  // 导出账号
  AccountList: 'apiBackgroundOrganizationAccountListExportPost',
  // 导出角色
  RoleList: 'apiBackgroundOrganizationRoleListExportPost',
  // 导出补贴
  CardSubsidy: 'apiCardServiceCardSubsidyListExportPost',
  // 导入部门
  UserDepartment: 'apiCardServiceCardDepartmentGroupBatchImportPost',
  // 导入分组
  UserGroup: 'apiCardServiceCardUserGroupBatchImportPost',
  // 导出用户
  ExportUser: 'apiCardServiceCardUserListExportPost',
  // 导入用户
  ImportUser: 'apiCardServiceCardUserBatchImportPost',
  // 导入编辑
  ImportEditUser: 'apiCardServiceCardUserImportModifyCardUserPost',
  // 导入分组
  ImportGroup: 'apiCardServiceCardUserImportSetCardUserGroupPost',
  // 导入退卡
  ImportReturnCard: 'apiCardServiceCardUserImportCardUserQuitPost',
  // 导出账户记录
  UserAccountList: 'apiCardServiceCardUserAccountListExportPost',
  // 导入充值
  ImportRecharge: 'apiCardServiceCardOperateBatchImportUserRechargePost',
  // 导入退户
  ImportWithdrawal: 'apiCardServiceCardOperateImportPersonQuitPost',
  // 导入消费
  ImportConsumption: 'apiCardServiceCardOperateBatchImportUserConsumePost',
  // 导入取款
  ImportExtract: 'apiCardServiceCardOperateBatchImportUserDrawPost',
  // 导出退户
  ExportWithdrawalList: 'apiCardServiceCardUserPersonQuitListExportPost',
  // 导出食材
  ExportFoodIngredientList: 'apiBackgroundFoodIngredientListExportPost',
  // 导出菜品
  ExoprtMealFoodList: 'apiBackgroundFoodFoodListExportPost',
  // 导出菜品库
  ingredientsCommodity: 'apiBackgroundFoodFoodStockListExportPost',
  // 导出营养
  ExportDietNutrition: 'apiBackgroundFoodDietGroupListExportPost',
  // 导出分组点餐
  ExportGroupCollectList: 'apiBackgroundOrderReservationOrderGroupCollectListExportPost',
  // 导出预约菜品汇总
  ExportCategoryList: 'apiBackgroundOrderReservationOrderFoodCollectListExportPost',
  // 预约订单表导出
  ExportOrderList: 'apiBackgroundOrderReservationOrderListExportPost',
  // 导出食堂预约点餐汇总
  ExportCanteenBooking: 'apiBackgroundOrderReservationOrderCollectListExportPost',
  // 导出
  ExportTrayQRcode: 'apiBackgroundDeviceTrayTrayBatExportPost',
  // 导入托盘
  importAddTray: 'apiBackgroundDeviceTrayTrayBatAddPost',
  // 导入编辑托盘
  importEditTray: 'apiBackgroundDeviceTrayTrayBatModifyPost',
  // 导入配送地址
  ImportAddress: 'apiAddressAddersCenterBatchImportPost',
  // 报表中心
  // 明显总表导出
  DetailTotalList: 'apiBackgroundReportCenterDataReportUnifiedOrderListExportPost',
  // 消费明细表
  ConsumeDetailList: 'apiBackgroundReportCenterDataReportPaymentOrderDetailListExportPost',
  // 充值明细表
  TopUpDetail: 'apiBackgroundReportCenterDataReportPechargeOrderListExportPost',
  // 导出消费订单
  ExportOrderConsumption: 'apiBackgroundOrderOrderPaymentListExportPost',
  // 导出退款订单
  ExportRefundOrder: 'apiBackgroundOrderOrderRefundReservationListExportPost',
  // 导出充值订单
  ExportChargeOrder: 'apiBackgroundOrderOrderChargeListExportPost',
  // 导出充值退款订单
  ExportChargeRefundOrder: 'apiBackgroundOrderOrderRefundChargeListExportPost',
  // 导出堂食消费订单
  ExportOnSceneList: 'apiBackgroundOrderOrderPaymentOnSceneListExportPost',
  // 导出堂食消费订单
  ExportRefundOnSceneList: 'apiBackgroundOrderOrderRefundOnSceneListExportPost',
  // 导出申诉待处理订单
  AppealOrderPending: 'apiBackgroundOrderOrderAppealPendListExportPost',
  // 导出申诉已处理订单
  AppealOrderProcessed: 'apiBackgroundOrderOrderAppealDealListExportPost',
  // 商户食材列表导出
  IngredientsAdmin: 'apiBackgroundFoodIngredientListExportPost',
  // 消费点对账表导出
  ConsumeReconciliation: 'apiBackgroundReportCenterDataReportReconciliationListExportPost',
  // 营业额日报表导出
  BusinessList: 'apiBackgroundReportCenterDataReportOrderBusinessListExportPost',
  // 导出补贴明细
  ExportSubsidyDetail: 'apiCardServiceCardSubsidyInfoListExportPost',
  // 导出透支明细表
  ExportDebtOrdeReport: 'apiCardServiceCardUserGroupDebtOrderReportExportPost',
  // 提现订单
  WithdrawOrder: 'apiBackgroundOrderOrderWithdrawListExportPost',
  // 提现明细
  WithdrawList: 'apiBackgroundReportCenterDataReportOrderWithdrawDetailsListExportPost',
  // 导入人脸
  MulImportFace: 'apiCardServiceCardUserFaceBatchImportPost',

  // 导出报餐订单明细
  ExportMealReportDetail: 'apiBackgroundOrderOrderReportMealListExportPost',
  // 导出部门报餐汇总
  ExportDepMealReport: 'apiBackgroundOrderOrderReportMealGroupCollectListExportPost',
  // 导出食堂报餐汇总
  ExportCanteenMealReport: 'apiBackgroundOrderOrderReportMealCollectListExportPost',
  // 导出工本费
  ExportFlatCostList: 'apiCardServiceFlatCostListExportPost',
  // 导出补卡费
  ExportRepairCardList: 'apiCardServiceFlatCostSupplementaryListExportPost',
  // 导出工本费收款明细
  ExportFlatCostReport: 'apiBackgroundReportCenterDataReportFlatCostListExportPost',
  // 导出工本费退款明细
  ExportFlatCostRefund: 'apiBackgroundReportCenterDataReportFlatCostRefundListExportPost',
  // 导出个人充值汇总
  PersonalRechargeSummary: 'apiBackgroundReportCenterDataReportPersonChargeListExportPost',
  // 导出部门消费汇总
  DepartmentalConsumptionSummary:
    'apiBackgroundReportCenterDataReportDepartmentPaymentCollectListExportPost',
  // 导出个人消费汇总
  PersonalConsumptionSummary:
    'apiBackgroundReportCenterDataReportPersonPaymentCollectListExportPost',
  // 导出账户钱包日报表
  AccountWalletDaily: 'apiBackgroundReportCenterDataReportWalletDailyListExportPost',
  // 批量添加溯源码地址
  importSupplierBatchAddSupplierIngredient:
    'apiBackgroundFoodIngredientSupplierBatchAddSupplierIngredientPost',
  // 导出缴费列表
  ExportJiaoFeiList: 'apiBackgroundJiaofeiJiaofeiSettingListExportPost',
  // 导出缴费订单
  ExportJiaoFeiOrder: 'apiBackgroundOrderOrderJiaofeiListExportPost',
  // 导出缴费退款订单
  ExportJiaoFeiRefundOrder: 'apiBackgroundOrderOrderJiaofeiRefundListExportPost',
  // 导出缴费退款申请订单
  ExportJiaoFeiRefundApply: 'apiBackgroundOrderOrderJiaofeiApprovalListExportPost',
  // 导入缴费明细
  ImportJiaoFeiDetail: 'apiBackgroundJiaofeiJiaofeiSettingJiaofeiDetailImportPost',
  // 导入缴费明细
  ExportJiaoFeiDetail: 'apiBackgroundJiaofeiJiaofeiSettingJiaofeiDetailListExportPost',
  // 预约订单套餐汇总表导出
  ExportSetMealSummary: 'apiBackgroundOrderOrderReservationSetMealCollectListExportPost',
  // 导出第三方对账表
  ExportThirdReconciliation: 'apiBackgroundReportCenterDataReportThirdOrderListExportPost',
  // 导出考勤记录
  AttendanceRecordListExport: 'apiBackgroundAttendanceAttendanceRecordListExportPost',
  // 导出考勤记录明细表
  AttendanceRecordDetailsListExport: 'apiBackgroundAttendanceAttendanceRecordDetailsListExportPost',
  // 个人考勤统计
  PersonAttendanceReportExport: 'apiBackgroundAttendanceAttendanceRecordDetailsCardUserPunchStatusCountExportPost',
  // 导出第三方对账表
  SettlementDetails: 'apiBackgroundReportCenterDataReportOutPutListExportPost',
  // 菜品销售排行表导出
  ExportFoodSaleRanking: 'apiBackgroundReportCenterManageReportFoodPaymentRankingListExportPost',
  // 导出设备消费明细表
  ExportDeviceCost: 'apiBackgroundReportCenterDataReportDeviceConsumeListExportPost',
  // 个人钱包日报表导出
  ExportPersonalWalletDaily: 'apiBackgroundReportCenterDataReportPersonWalletDailyListExportPost',
  // 收款码明细表导出
  ExportCollectionlCodeReport: 'apiBackgroundReportCenterDataReportInstorePaymentDetailListExportPost',
  // 导出缺卡
  AttendanceRecordAbsenceWorkExport: 'apiBackgroundAttendanceAttendanceRecordDetailsAbsenceWorkListExportPost',
  // 导出已存餐
  ExportSaveCupboardOrderList: 'apiBackgroundOrderOrderReservationSaveCupboardOrderListExportPost',
  // 预约订单取餐柜订单导出
  ExportCupboardOrderList: 'apiBackgroundOrderOrderReservationCupboardOrderListExportPost',
  // 导出存餐码
  ExportPutMealNumber: 'apiBackgroundOrderOrderReservationPutMealNumberExportPost',
  // 菜品预览数据导出
  ExportMenuPreview: 'apiBackgroundFoodMenuExportMenuPreviewPost',
  // 菜谱预览基本数据导出
  ExportMenuFoodPreview: 'apiBackgroundFoodMenuExportMenuFoodPreviewPost',
  // 用户档案列表导出
  ExportHealthyInfoList: 'apiBackgroundAdminHealthyInfoListExportPost',
  // 充值/消费手续费明细表导出
  ExportCommissionChargeList: 'apiBackgroundReportCenterDataReportCommissionChargeListExportPost',
  // 体检数据详情
  ExportHealthyInfoBodyData: 'apiBackgroundAdminHealthyInfoBodyDataExportPost'
}

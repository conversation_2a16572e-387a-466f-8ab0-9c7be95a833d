<template>
  <el-popover
    v-model="visibleVirtualList"
    @show="visible = true"
    @hide="visible = false"
    popper-class="select-virtual-list-popover"
    trigger="click"
    placement="bottom-start"
    :width="popoverWidth"
    transition="el-zoom-in-top"
  >
    <div :class="['text-box', !isEmpty?'':'empty']" :style="`height:${height}px`">
      <virtual-list
        v-if="!isEmpty"
        ref="virtualListRef"
        class="virtual-list"
        :data-key="option.value"
        :keeps="keeps"
        :data-sources="sourceLists"
        :data-component="itemComponent"
        :extra-props="{ curId, option }"
        :estimate-size="estimateSize"
        :item-class="'list-item-custom-class'"
      ></virtual-list>
      <p class="el-select-dropdown__empty" v-if="isEmpty">
      {{ emptyText }}
      </p>
    </div>
    <div slot="reference" class="el-select">
      <el-input
        v-model="curValue"
        readonly
        :style="`width:${width}px;`"
        class="el-input"
        :class="{ 'is-focus': visible }"
        :size="size"
        :placeholder="curPlaceholder"
      >
        <template slot="suffix">
          <i
            :class="[
              'el-select__caret',
              'el-input__icon',
              'el-icon-' + iconClass,
            ]"
          ></i>
        </template>
      </el-input>
      <input
        v-if="filterable"
        v-model="filterText"
        ref="queryRef"
        :style="`position:absolute;top:0;left:0;width:${width-42}px;`"
        :class="{ 'is-focus': visible, 'el-select__input':true }"
        type="text"
        :size="size"
        @input="handleInput"
      />
    </div>
  </el-popover>
</template>

<script>
// https://github.com/tangbc/vue-virtual-scroll-list
import VirtualList from "vue-virtual-scroll-list";
import VirtualItem from "./VirtualItem.vue";

export default {
  name: "SelectVirtualList",
  components: {
    VirtualList
  },
  props: {
    // 绑定的值
    value: {
      required: true
    },
    option: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id'
        }
      }
    },
    width: {
      type: Number,
      default: 250
    },
    popoverWidth: {
      type: Number,
      default: 250
    },
    size: {
      type: String,
      default: "small"
    },
    placeholder: {
      type: String,
      default: "请选择"
    },
    dataList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 虚拟列表在真实 dom 中保持渲染的项目数量
    keeps: {
      type: Number,
      default: 30
    },
    // 每个项目的估计大小，如果更接近平均大小，则滚动条长度看起来更准确。 建议分配自己计算的平均值。
    estimateSize: {
      type: Number,
      default: 32
    },
    // input输入触发方法
    virtualInputCall: Function,
    filterable: {
      type: Boolean,
      default: false
    },
    filterMethod: {
      type: Function,
      default: (value, data, option) => {
        if (!value) return true
        return data[option.label].indexOf(value) !== -1
      }
    }
  },
  data() {
    return {
      visible: "",
      curId: "", // 当前选择的 id
      curValue: "", // 当前选择的值
      curIndex: null, // 当前选择的索引
      curItem: '', // 当前的选中的内容
      visibleVirtualList: false, // 是否显示虚拟列表
      itemComponent: VirtualItem, // 由 vue 创建/声明的渲染项组件，它将使用 data-sources 中的数据对象作为渲染道具并命名为：source。
      filterText: '', // 搜索的文字
      sourceLists: [], // 显示的数据
      isEmpty: true, // 是否数据为空
      emptyText: '无数据',
      height: '300', // 默认select弹出窗的高度
      offsetTop: 0 // 当前滚动距离顶部的高度
    };
  },
  watch: {
    visibleVirtualList(n) {
      if (n) {
        if (this.filterable) this.$refs.queryRef.focus()
        // 当展示虚拟列表时，需要定位到选择的位置
        this.$nextTick(() => {
          let temp = this.curIndex ? this.curIndex : 0;
          // 方法一：手动设置滚动位置到指定索引。
          // this.$refs.virtualListRef.scrollToIndex(temp - 1);
          // 方法二：手动将滚动位置设置为指定的偏移量。
          // this.$refs.virtualListRef.scrollToOffset(this.offsetTop);
          if (temp > 0 && !this.offsetTop) {
            this.$refs.virtualListRef.scrollToIndex(temp - 1);
          } else if (this.sourceLists.length) {
            this.$refs.virtualListRef.scrollToOffset(this.offsetTop);
          }
        });
      } else {
        if (this.filterable) this.$refs.queryRef.blur()
      }
    },
    visible(val) {
      if (!this.filterable) return
      this.filterText = ''
      this.sourceLists = JSON.parse(JSON.stringify(this.dataList))
      if (val) {
        this.curValue = ''
      } else {
        this.curValue = this.curItem
      }
    },
    dataList(val) {
      // 监听下赋值
      // 用于最终显示的数据
      this.init(val)
    }
  },
  computed: {
    iconClass() {
      return this.visible ? "arrow-up is-reverse" : "arrow-up";
    },
    curPlaceholder() {
      if (this.filterText) return ''
      if (this.visible && this.curItem) {
        return this.curItem
      } else {
        return this.placeholder
      }
    }
  },
  created() {
    // 监听点击子组件
    this.$on("clickVirtualItem", (item) => {
      this.curId = item[this.option.value];
      this.curValue = item[this.option.label];
      this.curItem = item[this.option.label];
      this.curIndex = item.index;
      this.visibleVirtualList = false;
      this.$emit('input', this.curId)
      this.$emit('change', this.curId)
      this.offsetTop = this.$refs.virtualListRef.getOffset()
    });
  },
  mounted () {
    this.$nextTick(_ => {
      this.curId = this.value
      this.init(this.dataList)
    })
  },
  methods: {
    // 初始化
    init(val) {
      this.sourceLists = JSON.parse(JSON.stringify(val))
      this.isEmpty = !val.length
      this.setSelectHeight(val)
      if (!this.isEmpty) {
        this.setSelect()
      }
    },
    // 设置默认选中项
    setSelect() {
      for (let index = 0; index < this.dataList.length; index++) {
        const item = this.dataList[index];
        if (item[this.option.value] === this.value) {
          this.curId = item[this.option.value];
          this.curValue = item[this.option.label];
          this.curItem = item[this.option.label];
          this.curIndex = item.index;
          break;
        }
      }
    },
    // 设置列表高度
    setSelectHeight(list) {
      this.isEmpty = !this.sourceLists.length
      if (!this.isEmpty) {
        if (list.length < 9) {
          this.height = parseInt(list.length * 33.33) + 10
        } else {
          this.height = 300
        }
      } else {
        this.height = 46
      }
    },
    // 输入框改变
    handleInput(val) {
      if (!val) {
        this.curId = "";
        this.curIndex = null;
      }
      this.virtualInputCall && this.virtualInputCall(val);
      if (this.filterable) {
        this.sourceLists = this.dataList.filter(v => this.filterMethod(this.filterText, v, this.option))
        this.setSelectHeight(this.sourceLists)
      }
    }
  }
};
</script>

<style lang="scss">
.text-box{
  padding: 0;
  box-sizing: border-box;
  .virtual-list {
    width: 100%;
    height: 100%;
    padding: 5px 0;
    overflow-y: auto;
  }
  &.empty {
      height: 46px;
  }
}
.el-popover.el-popper{
  &.select-virtual-list-popover {
    padding: 0;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #ffffff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
  }
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #fff;
}
::-webkit-scrollbar-thumb {
  background-color: #aaa !important;
  border-radius: 10px !important;
}
::-webkit-scrollbar-track {
  background-color: transparent !important;
  border-radius: 10px !important;
  -webkit-box-shadow: none !important;
}
</style>

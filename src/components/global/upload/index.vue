<template>
  <el-upload
    class="file-upload"
    ref="fileUpload"
    :action="serverUrl"
    :file-list="fileLists"
    :data="uploadParams"
    :auto-upload="true"
    :on-remove="handleRemoveFile"
    :on-success="uploadSuccess"
    :before-upload="beforeUploadHandler"
    :limit='limit'
    v-bind="$attrs"
    :headers="headersOpts"
    :on-error="uploadError"
    >
    <slot>
      <el-button class="ps-btn" size="small" type="primary">{{$t('button.upload_file')}}</el-button>
    </slot>
  </el-upload>
</template>

<script>
import { getToken } from '@/utils'
export default {
  name: 'fileUpload',
  inheritAttrs: false,
  props: {
    type: {
      // 上传类型
      type: String,
      default: ''
    },
    fileLists: {
      type: Array,
      default() {
        return []
      }
    },
    limit: { // 现在数量
      type: Number,
      default: 1
    },
    prefix: { // 上传文件前缀
      type: String,
      default: ''
    },
    rename: { // 重命名
      type: Boolean,
      default: true
    },
    beforeUpload: Function
  },
  data() {
    return {
      serverUrl: '/api/background/file/upload',
      uploadParams: {},
      // fileLists: [],
      headersOpts: {
        TOKEN: getToken()
      }
    }
  },
  created() {
    if (this.prefix) {
      this.uploadParams.prefix = this.prefix
    }
    // this.getUploadToken()
  },
  mounted() {},
  methods: {
    // 上传文件
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: this.filePrefix
      });
      if (res.code === 0) {
        this.serverUrl = res.data.host;
        this.uploadParams = {
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: "200"
        };
      } else {
        this.$message.error(res.msg);
      }
    },
    handleRemoveFile(file, fileList) {
      this.fileLists.map((item, index) => {
        if (item.name === file.name) {
          this.fileLists.splice(index, 1)
        }
      })
      fileList.map((item, index) => {
        if (item.name === file.name) {
          fileList.splice(index, 1)
        }
      })
      this.$emit('fileLists', this.fileLists)
    },
    uploadSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.fileLists.push({
          url: res.data.public_url,
          name: file.name
        })
        this.$emit('fileLists', this.fileLists)
      } else {
        this.$message.error(res.msg)
        this.handleRemoveFile(file, fileList)
        this.$emit('uploadError', false)
      }
    },
    clearHandle() {
      this.$refs.fileUpload.clearFiles()
      this.fileLists = []
    },
    beforeUploadHandler(file) {
      if (this.rename) {
        let name = new Date().getTime() + Math.floor(Math.random() * 150) + this.get_suffix(file.name)
        this.uploadParams.key = this.uploadParams.prefix ? (this.uploadParams.prefix + name) : name
      } else {
        this.uploadParams.key = file.name;
      }
      if (this.beforeUpload) {
        return this.beforeUpload(file)
      }
    },
    // 获取后缀
    get_suffix(filename) {
      let pos = filename.lastIndexOf(".");
      let suffix = "";
      if (pos !== -1) {
        suffix = filename.substring(pos);
      }
      return suffix;
    },
    // 上传失败
    uploadError(error) {
      console.log("error", error);
      this.$emit('uploadError', false)
    }

  }
}
</script>

<style lang="scss" scoped>
.file-upload{
  display: inline-block;
}
</style>

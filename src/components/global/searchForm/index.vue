<template>
  <div class="search-form-wrapper">
    <div class="search-header">
      <div class="search-h-l">
        <span>{{ title }}</span>
      </div>
      <div class="search-h-r">
        <div v-if="showCollapseBtn" class="search-collapse-btn search-h-r-btn" @click="isCollapse = !isCollapse">
          <img v-if="isCollapse" src="@/assets/img/s-top2.png" alt="">
          <img v-else src="@/assets/img/s-top1.png" alt="">
        </div>
        <el-button v-if="isShowSearchBtn" @click="searchHandle" type="primary" class="ps-origin-btn search-h-r-btn" size="mini" :disabled="loading">筛选</el-button>
        <el-button v-if="isShowResetBtn" @click="resetForm" class="search-h-r-btn ps-plain-btn" size="mini" :disabled="loading">重置</el-button>
         <!--要加自定义按钮的用这个插槽-->
        <slot name="customBtn"></slot>
      </div>
    </div>
    <div class="collapse-wrapper ps-small-box" :style="collapseHeight">
      <el-form :model="formSetting" inline ref="searchFormRef" :size="size" :label-width="labelWidth" class="search-form-collapse">
        <slot name="perv"></slot>
        <slot>
          <el-form-item v-for="(item, key) in formSetting" :key="key" :label="item.label" :prop="key+'.value'" :label-width="item.labelWidth">
            <el-input v-if="item.type==='input'" class="search-item-w ps-input" v-model="item.value" :placeholder="item.placeholder" :clearable="item.clearable" :maxlength="item.maxlength" @input="searchHandle"></el-input>
            <el-select v-if="item.type==='select'" class="search-item-w ps-select" popper-class="ps-popper-select" v-model="item.value" :placeholder="item.placeholder" :multiple="item.multiple" :collapse-tags="item.collapseTags" :clearable="item.clearable" :filterable="item.filterable" :style="{'width':item.maxWidth}" @change="searchHandle">
              <el-option v-for="(option, i) in item.dataList" :key="i" :label="item.listNameKey?option[item.listNameKey]:option.label" :value="item.listValueKey?option[item.listValueKey]:option.value" :disabled="option.disabled"></el-option>
            </el-select>
            <el-switch v-if="item.type==='switch'" v-model="item.value" @change="searchHandle"></el-switch>
            <el-checkbox-group v-if="item.type==='checkboxGroup'" v-model="item.value" @change="searchHandle">
              <el-checkbox class="ps-checkbox" v-for="(checkbox, i) in item.dataList" :key="i" :label="item.listValueKey?checkbox[item.listValueKey]:checkbox.value" :name="item.listName">{{ item.listNameKey?checkbox[item.listNameKey]:checkbox.label }}</el-checkbox>
            </el-checkbox-group>
            <el-checkbox class="ps-checkbox" v-if="item.type==='checkbox'" v-model="item.value"  :label="item.checkboxLabel" :name="item.checkboxName" @change="searchHandle">{{ item.checkboxLabel }}</el-checkbox>
            <el-radio-group v-if="item.type==='radioGroup'" v-model="item.value" @change="searchHandle">
              <el-radio class="ps-radio" v-for="(radio, i) in item.dataList" :key="i" :label="item.listValueKey?radio[item.listValueKey]:radio.value" :name="item.listName">{{ item.listNameKey?radio[item.listNameKey]:checkbox.label }}</el-radio>
            </el-radio-group>
            <el-date-picker
              v-if="item.type === 'daterange'"
              v-model="item.value"
              type="daterange"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'yyyy-MM-dd'"
              :value-format="item.format ? item.format : 'yyyy-MM-dd'"
              align="left"
              unlink-panels
              range-separator="⇀"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="item.pickerOptions?item.pickerOptions:pickerOptions"
              class="ps-picker"
              popper-class="ps-poper-picker"
              @change="searchHandle"
            ></el-date-picker>
            <el-date-picker
              v-if="item.type === 'datetimerange'"
              v-model="item.value"
              type="datetimerange"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'yyyy-MM-dd HH:mm'"
              :value-format="item.format ? item.format : 'yyyy-MM-dd HH:mm'"
              align="left"
              unlink-panels
              range-separator="⇀"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="item.pickerOptions?item.pickerOptions:pickerOptions"
              :default-time="['00:00:00', '23:59:59']"
              class="ps-picker"
              popper-class="ps-poper-picker"
              @change="searchHandle"
            ></el-date-picker>
            <el-date-picker
              v-if="item.type === 'date'"
              v-model="item.value"
              type="date"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'yyyy-MM-dd'"
              :value-format="item.format ? item.format : 'yyyy-MM-dd'"
              align="left"
              class="ps-picker"
              popper-class="ps-poper-picker"
              :picker-options="item.pickerOptionsStatus ? pickerDateOptions : {}"
              @change="searchHandle"
            ></el-date-picker>
            <!--周的加这个会报错 :value-format="item.format ? item.format : 'yyyy-WW'" -->
            <el-date-picker
              v-if="item.type === 'week'"
              v-model="item.value"
              type="week"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'yyyy 第 WW 周'"
              placeholder="选择周"
              align="left"
              class="ps-picker"
              popper-class="ps-poper-picker"
              :picker-options="item.pickerOptions?item.pickerOptions:{}"
              @change="searchHandle">
            </el-date-picker>
            <el-date-picker
              v-if="item.type === 'month'"
              v-model="item.value"
              type="month"
              :clearable="item.clearable"
              :format="item.format ? item.format : 'yyyy-MM'"
              :value-format="item.format ? item.format : 'yyyy-MM'"
              placeholder="选择月份"
              align="left"
              class="ps-picker"
              popper-class="ps-poper-picker"
              @change="searchHandle">
            </el-date-picker>
            <tree-select
              v-if="item.type === 'treeselect'"
              :multiple="item.multiple"
              :options="item.dataList"
              :flat="item.flat"
              :limit="item.limit"
              :limitText="count => '+' + count"
              :default-expand-level="item.level"
              :normalizer="item.normalizer"
              placeholder="请选择"
              no-children-text="暂无更多"
              v-model="item.value"
              class="search-item-w ps-select"
              :value-consists-of="item.valueConsistsOf"
              :appendToBody="true"
              :z-index="3000"
              @input="searchHandle"
            >
            </tree-select>
            <user-department-select
              v-if="item.type === 'departmentSelect'"
              class="search-item-w ps-input"
              v-model="item.value"
              :clearable="item.clearable"
              :placeholder="item.placeholder"
              :multiple="item.multiple"
              :checkStrictly="item.checkStrictly"
              :isLazy="item.isLazy"
              :disabled="item.disabled"
              :collapseTags="item.collapseTags"
              :append-to-body="true"
              @change="searchHandle"
              >
            </user-department-select>
            <organization-department-select
              v-if="item.type === 'organizationDepartmentSelect'"
              class="search-item-w ps-input"
              v-model="item.value"
              :clearable="item.clearable"
              :placeholder="item.placeholder"
              :multiple="item.multiple"
              :checkStrictly="item.checkStrictly"
              :isLazy="item.isLazy"
              :disabled="item.disabled"
              :collapseTags="item.collapseTags"
              :append-to-body="true"
              @change="searchHandle"
              >
            </organization-department-select>
            <organization-select
              v-if="item.type === 'organizationSelect'"
              class="search-item-w ps-input"
              v-model="item.value"
              :placeholder="item.placeholder"
              :clearable="item.clearable"
              :multiple="item.multiple"
              :checkStrictly="item.checkStrictly"
              :isLazy="item.isLazy"
              :role="item.role"
              :disabled="item.disabled"
              :collapseTags="item.collapseTags"
              :append-to-body="true"
              :dataList="item.dataList"
              :filterable="item.filterable"
              @change="searchHandle"
              >
            </organization-select>
            <user-group-select
              v-if="item.type === 'groupSelect'"
              :options="groupOptions"
              :multiple="item.multiple"
              :clearable="item.clearable"
              :filterable="item.filterable"
              :collapse-tags="true"
              class="search-item-w ps-input"
              v-model="item.value"
              :placeholder="item.placeholder"
              @change="searchHandle"
              ></user-group-select>
            <consume-select
              v-if="item.type === 'consumeSelect'"
              class="search-item-w ps-input"
              v-model="item.value"
              :clearable="item.clearable"
              :multiple="item.multiple"
              :placeholder="item.placeholder"
              @change="searchHandle"
              ></consume-select>
            <company-select
              v-if="item.type === 'CompanySelect'"
              class="search-item-w ps-select"
              v-model="item.value"
              :options="item.companyOpts"
              :companyKey="item.companyKey"
              :collapse-tags="item.collapseTags"
              :clearable="item.clearable"
              :multiple="item.multiple"
              :placeholder="item.placeholder"
              @change="searchHandle"
              ></company-select>
            <!-- <select-tree
              v-if="item.type === 'selectTree'"
              class="search-item-w ps-input"
              v-model="item.value"
              :treeProps="item.treeProps"
              :treeData="item.dataList"
              :clearable="item.clearable"
              :accordion="item.accordion"
              :radioValue="item.radioValue"
              :multipleValues="item.multipleValues"
              :multiple="item.multiple"
              :checkStrictly="item.checkStrictly"
              :append-to-body="true"
              :collapseTags="item.collapseTags"
              :filterable="item.filterable"
              :placeholder="item.placeholder"
              :disabled="item.disabled"
              :isLazy="item.isLazy"
              :loadTree="item.loadTree"
              :expandOnClickNode="item.expandOnClickNode"
              :defaultExpandedKeys="item.defaultExpandedKeys"
              :isExpandedSelect="item.isExpandedSelect"
              :isSelectChild="item.isSelectChild"
              :size="item.size"
              @change="searchHandle"
              >
            </select-tree> -->
          </el-form-item>
        </slot>
        <slot name="append"></slot>
      </el-form>
    </div>
  </div>
</template>

<script>
import UserGroupSelect from '@/components/UserGroupSelect'
import UserDepartmentSelect from '@/components/UserDepartmentSelect'
import OrganizationSelect from '@/components/OrganizationSelect'
import ConsumeSelect from '@/components/ConsumeSelect'
import OrganizationDepartmentSelect from '@/components/OrganizationDepartmentSelect'
import CompanySelect from '@/components/CompanySelect'
import { debounce } from '@/utils'
// import SelectTree from '@/components/SelectTree'
export default {
  name: 'searchForm',
  components: {
    UserGroupSelect,
    UserDepartmentSelect,
    OrganizationSelect,
    ConsumeSelect,
    OrganizationDepartmentSelect,
    CompanySelect
    // SelectTree
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'small'
    },
    labelWidth: {
      type: String,
      default: '80px'
    },
    formSetting: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: '筛选查询'
    },
    isShowSearchBtn: { // 是否展示筛选头部的筛选按钮 ,默认展示
      type: Boolean,
      default: true
    },
    isShowResetBtn: { // 是否展示筛选头部的重置按钮，默认展示
      type: Boolean,
      default: true
    },
    isShowCollapse: { // 是否收起筛选条件
      type: Boolean,
      default: true
    }

  },
  data() {
    return {
      isCollapse: false,
      // xxx: {
      //   aa: {
      //     type: 'input',
      //     value: '',
      //     label: '',
      //     dataList: [{ label: '', id: '' }],
      //     listValueKey: '',
      //     listNameKey: '',
      //     checkboxLabel: ''
      //   }
      // }
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      pickerDateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() // 8.64e7=1000*60*60*24一天
        }
      },
      normalizer(node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children_list
        }
      },
      formRef: null,
      showCollapseBtn: this.isShowCollapse,
      groupOptions: {
        label: 'group_name',
        value: 'id'
      }
    }
  },
  computed: {
    collapseHeight() {
      return this.isCollapse ? 'height:54px;' : 'auto'
    }
  },
  watch: {
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.formRef = this.$refs.searchFormRef
      if (this.formRef.$el.getBoundingClientRect().height <= 54) {
        this.showCollapseBtn = false
      }
    })
  },
  updated() {},
  methods: {
    // 加个防抖吧
    searchHandle: debounce(function() {
      this.$emit('search')
    }, 300),
    resetForm() {
      this.$refs.searchFormRef.resetFields()
      this.$emit('reset') // 个别页面重置后会做操作，加下这个把
    },
    getValue(value, item) {
      item.value = value
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.search-form-wrapper {
  overflow: hidden;
  // border-radius: 6px;
  background: $mainSearchBg;
  .search-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 20px;
    &:after {
      content: '';
      position: absolute;
      left: 24px;
      right: 24px;
      bottom: 0;
      height: 1px;
      background-color: #e7ecf2;
    }
    .search-h-l {
      border-left: 4px solid #ff9b45;
      padding-left: 18px;
      font-size: 20px;
      color: #23282d;
    }
    .search-h-r {
      display: flex;
      align-items: center;
      .search-h-r-btn {
        margin-right: 10px;
        min-width: auto;
      }
      .search-collapse-btn {
        width: 121px;
        height: 16px;
        cursor: pointer;
        img {
          display: inline-block;
          width: 100%;
          vertical-align: middle;
        }
      }
    }
  }
  .collapse-wrapper {
    padding: 0 20px;
    // overflow: hidden;
  }
  .search-item-w {
    width: 200px;
  }
  .vue-treeselect__control {
    height: 40px;
  }
  .vue-treeselect__placeholder {
    line-height: 40px;
    font-size: 13px;
  }
}
</style>

<template>
  <div calss="ps-dialog-m">
    <el-dialog
      class="dialog-message"
      :custom-class="'ps-dialog ' +  customClass"
      :title="title"
      :visible.sync="visible"
      :width="width"
      :top="top"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="!isLoading && showClose"
      :center="center"
      @closed="handleClose"
      v-bind="$attrs"
      v-on="$listeners"
      >
      <slot name="title" slot="title"></slot>
      <div :class="center?'content':''" v-loading="isLoading">
        <slot>
          <span>{{message}}</span>
        </slot>
      </div>
      <slot name="tool">
        <div slot="footer" v-if="showFooter" :class="['dialog-footer',footerCenter?'footer-center':'']">
          <el-button v-if="cancelShow" :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">{{cancelText}}</el-button>
          <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">{{confirmText}}</el-button>
        </div>
      </slot>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'dialogMessage',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '提示'
    },
    message: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '30%'
    },
    top: {
      type: String,
      default: '15vh'
    },
    loading: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: true
    },
    confirmText: {
      type: String,
      default: '确 定'
    },
    cancelText: {
      type: String,
      default: '取 消'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    customClass: {
      type: String,
      default() {
        return 'ps-dialog-message'
      }
    },
    cancelShow: {
      type: Boolean,
      default: true
    },
    center: Boolean,
    footerCenter: Boolean
  },
  data() {
    return {
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    },
    isLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      }
    }
  },
  watch: {
    // $route: {
    //   handler: function(route) {
    //     const query = route.query
    //   },
    //   immediate: true
    // }
  },
  created () {
  },
  mounted () {
  },
  methods: {
    clickConfirmHandle() {
      if (this.isLoading) return;
      this.isLoading = true
      this.$emit('confirm')
    },
    clickCancleHandle() {
      if (this.cancelShow) {
        this.closeHandle()
      }
      this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.$emit('close')
    },
    closeHandle() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
.v-modal{
  z-index: 2000!important;
}
.ps-dialog-message{
  .content{
    text-align: center;
  }
}
.footer-center{
  text-align: center !important;
}
</style>

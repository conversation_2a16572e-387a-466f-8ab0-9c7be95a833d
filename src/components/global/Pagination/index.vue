<template>
  <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="pageSizes"
      :page-size="size"
      :layout="layout"
      :total="total"
      background
      v-bind="$attrs"
      v-on="$listeners"
      class="ps-text"
      popper-class="ps-popper-select"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    // 页码
    currentPage: {
      type: Number,
      default: 1
    },
    // 一页的数量
    pageSize: {
      type: Number,
      default: 10
    },
    // 总条数
    total: {
      type: Number,
      default: 0
    },
    // 事件
    onPaginationChange: {
      type: Function
    },
    pageSizes: {
      type: Array,
      default: () => {
        return [5, 10, 20, 30, 40]
      }
    },
    layout: {
      type: String,
      default: 'total, prev, pager, next, sizes, jumper'
    }
  },
  data() {
    return {
    }
  },
  computed: {
    page: {
      get() {
        return this.currentPage
      },
      set(val) {
        this.$emit('update:currentPage', val)
      }
    },
    size: {
      get() {
        return this.pageSize
      },
      set(val) {
        this.$emit('update:pageSize', val)
      }
    }
  },
  methods: {
    handleSizeChange(pageSize) {
      if (this.onPaginationChange) {
        this.onPaginationChange({ current: 1, pageSize })
      }
    },
    handleCurrentChange(current) {
      if (this.onPaginationChange) {
        this.onPaginationChange({ current, pageSize: this.pageSize })
      }
    }
  }
}
</script>

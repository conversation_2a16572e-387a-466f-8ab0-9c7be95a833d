<template>
  <div class="tree-select-wrapper">
    <el-select
      :value="selectValue"
      :multiple="multiple"
      :placeholder="placeholder"
      @remove-tag="removeTag"
      :clearable="clearable"
      @clear="clearHandle"
      @visible-change="visibleChangeHandle"
      :popper-class="popperClass + ' tree-select-popper'"
      :popper-append-to-body="appendToBody"
      :collapse-tags="collapseTags"
      :filterable="filterable"
      :filter-method="filterMethod"
      :disabled="disabled"
      class="ps-select w-100-percent"
      :size="size"
    >
      <el-option :value="{label: selectValue, value: selectValue, disable: true}" class="ps-tree-checkbox">
        <el-tree
          id="tree-option"
          ref="selectTree"
          :class="{ 'custom-checkbox': customCheckbox && !showCheckbox }"
          :accordion="accordion"
          :data="treeData"
          :props="treeProps"
          :load="loadTree"
          :lazy="isLazy"
          :show-checkbox="showCheckbox"
          :expand-on-click-node="expandOnClickNode"
          :default-expand-all="defaultExpandAll"
          :default-expanded-keys="expandedKeys"
          :node-key="treeProps.value"
          :check-strictly="checkStrictly"
          :filter-node-method="filterNode"
          @check="handleNodeCheck"
          @check-change="handleCheckChange"
          @node-click="handleNodeClick"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
          :render-content="renderContent"
        >
        </el-tree>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getTreeChildArr, deepClone, arrayEqual } from "@/utils"

export default {
  name: 'SelectTree',
  props: {
    // 绑定的值
    value: {
      required: true
    },
    // 配置选项
    treeProps: {
      type: Object,
      default: () => {
        return {
          value: 'id',
          label: 'label',
          children: 'children'
        }
      }
    },
    // 数据
    treeData: {
      type: Array,
      default: () => []
    },
    // 是否可清空选项
    clearable: {
      type: Boolean,
      default: () => true
    },
    // 自动收起
    accordion: {
      type: Boolean,
      default: false
    },
    // 多选初始值
    multipleValues: {
      type: Array,
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: () => true
    },
    // 是否不关联父子选中
    checkStrictly: {
      type: Boolean,
      default: () => false
    },
    // 添加到body
    appendToBody: {
      type: Boolean,
      default: () => false
    },
    // 隐藏过多的tags
    collapseTags: {
      type: Boolean,
      default: () => true
    },
    // 是否可搜索
    filterable: {
      type: Boolean,
      default: () => true
    },
    // placeholder
    placeholder: {
      type: String,
      default: () => '请选择'
    },
    // 禁止选择
    disabled: {
      type: Boolean,
      default: () => false
    },
    // 是否开启懒加载
    isLazy: {
      type: Boolean,
      default: () => false
    },
    // 懒加载执行的函数
    loadTree: Function,
    // 是否默认展开全部
    defaultExpandAll: {
      type: Boolean,
      default: () => false
    },
    // 是否在点击节点的时候展开或者收缩节点
    expandOnClickNode: {
      type: Boolean,
      default: () => false
    },
    // 默认展开的节点的 key 的数组
    defaultExpandedKeys: {
      type: Array
    },
    // 默认展开选中的节点，即初始化时的数据
    isExpandedSelect: {
      type: Boolean,
      default: () => true
    },
    // 多选时，并且设置checkStrictly时，开启选中父级并且子级也选上
    // 不要与onlySelectLastChild，onlySelectLevel同时使用
    isSelectChild: {
      type: Boolean,
      default: () => false
    },
    // size大小
    size: {
      type: String,
      default: 'medium'
    },
    // 可传递 tree 搜索方法
    filterNode: {
      type: Function,
      default: (value, data, node) => {
        if (!value) return true
        return data[node.store.props.label].indexOf(value) !== -1
      }
    },
    // 是否显示checkbox
    showCheckbox: {
      type: Boolean,
      default: false
    },
    // showCheckbox为false时可使用，后续可兼容下showCheckbox为true的情况
    // 是否只允许选中最后一级，即没有子级的层级，onlySelectLastChild的优先级比onlySelectLevel高
    onlySelectLastChild: {
      type: Boolean,
      default: false
    },
    // showCheckbox为false时可使用，后续可兼容下showCheckbox为true的情况
    // 允许指定层级勾选，根据数据的level进行判断，-1为不指定层级
    onlySelectLevel: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      selectValue: this.multiple ? [] : '', // select组件的值
      selectTreeKeys: [], // tree勾选的key
      expandedKeys: [], // 默认选中的节点展开
      middleExpandedKeys: [], // 定个中转expand，防止每次都修改原数据导致tree无法收起
      popperClass: 'tree-popper-' + new Date().getTime(), // 动态class防止串了
      wrapTop: 0,
      selectedNodes: [],
      customCheckbox: true // 自定义样式
    }
  },
  watch: {
    // 监听下value的变化
    // treeData(val, oldVal) {
    //   this.initSelect()
    // },
    // 监听下value的变化
    value(val, oldVal) {
      this.initSelect()
    },
    treeData: {
      handler() {
        this.initSelect()
      },
      deep: true
    }
  },
  mounted() {
    // 默认只执行一次 initDefault
    this.initDefault()
  },
  methods: {
    // 初始化
    initDefault() {
      this.initExpanded(true)
      this.initSelect()
    },
    // 设置下默认选中的节点展开
    initExpanded(init) {
      // 得拷贝一下防止它不会自动展开
      // init 初始化时调用
      if (init) {
        if (this.defaultExpandedKeys && this.defaultExpandedKeys.length > 0) {
          this.expandedKeys = deepClone(this.defaultExpandedKeys)
        } else {
          if (this.value && this.isExpandedSelect) {
            this.expandedKeys = deepClone(this.multiple ? this.value : [this.value])
          }
        }
        this.middleExpandedKeys = this.expandedKeys.slice(0)
      } else { // 非第一次初始化时调用
        this.expandedKeys = this.middleExpandedKeys.slice(0)
      }
    },
    // 初始化多选默认值，如果有传递默认值，则回显默认值
    initSelect() {
      if (this.multiple) {
        this.$refs.selectTree.setCheckedKeys(this.value || [])
        this.selectTreeKeys = this.value
      } else {
        this.$refs.selectTree.setCheckedKeys([this.value])
        this.selectTreeKeys = [this.value]
      }
      this.$nextTick(() => {
        // 一定要延时，不然没法还原selectValue
        this.initSelectValue()
      })
    },
    // 初始化下select中选中的值
    // 多选中如果有禁止选中的选项要确保禁止选中的选项数据顺序在最后
    initSelectValue(value) {
      let disabledList = [] // 禁止选中或删除的选项
      let select = []
      const allselect = this.$refs.selectTree.getCheckedNodes().map(item => {
        if (item.disabled) {
          disabledList.push(item[this.treeProps.label])
        } else {
          select.push(item[this.treeProps.label])
        }
        return item[this.treeProps.label]
      })
      if (this.multiple) {
        // 多选
        this.selectValue = select.reverse().concat(disabledList)
        this.selectedNodes = this.$refs.selectTree.getCheckedNodes()
      } else {
        // 单选
        this.selectValue = allselect.length ? allselect[0] : ''
      }
    },
    // 切换选项
    handleNodeCheck(node, el) {
      // this.$nextTick(_ => {
      this.selectedNodes = el.checkedNodes
      if (this.multiple) {
        // 多选
        this.selectValue = el.checkedNodes.map(item => item[this.treeProps.label])
      } else {
        // 单选
        this.selectValue = el.checkedNodes.length ? el.checkedNodes[0][this.treeProps.label] : ''
      }
      this.selectTreeKeys = this.getCheckedKeys()
      this.changeHandle()
    },
    // 移除选项
    removeTag(tag) {
      let arr = this.selectedNodes.filter(item => item[this.treeProps.label] === tag)
      // 有个先后顺序的问题，待解决ing
      if (arr[0].disabled) return
      this.$refs.selectTree.setChecked(arr[0].id, false)
      this.selectValue.splice(this.selectValue.indexOf(tag), 1)
      this.changeHandle()
    },
    // 清除选中项
    clearHandle() {
      if (this.multiple) {
        this.selectValue = []
      } else {
        this.selectValue = ''
      }
      this.$refs.selectTree.setCheckedKeys([])
      this.$emit('input', this.selectValue)
      this.changeHandle('clear')
    },
    // 获取选中id值
    getCheckedKeys() {
      return this.$refs.selectTree.getCheckedKeys()
    },
    handleCheckChange(data, checked) {
      this.$nextTick(_ => {
        if (!this.multiple && checked === true) {
          // 单选
          this.$refs.selectTree.setCheckedNodes([data])
        }
        // 多选时
        if (this.multiple && this.checkStrictly && this.isSelectChild) {
          // let check = this.getCheckedKeys()
          let check = deepClone(this.selectTreeKeys)
          let arr = []
          if (data[this.treeProps.children]) {
            arr = getTreeChildArr([data], data[this.treeProps.value], { key: this.treeProps.value, childkey: this.treeProps.children })
          }
          // showCheckbox还需要设置子级选中，不能用
          if (checked) {
            check.push(...arr)
            let list = [...new Set(check)]
            if (!arrayEqual(list, this.selectTreeKeys)) {
              this.$refs.selectTree.setCheckedKeys(list)
              this.selectTreeKeys = list
            }
          } else {
            let list = check.filter(v => {
              return !arr.includes(v)
            })
            if (!arrayEqual(list, this.selectTreeKeys)) {
              this.$refs.selectTree.setCheckedKeys(list)
              this.selectTreeKeys = list
            }
          }
        }
      })
    },
    // 搜索
    filterMethod(val) {
      // 远程加载的话数据和搜索共存时当前这步可以跳过
      this.$refs.selectTree.filter(val)
      // 远程加载用的
      this.$emit('filterText', val)
    },

    // 当下拉显示和隐藏时触发
    visibleChangeHandle(e) {
      if (!e) {
        // 隐藏时，清空下搜索框的搜索字段
        this.$refs.selectTree.filter('')
        let wrapDom = document
          .querySelector(`.${this.popperClass}`)
          .querySelector('.el-select-dropdown__wrap')
        if (wrapDom) {
          this.wrapTop = wrapDom.scrollTop
        }
      } else {
        this.initExpanded()
        this.$nextTick(() => {
          setTimeout(_ => {
            this.scollToSelectDom()
          }, 60)
        })
      }
    },
    // 获取dom滚动到顶部or滚动到响应选中的那个节点上
    scollToSelectDom() {
      // is-checked getBoundingClientRect
      let wrapDom = document
        .querySelector(`.${this.popperClass}`)
        .querySelector('.el-select-dropdown__wrap')
      if (wrapDom) {
        wrapDom.scrollTop = this.wrapTop
      }
    },
    changeHandle() {
      this.$nextTick(_ => {
        let checkList = this.getCheckedKeys()
        this.selectedNodes = this.$refs.selectTree.getCheckedNodes()
        let result = this.multiple ? checkList : checkList[0]
        this.$emit('input', result)
        let node = this.multiple ? this.selectedNodes : this.selectedNodes[0]
        this.$emit('change', node ? deepClone(node) : null)
      })
    },
    // 获取选中的值不包含父级
    getCheckedKeysByHalf() {
      return this.$refs.selectTree.getCheckedKeys(true, false)
    },
    // 自定义节点渲染内容
    renderContent(h, { node, data, store }) {
      // <el-button size="mini" type="text" on-click={ () => this.append(data) }>Append</el-button>
      let nodeClass = 'custom-tree-node clearfix'
      if (data.disabled) {
        nodeClass += ' disabled'
      }
      let labelClass = 'custom-label'
      if (!this.showCheckbox && node.checked) {
        labelClass = 'custom-label ps-origin'
      }
      let iClass = 'el-icon-check'
      if (node.checked) {
        iClass += ' checked'
      }
      if (node.indeterminate) {
        iClass += ' is-indeterminate'
      }
      if (!this.showCheckbox && this.customCheckbox) {
        return (
          <div class={nodeClass}>
            <span class={labelClass}>{node.label}</span>
            <span class="m-l-10 float-r">
              <i class={iClass} size={ this.size }></i>
            </span>
          </div>);
      }
      return (
        <div class={nodeClass}>
          <span class={labelClass}>{node.label}</span>
          { !this.showCheckbox ? (<span class="float-r m-l-20">
            { node.checked ? <i class="el-icon-check" size={ this.size }></i> : '' }
          </span>) : ''
          }
        </div>);
    },
    // 节点点击
    handleNodeClick(data, node, ref) {
      // 只有非checkbox才可使用 onlySelectLastChild
      if (this.showCheckbox) return
      // disabled不能继续执行
      if (data.disabled) return
      this.selectedNodes = node
      // console.log(data, node, ref)
      // 紧选中最后一个
      if (this.onlySelectLastChild) {
        !data[this.treeProps.children] && this.setCheckValue(data.checked, data, ref)
        return
      }
      // 指定level可选中
      if (this.onlySelectLevel > -1) {
        (data.level === this.onlySelectLevel) && this.setCheckValue(data.checked, data, ref)
        return
      }
      // 非showCheckbox时也可点击
      this.setCheckValue(data, node, ref)
    },
    // 非showCheckbox时设置选中数据专用
    setCheckValue(data, node, ref) {
      const value = data[this.treeProps.value]
      // 已选的需要去掉
      let valIndex = this.selectTreeKeys.indexOf(value)
      // this.$refs.selectTree.setCheckedKeys([0])
      this.$refs.selectTree.setChecked(value, !node.checked, true)
      // 走下流程
      this.$nextTick(_ => {
        // 先拿一遍数据后续要用
        this.selectTreeKeys = this.getCheckedKeys()
        this.changeHandle()
      })
      return
      if (valIndex > -1) {
        this.selectTreeKeys.splice(valIndex, 1)
        if (this.multiple) {
          this.$refs.selectTree.setCheckedKeys(this.selectTreeKeys)
        } else {
          this.$refs.selectTree.setCheckedKeys([this.selectTreeKeys])
        }
      } else {
        if (this.multiple) {
          this.selectTreeKeys.push(value)
          this.$refs.selectTree.setCheckedKeys(this.selectTreeKeys)
        } else {
          this.$refs.selectTree.setCheckedKeys([value])
        }
      }
      // 走下流程
      this.$nextTick(_ => {
        // 先拿一遍数据后续要用
        this.selectTreeKeys = this.getCheckedKeys()
        console.log(11111, this.selectTreeKeys)
        this.changeHandle()
      })
    },
    // 节点展开
    handleNodeExpand(data, node, ref) {
      this.middleExpandedKeys.push(data[this.treeProps.value])
    },
    // 节点收起
    handleNodeCollapse(data, node, ref) {
      let index = this.middleExpandedKeys.indexOf(data[this.treeProps.value])
      this.middleExpandedKeys.splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.tree-select-popper {
  .el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
    height: auto;
    padding: 0;
    overflow: hidden;
    overflow-y: auto;
  }

  .el-select-dropdown__item.selected {
    font-weight: normal;
  }

  ul li .el-tree .el-tree-node__content {
    height: auto;
    padding: 0 20px;
  }

  .el-tree-node__label {
    font-weight: normal;
  }

  // .el-tree .is-current .el-tree-node__label {
  //   color: #409eff;
  //   font-weight: 700;
  // }

  .el-tree .is-current .el-tree-node__children .el-tree-node__label {
    color: #606266;
    font-weight: normal;
  }

  .el-select {
    width: 100% !important;
  }
  .custom-tree-node{
    flex: 1;
    .el-icon-check{
      color: #ff9b45;
    }
  }
  .custom-tree-node {
    &.disabled{
      cursor: not-allowed;
      opacity: .5;
    }
    .custom-label{
      transition: color .15s ease-in-out;
      &.ps-origin{
        color: #ff9b45;
      }
    }
  }
  .custom-checkbox{
    .el-icon-check{
      font-size: 12px;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      color: #fff;
      transform: scale(0.9);
      &.checked{
        border-color: #ff9b45;
        background-color: #ff9b45;
      }
    }
    .el-icon-caret-right{
      vertical-align: middle;
    }
    .el-icon-caret-right:before {
      background: url('~@/assets/img/zz2.png') no-repeat 0 0px;
      content: '';
      display: block;
      width: 14px;
      height: 14px;
      font-size: 14px;
      background-size: 14px;
    }
    .is-checked{
      &>.el-tree-node__content{
        .el-icon-caret-right:before {
          background: url('~@/assets/img/zz1.png') no-repeat 0 0px;
          content: '';
          display: block;
          width: 14px;
          height: 14px;
          font-size: 14px;
          background-size: 14px;
        }
        .is-leaf.el-icon-caret-right:before {
          background: transparent;
        }
      }
    }
    .is-indeterminate{
      border-color: #ff9b45;
      background-color: #ff9b45;
      width: 13.6px;
      height: 13.6px;
      vertical-align: middle;
      &::before{
        content: "";
        position: absolute;
        display: block;
        background-color: #fff;
        height: 2px;
        // transform: scale(.5);
        left: 3px;
        right: 3px;
        top: 50%;
        // border:1px solid #ff9b45;
        transform: translateY(-50%);
      }
    }
    .expanded.el-tree-node__expand-icon{
      transform:rotate(180deg);
    }
    .is-leaf.el-icon-caret-right:before {
      background: transparent;
    }
  }
}
.w-100-percent {
  width: 100%;
}
</style>

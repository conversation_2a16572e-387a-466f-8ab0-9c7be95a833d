<template>
  <div class="data-display">
    <!-- 顶部合计 start -->
    <div class="money_type flex">
      <div
        class="money_type_item"
        v-for="item in businessSummery"
        :key="item.key"
        :class="item.class"
      >
        <div class="title">{{ item.label }}</div>
        <div class="money">
          <span>{{ item.value | formatPriceTo3 }}</span>
        </div>
      </div>
    </div>
    <!-- 顶部合计 end -->
    <!-- 经营趋势 start -->
    <div class="line-trend" v-loading="isBusinessLoading">
      <div class="top flex">
        <div class="title">经营趋势</div>
        <!-- <div class="company_fee">单位：元</div> -->
      </div>
      <div class="nav_cen flex">
        <div
          class="item"
          :class="selectTrend.key === item.key ? 'nav_active' : ''"
          v-for="item in businessSummery"
          :key="item.key"
          @click="changetrendHandle(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div ref="line_chart" id="line_chart"></div>
    </div>
    <!-- 经营趋势 end -->
    <div class="annular-chart flex">
      <div class="meal_type meal_item">
        <div class="meal_type_header">
          <div class="title">餐段统计</div>
          <el-radio-group class="ps-radio-btn" v-model="mealStatisticType" @change="mealStatisticChange" size="mini" prop="couponType">
            <el-radio-button label="count">按笔数</el-radio-button>
            <el-radio-button label="user">按人数</el-radio-button>
          </el-radio-group>
        </div>
        <div ref="circular_chart" id="circular_chart"></div>
      </div>
      <div class="eat_type meal_item">
        <div class="title">取餐方式统计</div>
        <div ref="annular_chart" id="annular_chart"></div>
      </div>
      <div class="meal_consumption meal_item">
        <div class="title">餐段消费情况</div>
        <el-table
          :data="mealConsumeData"
          style="width: 100%"
          :cell-style="CellStyle"
          :header-cell-style="HeaderCell"
        >
          <el-table-column
            v-for="col in mealConsumeColumns"
            :prop="col.key"
            :label="col.label"
            :key="col.key"
            :width="col.width"
          >
            <template slot-scope="scope">
              <span v-if="col.key === 'turnover'">
                ￥{{ scope.row[col.key] | formatMoney | formatPriceTo3 }}
              </span>
              <span v-else-if="col.key === 'percent'">
                <el-progress
                  :stroke-width="5"
                  :percentage="scope.row[col.key]"
                  color="#FF9144"
                  :show-text="false"
                ></el-progress>
              </span>
              <span v-else>
                <span>{{ scope.row[col.key] }}</span>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="food_TOP ranking_item">
        <div class="top flex" style="justify-content: space-between;">
          <div class="title">菜品销量排行TOP10</div>
          <div class="nav">
            <el-button
              size="mini"
              :class="foodSalesRankingType === 'fee' ? 'top_nav' : ''"
              @click="changeFoodSalesRankingType('fee')"
            >
              销额
            </el-button>
            <el-button
              size="mini"
              :class="foodSalesRankingType === 'count' ? 'top_nav' : ''"
              @click="changeFoodSalesRankingType('count')"
            >
              销量
            </el-button>
          </div>
        </div>
        <div class="m-t-10" style="width: 200px;">
          <food-category @input="changeCategory" />
        </div>
        <div class="TOP_item flex" v-for="(item, index) in foodSalesRankingData" :key="index">
          <div class="left flex">
            <div class="count" :class="foodTOPThreeClass(index + 1)">{{ index + 1 }}</div>
            <div class="food">{{ item.name }}</div>
          </div>
          <div class="right" v-if="foodSalesRankingType === 'fee'">
            ￥{{ item.total_fee | formatMoney | formatPriceTo3  }}
          </div>
          <div class="right" v-else>{{ item.food_count | formatPriceTo3 }}笔</div>
        </div>
        <div class="flex flex-center empty-text" v-if="!foodSalesRankingData.length"> 暂无更多 </div>
      </div>
      <div class="pay_TOP ranking_item">
        <div class="title">支付方式排行</div>
        <div class="scroll-box">
          <div class="TOP_item" v-for="(item, index) in payTypeData" :key="index">
            <div class="top flex">
              <div class="pay_type">{{ item.st_payway }}</div>
              <div class="right flex">
                <div class="count">{{ item.payway_count | formatPriceTo3 }}笔</div>
                <div class="pay_fee">￥{{ item.total_payfee | formatMoney | formatPriceTo3 }}</div>
              </div>
            </div>
            <el-progress
              :stroke-width="9"
              :percentage="item.percent"
              :color="payColor(index)"
              :show-text="false"
            ></el-progress>
          </div>
          <div class="flex flex-center empty-text" v-if="!payTypeData.length"> 暂无更多 </div>
        </div>
      </div>
      <div class="shebei_TOP ranking_item">
        <div class="title">设备消费统计</div>
        <div class="scroll-box">
          <div class="TOP_item" v-for="(item, index) in devicePayData" :key="index">
            <div class="left flex">
              <el-progress
                type="circle"
                :percentage="item.percent"
                :stroke-width="4"
                :width="50"
                :height="50"
                color="#FF9144"
                :format="formatDevice(index + 1)"
                style="font-weight: bold;"
              ></el-progress>
              <div class="type">
                <div class="name">{{ item.device_name }}</div>
                <div class="number flex">
                  <div class="count">{{ item.paydevice_count | formatPriceTo3 }}笔</div>
                  <div class="fee">￥{{ item.total_payfee | formatMoney | formatPriceTo3 }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-center empty-text" v-if="!devicePayData.length"> 暂无更多 </div>
        </div>
      </div>
    </div>
    <div v-if="showBarChart" class="bar-chart">
      <div class="top flex">
        <div class="title">营业额统计</div>
        <!-- <div class="company_fee">单位：元</div> -->
      </div>
      <div ref="bar_chart" id="bar_chart"></div>
    </div>
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
import { divide, to, debounce, deepClone } from '@/utils'
import { TREND_SETTING, MEALTIME_SETTING, MEALWAY_SETTING, MEALCONSUME_COLUMS, TURNOVER_SETTING } from './constants'
import NP from 'number-precision'
import FoodCategory from './components/FoodCategory'

export default {
  name: 'DataDisplay',
  components: { FoodCategory },
  props: {
    change_time: Array,
    orgid: [Number, String, Array],
    chartdata: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      queryDateList: [], // 时间列表
      isBusinessLoading: false,
      businessSummery: [ // 合计
        { label: '营业额', value: 0, key: 'turnover', class: "turnover_fee", type: 'money' },
        { label: '实收金额', value: 0, key: 'real_income', class: "origin_fee", type: 'money' },
        { label: '消费订单笔数', value: 0, key: 'consume_order_count', class: "consumption_count" },
        { label: '充值金额', value: 0, key: 'total_charge', class: "recharge_fee", type: 'money' },
        { label: '消费退款金额', value: 0, key: 'total_consume_refund', class: "refund_fee", type: 'money' }
      ],
      selectTrend: {},
      mealConsumeData: [], // 餐段消费统计
      mealConsumeColumns: MEALCONSUME_COLUMS, // 餐段消费统计设置
      foodSalesRankingType: 'fee', // fee，count
      foodSalesRankingData: [],
      payTypeData: [], // 支付类型数据
      devicePayData: [], // 设备消费统计
      tableData: [],
      Top_change: 'xiaoE', // 菜品排行榜的默认值
      TopFood_arr: [],
      TopPay_arr: [], // 支付类型排行
      shebei_arr: [], // 设备统计
      LineChartObj: {}, // 选择时间段的经营趋势的五个按钮对应曲线图的数据
      lineChart: null, // 经营趋势
      pieMealTimeChart: null, // 餐段统计
      mealStatisticType: 'count', // 餐段统计type
      pieMealWayChart: null, // 取餐方式统计
      barTurnoverChart: null, // 营业额统计
      showBarChart: false,
      secondCategories: [] // 二级分类id
    }
  },
  computed: {
    diffDay() {
      return this.getDiffDay(this.change_time[1], this.change_time[0]) + 1
    }
  },
  watch: {
    chartdata: {
      deep: true,
      handler(v) {
        // this.setSunmaryHandle(v.business_trend)
        this.setSummaryData(v.summary)
        // 餐段统计的
        if (this.mealStatisticType === 'count') {
          this.initMealTimeDataPie(v.meal_time_statistic, '笔', '订单总笔数') // 按笔数
        } else {
          this.initMealTimeDataPie(v.meal_time_statistic_for_user, '人', '下单总人数') // 按人数
        }
        this.initMealPayDataPie(v.take_meal_type_st)
        this.initMealConsume(v.meal_consume_statistic)
        this.initFoodSalesRanking(v)
        this.initPayTypeData(v.payinfo_st)
        this.initDevicePayData(v.device_payinfo_st)
        this.initTurnoverData(v.turnover_statistic)
      }
    },
    orgid: function(newVal) {
      // this.getBusinessTrendList(this.secondCategories)
    },
    change_time: function(newVal) {
      this.queryDateList = this.setDateList()
      this.getBusinessTrendList(this.secondCategories)
    }
  },
  created() {},
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChartHandle)
  },
  mounted() {
    this.selectTrend = this.businessSummery[0]
    window.addEventListener('resize', this.resizeChartHandle)
  },
  methods: {
    // 计算两个时间相差的天数， value1要大于value2
    getDiffDay(value1, value2) {
      return dayjs(value1).diff(value2, 'day')
    },
    // 计算合计
    setSunmaryHandle(data) {
      let result = {}
      data.forEach(item => {
        Object.keys(item).forEach(key => {
          if (key !== 'st_date') {
            if (result[key] === undefined) {
              result[key] = item[key] ? item[key] : 0
            } else {
              result[key] += item[key]
            }
          }
        })
      })
    },
    // 合计赋值
    setSummaryData(data) {
      this.businessSummery.forEach(item => {
        if (item.key === 'consume_order_count') {
          this.setDataValue(item, 'value', data[item.key])
        } else {
          this.setDataValue(item, 'value', divide(data[item.key]))
        }
      })
    },
    // 设置响应式数据
    setDataValue(data, key, value) {
      this.$set(data, key, value)
    },
    // 生成日期数组
    setDateList() {
      let dataList = []
      let minDate = this.change_time[0]
      if (this.diffDay > 0) {
        for (let index = 0; index < this.diffDay; index++) {
          let date = dayjs(minDate).add(index, 'day').format('YYYY-MM-DD')
          dataList.push(date)
        }
      } else {
        dataList = [minDate]
      }
      return dataList
    },
    // 获取经营趋势数据
    async getBusinessTrendList(secondCategories) {
      this.isBusinessLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundHomepageDataGetBusinessTrendDataPost({
        start_date: this.change_time[0],
        end_date: this.change_time[1],
        query_type: this.selectTrend.key,
        org_id: this.orgid,
        second_categories: secondCategories
      }))
      this.isBusinessLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.chartData = res.data
        this.initTurnoverTrendLine(res.data)
        // this.queryDateList
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化 经营趋势
    initTurnoverTrendLine(data) {
      let trendSetting = TREND_SETTING
      trendSetting.xAxis.data = this.setDateList()
      trendSetting.tooltip.formatter = `<div style="padding:5px;font-size:16px;font-weight: 540;">{b0}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">${this.selectTrend.label}</span>${this.selectTrend.type === 'money' ? '￥' : ''}{c0}</div>`
      trendSetting.series[0].showSymbol = this.diffDay < 10
      let dataList = this.queryDateList.map(date => {
        let value = 0
        data.forEach(v => {
          if (date === v.st_date && v[this.selectTrend.key]) {
            if (this.selectTrend.key === 'consume_order_count') {
              value = v[this.selectTrend.key]
            } else {
              value = divide(v[this.selectTrend.key])
            }
          }
        })
        return { value, date }
      })
      trendSetting.series[0].data = dataList
      if (!this.lineChart) {
        this.lineChart = this.$echarts.init(this.$refs.line_chart)
      }
      if (this.lineChart) {
        this.lineChart.setOption(trendSetting)
      }
    },
    // 更新 经营趋势
    updateTurnoverTrend(data) {
      this.initTurnoverTrendLine()
    },
    // 经营趋势切换
    changetrendHandle(item) {
      this.selectTrend = item
      this.getBusinessTrendList(this.secondCategories)
    },
    // 餐段统计
    mealStatisticChange() {
      if (this.mealStatisticType === 'count') {
        this.initMealTimeDataPie(this.chartdata.meal_time_statistic, '笔', '订单总笔数') // 按笔数
      } else {
        this.initMealTimeDataPie(this.chartdata.meal_time_statistic_for_user, '人', '下单总人数') // 按人数
      }
    },
    // 初始化餐段统计数据
    initMealTimeDataPie(list, type, title) {
      let data = [
        { value: 0, name: '早餐', key: 'breakfast_count' },
        { value: 0, name: '午餐', key: 'lunch_count' },
        { value: 0, name: '下午茶', key: 'afternoon_count' },
        { value: 0, name: '晚餐', key: 'dinner_count' },
        { value: 0, name: '宵夜', key: 'supper_count' },
        { value: 0, name: '凌晨餐', key: 'morning_count' }
      ]
      let nameKey = {}
      data.forEach(item => {
        item.value = list[item.key] ? list[item.key] : 0
        nameKey[item.name] = item.key
      })
      let mealtimeSetting = MEALTIME_SETTING
      mealtimeSetting.legend.formatter = function(name) {
        let value = list[nameKey[name]]
        return name + '    ' + (value || 0) + type
      }
      mealtimeSetting.title.subtext = title
      mealtimeSetting.series[0].data = data
      mealtimeSetting.title.text = data.reduce((x, y) => {
        return x + y.value;
      }, 0)
      if (!this.pieMealTimeChart) {
        this.pieMealTimeChart = this.$echarts.init(this.$refs.circular_chart)
      }
      this.pieMealTimeChart.setOption(mealtimeSetting)
    },
    // 初始化取餐方式统计数据
    initMealPayDataPie(list) {
      let data = [
        { value: 0, name: '线下堂食', key: 'instore_on_scene_count' },
        { value: 0, name: '预约堂食', key: 'online_on_scene_count' },
        { value: 0, name: '外卖配送', key: 'waimai_count' },
        { value: 0, name: '食堂自提', key: 'bale_count' },
        { value: 0, name: '取餐柜自提', key: 'cupboard_count' }
      ]
      let nameKey = {}
      data.forEach(item => {
        item.value = list[item.key] ? list[item.key] : 0
        nameKey[item.name] = item.key
      })
      let mealWaySetting = MEALWAY_SETTING
      mealWaySetting.legend.formatter = function(name) {
        let value = list[nameKey[name]]
        return name + '    ' + (value || 0) + '笔'
      }
      mealWaySetting.series[0].data = data
      // mealtimeSetting.title.text = data.reduce((x, y) => {
      //   return x + y.value;
      // }, 0)
      if (!this.pieMealWayChart) {
        this.pieMealWayChart = this.$echarts.init(this.$refs.annular_chart)
      }
      this.pieMealWayChart.setOption(mealWaySetting)
    },
    // meal_consume_statistic
    // 餐段消费
    initMealConsume(data) {
      let total = 0 // 总
      let consumeData = []
      let defaultData = [
        { id: 1, meal_type: '早餐', turnover: 0, key: 'breakfast', percent: 0 },
        { id: 2, meal_type: '午餐', turnover: 0, key: 'lunch', percent: 0 },
        { id: 3, meal_type: '下午茶', turnover: 0, key: 'afternoon', percent: 0 },
        { id: 4, meal_type: '晚餐', turnover: 0, key: 'dinner', percent: 0 },
        { id: 5, meal_type: '宵夜', turnover: 0, key: 'supper', percent: 0 },
        { id: 6, meal_type: '凌晨餐', turnover: 0, key: 'morning', percent: 0 }
      ]
      for (const key in data) {
        if (Object.hasOwnProperty.call(data, key)) {
          const value = data[key];
          defaultData.forEach(item => {
            if (item.key === key) {
              item.turnover = data[key]
              consumeData.push(item)
            }
          })
          if (value) {
            total += value
          }
        }
      }
      let len = consumeData.length - 1
      let sum = 0
      this.sortData(consumeData, 'turnover')
      if (consumeData.length > 1) {
        consumeData.forEach((item, index) => {
          if (index < len) {
            item.percent = this.computedPercent(item.turnover, total)
            sum = NP.plus(sum, item.percent)
          } else {
            if (sum) {
              item.percent = NP.minus(100, sum)
            } else { // !item.turnover && !sum
              item.percent = 0
            }
          }
        })
      } else if (consumeData.length === 1) {
        consumeData[0].percent = 100
      }
      this.mealConsumeData = this.sortData(consumeData, 'id', 'up')
    },
    // 菜品销量排行
    initFoodSalesRanking(v) {
      let data = []
      let foodData = []
      if (this.foodSalesRankingType === 'fee') {
        data = v.food_sales_amount_ranking
      } else {
        data = v.food_sales_number_ranking
      }
      if (data) foodData = deepClone(data)
      if (this.foodSalesRankingType === 'fee') {
        this.sortData(foodData, 'total_fee')
      } else {
        this.sortData(foodData, 'total_fee')
      }
      this.foodSalesRankingData = foodData
    },
    changeFoodSalesRankingType(type) {
      this.foodSalesRankingType = type
      this.initFoodSalesRanking(this.chartdata)
    },
    // 初始化支付类型排行
    initPayTypeData(list) {
      let data = []
      if (list) data = deepClone(list)
      let total = data.reduce((total, curr) => {
        return total + curr.total_payfee
      }, 0)
      let len = data.length - 1
      let sum = 0
      this.sortData(data, 'total_payfee')
      let dataList = data.map((item, index) => {
        if (!len) {
          item.percent = 100
          return item
        }
        if (index < len) {
          item.percent = this.computedPercent(item.total_payfee, total)
          sum = NP.plus(sum, item.percent)
        } else {
          if (sum) {
            item.percent = NP.minus(100, sum)
          } else { // !item.turnover && !sum
            item.percent = 0
          }
        }
        return item
      })
      this.payTypeData = this.sortData(dataList, 'total_payfee')
    },
    // 设备消费统计
    initDevicePayData(list) {
      let data = []
      if (list) data = deepClone(list)
      let total = data.reduce((total, curr) => {
        return total + curr.total_payfee
      }, 0)
      let len = data.length - 1
      let sum = 0
      this.sortData(data, 'total_payfee')
      let dataList = data.map((item, index) => {
        if (!len) {
          item.percent = 100
          return item
        }
        if (index < len) {
          item.percent = this.computedPercent(item.total_payfee, total)
          sum = NP.plus(sum, item.percent)
        } else {
          if (sum) {
            item.percent = NP.minus(100, sum)
          } else { // !item.turnover && !sum
            item.percent = 0
          }
        }
        return item
      })
      this.devicePayData = this.sortData(dataList, 'total_payfee')
    },
    // 营业额统计
    initTurnoverData(list) {
      let data = []
      if (list) data = deepClone(list)
      let turnoverSetting = TURNOVER_SETTING
      let xAxisData = []
      let seriesData = []
      this.showBarChart = list.length && (list.length > 0)
      if (!this.showBarChart) return

      data.forEach(item => {
        xAxisData.push(item.name)
        seriesData.push({
          ...item,
          value: divide(item.total_payfee)
        })
      })
      turnoverSetting.xAxis.data = xAxisData
      turnoverSetting.series[0].data = seriesData
      if (!xAxisData.length) {
        turnoverSetting.xAxis.axisLine.show = true
      }
      this.$nextTick(() => {
        // if (!this.barTurnoverChart) {
        this.barTurnoverChart = this.$echarts.init(this.$refs.bar_chart)
        // }
        if (this.barTurnoverChart) {
          this.barTurnoverChart.setOption(turnoverSetting)
        }
      })
    },
    // 计算占比
    computedPercent(value, total) {
      if (!total) return 0
      return parseInt((Number(value) / total) * 100)
    },
    // 排序 type升序up，倒叙down
    sortData(data, key, type) {
      let sortData = data
      sortData.sort((a, b) => {
        if (type === 'up') {
          return a[key] - b[key]
        } else {
          return b[key] - a[key]
        }
      })
      return sortData
    },
    CellStyle({ rowIndex }) {
      let width = window.innerWidth
      if (width <= 1350) {
        return 'padding: 14px 0 14px 10px; color: #000; fontSize:12px;'
      } else {
        return 'padding: 14px 0 14px 10px; color: #000; fontSize:16px;'
      }
    },
    HeaderCell({ columnIndex }) {
      if (columnIndex === 2) {
        return ' text-align: center;'
      }
      return 'padding: 14px 0 14px 10px'
    },
    // 5种fee类型
    moneyTypeClass(id) {
      if (id === 1) {
        return 'turnover_fee'
      } else if (id === 2) {
        return 'origin_fee'
      } else if (id === 3) {
        return 'consumption_count'
      } else if (id === 4) {
        return 'recharge_fee'
      } else if (id === 5) {
        return 'refund_fee'
      }
    },
    // 菜品前三名
    foodTOPThreeClass(id) {
      if (id === 1) {
        return 'No1'
      } else if (id === 2) {
        return 'No2'
      } else if (id === 3) {
        return 'No3'
      }
    },
    // 支付类型进度条颜色
    payColor(id) {
      if (id === 1) {
        return '#03CDFE'
      } else if (id === 2) {
        return '#7E86FF'
      } else if (id === 3) {
        return '#05DACB'
      } else if (id === 4) {
        return '#49D96C'
      } else if (id === 5) {
        return '#569BFE'
      } else if (id === 6) {
        return '#FFB72F'
      }
    },
    // 球进度条 自定义设置文字
    formatDevice(index) {
      return () => {
        return index
      }
    },
    resizeChartHandle: debounce(function() {
      if (this.lineChart) this.lineChart.resize()
      if (this.pieMealTimeChart) this.pieMealTimeChart.resize()
      if (this.pieMealWayChart) this.pieMealWayChart.resize()
      if (this.barTurnoverChart) this.barTurnoverChart.resize()
    }, 300),
    changeCategory(e) {
      console.log(e)
      this.secondCategories = e
      this.$emit('udpcategories', e)
    }
  }
}
</script>

<style lang="scss" scoped>

.flex {
  display: flex;
}
.flex-center{
  text-align: center;
  justify-content: center;
}
.empty-text{
  color: #a9a9a9;
  font-size: 14px;
  height: 120px;
  line-height: 120px;
}
.scroll-box{
  height: 100%;
  padding-bottom: 10px;
  padding-right: 10px;
  overflow-y: auto;
}
#line_chart {
  width: 100%;
  height: 320px;
}
#bar_chart {
  width: 100%;
  height: 350px;
}
#circular_chart {
  position: relative;
  max-width: 425px;
  width: 100%;
  height: 400px;
  margin: auto;
}
#annular_chart {
  max-width: 425px;
  width: 100%;
  height: 400px;
  margin: auto;
}
.data-display {
  // width: 1312px;
  .money_type {
    justify-content: space-between;
    margin-bottom: 20px;
    .money_type_item {
      flex: 1;
      height: 100px;
      margin-right: 30px;
      padding: 20px 0 0 20px;
      box-sizing: border-box;
      color: #fff;
      border-radius: 10px;
      overflow: hidden;
      &:nth-child(5) {
        margin-right: 0;
      }
      .title {
        color: rgba(255, 255, 255, 0.7);
        height: 20px;
        overflow: hidden;
      }
      .money {
        position: relative;
        font-size: 20px;
        margin-top: 10px;
      }
    }
  }
  .line-trend {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    .top {
      justify-content: space-between;
      align-items: center;
      .title {
        font-size: 18px;
        font-weight: bold;
      }
      .company_fee {
        font-size: 14px;
        color: #999;
        font-weight: bold;
      }
    }
    .nav_cen {
      margin: 20px 0;
      .item {
        font-size: 14px;
        color: #78797d;
        border: 1px solid #dfe5ec;
        border-radius: 20px;
        padding: 2px 10px;
        margin-right: 10px;
        cursor: pointer;
      }
      .nav_active {
        background-color: #fe943c;
        color: #fff;
        border: 1px solid #fe943c;
      }
    }
  }
  .annular-chart {
    justify-content: space-between;

    flex-wrap: wrap;
    .meal_type_header{
      display: flex;
      justify-content: space-between;
    }
    .meal_item {
      width: 32%;
      // width: 420px;
      height: 460px;
      margin-top: 20px;
      background-color: #fff;
      border-radius: 10px;
      padding: 20px;
      box-sizing: border-box;
      // position: relative;
      .title {
        font-weight: bold;
        font-size: 18px;
        color: #000;
      }
      &:nth-child(3) {
        overflow-y: scroll;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .el-table {
        margin-top: 25px;
        border: 1px solid #ebeef5;
        border-radius: 10px;
      }
    }
    .top_nav {
      background-color: #fe943c;
      color: #fff;
      border: 1px solid #fe943c;
    }
    .ranking_item {
      width: 32%;
      padding: 20px;
      height: 460px;
      margin: 20px 0;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 10px;
      overflow: hidden;
      .title {
        font-weight: bold;
        font-size: 18px;
        color: #000;
      }
      .el-button {
        margin: 0;
      }
    }
    .food_TOP {
      .title {
        color: #000;
      }
      .el-button {
        border-radius: 0;
        &:nth-child(1) {
          border-radius: 3px 0 0 3px;
        }
        &:nth-child(2) {
          border-radius: 0 3px 3px 0;
        }
        &:hover {
          background-color: #fe943c;
          color: #fff;
          border: 1px solid #fe943c;
        }
      }
      .TOP_item {
        justify-content: space-between;
        align-items: center;
        margin-top: 17px;
        .left {
          .count {
            width: 22px;
            height: 22px;
            line-height: 23px;
            text-align: center;
            border-radius: 30px;
            font-weight: bold;
            margin-right: 20px;
          }
          .food {
            width: 180px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #000;
          }
        }
        .right {
          color: #777;
        }
        .No1 {
          color: #fff;
          font-weight: normal;
          background-color: #fd943c;
        }
        .No2 {
          color: #fff;
          font-weight: normal;
          background-color: #f4c70a;
        }
        .No3 {
          color: #fff;
          font-weight: normal;
          background-color: #a1a5a8;
        }
      }
    }
    .pay_TOP {
      .TOP_item {
        margin-top: 25px;
        .top {
          justify-content: space-between;
          margin-bottom: 10px;
          .pay_type {
            font-weight: bold;
          }
          .right {
            width: 65%;
            justify-content: space-between;
            .count {
              width: 100px;
              text-align: right;
            }
            .pay_fee,
            .count {
              color: #777;
            }
          }
        }
      }
    }
    .shebei_TOP {
      .TOP_item {
        justify-content: space-between;
        color: #777;
        margin-top: 28px;
        align-items: center;
        .left {
          align-items: center;
          .type {
            flex: 1;
            margin-left: 10px;
            .name {
              color: #000;
              font-size: 16px;
            }
            .number {
              justify-content: space-between;
            }
          }
        }
      }
    }
  }
  .bar-chart {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    .top {
      justify-content: space-between;
      align-items: center;
      .title {
        font-size: 18px;
        font-weight: bold;
        color: #000;
      }
      .company_fee {
        font-size: 14px;
        color: #999;
        font-weight: bold;
      }
    }
  }
}
.turnover_fee {
  background: url('../../../assets/img/01.png') no-repeat 90% 50% #fe943c;
}
.origin_fee {
  background: url('../../../assets/img/02.png') no-repeat 90% 50% #feab01;
}
.consumption_count {
  background: url('../../../assets/img/03.png') no-repeat 90% 50% #03cdff;
}
.recharge_fee {
  background: url('../../../assets/img/04.png') no-repeat 90% 50% #4bdd6e;
}
.refund_fee {
  background: url('../../../assets/img/05.png') no-repeat 90% 50% #ff4b54;
}
@media screen and (max-width: 1700px) {
  .data-display {
    .money_type {
      .money {
        font-size: 18px !important;
      }
    }
    .annular-chart {
      .meal_item,
      .ranking_item {
        width: 49% !important;
      }
      .food_TOP {
        margin-bottom: 0px !important;
      }
    }
  }
}
</style>
<style lang="scss">
.shebei_TOP {
  .el-progress__text {
    font-size: 16px !important;
    font-weight: bold;
  }
}
</style>

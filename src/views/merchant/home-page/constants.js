// 经营趋势
export const TREND_SETTING = {
  xAxis: {
    type: 'category',
    data: [],
    axisLine: { show: false },
    axisTick: {
      show: false
    },
    splitLine: {
      show: false
    },
    axisLabel: {
      textStyle: {
        fontSize: 14,
        color: '#666',
        lineHeight: 50
      }
    }
  },
  yAxis: {
    type: 'value',
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#dfe5ec'
      }
    },
    splitNumber: 4,
    axisLabel: {
      textStyle: {
        fontSize: 16,
        color: '#666'
      },
      formatter: function(value, index) {
        if (value >= 1000) {
          value = value / 1000 + 'k'
        }
        return value
      }
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      // type: 'cross',
      lineStyle: {
        type: 'dashed'
      }
    },
    transitionDuration: 0,
    borderColor: '#FCA155',
    textStyle: {
      color: '#000'
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;'
    // formatter:
    //   `<div style="padding:5px;font-size:16px;font-weight: 540;">{b0}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">${this.selectTrend.label}</span>￥{c0}</div>`
  },
  grid: {
    left: '40',
    top: '20',
    right: '40'
  },
  series: [
    {
      data: [],
      type: 'line',
      smooth: true,
      symbolSize: 10,
      showSymbol: true, // 隐藏点
      lineStyle: {
        color: '#FCAD6A',
        width: 4
      },
      itemStyle: {
        borderColor: '#FCAD6A',
        borderWidth: 3
      }
    }
  ]
}

export const MEALTIME_SETTING = {
  tooltip: {
    trigger: 'item',
    borderColor: '#FCA155',
    textStyle: {
      color: '#000',
      fontWeight: 500
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      let marker = params.marker
      let percent = params.percent
      return marker + params.name + '&nbsp;&nbsp;&nbsp;' + percent + '%'
    }
  },
  title: {
    text: 0,
    subtext: '订单总笔数',
    left: 'center',
    top: '30%',
    textStyle: {
      color: '#000',
      fontSize: 40,
      align: 'center'
    },
    subtextStyle: {
      color: '#999',
      fontSize: 16,
      align: 'center'
    }
  },
  legend: {
    bottom: '5%',
    left: 'center',
    // formatter: function(name) {
    //   return name
    // },
    icon: 'circle'
    // itemWidth: 8, // 设置宽度
    // itemHeight: 8 // 设置高度
    // padding: [
    //   0, // 上
    //   10, // 右
    //   0, // 下
    //   0 // 左
    // ]
    // textStyle: {
    //   color: 'red',
    //   width: 100,
    //   overflow: 'truncate'
    // }
    // width: 400,
    // itemWidth: 50
  },

  series: [
    {
      // name: 'Access From',
      type: 'pie',
      radius: ['70%', '60%'],
      avoidLabelOverlap: false,
      top: '-25%',
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 5
      },
      hoverAnimation: false,
      label: {
        show: false,
        position: 'center'
        // normal: {
        //   formatter: '{{b},{d}%}'
        // }
      },
      emphasis: {
        label: {
          show: false,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ],
  color: ['#07DED0', '#FE985F', '#9E92F7', '#F97C95', '#58AFFE', '#F8C345']
}

export const MEALWAY_SETTING = {
  tooltip: {
    trigger: 'item',
    borderColor: '#FCA155',
    textStyle: {
      color: '#000'
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      let marker = params.marker
      let percent = params.percent
      return marker + params.name + '&nbsp;&nbsp;&nbsp;' + percent + '%'
    }
  },
  legend: {
    bottom: '5%',
    left: 'center',
    // formatter: function(name) {
    //   return name
    // },
    icon: 'circle'
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      top: '-20%',
      left: '-5%',
      label: {
        show: false,
        position: 'center'
      },
      labelLine: {
        show: false
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '20',
          fontWeight: 'bold',
          minWidth: 300
        }
      },
      data: []
    }
  ],
  color: ['#4D95FA', '#07D7D7', '#727AFF', '#4AD96D', '#ffdc60']
}

export const MEALCONSUME_COLUMS = [
  { label: '餐段', key: 'meal_type' },
  { label: '营业额', key: 'turnover' },
  { label: '占比', key: 'percent' }
]

function formatPriceTo3(val) {
  if (val) {
    // eslint-disable-next-line no-constant-condition
    if (typeof (val !== 'string')) {
      val = val.toString()
    }
    return val.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
  } else {
    return val
  }
}
export const TURNOVER_SETTING = {
  title: {
    text: '单位：元',
    right: 40,
    textStyle: {
      color: '#a0a1a3',
      fontSize: 14
    }
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLine: { show: false },
    axisTick: {
      show: false
    },
    splitLine: {
      show: false
    },
    axisLabel: {
      // width: 120,
      // rotate: 90,
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      formatter: function (value, index) {
        let text = ''
        if (value.length > 4) {
          text = value.slice(0, 4)
          text += '\n' + value.slice(4, 8)
          if (value.length > 8) {
            text += '...'
          }
        } else {
          text = value
        }
        return text
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: { show: false },
    axisTick: {
      show: false
    },
    splitNumber: 4,
    axisLabel: {
      textStyle: {
        fontSize: 16,
        color: '#666'
      },
      formatter: function(value, index) {
        if (value >= 1000) {
          value = value / 1000 + 'k'
        }
        return value
      }
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
        color: '#dfe5ec'
      }
    }
  },
  grid: {
    left: '40',
    top: '50',
    right: '40'
  },
  tooltip: {
    borderColor: '#FCA155',
    textStyle: {
      color: '#000'
    },
    transitionDuration: 0,
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      let value = params.value
      let name = params.name
      return `<div style="padding:5px;font-size:16px;font-weight: 540;">${name}<br /><span style=" color: #666;display:inline-block;margin: 5px 20px 0 0;">营业额</span>￥${formatPriceTo3(
        value
      )}`
    }
  },
  series: [
    {
      barMaxWidth: 30, // 柱图宽度
      color: '#FE943C',
      data: [],
      type: 'bar',
      large: true
    }
  ]
}

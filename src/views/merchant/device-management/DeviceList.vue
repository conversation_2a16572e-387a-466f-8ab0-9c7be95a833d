<template>
  <div class="DeviceList container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">设备列表</div>
        <div class="align-r">
          <button-icon name="批量编辑" color="origin" type="mul" @click="openDeviceDialog('muleditname')">批量编辑</button-icon>
          <button-icon name="批量删除" color="plain" type="mul" @click="mulOperation('mulDel')">批量删除</button-icon>
          <button-icon name="切换视图" color="plain" type="change" @click="changeView">切换视图</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          v-if="dataView === 'tableData'"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="" label="设备图片" align="center" width="150">
            <template slot-scope="scope">
              <!-- <img v-if="scope.row.device_model" class="w-100-p" style="max-height: 100px;" :src="loadDeviceImg(scope.row.device_model)" alt="暂无图片" /> -->
              <el-image v-if="scope.row.device_model"  class="w-100-p" style="max-height: 100px;" :src="loadDeviceImg(scope.row.device_model)">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="consumer_name" label="所属组织" align="center"></el-table-column>
          <el-table-column prop="group_name_alias" label="适用分组" align="center">
            <template slot-scope="scope">
              <span class="ps-i pointer" @click="openDeviceDialog('group', scope.row)">查看</span>
              <!-- <i class="el-icon-edit ps-i" @click="openDeviceDialog('group', scope.row)"></i> -->
            </template>
          </el-table-column>
          <el-table-column prop="device_type_alias" label="设备类型" align="center"></el-table-column>
          <el-table-column prop="device_model_alias" label="设备型号" align="center" show-overflow-tooltip width="140"></el-table-column>
          <el-table-column prop="device_name" label="设备名" align="center" width="120">
            <template slot-scope="scope">
              <span>{{scope.row.device_name}}</span>
              <i class="el-icon-edit ps-i" @click="openDeviceDialog('name', scope.row)"></i>
            </template>
          </el-table-column>
          <el-table-column prop="" label="设备号" align="center"></el-table-column>
          <el-table-column prop="serial_no" label="SN码" align="center"></el-table-column>
          <el-table-column prop="device_mac" label="设备地址" align="center"></el-table-column>
          <el-table-column prop="activation_status" label="激活状态" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.activation_status?'已激活':'未激活' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="online" label="设备状态" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.online?'在线':'离线' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="activate_time" label="激活码有效期" align="center" width="170">
            <template slot-scope="scope">
              <div>生效时间：{{scope.row.effective}}</div>
              <div>失效时间：{{scope.row.expiration}}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDeviceDialog('setting', scope.row)"
              >设置</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-blue"
                v-if="scope.row.device_type === 'QCG'"
                @click="gotoCupboardEdit(scope.row)"
              >编辑信息</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-blue"
                v-if="scope.row.device_type === 'ZNC'"
                @click="gotoSetFood(scope.row)"
              >绑定菜品</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="mulOperation('del', scope.row.device_no)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
        <!-- list end -->
        <div class="view-data" v-if="dataView === 'viewData'">
          <div v-for="(item, index) in tableData" :key='index' class="view-data-item">
            <div class="item-top">
              <div>
                <el-checkbox v-model="item.isChoice">{{item.device_name}}</el-checkbox>
                <!-- <i class="el-icon-edit ps-i"></i> -->
              </div>
              <div class="item-status">
                <div :class="['item-point',statusBgColor(item.status_alias)]"></div>
                <div>{{item.status_alias}}</div>
              </div>
            </div>
            <div class="item-img">
              <img class="w-100-p" :src="loadDeviceImg(item.device_model)" alt="暂无图片" />
            </div>
            <div :class="['item-bottom',statusBgColor(item.status_alias)]"></div>
            <div class="item-mask">
              <div class="text">所属组织：{{item.consumer_name}}</div>
              <div class="text">
                适用分组：{{item.group_name_alias}}
                <i class="el-icon-edit ps-i" @click="openDeviceDialog('group', item)"></i>
              </div>
              <div class="btn">
                <el-button type="primary"
                  size="mini"
                  class="ps-origin-btn"
                  style="min-width:56px;"
                  @click="openDeviceDialog('setting', item)"
                >设置</el-button>
                <el-button
                  type="primary"
                  size="mini"
                  class="ps-plain-btn"
                  style="min-width:56px;"
                  v-if="item.device_type === 'QCG'"
                @click="gotoCupboardEdit(item)"
                >编辑信息</el-button>
                <el-button
                  type="primary"
                  size="mini"
                  class="ps-plain-btn"
                  style="min-width:56px;"
                  v-if="item.device_type === 'ZNC'"
                  @click="gotoSetFood(item)"
                >绑定菜品</el-button>
                <el-button
                  type="primary"
                  size="mini"
                  class="ps-red-btn"
                  style="min-width:56px;"
                  @click="mulOperation('del', item.device_no)"
                >删除</el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- list end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <device-dialog
      :isshow.sync="deviceDialogVisible"
      :title="deviceDialogTitle"
      :type="deviceDialogType"
      :width="deviceDialogWidth"
      :device-info="deviceInfo"
      :device-list="selectList"
      :organizationList="searchFormSetting.organization_id.dataList"
      :deviceTypeList="searchFormSetting.device_type.dataList"
      :confirm="searchHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
import deviceDialog from './components/DeviceDialog.vue'
import { DEVICE_IMG } from './constants'

export default {
  name: 'DeviceList',
  components: { deviceDialog },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [
        {
          name: '1'
        }
      ],
      viewData: [
        {
          name: '取餐柜001',
          isChoice: false,
          status: '在线',
          img: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9df1ad0960343dc5cbeb75b321c6c4661640336843252.png',
          point: 'XXX食堂/XXX档口',
          group: 'XXX分组'
        },
        {
          name: '取餐柜001',
          isChoice: false,
          status: '离线',
          img: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9df1ad0960343dc5cbeb75b321c6c4661640336843372.png',
          point: 'XXX食堂/XXX档口/XXX食堂/XXX档口',
          group: 'XXX分组'
        },
        {
          name: '取餐柜001',
          isChoice: false,
          status: '未激活',
          img: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9df1ad0960343dc5cbeb75b321c6c4661640336843431.png',
          point: 'XXX食堂/XXX档口',
          group: 'XXX分组'
        }
      ],
      searchFormSetting: {
        organization_id: {
          type: 'consumeSelect',
          label: '消费点',
          value: '',
          placeholder: '请选择消费点'
        },
        online: {
          type: 'select',
          label: '设备状态',
          value: '',
          placeholder: '请选择设备状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '离线',
            value: false
          }, {
            label: '在线',
            value: true
          }]
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        device_name: {
          type: 'input',
          label: '设备名',
          value: '',
          placeholder: '请输入设备名'
        },
        device_no: {
          type: 'input',
          label: '设备号',
          value: '',
          placeholder: '请输入设备号'
        },
        activation_status: {
          type: 'select',
          label: '激活状态',
          value: '',
          placeholder: '请选择激活状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '未激活',
            value: false
          }, {
            label: '已激活',
            value: true
          }]
        }
      },
      dataView: 'tableData',
      deviceDialogVisible: false,
      deviceDialogTitle: '',
      deviceDialogType: '',
      deviceDialogWidth: '',
      deviceInfo: {},
      selectList: [],
      deviceUi: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDeviceList()
      this.getDeviceType()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.deviceDialogVisible = false
      this.getDeviceList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取设备列表
    async getDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.map(item => {
          item.effective = item.activate_time.split(' ')[0]
          item.expiration = item.activate_time.split(' ')[3]
          item.group_name_alias = ''
          item.group_name.map(group => {
            item.group_name_alias += group + '， '
          })
          item.group_name_alias = item.group_name_alias.slice(0, -2)
          item.status_alias = item.activation_status ? item.online ? '在线' : '离线' : '未激活'
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDeviceList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDeviceList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectList = []
      this.selectList = Object.freeze(val) // 解除下监听吧，节约点资源
    },
    // 批量操作 type表示类型，content是弹窗文字，data是单个操作的时候带过去的数据
    mulOperation(type, data) {
      if (!data && !this.selectList.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case "mulDel":
          content = '确定批量删除所选设备吗？'
          break;
        case "del":
          content = '确定删除该设备吗？'
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {}
            let ids = []
            switch (type) {
              case "mulDel":
                this.selectList.map(item => { ids.push(item.device_no) })
                params.device_nos = ids
                params.choices = 3
                break;
              case "del":
                params.device_nos = [data]
                params.choices = 3
                break;
            }
            this.delDevice(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 删除设备
    async delDevice(params) {
      const res = await this.$apis.apiBackgroundDeviceDeviceBatchModifyPost(params)
      if (res.code === 0) {
        this.$message.success('删除设备成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    openDeviceDialog(type, data) {
      // console.log(data, 'jiushi');
      this.deviceDialogType = type
      this.deviceInfo = data
      switch (type) {
        case 'group':
          this.deviceDialogTitle = '修改分组'
          this.deviceDialogWidth = '400px'
          break;
        case 'name':
          this.deviceDialogTitle = '修改设备名'
          this.deviceDialogWidth = '500px'
          break;
        case 'setting':
          this.deviceDialogTitle = '设置'
          this.deviceDialogWidth = '500px'
          break;
        case 'muleditname':
          this.deviceDialogTitle = '批量编辑'
          this.deviceDialogWidth = '600px'
          if (!this.selectList.length) {
            this.deviceDialogVisible = false
            return this.$message.error('请先选择数据！')
          }
          break;
      }
      this.deviceDialogVisible = true
    },
    changeView() {
      if (this.dataView === 'tableData') {
        this.dataView = 'viewData'
      } else if (this.dataView === 'viewData') {
        this.dataView = 'tableData'
      }
    },
    statusBgColor(status) {
      if (status === '未激活') {
        return 'notActive'
      } else if (status === '在线') {
        return 'online'
      } else if (status === '离线') {
        return 'offline'
      }
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    gotoSetFood(data) {
      this.$router.push({
        name: 'WeightFood',
        query: {
          deviceId: data.device_no,
          deviceModel: data.device_model,
          deviceName: data.device_name
        }
      })
    },
    gotoCupboardEdit(data) {
      let ceilList = JSON.parse(data.cupboard_json).ceil_list
      this.$router.push({
        name: 'CupboardEdit',
        query: {
          device_no: data.device_no,
          device_name: data.device_name,
          ceil_list: JSON.stringify(ceilList)
        }
      })
    },
    // 加载设备图片
    loadDeviceImg(deviceModel) {
      if (DEVICE_IMG[deviceModel]) {
        return require('@/assets/img/device/' + DEVICE_IMG[deviceModel])
      }
      return ''
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";
.DeviceList{
  .view-data{
    display: flex;
    flex-wrap: wrap;
    .view-data-item{
      width: 270px;
      margin-right: 20px;
      margin: 15px;
      box-shadow: 0px 0px 10px #ababab;
      position: relative;
      font-size: 14px;
      border-radius: 8px;
      .item-top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #ECEDF2;
        padding: 0 15px;
        height: 40px;
        border-radius: 8px 8px 0 0;
        .el-checkbox{
          display: flex;
          align-items: center;
        }
        .el-checkbox__label{
          max-width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .item-status{
          display: flex;
          align-items: center;
          .item-point{
            width: 6px;
            height: 6px;
            border-radius: 10px;
            margin-right: 5px;
          }
        }
      }
      .item-img{
        margin: 20px 0;
        img{
          width: 100%;
        }
      }
      .item-bottom{
        padding: 0 15px;
        height: 5px;
        border-radius: 0 0 8px 8px;
      }
      .item-mask{
        display: none;
        position: absolute;
        bottom: 5px;
        background-color: #ffffffd9;
        padding: 15px 20px;
        .text{
          line-height: 30px;
          width: 230px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .btn{
          text-align: center;
          margin-top: 10px;
        }
      }
      .notActive{
        background-color: #FB5D51;
      }
      .online{
        background-color: #28D36E;
      }
      .offline{
        background-color: #FE943C;
      }
    }
    .view-data-item:hover{
      .item-mask{
        display: block;
      }
    }
  }
}
</style>

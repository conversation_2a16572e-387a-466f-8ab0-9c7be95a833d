<template>
  <div>
    <div class="table-wrapper">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div class="table-title">编辑信息</div>
        <div style="padding-right:20px;">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm">保存</el-button>
        </div>
      </div>
      <el-form
        v-loading="isLoading"
        :model="deviceInfo"
        inline
        ref="deviceForm"
        :rules="deviceFormRule"
        style="padding: 0 25px;"
        label-width="100px"
      >
        <el-form-item label="设备名" prop="deviceName">
          <el-input v-model="deviceInfo.deviceName" class="ps-input w-180"></el-input>
        </el-form-item>
        <el-form-item label="设备型号">
          <el-input disabled v-model="deviceInfo.deviceModel" class="ps-input w-180"></el-input>
        </el-form-item>
        <el-form-item label="所属组织">
          <el-input disabled v-model="deviceInfo.organizationName" class="ps-input w-180"></el-input>
        </el-form-item>
        <el-form-item label="取餐柜终端id">
          <el-input disabled v-model="deviceInfo.appId" class="ps-input w-180"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-wrapper">
      <div class="table-header" style="display: flex;align-items: center; justify-content: space-between;">
        <div class="table-title">配送区域：
          <el-select :multiple="true" collapse-tags v-model="deviceInfo.addrArea" @change="addrAreaChange" class="ps-select w-300">
            <el-option
              v-for="item in addressArea"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
        <div class="align-r">
          <button-icon color="plain" type="del" @click="mulOperation('mulDel')">批量删除</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column prop="name" label="配送区域" align="center"></el-table-column>
          <el-table-column width="180" label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="mulOperation('del', scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'DeviceArea',
  props: {
    deviceNo: [String, Number]
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      deviceInfo: {
        deviceName: "",
        deviceModel: "",
        organizationName: "",
        groupIds: [],
        appId: "",
        addrArea: []
      }, // 数据
      deviceFormRule: {
        deviceName: [
          { required: true, message: '请输入设备名', trigger: 'blur' }
        ]
      },
      deviceOrg: "",
      tableData: [],
      addressArea: [],
      selectListId: []
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      await this.getSettingDetail()
      this.getAddressAreaList()
    },
    checkForm() {
      this.$refs.deviceForm.validate(valid => {
        if (valid) {
          let params = {
            device_no: this.$route.query.device_no,
            device_name: this.deviceInfo.deviceName
            // user_group_ids: this.deviceInfo.groupIds
          }
          this.saveSetting(params)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveSetting(params) {
      const res = await this.$apis.apiBackgroundDeviceDeviceModifyPost(params)
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.getSettingDetail()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getSettingDetail() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceInfoPost({
        device_no: this.deviceNo
      })
      if (res.code === 0) {
        if (res.data) {
          this.deviceInfo.deviceName = res.data.device_name
          this.deviceInfo.deviceModel = res.data.device_model
          this.deviceInfo.organizationName = res.data.organization_name
          // this.deviceInfo.groupIds = []
          // res.data.user_group.map(item => {
          //   this.deviceInfo.groupIds.push(item.id)
          // })
          // console.log(JSON.stringify(res.data.cupboard_json) === "{}")
          this.deviceInfo.appId = res.data.cupboard_json.appId
          this.deviceInfo.addrArea = []
          res.data.addr_area.map(item => {
            this.deviceInfo.addrArea.push(item.id)
          })
          this.tableData = res.data.addr_area
          this.deviceOrg = res.data.organization
        } else {
          this.resetForm()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async getAddressAreaList() {
      const res = await this.$apis.apiAddressAddersAreaAllPost({ id: this.deviceOrg })
      if (res.code === 0) {
        this.addressArea = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    async addrAreaChange() {
      const res = await this.$apis.apiBackgroundDeviceDeviceCupboardModifyPost({
        device_no: this.deviceNo,
        addr_area: this.deviceInfo.addrArea
      })
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.getSettingDetail()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    // 操作
    mulOperation(type, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case 'mulDel':
          content = '确定批量删除所选配送区域吗？'
          break
        case 'del':
          content = '确定删除该配送区域吗？'
          break
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              device_no: this.deviceNo,
              can_refund: 0,
              addr_area: this.deviceInfo.addrArea
            }
            switch (type) {
              case 'mulDel':
                params.addr_area = this.setDelId(this.selectListId)
                break
              case 'del':
                params.addr_area = this.setDelId([data])
                break
            }
            this.addrAreaChange(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    setDelId(ids) {
      ids.map(idItem => {
        let index = this.deviceInfo.addrArea.findIndex(item => item === idItem)
        if (index >= 0) {
          this.deviceInfo.addrArea.splice(index, 1)
        }
      })
    }
  }
}
</script>

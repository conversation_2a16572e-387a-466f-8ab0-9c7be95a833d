<template>
  <div>
    <div class="table-wrapper" style="margin-bottom: 20px;">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div style="display: flex;align-items: center;">
          <div class="table-title">取餐柜设置</div>
        </div>
        <div style="padding-right:20px;">
          <div>当前设备：{{deviceName}}</div>
        </div>
      </div>
      <div class="content-wrapper" v-loading="isLoading">
        <div class="ceil-top flex-center">
          <div>
            餐格设置：
            <el-button type="primary" class="origin-btn" @click="setCupboardCeilLock('big')">大餐格</el-button>
            <el-button type="plain" @click="setCupboardCeilLock('little')">小餐格</el-button>
            根据取餐柜可用餐格设置可被分配的餐格，避免出现不可用餐格被系统自动分配
          </div>
          <div><span class="green-text">{{canUseCeilNum}}个可用</span><span>/{{totalCeilNum}}个</span></div>
        </div>
        <div class="ceil-list">
          <div
            v-for="(item, index) in cupboardCeilList"
            :key="index"
            :class="[selectList.indexOf(item.ceil_no) === -1 ? '' : 'active-ceil']"
            class="ceil-list-item"
            @click="selectCeil(item)"
          >
            <div class="ceil-list-item-top flex-center">
              <div>
                <span class="ceil-item-size">{{ item.ceil_size ? '大' : '小' }}</span>{{ item.ceil_no }}
              </div>
              <!-- <div class="status-img flex-center">
                <img src="@/assets/img/cupboard_open_icon.png" alt="" srcset="">
                <img src="@/assets/img/cupboard_light_icon.png" alt="" srcset="">
                <img src="@/assets/img/cupboard_disinfect_icon.png" alt="" srcset="">
                <img src="@/assets/img/cupboard_hot_icon.png" alt="" srcset="">
              </div> -->
            </div>
            <div>
              <div>订单号：{{ item.trade_no ? item.trade_no : '--' }}</div>
              <div>取餐码：{{ item.take_meal_number ? item.take_meal_number : '--' }}</div>
              <div>存餐码：{{ item.put_meal_number ? item.put_meal_number : '--' }}</div>
            </div>
            <div><img v-if="item.forbidden" class="ceil-lock" src="@/assets/img/cupboard_lock_icon.png" alt="" srcset=""></div>
          </div>
        </div>
      </div>
    </div>
    <div class="button-footer flex-center">
      <div style="min-width: 130px;">
        <el-checkbox v-model="selectAll" class="ps-checkbox" @change="selectAllCeil"></el-checkbox> 全选，已选{{ selectList.length }}个
      </div>
      <div style="display: flex; flex-wrap: wrap;">
        <div class="btn" @click="setCupboardCeilLock('lock')">锁定餐格</div>
        <div class="btn" @click="setCupboardCeilLock('unlock')">解锁餐格</div>
        <div class="btn" @click="cupboardMessageSend(406)">开柜</div>
        <div class="btn" @click="cupboardMessageSend(400)">开灯</div>
        <div class="btn" @click="cupboardMessageSend(401)">关灯</div>
        <div class="btn" @click="cupboardMessageSend(402)">开加热</div>
        <div class="btn" @click="cupboardMessageSend(403)">关加热</div>
        <div class="btn" @click="cupboardMessageSend(404)">开消毒</div>
        <div class="btn" @click="cupboardMessageSend(405)">关消毒</div>
        <div class="btn" @click="cupboardMessageSend(310)">销单</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'DeviceCeil',
  props: {
    deviceNo: [String, Number],
    deviceName: String,
    ceilList: Array
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      cupboardCeilList: [],
      selectList: [],
      selectAll: false,
      canUseCeilNum: 0,
      totalCeilNum: 0
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.selectList = []
      this.selectAll = false
      this.getCupboardInfo()
    },
    // 获取格子信息
    async getCupboardInfo() {
      const res = await this.$apis.apiBackgroundDeviceDeviceGetCupboardCeilInfoListPost({
        device_no: Number(this.deviceNo)
      })
      if (res.code === 0) {
        this.cupboardCeilList = res.data.info
        this.canUseCeilNum = res.data.can_use_ceil_num
        this.totalCeilNum = res.data.total_ceil_num
      } else {
        this.$message.error(res.msg)
      }
    },
    selectCeil(data) {
      let index = this.selectList.indexOf(data.ceil_no)
      if (index === -1) {
        this.selectList.push(data.ceil_no)
      } else {
        this.selectList.splice(index, 1)
      }
    },
    selectAllCeil() {
      if (this.selectAll) {
        this.cupboardCeilList.map(item => {
          this.selectList.push(item.ceil_no)
        })
      } else {
        this.selectList = []
      }
    },
    async cupboardMessageSend(code) {
      if (!this.selectList.length) {
        return this.$message.error('请先选择餐格！（点击餐格进行选择）')
      }
      let params = {
        device_no: this.deviceNo,
        ceil_no_list: this.selectList,
        mqtt_code: code
      }
      let tradeNos = []
      if (code === 310) { // 销单
        let list = this.cupboardCeilList.filter(item => this.selectList.indexOf(item.ceil_no) !== -1)
        if (list.findIndex(item => item.trade_no.length !== 0) === -1) {
          return this.$message.error("所选餐格暂无订单")
        } else {
          let tradeList = list.filter(item => item.trade_no.length !== 0)
          tradeList.map(item => {
            tradeNos.push(item.trade_no)
          })
          params.trade_nos = tradeNos
        }
      }
      if (this.isLoading) return this.$message.error('请勿重复操作')
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceCupboardMessageSendPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.initLoad()
        this.selectList = []
      } else {
        this.$message.error(res.msg)
      }
    },
    async setCupboardCeilLock(type) {
      if (!this.selectList.length) {
        return this.$message.error('请先选择餐格！（点击餐格进行选择）')
      }
      let bigCeilList = [] // 大餐格
      let littleCeilList = [] // 小餐格
      let lockCeilList = [] // 锁
      let unlockCeilList = [] // 解锁
      switch (type) {
        case "big":
          bigCeilList = this.selectList
          break;
        case "little":
          littleCeilList = this.selectList
          break;
        case "lock":
          lockCeilList = this.selectList
          break;
        case "unlock":
          unlockCeilList = this.selectList
          break;
      }
      let list = this.cupboardCeilList.filter(item => this.selectList.indexOf(item.ceil_no) === -1)
      list.map(item => {
        if (item.forbidden) {
          lockCeilList.push(item.ceil_no)
        } else {
          unlockCeilList.push(item.ceil_no)
        }
        if (item.ceil_size) {
          bigCeilList.push(item.ceil_no)
        } else {
          littleCeilList.push(item.ceil_no)
        }
      })
      let params = {
        device_no: this.deviceNo
      }
      let api
      if (type === 'big' || type === 'little') {
        params.big_ceil_list = bigCeilList
        params.little_ceil_list = littleCeilList
        api = this.$apis.apiBackgroundDeviceDeviceSetCupboardCeilTypePost(params)
      } else if (type === 'lock' || type === 'unlock') {
        params.ceil_forbidden_list = lockCeilList
        params.ceil_allow_list = unlockCeilList
        api = this.$apis.apiBackgroundDeviceDeviceSetCupboardCeilForbiddenPost(params)
      }
      if (this.isLoading) return this.$message.error('请勿重复操作')
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped >
  .DeviceCeilSet{
    margin-top: 30px;
    .origin-btn{
      background-color: #ff9b45;
      border-color: #ff9b45;
    }
    .green-text{
      color: #43d67e;
    }
    .flex-center{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .content-wrapper{
      padding: 0 20px 30px;
      font-size: 14px;
    }
    .ceil-list{
      display: flex;
      flex-wrap: wrap;
      margin-top: 20px;
      .active-ceil{
        border: 2px #ff9b45 solid;
      }
      .ceil-list-item{
        border-radius: 8px;
        box-shadow:   0px 0px 6px rgba(147, 150, 155, 0.3);
        width: 250px;
        margin-right: 20px;
        margin-bottom: 20px;
        padding: 15px 15px 10px;
        line-height: 30px;
        position: relative;
        cursor: pointer;
        font-size: 14px;
        .ceil-list-item-top{
          border-bottom: 1px solid #DCDFE6;
          padding-bottom: 10px;
          margin-bottom: 10px;
          .ceil-item-size{
            padding: 6px;
            color: #fff;
            background-color: #ff9b45;
            border-radius: 20px;
            margin-right: 5px;
          }
        }
        .ceil-lock{
          position: absolute;
          right: 15px;
          bottom: 15px;
        }
        .status-img{
          img{
            width: 23px;
            margin-left: 4px;
          }
        }
      }
    }
    .button-footer{
      width: 100%;
      padding: 10px;
      padding-right: 40px;
      padding-left: 270px;
      position: fixed;
      bottom: 0;
      right: 0;
      background-color: #fff;
      text-align: right;
      z-index: 9999;
      .btn{
        width: 90px;
        text-align: center;
        background-color: #ff9b45;
        margin: 5px;
        margin-left: 20px;
        padding: 5px 10px;
        border-radius: 20px;
        color: #ffffff;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }
</style>

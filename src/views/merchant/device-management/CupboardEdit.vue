<template>
  <div class="CupboardEdit container-wrapper">
    <div>
      <el-radio-group class="ps-radio-btn m-t-20" v-model="pageType" @change="changePageType">
        <el-radio-button label="area">配送区域设置</el-radio-button>
        <el-radio-button label="ceil">餐格设置</el-radio-button>
      </el-radio-group>
    </div>
    <div v-if="pageType === 'area'">
      <device-area :device-no="deviceNo"/>
    </div>
    <div v-if="pageType === 'ceil'" class="DeviceCeilSet">
      <device-ceil :device-no="deviceNo" :device-name="deviceName"  :ceil-list="ceilList"/>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import DeviceArea from './components/DeviceArea.vue'
import DeviceCeil from './components/DeviceCeil.vue'
export default {
  name: 'CupboardEdit',
  components: {
    DeviceArea,
    DeviceCeil
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      pageType: 'area',
      isLoading: false, // 刷新数据
      deviceNo: '',
      deviceName: '',
      ceilList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    changePageType(type) {
      this.pageType = type
    },
    async initLoad() {
      this.deviceNo = this.$route.query.device_no
      // 餐格相关
      this.deviceName = this.$route.query.device_name
      this.ceilList = JSON.parse(this.$route.query.ceil_list)
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>

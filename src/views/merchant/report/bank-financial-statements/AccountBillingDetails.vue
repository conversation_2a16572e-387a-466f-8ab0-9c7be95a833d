<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        label-width="120px"
        :form-setting="searchFormSetting"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport" v-permission="['background_order.finance_report.sub_mch_order_list_export']">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row" />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { ACCOUNT_BILLING_DETAILS } from './constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapGetters } from 'vuex'

export default {
  name: 'AccountBillingDetails',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '序号', key: 'index', type: 'index' },
        { label: '经营时间', key: 'date' },
        { label: '订单金额', key: 'success_total_fee', type: 'money' },
        { label: '订单金额费率', key: 'success_rate_fee', type: 'money' },
        { label: '退款金额', key: 'refund_total_fee', type: 'money' },
        { label: '退款金额费率', key: 'refund_rate_fee', type: 'money' },
        { label: '营收金额', key: 'real_total_fee', type: 'money' },
        { label: '营收费率', key: 'rate_real_total_fee', type: 'money' },
        { label: '实际营收金额', key: 'fin_total_fee', type: 'money' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: ACCOUNT_BILLING_DETAILS,
      collect: [ // 统计
        { key: 'total_income_fee', value: 0, label: '营收金额=订单金额-退款金额=¥', type: 'money' },
        { key: 'total_income_rate', value: 0, label: '营收费率=订单金额费率-退款金额费率=￥', type: 'money' },
        { key: 'total_pay_fee', value: '', label: '实际营收金额：营收金额-营收费率', block: "true" }
      ],
      printType: 'AccountBillingDetails'
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization'])
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      // this.currentTableSetting = this.tableSetting
      this.initPrintSetting()
      await this.getSubMchList()
      this.getSubMachOrderList()
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.getSubMachOrderList()
    },

    async searchHandle() {
      this.currentPage = 1
      this.getSubMachOrderList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求账户数据
    async getSubMchList() {
      // const params = formatQueryParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const params = {
        company_id: this.userInfo.company_id,
        organization_id: this.organization
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAbcOutputOutputGetAbcPayinfoPost(params)
      this.isLoading = false
      if (res.code === 0) {
        if (res.data && res.data.length) {
          this.subMchList = res.data.map((v, i) => {
            if (i === 0) {
              this.searchFormSetting.sub_mch_id.value = v.sub_mch_id
            }
            return { label: v.sub_mch_id, id: v.payinfo_id, value: v.sub_mch_id }
          })
          this.searchFormSetting.sub_mch_id.dataList = this.subMchList
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 请求列表数据
    async getSubMachOrderList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportSubMchOrderListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSubMachOrderList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'AccountBillingDetails',
        url: 'apiBackgroundReportCenterDataReportSubMchOrderListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_type: this.printType,
          print_title: '账户结账明细',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportSubMchOrderListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
</style>

<template>
  <div class="report-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :loading="isLoading"
      @search="searchHandle"
      :form-setting="searchFormSetting"
    ></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-button size="mini" @click="gotoExport">导出Excel</el-button>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="currentTableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        />
      </div>
      <!-- table content end -->
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="total"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { ACCOUNT_WALLET_DAILY } from './constantsConfig'
import { debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'AccountWalletDaily',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '日期', key: 'date' },
        { label: '期初余额', key: 'date_start_fee', type: 'money' },
        { label: '期末余额', key: 'date_end_fee', type: 'money' },
        { label: '充值金额', key: 'charge_fee', type: 'money' },
        { label: '充值退款', key: 'charge_refund_fee', type: 'money' },
        { label: '储值钱包消费金额', key: 'wallet_fee', type: 'money' },
        { label: '储值钱包消费退款', key: 'wallet_refund_fee', type: 'money' },
        { label: '提现金额', key: 'withdraw', type: 'money' },
        // { label: '退户金额（未提现）', key: 'xxxxx', type: 'money' },
        { label: '补贴发放金额', key: 'subsidy_add_fee', type: 'money' },
        { label: '补贴钱包消费金额', key: 'subsidy_fee', type: 'money' },
        { label: '补贴钱包退款金额', key: 'subsidy_refund_fee', type: 'money' },
        { label: '补贴钱包清零金额', key: 'subsidy_clear_fee', type: 'money' },
        { label: '赠送金额', key: 'complimentary_add_fee', type: 'money' },
        { label: '赠送钱包消费金额', key: 'complimentary_fee', type: 'money' },
        { label: '赠送钱包清零金额', key: 'complimentary_clear_fee', type: 'money' },
        { label: '赠送钱包退款金额', key: 'complimentary_refund_fee', type: 'money' }

        // { label: '消费金额', key: 'consume_fee', type: 'money' },
        // { label: '消费退款', key: 'refund_fee', type: 'money' },
        // { label: '提现金额', key: 'withdraw', type: 'money' },
        // { label: '赠送清零金额', key: 'complimentary_clear_fee', type: 'money' },
        // { label: '补贴清零金额', key: 'subsidy_clear_fee', type: 'money' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: ACCOUNT_WALLET_DAILY,
      collect: [
        // 统计
        {
          key: 'total_consume_fee',
          value: '',
          label: '总消费合计：',
          class: 'origin',
          type: 'money'
        },
        {
          key: 'total_refund_fee',
          value: '',
          label: '总退款合计：',
          class: 'origin',
          type: 'money'
        },
        {
          key: 'text',
          value:
            '期末余额=期初余额+充值金额-充值退款-储值钱包消费金额+储值钱包退款金额-提现金额+补贴发放金额-补贴钱包消费金额+补贴钱包退款金额-补贴钱包清零金额+赠送金额-赠送钱包消费金额-赠送钱包清零金额+赠送钱包退款金额。',
          label: '',
          block: true
        }
      ],
      printType: 'AccountWalletDaily'
    }
  },
  mounted() {
    this.searchFormSetting.org_ids.value = [this.$store.getters.organization]
    this.initLoad()
  },
  methods: {
    async initLoad() {
      // this.currentTableSetting = this.tableSetting
      this.initPrintSetting()
      // await this.getOrgWallet()
      // this.getPechargeMethod()
      this.getPersonPaymentList()
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getPersonPaymentList()
    }, 300),
    // 充值方式
    async getPechargeMethod() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeMethod({
        page: 1,
        page_size: 999,
        org_ids: []
      })
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.payway.dataList = [
          {
            label: '全部',
            value: ''
          },
          ...result
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getPersonPaymentList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportWalletDailyListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getPersonPaymentList()
    },
    gotoExport() {
      const option = {
        type: 'AccountWalletDaily',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: '账户钱包日报表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportWalletDailyListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table {
  text-align: center;
  font-size: 12px;
}
</style>

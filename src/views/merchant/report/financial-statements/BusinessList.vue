<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form ref="searchRef" @search="searchHandle" :loading="isLoading" :form-setting="searchFormSetting">
        <div class="searchref_top" slot="perv">
          <el-button
            v-for="(item, index) in tabsList"
            :key="index"
            :class="{ active: item.type === currentType }"
            @click="tabHandler(item)"
          >
            {{ item.name }}
          </el-button>
        </div>
      </search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="gotoExport"  v-permission="['background_order.finance_report.order_business_list_export']">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
          />
          <!-- show-summary
            :summary-method="getSummaries" -->
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="page"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="total"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { nowRecentSevenDay, nowPickerOptions } from '@/utils/formatPickerOptions'
import { getRequestParams, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'BusinessList',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      currentType: 'consume_date',
      tabsList: [
        {
          type: 'consume_date',
          name: '消费时间'
        },
        {
          type: 'pay_date',
          name: '支付时间'
        },
        {
          type: 'deduction_date',
          name: '扣款时间'
        }
      ],
      tableSetting: [
        { key: 'date', label: '日期' },
        {
          key: 'consume_total',
          label: '合计',
          children: [
            { key: 'consume', label: '营业额', type: 'money' },
            { key: 'consume_count', label: '消费笔数' },
            { key: 'consume_real', label: '实收金额', type: 'money' },
            { key: 'consume_refund', label: '退款金额', type: 'money' }
          ]
        }
        // { key: 'consume', label: '总营业额', type: 'money' },
        // { key: 'consume_count', label: '总消费笔数' }
      ],
      tableData: [],
      collectList: {}, // 合计
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '搜索时间',
          value: nowRecentSevenDay,
          pickerOptions: nowPickerOptions,
          clearable: false
        },
        org_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        add_food_subsidy_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '加入餐补',
          value: false
        }
      },
      collect: [
        // 统计
        {
          key: 'text',
          value:
            '报表说明：营业额 = 消费订单客户支付金额（营业额没有减去消费退款金额），营业额-退款金额=实收金额',
          label: ''
        }
      ],
      printType: 'BusinessList'
    }
  },
  watch: {
    'searchFormSetting.add_food_subsidy_fee.value': {
      handler: function(val) {
        if (!val) {
          this.collect = [{
            key: 'text',
            value:
              '报表说明：营业额 = 消费订单客户支付金额（营业额没有减去消费退款金额），营业额-退款金额=实收金额',
            label: ''
          }]
        } else {
          this.collect = [{
            key: 'text1',
            value:
              '营业额=所有支付成功订单实付金额合计+所有支付成功订单餐补金额合计',
            label: ''
          }, {
            key: 'text2',
            value:
              '退款金额=操作类型为“部分退款”订单动账金额合计+操作类型为“退款”订单动账金额合计+操作类型为“退款”对应原支付订单的餐补金额合计',
            label: ''
          }]
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.isLoading = true
      await this.getLevelName()
      await this.getMealType()
      this.getBusinessList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.tableSetting = [
        { key: 'date', label: '日期' },
        {
          key: 'consume_total',
          label: '合计',
          children: [
            { key: 'consume', label: '营业额', type: 'money' },
            { key: 'consume_count', label: '消费笔数' },
            { key: 'consume_real', label: '实收金额', type: 'money' },
            { key: 'consume_refund', label: '退款金额', type: 'money' }
          ]
        }
        // { key: 'consume', label: '总营业额', type: 'money' },
        // { key: 'consume_count', label: '总消费笔数' }
      ]
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.page = 1
      this.getBusinessList()
    }, 300),
    // 获取列表数据
    async getBusinessList() {
      const params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportOrderBusinessListPost({
        date_type: this.currentType,
        ...params
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        // let priceKey = ['consume', 'breakfast_consume', 'lunch_consume', 'afternoon_consume', 'dinner_consume', 'supper_consume', 'morning_consume']
        this.tableData = res.data.result
        // 设置合计的值
        this.setSummaryData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // level name
    async getLevelName() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      if (res.code === 0) {
        if (res.data) {
          let levelList = res.data.map(v => {
            return {
              key: v.level,
              label: v.name
            }
          })
          // 因为需要添加日期，截取第一个再添加
          this.tableSetting.splice(1, 0, ...levelList)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取餐段
    async getMealType() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetMealTypePost({
        org_id: this.$store.getters.organization
      })
      if (res.code === 0) {
        if (res.data.meal_type_info) {
          let mealList = res.data.meal_type_info.map(v => {
            return {
              key: v.meal_type,
              label: v.meal_type_alias,
              children: [
                { key: v.meal_type + '_consume', label: '营业额', type: 'money' },
                { key: v.meal_type + '_consume_count', label: '消费笔数' },
                { key: v.meal_type + '_consume_real', label: '实收金额', type: 'money' },
                { key: v.meal_type + '_consume_refund', label: '退款金额', type: 'money' }
              ]
            }
          })
          this.tableSetting.push(...mealList)
          // this.currentTableSetting = this.tableSetting
          this.initPrintSetting()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    async tabHandler(data) {
      this.collect = []
      this.tableData = []
      this.tableSetting = []
      this.currentTableSetting = []
      this.currentType = data.type
      if (data.type === 'pay_date') {
        this.tableSetting = [
          { key: 'date', label: '日期' },
          { key: 'consume', label: '营业额', type: 'money' },
          { key: 'consume_count', label: '消费笔数' },
          { key: 'consume_real', label: '实收金额', type: 'money' },
          { key: 'consume_refund', label: '退款金额', type: 'money' }
        ]
        this.collect = [
          // 统计
          {
            key: 'text',
            value:
              '报表说明：营业额 = 消费订单客户支付金额（营业额没有减去消费退款金额），营业额-退款金额=实收金额',
            label: ''
          },
          {
            key: 'text1',
            value: '支付时间存在餐段空档无法统计的问题，只统计当天的汇总数据',
            label: '',
            block: true
          }
        ]
        await this.getLevelName()
      } else if (data.type === 'consume_date' || data.type === 'deduction_date') {
        this.tableSetting = [
          { key: 'date', label: '日期' },
          {
            key: 'consume_total',
            label: '合计',
            children: [
              { key: 'consume', label: '营业额', type: 'money' },
              { key: 'consume_count', label: '消费笔数' },
              { key: 'consume_real', label: '实收金额', type: 'money' },
              { key: 'consume_refund', label: '退款金额', type: 'money' }
            ]
          }
        ]
        this.collect = [
          // 统计
          {
            key: 'text',
            value:
              '报表说明：营业额 = 消费订单客户支付金额（营业额没有减去消费退款金额），营业额-退款金额=实收金额',
            label: ''
          }
        ]
        await this.getLevelName()
        await this.getMealType()
      }
      this.initPrintSetting()
      this.searchHandle()
    },
    // 表格合计
    getSummaries(param) {
      return
      const { columns, data } = param
      const list = [
        'consume',
        'consume_count',
        'consume_real',
        'consume_refund',

        'breakfast_consume',
        'breakfast_consume_count',
        'breakfast_real',
        'breakfast_refund',

        'lunch_consume',
        'lunch_consume_count',
        'lunch_real',
        'lunch_refund',
        'afternoon_consume',
        'afternoon_consume_count',
        'afternoon_real',
        'afternoon_refund',
        'dinner_consume',
        'dinner_consume_count',
        'dinner_real',
        'dinner_refund',
        'supper_consume',
        'supper_consume_count',
        'supper_real',
        'supper_refund',
        'morning_consume',
        'morning_consume_count',
        'morning_real',
        'morning_refund'
      ]
      let sums = []
      if (data.length) {
        columns.forEach((item, index) => {
          if (index === 0) {
            sums[index] = '合计'
          } else if (list.includes(item.property)) {
            sums[index] = this.collectList[item.property]
            console.log(this.collectList[item.property])
          } else {
            sums[index] = ''
          }
        })
      }
      return sums
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.page = val.current
      this.pageSize = val.pageSize
      this.getBusinessList()
    },
    gotoExport() {
      const option = {
        type: 'BusinessList',
        params: {
          ...getRequestParams(this.searchFormSetting, this.page, this.pageSize),
          date_type: this.currentType
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = getRequestParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: '营业额日报表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportOrderBusinessListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          show_summary: false, // 合计
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: true, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            date_type: this.currentType,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.booking-meal-wrapper {
  .active {
    background-color: #ff9b45;
    color: #fff;
  }
  .searchref_top {
    margin-bottom: 10px;
    .el-button {
      width: 120px;
    }
  }
}
</style>

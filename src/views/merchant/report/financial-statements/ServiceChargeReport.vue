<template>
  <div class="ServiceChargeReport container-wrapper">
    <div class="table-type">
      <div
        :class="['table-type-btn', tableType === 'consume' ? 'active-btn' : '']"
        @click="changeTableType('consume')"
      >
        扣款手续费
      </div>
      <div
        :class="['table-type-btn', tableType === 'charge' ? 'active-btn' : '']"
        @click="changeTableType('charge')"
      >
        充值手续费
      </div>
    </div>
    <consume-service-report v-if="tableType === 'consume'" ref="consumeService"/>
    <charge-service-report v-if="tableType === 'charge'" ref="chargeService"/>
  </div>
</template>
<script>
import { debounce } from '@/utils'
import ConsumeServiceReport from './components/ConsumeServiceReport'
import ChargeServiceReport from './components/ChargeServiceReport'
export default {
  components: { ConsumeServiceReport, ChargeServiceReport },
  name: 'MerchantServiceChargeReport',
  data() {
    return {
      tableType: 'consume'
    }
  },
  created() {},
  mounted() {},
  methods: {
    initLoad() {},
    changeTableType(type) {
      this.tableType = type
    },

    // 节下流咯
    searchHandle: debounce(function() {}, 300)
  }
}
</script>
<style lang="scss" scoped>
.ServiceChargeReport {
  .table-type {
    padding: 20px 0;
    display: flex;
    font-size: 16px;
    .table-type-btn {
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #e8f0f8;
      border-radius: 40px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn {
      color: #fff;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>

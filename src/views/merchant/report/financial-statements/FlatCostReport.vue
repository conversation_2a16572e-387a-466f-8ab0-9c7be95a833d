<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form ref="searchRef" @search="searchHandle" :loading="isLoading" :form-setting="searchFormSetting"></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="handleExport">导出记录</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"/>
        </div>
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { recentSevenDay } from './constantsConfig'
import { debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'FlatCostReport',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { key: 'name', label: '姓名' },
        { key: 'person_no', label: '人员编号' },
        { key: 'card_no', label: '卡号' },
        { key: 'phone', label: '手机号' },
        { key: 'create_time', label: '创建时间' },
        { key: 'user_group_name', label: '分组' },
        { key: 'department_group_name', label: '部门' },
        { key: 'pay_time', label: '扣款时间' },
        { key: 'pay_method_alias', label: '收款方式' },
        { key: 'pay_fee', label: '收款金额', type: 'money' },
        { key: 'operate_type_alias', label: '操作类型' },
        { key: 'operate_name', label: '操作员' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        select_date: {
          type: 'daterange',
          label: '搜索时间',
          value: recentSevenDay,
          clearable: false
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        phone: {
          type: 'input',
          label: '手机',
          value: '',
          placeholder: '请输入手机号码'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        card_user_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: '',
          multiple: true,
          placeholder: '请选择分组'
        },
        card_department_group_id: {
          type: 'organizationDepartmentSelect',
          multiple: false,
          isLazy: false,
          checkStrictly: true,
          label: '部门',
          value: '',
          placeholder: '请选择部门'
        },
        pay_method: {
          type: 'select',
          label: '收款方式',
          value: '',
          placeholder: '请选择收款方式',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        }
      },
      collect: [ // 统计
        { key: 'deducted_amount', value: 0, label: '工本费收款总金额:￥', type: 'money' }
      ],
      printType: 'FlatCostReport'
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      // this.currentTableSetting = this.tableSetting
      this.initPrintSetting()
      this.getFlatCostReport()
      this.getPayMethod()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getFlatCostReport()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getFlatCostReport() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportFlatCostListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getFlatCostReport()
    },
    async getPayMethod() {
      const res = await this.$apis.apiCardServiceFlatCostGetPayMethodListPost()
      if (res.code === 0) {
        this.searchFormSetting.pay_method.dataList = res.data.pay_method_list
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExport() {
      const option = {
        type: "ExportFlatCostReport",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_type: this.printType,
          print_title: '工本费收款明细',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportFlatCostListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>

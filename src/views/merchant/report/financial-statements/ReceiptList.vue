<template>
<!-- eslint-disable vue/no-unused-vars -->
  <div class="receipt-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :form-setting="searchSetting" :loading="isLoading" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['background_invoice.invoice_record.invoice_record_list_export']">导出报表</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column :index="indexMethod" v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { RECEIPT_LIST_SEARCH } from './constantsConfig'
import report from '@/mixins/report' // 混入

export default {
  name: 'ReceiptList',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tabType: 1,
      searchSetting: deepClone(RECEIPT_LIST_SEARCH),
      // 数据列表
      tableData: [],

      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      levelList: [],

      // 报表设置相关
      tableSetting: [
        { label: '创建时间', key: 'create_time' },
        { label: '申请时间', key: 'apply_time' },
        { label: '总单号', key: 'unified_out_trade_no' },
        { label: '订单号', key: 'order_trade_no' },
        { label: '开票状态', key: 'invoice_status_alias' },
        { label: '开票金额', key: 'invoice_fee', type: 'money' },
        { label: '开票日期', key: 'open_time' },
        { label: '发票代码', key: 'invoice_code' },
        { label: '发票号码', key: 'invoice_no' },
        { label: '净价金额', key: 'net_price', type: 'money' },
        { label: '税额', key: 'total_tax', type: 'money' },
        { label: '税率', key: 'tax_rate', type: 'percent' },
        { label: '发票抬头', key: 'invoice_header' },
        { label: '税号', key: 'tax_no' },
        { label: '发票内容', key: 'invoice_content' },
        { label: '开票人', key: 'name' }
        // { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'ReceiptList'
    }
  },
  created() {
    this.getLevelName()
    this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      // this.getWalletList()
      // this.getOrgWallet()
      this.getReceiptList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getReceiptList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 获取动账钱包
    async getWalletList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportWalletListPost()
      if (res.code === 0) {
        const result = []
        res.data.result.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        // APPEAL_ORDER_SEARCH_PENDING.wallet_org.dataList = result
        // APPEAL_ORDER_SEARCH_PROCESSED.wallet_org.dataList = result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            if (data[key].value instanceof Array) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取待处理订单列表
    async getReceiptList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundInvoiceInvoiceRecordInvoiceRecordListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchSetting)
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // level name
    async getLevelName() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      if (res.code === 0) {
        if (res.data) {
          this.levelList = res.data.map(v => {
            return {
              key: v.level,
              label: v.name
            }
          })
          // 初始化每个tableSetting
          // this.pendingTableSetting = deepClone(RECEIPT_LIST_SEARCH)
          // this.pendingTableSetting.splice(22, 0, ...this.levelList)
          this.tableSetting.splice(15, 0, ...this.levelList)
          this.initPrintSetting()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取组织下的钱包
    async getOrgWallet() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetOrgWalletPost()
      if (res.code === 0) {
        // res.data.wallet.map(v => {
        //   this.columns.forEach(item => {
        //     if (this.showWalletList[v].includes(item.column)) {
        //       item.show = true
        //     }
        //   })
        // })
        console.log(this.columns)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getReceiptList()
    },
    gotoDetail(type, row) {
      // this.$router.push({
      //   name: 'AppealOrderDetail',
      //   query: {
      //     type: type,
      //     id: row.id
      //   }
      // })
    },
    // 导出报表
    handleExport() {
      const option = {
        type: this.printType,
        url: 'apiBackgroundInvoiceInvoiceRecordInvoiceRecordListExportPost',
        params: {
          page: this.currentPage,
          page_size: 999999,
          ...this.formatQueryParams(this.searchSetting)
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>

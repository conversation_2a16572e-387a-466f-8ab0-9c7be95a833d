<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_order.finance_report.pecharge_order_list_export']">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :index="indexMethod"
            header-row-class-name="ps-table-header-row"/>
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <common-pagination
          ref="pagination"
          :total="total"
          :onPaginationChange="onPaginationChange"
          :page="page"
        ></common-pagination>
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import CommonPagination from '../../meal-management/booking-setting/CommonPagination.vue'
import { TopUpDetailSearchForm } from '../financial-statements/constantsConfig'
import { getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'TopUpDetail',
  components: {
    CommonPagination
  },
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '序号', key: 'index', type: "index" },
        { label: '订单编号', key: 'trade_no', width: '160px' },
        { label: '充值时间', key: 'pay_time', width: '160px' },
        { label: '到账时间', key: 'finish_time', width: '160px' },
        { label: '姓名', key: 'name', width: '100px' },
        { label: '手机号', key: 'mobile', width: '120px' },
        { label: '人员编号', key: 'person_no' },
        { label: '分组', key: 'payer_group', width: '100px' },
        { label: '部门', key: 'payer_department_group', width: '100px' },
        { label: '充值类型', key: 'recharge_type_alias' },
        { label: '充值方式', key: 'recharge_method_alias' },
        { label: '原充值金额', key: 'origin_fee', type: 'money' },
        { label: '充值钱包', key: 'recharge_wallet' },
        { label: '充值到账', key: 'fee', type: 'money' },
        { label: '赠送钱包', key: 'presented_wallet' },
        { label: '赠送钱包到账', key: 'presented_fee', width: '120px', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '备注', key: 'attach' },
        { label: '订单来源', key: 'order_source' },
        { label: '第三方订单号', key: 'out_trade_no', width: '160px' },
        { label: '交易流水号', key: 'provider_trade_no', width: '160px' }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: TopUpDetailSearchForm,
      collect: [ // 统计
        { key: 'pecharge_fee', value: 0, label: '原充值金额:￥', type: 'money' },
        { key: 'real_fee', value: 0, label: '充值到账金额:￥', type: 'money' },
        { key: 'discount_fee', value: 0, label: '赠送钱包金额:￥', type: 'money' },
        { key: 'real_fee', value: 0, label: '手续费合计:￥', type: 'money' }
      ],
      printType: 'TopUpDetail'
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      // this.currentTableSetting = this.tableSetting
      this.initPrintSetting()
      this.requestPechargeOrderList()
      // this.userGroupList()
      this.getPechargeMethod()
      // this.getCharInfoList()
    },
    // 充值方式
    async getPechargeMethod() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeMethod({
        page: 1,
        page_size: 999,
        org_ids: []
      })
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.recharge_method.dataList = [{ label: '全部', value: '' }, ...result]
      } else {
        this.$message.error(res.msg)
      }
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.pageSize = 10
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },

    // 获取分组信息
    // async userGroupList() {
    //   this.isLoading = true
    //   const res = await this.$apis.apiCardServiceCardUserGroupListPost({
    //     status: 'enable',
    //     page: 1,
    //     page_size: 9999999
    //   })
    //   this.isLoading = false
    //   if (res.code === 0) {
    //     this.searchFormSetting.pay_group_ids.dataList = res.data.results
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },

    async searchHandle() {
      this.page = 1
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },

    // 导出 列表
    handleExport() {
      this.$confirm(`确定导出？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true
      })
        .then(e => {
          const searchData = {}
          Object.keys(this.searchFormSetting).forEach(key => {
            if (key !== 'select_time' && this.searchFormSetting[key].value !== '') {
              searchData[key] = this.searchFormSetting[key].value
            }
          })
          const params = {
            page: 1,
            page_size: 9999999,
            start_date: this.searchFormSetting.select_time.value[0],
            end_date: this.searchFormSetting.select_time.value[1],
            ...searchData
          }
          this.$router.push({
            name: 'Excel',
            query: {
              type: 'TopUpDetail',
              params: JSON.stringify(params)
            }
          })
        })
        .catch(e => {})
    },

    // 请求列表数据
    async requestPechargeOrderList() {
      const params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeOrderListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },

    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },

    departmentNode(node) {
      return {
        id: node.id,
        label: node.group_name,
        children: node.children_list
      }
    },

    // 翻页
    onPaginationChange(data) {
      this.page = data.current
      this.pageSize = data.pageSize
      this.requestPechargeOrderList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'TopUpDetail',
        params: getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = getRequestParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_type: this.printType,
          print_title: '充值明细表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportPechargeOrderListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
</style>

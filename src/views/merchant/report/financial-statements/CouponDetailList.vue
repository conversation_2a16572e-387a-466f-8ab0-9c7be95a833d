<template>
  <div>
    <div class="coupon-details-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <div class="tab-box">
        <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
          <el-radio-button
            v-loading="isLoading"
            v-for="tab in tabTypeList"
            :key="tab.value"
            :label="tab.value"
          >
            {{ tab.label }}
          </el-radio-button>
        </el-radio-group>
      </div>
      <div class="tab-item m-t-20">
        <search-form
          ref="searchRef"
          :loading="isLoading"
          @search="searchHandle"
          :form-setting="searchFormSetting"
          label-width="120px"
          :autoSearch="false"
        ></search-form>

        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="align-r">
              <el-button
                size="mini"
                @click="gotoExport"
                v-permission="[
                  'background_report_center.finance_report.coupon_order_list_export',
                  'background_report_center.finance_report.coupon_refund_order_list_export'
                ]"
              >
                <!-- v-permission="[
                  'background_order.finance_report.sub_mch_order_detail_list_export',
                  'background_report_center.data_report.sub_mch_order_detail_refund_list_export'
                ]" -->
                导出Excel
              </el-button>
              <!-- <button-icon color="plain" @click="gotoPrint">打印</button-icon> -->
              <!-- <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon> -->
            </div>
          </div>

          <!-- table-content start -->
          <div class="table-content">
            <custom-table
              border
              v-loading="isLoading"
              :table-data="tableData"
              :table-setting="tableSetting"
              ref="tableData"
              style="width: 100%"
              stripe
              header-row-class-name="ps-table-header-row"
              :isFirst="isFirstSearch"
            />
            <!-- :isFirst="isFirstSearch" -->
          </div>
          <!-- table content end -->
          <!-- 统计 start -->
          <table-statistics :statistics="collect" />
          <!-- end -->
          <!-- 分页 start -->
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next, jumper'"
            :total="totalCount"
          ></pagination>
          <!-- 分页 end -->
        </div>
      </div>
    </div>
    <!-- <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting> -->
  </div>
</template>

<script>
import { COUPON_DETAILS } from './constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
// import { mapGetters } from 'vuex'
import { deepClone } from '@/utils'

export default {
  name: 'BankFlowDetails',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tabType: 'consume',
      tabTypeList: [
        // permissions: 'background_report_center.data_report.sub_mch_order_detail_refund_list'
        { label: '消费订单', value: 'consume' },
        { label: '退款订单', value: 'refund' }
      ],
      tableSetting: [],
      consumeSetting: [
        { label: '订单号', key: 'trade_no' },
        { label: '姓名', key: 'name' },
        { label: '手机号', key: 'phone' },
        { label: '人员编号', key: 'person_no' },
        { label: '分组', key: 'payer_group_name' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '实收金额', key: 'pay_fee', type: 'money' },
        { label: '券类型', key: 'coupon_type_alias' },
        { label: '优惠券规则名字', key: 'coupon_name', width: '120' },
        { label: '抵扣金额', key: 'deduction_fee', type: 'money' },
        { label: '所属组织', key: 'primary' },
        { label: '所属餐段', key: 'meal_type_alias' },
        { label: '创建时间', key: 'create_time' },
        { label: '扣款时间', key: 'pay_time' }
      ],
      refundSetting: [
        { label: '订单号', key: 'refund_no' },
        { label: '姓名', key: 'payer_name' },
        { label: '手机号', key: 'payer_phone' },
        { label: '人员编号', key: 'payer_person_no' },
        { label: '分组', key: 'payer_group' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '退款金额', key: 'refund_fee', type: 'money' },
        { label: '券类型', key: 'coupon_type_alias' },
        { label: '优惠券规则名字', key: 'coupon_name', width: '120' },
        { label: '抵扣退款', key: 'deduction_fee', type: 'money' },
        { label: '所属组织', key: 'primary' },
        { label: '所属餐段', key: 'meal_type_alias' },
        { label: '创建时间', key: 'create_time' },
        { label: '扣款时间', key: 'finish_time' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: deepClone(COUPON_DETAILS),
      collect: [], // 统计
      // printType: 'BankFlowDetailsFlow',
      isFirstSearch: true
    }
  },
  computed: {
    // ...mapGetters(['userInfo', 'organization', 'allPermissions'])
  },
  created() {},
  mounted() {
    if (this.tabType === 'consume') {
      this.tableSetting = this.consumeSetting
    } else {
      this.tableSetting = this.refundSetting
    }
    // this.initPrintSetting()
    // this.initTabList()
  },
  methods: {
    // 初始页面权限
    initTabList() {
      // let result = this.tabTypeList.slice(0)
      // let result = []
      // this.tabTypeList.forEach(v => {
      //   if (this.allPermissions.includes(v.permissions)) {
      //     result.push(v)
      //   }
      // })
      // this.tabTypeList = result
      this.tabType = this.tabTypeList.length ? this.tabTypeList[0].value : ''
      this.setTabDataHandle(this.tabType)
    },
    async refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.isFirstSearch = true
      this.getOrderList()
    },

    async searchHandle(e) {
      // if (e && e === 'search') {
      this.currentPage = 1
      this.getOrderList()
      this.isFirstSearch = false
      // }
    },
    changeTabHandle(e) {
      // let subMachId = this.searchFormSetting.sub_mch_id.value
      this.setTabDataHandle(e)
      // this.searchFormSetting.sub_mch_id.value = subMachId
      // this.initPrintSetting()
      this.isFirstSearch = true
    },
    setTabDataHandle(e) {
      if (e === 'consume') {
        this.tableSetting = this.consumeSetting
        this.searchFormSetting = deepClone(COUPON_DETAILS)
        // this.printType = 'BankFlowDetailsFlow'
        this.collect = [
          { key: 'total_amount', value: 0, label: '汇总金额：￥', type: 'money' },
          { key: 'total_deduction_fee', value: 0, label: '抵扣合计：￥', type: 'money' },
          { key: 'total_pay_amount', value: '', label: '实收金额：￥', type: 'money' }
        ]
      } else if (e === 'refund') {
        this.tableSetting = this.refundSetting
        this.collect = [
          { key: 'total_amount', value: 10, label: '退款全额：￥', type: 'money' },
          { key: 'total_deduction_fee', value: 0, label: '抵扣退款：￥', type: 'money' }
        ]
        this.searchFormSetting = deepClone(COUPON_DETAILS)
        this.searchFormSetting = JSON.parse(
          JSON.stringify(this.searchFormSetting).replace('trade_no', 'refund_no')
        )
        // this.printType = 'BankFlowDetailsRefund'
      }
      this.tableData = []
      this.currentPage = 1 // 第几页
      // 等
      // this.$nextTick(() => {
      //   this.getOrderList()
      // })
      // this.initPrintSetting()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    getOrderList() {
      if (this.tabType === 'consume') {
        this.getReportCouponOrderList()
      } else if (this.tabType === 'refund') {
        this.getCouponRefundOrderList()
      }
    },
    // 请求列表数据
    async getReportCouponOrderList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportCouponOrderListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取退款订单数据
    async getCouponRefundOrderList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportCouponRefundOrderListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getOrderList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: this.printType,
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      if (this.tabType === 'consume') {
        option.url = 'apiBackgroundReportCenterDataReportCouponOrderListExportPost'
      } else {
        option.url = 'apiBackgroundReportCenterDataReportCouponRefundOrderListExportPost'
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      // const params = this.formatQueryParams(this.searchFormSetting)
      // const { href } = this.$router.resolve({
      //   name: 'Print',
      //   query: {
      //     print_date_state: true,
      //     print_type: this.printType,
      //     print_title: '交易流水明细',
      //     result_key: 'results', // 返回的数据处理的data keys
      //     api:
      //       this.tabType === 'consume'
      //         ? 'apiBackgroundReportCenterDataReportSubMchOrderDetailListPost'
      //         : 'apiBackgroundReportCenterDataReportSubMchOrderDetailRefundListPost', // 请求的api
      //     show_summary: false, // 合计
      //     show_print_header_and_footer: true, // 打印页头页尾
      //     table_setting: JSON.stringify(this.tableSetting),
      //     current_table_setting: JSON.stringify(this.currentTableSetting),
      //     collect: JSON.stringify(this.collect),
      //     push_summary: false, // 合计添加到到table数据最后
      //     params: JSON.stringify({
      //       ...params,
      //       page: 1,
      //       page_size: this.totalCount || 10
      //     })
      //   }
      // })
      // window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.coupon-details-wrapper {
  .tab-item-setting {
    padding-bottom: 10px;
    background-color: #fff;
  }
  .tab-item-content {
    margin: 20px 0;
    padding: 20px;
    border-radius: 5px;
    background-color: #fff;
    .cash-btn {
      margin-left: 10px;
    }
  }
}
</style>

<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <!-- <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
          >
            <el-table-column
              align="center"
              v-for="col in tableSetting"
              :prop="col.column"
              :label="col.label"
              :key="col.column"
              :width="col.width"
            >
              <template slot-scope="scope">
                <span v-if="col.column.includes('fee') || col.column.includes('wallet_money')">
                  ￥{{ (Number(scope.row[col.column]) / 100).toFixed(2) }}
                </span>
                <span v-else-if="scope.row[col.column] === ''">
                  <span>--</span>
                </span>
                <span v-else-if="col.column === 'index'">
                  <span v-text="(currentPage - 1) * pageSize + 1 + scope.$index"></span>
                </span>
                <span v-else>
                  <span>{{ scope.row[col.column] }}</span>
                </span>
              </template>
            </el-table-column>
          </el-table> -->
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row" />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { WithdrawSearchForm } from './constantsConfig'
import { getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'WithdrawList',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '序号', key: 'index', type: 'index' },
        { label: '订单编号', key: 'trade_no', width: '160px' },
        { label: '申请时间', key: 'create_time', width: '160px' },
        { label: '到账时间', key: 'pay_time', width: '160px' },
        { label: '姓名', key: 'name', width: '100px' },
        { label: '手机号', key: 'phone', width: '120px' },
        { label: '人员编号', key: 'person_no' },
        { label: '卡号', key: 'card_no', width: '100px' },
        { label: '分组', key: 'payer_group_name', width: '100px' },
        { label: '动账钱包', key: 'wallet_name', width: '100px' },
        { label: '部门', key: 'payer_department_group_name', width: '100px' },
        { label: '提现金额', key: 'withdraw_fee', type: 'money' },
        { label: '动账金额', key: 'wallet_fee', type: 'money' },
        { label: '赠送清零', key: 'complimentary_fee', type: 'money' },
        { label: '提现方式', key: 'payway_alias' },
        { label: '操作员', key: 'operator_name' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: WithdrawSearchForm,
      collect: [ // 统计
        { key: 'total_success_amount', value: 0, label: '累计提现:￥', type: 'money' },
        { key: 'total_complimentary_amount', value: 0, label: '赠送清零:￥', type: 'money' }
      ],
      printType: 'WithdrawList'
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      // this.currentTableSetting = this.tableSetting
      this.initPrintSetting()
      this.getWithdrawList()
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.getWithdrawList()
    },

    async searchHandle() {
      this.currentPage = 1
      this.getWithdrawList()
    },

    // 请求列表数据
    async getWithdrawList() {
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportOrderWithdrawDetailsListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWithdrawList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'WithdrawList',
        params: getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = getRequestParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_type: this.printType,
          print_title: '提现明细表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportOrderWithdrawDetailsListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
</style>

import * as dayjs from 'dayjs'
import { MEAL_TYPES } from '@/utils/constants'
import { beforeRecentSevenDay, berforePickerOptions, nowRecentSevenDay, nowPickerOptions } from '@/utils/formatPickerOptions'
// const rechargeMethod = [
//   { label: '线上', value: 'ONLINE' },
//   { label: '线下', value: 'OFFLINE' }
// ]

// const operateTypeList = [
//   { value: 'JC_CONSUME', label: '计次消费' },
//   { value: 'KF_CONSUME', label: '扣费消费' },
//   { value: 'RESERVATION_CONSUME', label: '预约' },
//   { value: 'REPORT_CONSUME', label: '报餐' },
//   { value: 'SUBSIDY_PUBLISH', label: '补贴发放' },
//   { value: 'SUBSIDY_CLEAR', label: '补贴清零' },
//   { value: 'ORDER_REFUND', label: '全额退款' },
//   { value: 'ORDER_PART_REFUND', label: '部分退款' },
//   { value: 'RECHARGE', label: '充值 ' }
//   // { value: 'CASH_WITHDRAWAL', label: '提现' }
//   // { value: 'DRAW', label: '后台取款' },
//   // { value: 'RECHARGE', label: '充值' },
//   // { value: 'FLAT_COST_CONLLECTION', label: '工本费退款' },
//   // { value: 'PATCH_COST', label: '补卡' },
//   // { value: 'UNKNOWN', label: '未知' }
// ]

const operateTypeList = [
  { value: 'ORDER_PART_REFUND', label: '部分退款' },
  { value: 'ORDER_REFUND', label: '退款' },
  { value: 'JC_CONSUME', label: '计次消费' },
  { value: 'KF_CONSUME', label: '扣费消费' },
  { value: 'RESERVATION_CONSUME', label: '预约消费' },
  { value: 'REPORT_MEAL_CONSUME', label: '报餐消费' },
  { value: 'BUFFET_CONSUME', label: '称重消费' },
  { value: 'DRAW', label: '后台提现' },
  { value: 'RECHARGE', label: '充值' },
  // { value: 'JIAOFEI', label: '缴费' },
  { value: 'SUBSIDY_PUBLISH', label: '补贴发放' },
  { value: 'SUBSIDY_CLEAR', label: '补贴清零' },
  { value: 'FLAT_COST_REFUND', label: '退费' },
  { value: 'SUPPLEMENTARY', label: '补卡' },
  { value: 'PUBLISH', label: '发卡' },
  { value: 'ORDERING_FOOD_PAY', label: '订餐扣费' },
  { value: 'UNKNOWN', label: '未知' }
]

// const wallteArr = [
//   { label: '', value: 'online' },
//   { label: '', value: 'instore' },
//   { label: '', value: 'charge' },
//   { label: '', value: 'charge_offline' }
// ]

export const payMethods = [
  { value: 'PushiPay', label: '朴食储值支付' },
  { value: 'OCOMPAY', label: '一卡通-鑫澳康支付' },
  { value: 'SHIYAOPAY', label: '一卡通-石药支付' },
  { value: 'ABCPay', label: '农行支付' },
  { value: 'CCBPAY', label: '建行支付' },
  { value: 'BOCPAY', label: '中行支付' },
  { value: 'ICBCPAY', label: '工行支付' },
  { value: 'MEITUANPAY', label: '美团支付' },
  { value: 'ShouqianbaPay', label: '收钱吧支付' },
  { value: 'WechatPay', label: '微信支付' },
  // { value: 'UNKNOWN', label: '未知' },
  { value: 'CashPay', label: '现金支付' }
]

// 消费类型
export const paymentOrderType = [
  { value: 'reservation', label: '预约订单' },
  { value: 'report_meal', label: '报餐' },
  { value: 'buffet', label: '称重' },
  { value: 'instore', label: '到店就餐' },
  { value: 'FLAT_COST_REFUND', label: '退费' },
  { value: 'SUPPLEMENTARY', label: '补卡' },
  { value: 'PUBLISH', label: '发卡' },
  { value: 'other', label: '其他' }
]

// 设备状态
export const DEVICE_STATUS = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '在线',
    value: 'ONLINE'
  },
  {
    label: '离线',
    value: 'OFFLINE'
  }
]
export const ORDER_TYPE = [
  { value: 0, label: '消费类' },
  { value: 1, label: '退款类' },
  { value: 2, label: '提现类' },
  { value: 3, label: '充值类' },
  { value: 4, label: '补贴类' },
  { value: 5, label: '工本费类' },
  { value: 6, label: '缴费类' }
]

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]
export const DetailTotalSearchForm = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  card_no: {
    type: 'input',
    value: '',
    label: '卡号',
    placeholder: '请输入卡号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  payment_order_type: {
    type: 'select',
    value: '',
    label: '消费类型',
    clearable: true,
    // multiple: true,
    // collapseTags: true,
    dataList: paymentOrderType
  },
  payway: {
    type: 'select',
    value: '',
    label: '支付类型',
    dataList: payMethods,
    clearable: true
  },
  sub_payway: {
    type: 'select',
    value: '',
    label: '支付方式',
    dataList: [],
    clearable: true
  },
  meal_type: {
    type: 'select',
    value: null,
    label: '餐段',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      ...MEAL_TYPES
    ]
  },
  device_type: {
    type: 'select',
    value: '',
    label: '设备类型',
    listNameKey: 'name',
    listValueKey: 'key',
    dataList: [],
    clearable: true
  },
  device_name: {
    type: 'input',
    value: '',
    label: '交易设备',
    placeholder: '请输入交易设备'
  },
  pay_device_status: {
    type: 'select',
    value: '',
    label: '设备状态',
    dataList: DEVICE_STATUS,
    clearable: true
  },
  wallet_org: {
    type: 'organizationSelect',
    value: [],
    label: '动账钱包',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    clearable: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    clearable: true
  },
  controller: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入要搜索的操作员'
  }
}

export const DetailTotalSearchForm2 = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  card_no: {
    type: 'input',
    value: '',
    label: '卡号',
    placeholder: '请输入卡号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  operate_type: {
    type: 'select',
    value: '',
    label: '操作类型',
    // multiple: true,
    // collapseTags: true,
    dataList: [{ label: '全部', value: '' }, ...operateTypeList],
    clearable: true
  },
  payway: {
    type: 'select',
    value: '',
    label: '支付类型',
    dataList: [],
    clearable: true
  },
  sub_payway: {
    type: 'select',
    value: [],
    label: '支付方式',
    dataList: [],
    clearable: true
  },
  meal_type: {
    type: 'select',
    value: '',
    label: '餐段',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      ...MEAL_TYPES
    ]
  },
  // device_name: {
  //   type: 'select',
  //   value: '',
  //   label: '交易设备',
  //   listNameKey: 'name',
  //   listValueKey: 'key',
  //   dataList: []
  // },
  wallet_org: {
    type: 'organizationSelect',
    value: [],
    label: '动账组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  controller: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入要搜索的操作员'
  },
  only_discount: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看优惠',
    value: false
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  }
}

export const TopUpDetailSearchForm = {
  select_time: {
    type: 'daterange',
    label: '充值时间',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    listNameKey: 'group_name',
    listValueKey: 'id',
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    flat: false,
    checkStrictly: true,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  recharge_method: {
    type: 'select',
    value: '',
    label: '充值方式',
    dataList: [],
    clearable: true
  },
  recharge_type: {
    type: 'select',
    value: '',
    label: '充值类型',
    dataList: [
      { label: '线上充值', value: 'charge' },
      { label: '线下充值', value: 'charge_offline' }
    ],
    clearable: true
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  }
}

export const DeviceCodeSearchForm = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: recentSevenDay
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  device_id: {
    type: 'input',
    value: '',
    label: '设备号',
    placeholder: '请输入设备号'
  },
  // device_name: {
  //   type: 'input',
  //   value: '',
  //   label: '设备名',
  //   placeholder: '请输入设备名'
  // },
  device_type: {
    type: 'select',
    value: '',
    label: '设备类型',
    listNameKey: 'name',
    listValueKey: 'key',
    dataList: [],
    clearable: true
  }
}

// 提现明细表
export const WithdrawSearchForm = {
  select_time: {
    type: 'daterange',
    label: '申请时间',
    value: recentSevenDay
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号',
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名',
    clearable: true
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号',
    clearable: true
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号',
    clearable: true
  },
  card_no: {
    type: 'input',
    value: '',
    label: '卡号',
    placeholder: '请输入卡号',
    clearable: true
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  payway: {
    type: 'select',
    value: '',
    label: '提现方式',
    dataList: payMethods,
    clearable: true
  }
}

// 个人充值汇总
export const PERSONAL_RECHARGE_SUMMARY = {
  select_time: {
    type: 'daterange',
    label: '搜索时间',
    value: recentSevenDay,
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  payway: {
    type: 'select',
    value: '',
    label: '充值方式',
    dataList: [],
    clearable: true
  }
}

// 部门消费汇总
export const DEPARTMENTAL_CONSUMPTION_SUMMARY = {
  select_time: {
    labelWidth: '110px',
    type: 'daterange',
    label: '消费时间',
    value: beforeRecentSevenDay,
    pickerOptions: berforePickerOptions,
    clearable: false
  },
  // name: {
  //   type: 'input',
  //   value: '',
  //   label: '姓名',
  //   placeholder: '请输入姓名'
  // },
  // phone: {
  //   type: 'input',
  //   value: '',
  //   label: '手机',
  //   placeholder: '请输入手机'
  // },
  // person_no: {
  //   type: 'input',
  //   value: '',
  //   label: '人员编号',
  //   placeholder: '请输入人员编号'
  // },
  // payer_group_ids: {
  //   type: 'select',
  //   label: '分组',
  //   value: [],
  //   placeholder: '请选择分组',
  //   dataList: [],
  //   listNameKey: 'group_name',
  //   listValueKey: 'id',
  //   multiple: true,
  //   collapseTags: true
  // },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}
// 部门消费汇总
export const PERSONAL_CONSUMPTION_SUMMARY = {
  select_time: {
    labelWidth: '110px',
    type: 'daterange',
    label: '消费时间',
    value: nowRecentSevenDay,
    pickerOptions: nowPickerOptions,
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机',
    placeholder: '请输入手机'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

// 部门消费汇总
export const ACCOUNT_WALLET_DAILY = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: recentSevenDay,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

export const PersonalWalletDailySearchForm = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: beforeRecentSevenDay,
    pickerOptions: berforePickerOptions,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    maxlength: 20,
    placeholder: '请输入姓名'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    multiple: true,
    checkStrictly: true,
    flat: false,
    label: '部门',
    value: [],
    placeholder: '请选择部门',
    dataList: [],
    limit: 1,
    level: 1,
    clearable: true
    // normalizer: this.departmentNode
  }
}
export const CollectionlCodeReportSearchForm = {
  select_time: {
    type: 'daterange',
    label: '搜索日期',
    value: recentSevenDay,
    clearable: false
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}
// 第三方对账表
export const THIRD_RECONCILIATION = {
  select_time: {
    type: 'daterange',
    label: '时间',
    value: recentSevenDay
  },
  order_type: {
    type: 'select',
    value: '',
    label: '订单类型',
    dataList: ORDER_TYPE,
    clearable: true
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '总单号',
    placeholder: '请输入总单号',
    clearable: true
  },
  third_name: {
    type: 'input',
    value: '',
    label: '第三方名称',
    placeholder: '请输入第三方名称',
    clearable: true
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入第三方订单号',
    clearable: true
  }
  // name: {
  //   type: 'input',
  //   value: '',
  //   label: '姓名',
  //   placeholder: '请输入姓名',
  //   clearable: true
  // },
  // phone: {
  //   type: 'input',
  //   value: '',
  //   label: '手机号',
  //   placeholder: '请输入手机号',
  //   clearable: true
  // }
}
export const DeductionServiceReportSetting = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '预约时间',
        value: 'reservation_date'
      }
    ]
  },
  select_date: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  payway: {
    type: 'select',
    listNameKey: 'name',
    listValueKey: 'payway',
    value: [],
    label: '支付渠道',
    dataList: [],
    clearable: true,
    multiple: true,
    collapseTags: true
  },
  take_type: {
    type: 'select',
    value: '',
    label: '就餐方式',
    dataList: [
      {
        label: '到店就餐',
        value: 'instore'
      },
      {
        label: '预约-堂食',
        value: 'on_scene'
      },
      {
        label: '预约-食堂自取',
        value: 'bale'
      },
      {
        label: '预约-取餐柜取餐',
        value: 'cupboard'
      },
      {
        label: '预约-外卖',
        value: 'waimai'
      },
      {
        label: '报餐-堂食',
        value: 'report_on_scene'
      },
      {
        label: '报餐-堂食自提',
        value: 'report_bale'
      }
    ],
    clearable: true
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '涉及订单号',
    labelWidth: '100px',
    placeholder: '请输入涉及订单号',
    clearable: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  meal_time: {
    type: 'select',
    value: null,
    label: '餐段',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      ...MEAL_TYPES
    ]
  }
}
export const RechargeServiceReportSetting = {
  date_type: {
    type: 'select',
    value: 'create_time',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      }
    ]
  },
  select_date: {
    type: 'daterange',
    value: recentSevenDay,
    format: 'yyyy-MM-dd',
    clearable: false
  },
  payway: {
    type: 'select',
    listNameKey: 'name',
    listValueKey: 'payway',
    value: [],
    label: '支付渠道',
    dataList: [],
    clearable: true,
    multiple: true,
    collapseTags: true
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '涉及订单号',
    labelWidth: '100px',
    placeholder: '请输入涉及订单号',
    clearable: true
  }
}

export const RECEIPTSTATUS = [
  { label: '全部', value: '' },
  { label: '已开票', value: 'success' },
  { label: '未开票', value: 'non_open' },
  { label: '开票失败', value: 'fail' },
  { label: '红冲', value: 'red' }
]

// 开票记录
export const RECEIPT_LIST_SEARCH = {
  date_type: {
    type: 'select',
    value: 2,
    maxWidth: '130px',
    dataList: [
      {
        label: '创建时间',
        value: 1
      },
      {
        label: '申请时间',
        value: 2
      }
    ]
  },
  select_time: {
    type: 'daterange',
    label: '',
    clearable: true,
    value: recentSevenDay
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '总单号/订单号',
    placeholder: ''
  },
  invoice_status: {
    type: 'select',
    label: '开票状态',
    value: '',
    placeholder: '',
    dataList: RECEIPTSTATUS
  },
  organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  name: {
    type: 'input',
    value: '',
    label: '开票人'
  }
}

// 优惠券明细表
export const COUPON_DETAILS = {
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入订单号'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    dataList: [],
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  coupon_type_list: {
    type: 'select',
    value: [],
    label: '券类型',
    placeholder: '请选择券类型',
    multiple: true,
    collapseTags: true,
    dataList: [
      {
        label: '满减券',
        value: 'FULL_DISCOUNT'
      },
      {
        label: '代金券',
        value: 'INSTANT_DISCOUNT'
      },
      {
        label: '折扣券',
        value: 'DISCOUNT'
      },
      {
        label: '兑换券',
        value: 'EXCHANGE'
      }
    ]
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '所属组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    placeholder: '请选择',
    collapseTags: true,
    dataList: MEAL_TYPES
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  }
}

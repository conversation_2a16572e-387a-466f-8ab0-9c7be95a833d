<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <search-form
        ref="searchRef"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="gotoExport">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :index="indexMethod"
            header-row-class-name="ps-table-header-row"
          />
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { to, deepClone } from '@/utils'
import { DeductionServiceReportSetting } from '../constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'ConsumeService',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '创建时间', key: 'create_time' },
        { label: '支付时间', key: 'pay_time' },
        { label: '预约时间', key: 'reservation_time' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '涉及订单号', key: 'trade_no' },
        { label: '支付渠道', key: 'payway_verbose' },
        { label: '扣款钱包', key: 'sub_payway_verbose' },
        { label: '所属组织', key: 'org_name_verbose' },
        { label: '就餐方式', key: 'take_type_verbose' },
        { label: '餐段', key: 'meal_type_verbose' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      total: 0,
      searchFormSetting: deepClone(DeductionServiceReportSetting),
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'ConsumeService'
    }
  },
  mounted() {
    this.initLoad()
    this.getCompanyPayinfo()
  },
  methods: {
    initLoad() {
      this.initPrintSetting()
      this.getCommissionChargeList()
    },
    searchHandle() {
      this.currentPage = 1
      this.getCommissionChargeList()
    },
    // 获取列表数据
    async getCommissionChargeList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundReportCenterDataReportCommissionChargeListPost({
          type: 'payment',
          page: this.currentPage,
          page_size: this.pageSize,
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.total = res.data.count
        this.totalCount = res.data.count
        this.tableData = res.data.result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 支付配置
    async getCompanyPayinfo() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminPayInfoGetCompanyPayinfoPost({
          company_id: this.$store.getters.userInfo.company_id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.payway.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getCommissionChargeList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getCommissionChargeList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'ExportCommissionChargeList',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          type: 'payment',
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 导出 列表
    handleExport() {
      this.$confirm(`确定导出？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true
      })
        .then(e => {
          this.$router.push({
            name: 'Excel',
            query: {
            // type: 'payment',
              params: {
                ...this.formatQueryParams(this.searchFormSetting),
                type: 'payment',
                page: this.currentPage,
                page_size: this.pageSize
              }
            }
          })
        })
        .catch(e => {})
    },
    gotoPrint() {
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: '扣款手续费',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportCommissionChargeListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...this.formatQueryParams(this.searchFormSetting),
            type: 'payment',
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>
<style lang="scss" scoped></style>

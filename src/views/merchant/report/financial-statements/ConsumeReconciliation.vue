<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_order.finance_report.reconciliation_list_export']">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
           />
           <!-- show-summary
            :summary-method="getSummaries" -->
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="page"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="total"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { recentSevenDay } from './constantsConfig'
import { getRequestParams, divide, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'ConsumeReconciliation',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '消费总额', key: 'consume', type: 'money' },
        { label: '消费笔数', key: 'consume_count' },
        { label: '朴食钱包消费', key: 'pushi_consume', hidden: true, type: 'money' },
        { label: '朴食钱包退款', key: 'pushi_refund', hidden: true, type: 'money' },
        { label: '电子账户消费', key: 'e_wallet_consume', hidden: true, type: 'money' },
        { label: '电子账户退款', key: 'e_wallet_refund', hidden: true, type: 'money' },
        { label: '补贴钱包消费', key: 'subsidy_consume', hidden: true, type: 'money' },
        { label: '补贴钱包退款', key: 'subsidy_refund', hidden: true, type: 'money' },
        { label: '第三方钱包消费', key: 'third_consume', hidden: true, type: 'money' },
        { label: '第三方钱包退款', key: 'third_refund', hidden: true, type: 'money' },
        { label: '赠送钱包消费', key: 'complimentary_consume', hidden: true, type: 'money' },
        { label: '赠送钱包退款', key: 'complimentary_refund', hidden: true, type: 'money' },
        { label: '收款码支付消费', key: 'qr_consume', type: 'money' },
        { label: '收款码支付退款', key: 'qr_refund', type: 'money' },
        { label: '移动支付消费', key: 'mobile_consume', type: 'money' },
        { label: '餐补金额', key: 'food_subsidy_fee', type: 'money' },
        { label: '移动支付退款', key: 'mobile_refund', type: 'money' },
        { label: '优惠金额', key: 'discounts', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '计次消费', key: 'jc_consume' }
      ],
      tableData: [],
      collectList: {},
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '搜索时间',
          value: recentSevenDay,
          clearable: false
        },
        org_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        }
      },
      showWalletList: {
        store_wallet: ['pushi_consume', 'pushi_refund'],
        electronic_wallet: ['e_wallet_consume', 'e_wallet_refund'],
        subsidy_wallet: ['subsidy_consume', 'subsidy_refund'],
        complimentary_wallet: ['complimentary_consume', 'complimentary_refund'],
        other_wallet: ['third_consume', 'third_refund']
      },
      collect: [ // 统计
        { key: 'text', value: '消费按实际支付时间统计，退款按实际退款时间统计。', label: '' }
      ],
      printType: 'ConsumeReconciliation'
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.getLevelNameList()
      await this.getOrgWallet()
      this.requestReconciliationList()
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.page = 1;
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.page = 1;
      this.requestReconciliationList()
    }, 300),

    async requestReconciliationList() {
      const params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportReconciliationStatementList(
        params
      )
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        // let priceKey = ['complimentary_consume', 'complimentary_refund', 'consume', 'discounts', 'e_wallet_consume', 'e_wallet_refund', 'mobile_consume', 'pushi_consume', 'pushi_refund', 'qr_consume', 'subsidy_consume', 'subsidy_refund', 'third_consume', 'third_refund']
        // this.tableData = res.data.result.map(item => {
        //   priceKey.forEach(v => {
        //     if (item[v] !== '') {
        //       item[v] = divide(item[v])
        //     }
        //   })
        //   return item
        // })
        // for (let k in res.data.collect_list) {
        //   if (priceKey.includes(k)) {
        //     res.data.collect_list[k] = divide(res.data.collect_list[k])
        //   }
        // }
        // 设置合计的值
        this.setSummaryData(res)
        // this.collectList = res.data.collect
      } else {
        this.$message.error(res.msg)
      }
    },
    // 表格合计
    getSummaries(param) {
      const { columns, data } = param
      const list = ['consume_count', 'complimentary_consume', 'complimentary_refund', 'consume', 'discounts', 'e_wallet_consume', 'e_wallet_refund', 'mobile_consume', 'pushi_consume', 'pushi_refund', 'qr_consume', 'subsidy_consume', 'subsidy_refund', 'third_consume', 'third_refund', 'jc_consume']
      let sums = []
      if (data.length) {
        columns.forEach((item, index) => {
          if (index === 0) {
            sums[index] = '合计'
          } else if (list.includes(item.property) && this.collectList) {
            if (item.property.indexOf('_count') > 0) {
              sums[index] = this.collectList[item.property] ? this.collectList[item.property] : 0
            } else {
              sums[index] = this.collectList[item.property] ? divide(this.collectList[item.property]) : 0
            }
          } else {
            sums[index] = ''
          }
        })
      }
      return sums
    },
    // 获取组织下的钱包
    async getOrgWallet() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetOrgWalletPost()
      if (res.code === 0) {
        res.data.wallet.map(v => {
          this.tableSetting.forEach(item => {
            if (this.showWalletList[v] && this.showWalletList[v].includes(item.key)) {
              item.hidden = false
            }
          })
        })
        // this.currentTableSetting = this.tableSetting
        this.initPrintSetting()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.page = val.current
      this.pageSize = val.pageSize
      this.requestReconciliationList()
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      // this.currentTableSetting = this.tableSetting
      let result = res.data.map(v => {
        return {
          label: v.name,
          key: v.level
        }
      })
      this.tableSetting.unshift(...result)
      this.initPrintSetting()
    },
    gotoExport() {
      const option = {
        type: "ConsumeReconciliation",
        params: getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = getRequestParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_type: this.printType,
          print_title: '消费点对账表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportReconciliationStatementList', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: true, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table {
  text-align: center;
  font-size: 12px;
}
</style>

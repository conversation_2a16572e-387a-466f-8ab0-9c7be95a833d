<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
      ></search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport">导出Excel</el-button>
            <el-button size="mini" @click="gotoPrint">打印</el-button>
            <el-button size="mini" @click="openPrintSetting">报表设置</el-button>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :index="indexMethod"
            header-row-class-name="ps-table-header-row"
          />
          <!-- table content end -->
        </div>
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="total"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { DeviceCostForm } from './constantsConfig'
import { debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
export default {
  name: 'DeviceCost',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '设备类型', key: 'device_type_alias' },
        { label: '设备名称', key: 'device_name' },
        { label: '消费总金额', key: 'consume', type: 'money' },
        { label: '退款总金额', key: 'refund_fee', type: 'money' },
        { label: '实收总金额', key: 'real_fee', type: 'money' },
        { label: '消费笔数', key: 'consume_count' }
      ],
      tableData: [],
      currentPage: 1,
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: DeviceCostForm,
      collect: [
        // 统计
        {
          key: 'text',
          value: '消费总金额-退款总金额=实收总金额，消费笔数没有减去退款的订单笔数。',
          label: ''
        }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'DeviceCost'
    }
  },
  watch: {
    'searchFormSetting.add_food_subsidy_fee.value': {
      handler: function(val) {
        if (!val) {
          this.collect = [{
            key: 'text',
            value:
              '消费总金额-退款总金额=实收总金额，消费笔数没有减去退款的订单笔数。',
            label: ''
          }]
        } else {
          this.collect = [{
            key: 'text1',
            value:
              '消费总金额=所有支付成功订单实付金额合计+所有支付成功订单餐补金额合计。',
            label: ''
          }, {
            key: 'text2',
            value:
              '退款总金额=操作类型为“部分退款”订单动账金额合计+操作类型为“退款”订单动账金额合计+操作类型为“退款”对应原支付订单的餐补金额合计。',
            label: ''
          }]
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getLevelNameList()
    this.requestDeviceType()
    this.getDeviceSelectList()
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.requestDeviceConsumeList()
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.requestDeviceConsumeList()
    }, 300),
    // 获取设备名称
    async getDeviceSelectList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportDeviceSelectListPost({
        org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.device_ids.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async requestDeviceType() {
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceTypePost({})
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表数据
    async requestDeviceConsumeList() {
      this.isLoading = true
      // params
      const res = await this.$apis.apiBackgroundReportCenterDataReportDeviceConsumeListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        // 统计
        this.setCollectData(res)
        this.setSummaryData(res)
      } else {
        this.$message.error(res.msg)
      }
    },

    // 翻页
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.requestDeviceConsumeList()
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      // this.currentTableSetting = this.tableSetting
      let result = res.data.map(v => {
        return {
          label: v.name,
          key: v.level
        }
      })
      this.tableSetting.unshift(...result)
      this.initPrintSetting()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'ExportDeviceCost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      console.log(option)
      this.exportHandle(option)
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    gotoPrint() {
      // const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_type: this.printType,
          print_title: '设备消费明细',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportDeviceConsumeListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          push_summary: true, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...this.formatQueryParams(this.searchFormSetting),
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px;
}
</style>

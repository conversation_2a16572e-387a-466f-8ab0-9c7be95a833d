<template>
  <div class="CardSubsidyDetail container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">补贴规则</div>
      </div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        inline
        ref="ruleForm"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-form-item label="补贴名称" prop="name">
          <el-input v-model="ruleForm.name" class="ps-input" style="width:300px;" :disabled="subsidyDisabled"></el-input>
        </el-form-item>
        <el-form-item label="领取方式">
          <el-radio-group class="ps-radio" v-model="ruleForm.receive_way" @change="changeReceiveWay" :disabled="subsidyDisabled">
            <el-radio label="AUTO">自动</el-radio>
            <el-radio label="MANUAL">手动</el-radio>
            <el-radio label="TOPUP">充值领取</el-radio>
          </el-radio-group>
          <el-form-item label="未领取是否失效" label-width="150px" v-if="ruleForm.receive_way === 'MANUAL' || ruleForm.receive_way === 'TOPUP'">
            <el-radio-group class="ps-radio" v-model="ruleForm.unreceived_effective" @change="changeUnreceivedEffective" :disabled="subsidyDisabled">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form-item>
        <el-form-item label="补贴金额">
          <el-radio-group class="ps-radio" v-model="ruleForm.subsidy_money_type" @change="changeMoneyType" :disabled="subsidyDisabled">
            <el-radio label="batch">批量金额</el-radio>
            <el-radio label="fixed" style="margin-right:30px;">固定金额</el-radio>
          </el-radio-group>
          <el-form-item label="" prop="money" v-if="ruleForm.subsidy_money_type == 'fixed'">
            请输入补贴金额
            <el-input v-model="ruleForm.money" class="ps-input" style="width:150px;" :disabled="subsidyDisabled"></el-input>
            <i>元</i>
          </el-form-item>
        </el-form-item>
        <el-form-item label="充值金额" prop="top_up_money" v-if="ruleForm.receive_way === 'TOPUP'">
          <el-input v-model="ruleForm.top_up_money" class="ps-input" style="width:150px;" :disabled="subsidyDisabled"></el-input>
          <i>元</i>
        </el-form-item>
        <el-form-item label="充值金额是否退还" label-width="150px" v-if="ruleForm.receive_way === 'TOPUP'">
          <el-radio-group class="ps-radio" v-model="ruleForm.is_top_up_money_refund" :disabled="subsidyDisabled">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="用户可领取的金额+补贴钱包余额需小于等于" label-width="330px" prop="max_subsidy_balance">
            <el-input v-model="ruleForm.max_subsidy_balance" class="ps-input" style="width:150px;" :disabled="subsidyDisabled"></el-input>
            <i>元</i>
          </el-form-item>
        <div>
          <el-form-item label="发放规则" prop="subsidy_type">
            <el-radio-group v-model="ruleForm.subsidy_type" class="ps-radio" :disabled="subsidyDisabled">
              <el-radio label="MONTH_RELEASE" style="margin-right:70px;">每月自动发放</el-radio>
              <el-radio label="WEEK_RELEASE" style="margin-right:70px;">每周自动发放</el-radio>
              <el-radio label="DAY_RELEASE" style="margin-right:70px;">每日自动发放</el-radio>
              <el-radio label="ONE_RELEASE">仅本次发放</el-radio>
            </el-radio-group>
            <div style="display: flex;" v-if="ruleForm.subsidy_type == 'MONTH_RELEASE'">
              <el-form-item label="每月发放时间" prop="release_day">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.release_day"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option
                    v-for="item in ruleFormDateOpts.maxdatedata"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="每月清零" prop="is_refresh" style="padding-right:30px;">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.is_refresh"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option label="是" v-if="ruleForm.receive_way==='AUTO'||ruleForm.unreceived_effective" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="ruleForm.is_refresh" label="每月清零时间" prop="clear_day">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.clear_day"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option
                    v-for="item in ruleFormDateOpts.maxdatedata"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display: flex;" v-if="ruleForm.subsidy_type == 'WEEK_RELEASE'">
              <el-form-item label="每周发放时间" prop="release_day">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.release_day"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option
                    v-for="item in ruleFormDateOpts.weekmaxdatedata"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="每周清零" prop="is_refresh" style="padding-right:30px;">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.is_refresh"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option label="是" v-if="ruleForm.receive_way==='AUTO'||ruleForm.unreceived_effective" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="ruleForm.is_refresh" label="每周清零时间" prop="clear_day">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.clear_day"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option
                    v-for="item in ruleFormDateOpts.weekmaxdatedata"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display: flex;" v-show="ruleForm.subsidy_type == 'DAY_RELEASE'">
              <el-form-item label="">
                <el-checkbox v-model="ruleForm.saturday" class="ps-checkbox" :disabled="subsidyDisabled">跳过星期六</el-checkbox>
                <el-checkbox v-model="ruleForm.sunday" class="ps-checkbox" :disabled="subsidyDisabled">跳过星期日</el-checkbox>
                <el-checkbox v-model="ruleForm.holiday" class="ps-checkbox" :disabled="subsidyDisabled">跳过节假日</el-checkbox>
              </el-form-item>
              <el-form-item label="是否清零" prop="is_refresh">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.is_refresh"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option label="是" v-if="ruleForm.receive_way==='AUTO'||ruleForm.unreceived_effective" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div v-if="ruleForm.subsidy_type == 'ONE_RELEASE'" style=" display: flex;">
              <el-form-item label="是否清零" prop="is_refresh">
                <el-select
                  class="ps-select"
                  popper-class="ps-popper-select"
                  v-model="ruleForm.is_refresh"
                  placeholder="请选择"
                  style="width:110px;"
                  :disabled="subsidyDisabled"
                >
                  <el-option label="是" v-if="ruleForm.receive_way==='AUTO'||ruleForm.unreceived_effective" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="ruleForm.is_refresh" label="清零时间" prop="clear_time">
                <!-- <DateSelect @dateChange="dateChange" :type="ruleForm.subsidy_type" /> -->
                <el-date-picker
                  v-model="ruleForm.clear_time"
                  type="date"
                  placeholder="选择日期时间"
                  value-format="yyyy-MM-dd"
                  :picker-options="pickerOptions"
                  :disabled="subsidyDisabled">
                </el-date-picker>
              </el-form-item>
            </div>
          </el-form-item>
        </div>
        <div class="tips">
          注：<br/>
          1.  "用户领取的金额+补贴钱包余额小于等于"—不填则代表无限制。<br/>
          2.  清零会导致“未领取”的补贴失效<br/>
        </div>
      </el-form>
    </div>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="chooseData" :show="ruleForm.subsidy_money_type=='batch'?false:true">选择补贴人员</button-icon>
          <button-icon color="plain" type="Import" @click="showDialog = true" >导入补贴</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          ref="allMultipleTable"
          :data="selectSubsidyData.currentPageData"
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="name" label="名称" align="center"></el-table-column>
          <el-table-column prop="card_department_group_alias" label="部门" align="center" ></el-table-column>
          <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
          <el-table-column
            v-if="ruleForm.subsidy_money_type !== 'fixed'"
            prop="subsidy_balance"
            label="补贴余额"
            align="center"
          ></el-table-column>
          <el-table-column label="操作" width="160" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="delete-txt-btn"
                :disabled="scope.row.subsidyDisabled"
                @click="deleteMergedata(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="selhandleSizeChange"
          @current-change="selhandleCurrentChange"
          :current-page="selectSubsidyData.currentPage"
          :page-sizes="[5, 10, 15, 20]"
          :page-size="selectSubsidyData.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="selectSubsidyData.totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <div  class="table-wrapper" style="margin-top:20px;text-align:right;padding:20px;">
      <el-button class="ps-cancel-btn" @click="cancelClick">取消</el-button>
      <el-button v-if="this.ruleForm.last_release_time || !subsidyDisabled " class="ps-origin-btn" type="primary" @click="submitForm(true)">
        立即发放
      </el-button>
      <el-button type="primary" @click="submitForm(false)" class="ps-plain-btn ps-origin" style="border-color:#ff9b45;">
        {{ ruleForm.subsidy_type == 'ONE_RELEASE' ? '保存不发放' : '到期发放' }}
      </el-button>
    </div>
    <import-dialog
      :templateUrl="templateUrl"
      :tableSetting="tableSetting"
      :show.sync="showDialog"
      :title="'导入补贴'"
      importType="data"
      @confirm="confirmImportData"
    ></import-dialog>
    <!-- 选择人员弹窗 -->
    <el-dialog
      title="选择补贴人员"
      :visible.sync="memberDialog"
      width="700px"
      top="15vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <el-form ref="cardruleForm" label-width="90px" class="demo-ruleForm">
          <div style="display:flex;">
            <el-form-item label="部门">
              <el-cascader
                v-model="memberOpts.selectGroup"
                :options="memberOpts.departmentList"
                :props="groupOpts"
                clearable
                style="width:180px;"
                @change="changeGroupHandle"
                collapse-tags>
              </el-cascader>
            </el-form-item>
            <el-form-item label="人员编号">
              <el-input
                class="ps-input"
                @change="changePersonNo"
                v-model="memberOpts.personNo"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
            <div style="margin-left: 20px;">
              <el-button type="primary" @click="changeGroupHandle">搜索</el-button>
            </div>
          </div>
        </el-form>
        <el-table
          ref="selectTultipleTable"
          :data="memberOpts.tableData"
          tooltip-effect="dark"
          header-row-class-name="table-header-row"
          :row-class-name="tableRowClassName"
          v-loading="isLoading"
          @selection-change="handleSelectionChange"
          @select-all="selectAlllist"
          @select="selectDataList"
        >
          <el-table-column class-name="ps-checkbox" type="selection" width="37"></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="name" label="名称" align="center"></el-table-column>
          <el-table-column prop="card_department_group_alias" label="部门" align="center" ></el-table-column>
          <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
          <el-table-column prop="subsidy_balance" label="补贴余额" align="center"></el-table-column>
        </el-table>
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="memberSizeChange"
            @current-change="memberCurrentChange"
            :current-page="memberOpts.currentPage"
            :page-sizes="[5, 10, 15, 20]"
            :page-size="memberOpts.pageSize"
            layout="total, prev, pager, next"
            :total="memberOpts.totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" type="primary" @click="memberDialog = false">
          {{ $t('dialog.cancel_btn') }}
        </el-button>
        <el-button class="ps-btn" type="primary" @click="submitMemberDialog">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { divide, times } from '@/utils'
// import DateSelect from '../../../components/DateSelect/DateSelect.vue'
import { mapActions } from 'vuex'

export default {
  name: 'CardSubsidyDetail',
  components: {
    // DateSelect
  },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    var validClearTime = (rule, value, callback) => {
      if (this.ruleForm.subsidy_type === 'ONE_RELEASE' && this.ruleForm.is_refresh) {
        this.ruleForm.release_day = ''
        this.ruleForm.clear_day = ''
        if (value === '') {
          return callback(new Error('请选择日期！'))
        }
        callback()
      } else {
        callback()
      }
    }
    var validReleaseDay = (rule, value, callback) => {
      if (this.ruleForm.subsidy_type !== 'ONE_RELEASE') {
        // this.ruleForm.clear_time = ''
        if (value === '') {
          return callback(new Error('请选择发放日期！'))
        } else {
          // let reg = /^\d+$/;
          let reg = /^(0|[1-9][0-9]*|-[1-9][0-9]*)$/
          if (!reg.test(value)) {
            return callback(new Error('请输入正确的日期格式！'))
          }
          if (value > 28) {
            return callback(new Error('日期不能大于28'))
          }
          callback()
        }
      } else {
        callback()
      }
    }
    var validClearDayl = (rule, value, callback) => {
      if (this.ruleForm.subsidy_type !== 'ONE_RELEASE' && this.ruleForm.is_refresh) {
        if (value === '') {
          callback(new Error('请选择清零日期！'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let validMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        let reg = /^(([1]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      }
    }
    let validMaxMoney = (rule, value, callback) => {
      let reg = /^(([1]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      if (value && !reg.test(value)) {
        callback(new Error('金额格式有误'))
      } else {
        callback()
      }
    }
    let validRechargeMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        let reg = /^(([1]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          if (value > 9999) {
            return callback(new Error('最大充值金额为9999'))
          } else {
            callback()
          }
        }
      }
    }
    return {
      isLoading: false, // 刷新数据
      selectSubsidyData: {
        memberseldata: [], // 选择的人员数据
        importseldata: [], // 导入的人员数据
        mergedata: [], // 合并memberseldata和importseldata的数据，需去重，以memberseldata数据为准
        pageSize: 5, // 每页数量
        totalCount: 0, // 总条数
        currentPage: 1, // 第几页
        currentPageData: []
      },
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      ruleForm: {
        name: '',
        receive_way: 'AUTO',
        unreceived_effective: 1,
        subsidy_money_type: 'fixed',
        money: '',
        max_subsidy_balance: '',
        subsidy_type: 'MONTH_RELEASE', // 补贴类型
        is_refresh: '', // 是否清零
        now_release: '', // 立刻发放
        card_user_id: [], // 发放补贴的用户
        clear_time: '', // 清零时间，按次发选填
        release_day: '', // 发放日，按月选填
        clear_day: '', // 清零日，按月选填
        saturday: false,
        sunday: false,
        holiday: false,
        last_release_time: null,
        top_up_money: '', // 充值金额
        is_top_up_money_refund: 1 //
      },
      ruleFormDateOpts: {
        maxdatedata: [], // 月的天数
        weekmaxdatedata: [] // 周的天数
      },
      rules: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
        money: [{ required: true, validator: validMoney, trigger: 'blur' }],
        subsidy_type: [{ required: true, message: '请选择发放规则', trigger: 'blur' }],
        max_subsidy_balance: [{ validator: validMaxMoney, trigger: 'blur' }],
        is_refresh: [{ required: true, message: '请选择', trigger: 'blur' }],
        clear_time: [{ required: true, validator: validClearTime, trigger: 'blur' }],
        release_day: [{ required: true, validator: validReleaseDay, trigger: 'blur' }],
        clear_day: [{ required: true, validator: validClearDayl, trigger: 'blur' }],
        top_up_money: [{ required: true, validator: validRechargeMoney, trigger: 'blur' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        }
      },
      templateUrl:
        'https://cashier-v4.debug.packertec.com/api/temporary/template_excel/卡务模板/导入补贴.xls',
      tableSetting: [
        { key: 'person_no', label: '人员编号' },
        { key: 'name', label: '名称' },
        { key: 'card_department_group_alias', label: '部门' },
        { key: 'card_no', label: '卡号' },
        { key: 'subsidy_balance', label: '补贴余额' }
      ],
      showDialog: false,
      // 选择人员弹窗相关
      memberDialog: false,
      // 搜索分组配置
      groupOpts: {
        value: 'id',
        label: 'group_name',
        children: 'children_list',
        checkStrictly: true
      },
      memberOpts: {
        tableData: [],
        isall: false,
        personNo: '',
        selectGroup: [],
        departmentList: [],
        pageSize: 5, // 每页数量
        totalCount: 0, // 总条数
        currentPage: 1, // 第几页
        selectData: [], // 当前表格选中的数据
        allSelectData: [], // 所有选中的数据，包括导入到selectSubsidyData中的
        selkeyvaldata: {}
      },
      subsidyId: '',
      subsidyType: '',
      subsidyDisabled: false
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    $route: {
      handler: function(route) {
        if (this.$route.query.id) {
          this.subsidyId = this.$route.query.id
          this.getSubsidyDetail()
        }
        if (this.$route.query.type) {
          this.subsidyType = this.$route.query.type
        }
        if (this.$route.query.status === 'STARTING') {
          this.subsidyDisabled = true
        } else {
          this.subsidyDisabled = false
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    ...mapActions({
      _groupList: 'groupList',
      _addCardSubsidy: 'addCardSubsidy'
    }),
    initLoad() {
      this.selectDatePicker()
      this.selectWeekDatePicker()
    },
    async getSubsidyDetail() {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardSubsidyGetCardSubsidyPost({
        id: this.subsidyId
      })
      this.isLoading = false
      if (res.code === 0) {
        this.ruleForm.name = res.data.name
        this.ruleForm.receive_way = res.data.receive_way
        this.ruleForm.unreceived_effective = res.data.unreceived_effective
        this.ruleForm.subsidy_money_type = res.data.money ? 'fixed' : 'batch'
        this.ruleForm.money = divide(res.data.money)
        this.ruleForm.max_subsidy_balance = this.ruleForm.max_subsidy_balance ? divide(res.data.max_subsidy_balance) : ''
        this.ruleForm.subsidy_type = res.data.subsidy_type
        this.ruleForm.is_refresh = res.data.is_refresh
        this.ruleForm.clear_time = res.data.clear_time
        this.ruleForm.release_day = res.data.release_day
        this.ruleForm.clear_day = res.data.clear_day
        this.ruleForm.saturday = res.data.is_pass_saturday
        this.ruleForm.sunday = res.data.is_pass_sunday
        this.ruleForm.holiday = res.data.is_pass_holiday
        this.ruleForm.last_release_time = res.data.last_release_time
        this.ruleForm.top_up_money = divide(res.data.top_up_money)
        this.ruleForm.is_top_up_money_refund = res.data.is_top_up_money_refund
        res.data.card_user_list.map(item => {
          item.subsidyDisabled = this.subsidyDisabled
        })
        this.selectSubsidyData.mergedata = res.data.card_user_list
        this.memberOpts.allSelectData = res.data.card_user_list
        this.selectSubsidyData.memberseldata = res.data.card_user_list
        this.setSelCurrentPageData()
      } else {
        this.$message.error(res.msg)
      }
    },
    confirmImportData(data) {
      if (this.selectSubsidyData.importseldata.length) {
        this.selectSubsidyData.importseldata = this.selectSubsidyData.importseldata.concat(
          data.allData
        )
      } else {
        this.selectSubsidyData.importseldata = data.allData
      }
      this.mergeUniqueData()
      this.showDialog = false
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    dateChange(data) {
      this.ruleForm.clear_time = data
    },
    selectDatePicker() {
      for (let i = 0; i < 28; i++) {
        this.ruleFormDateOpts.maxdatedata.push({
          id: i + 1,
          label: i + 1 + '日'
        })
      }
      this.ruleFormDateOpts.maxdatedata.push({
        id: -1,
        label: '每月最后一日'
      })
    },
    selectWeekDatePicker() {
      let week = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
      week.map((item, index) => {
        this.ruleFormDateOpts.weekmaxdatedata.push({
          id: index + 1,
          label: item
        })
      })
    },

    // 选择人员弹窗相关：
    chooseData() {
      this.memberDialog = true
      this.getCardUserList()
      this.getDepartmentList()
    },
    // 获取人员
    async getCardUserList() {
      this.isLoading = true
      let params = {
        page_size: this.memberOpts.pageSize,
        page: this.memberOpts.currentPage,
        card_department_group_id: this.memberOpts.selectGroup[this.memberOpts.selectGroup.length - 1]
      }
      if (this.memberOpts.personNo) { params.person_no = this.memberOpts.personNo }
      const res = await this.$apis.apiCardServiceCardSubsidyUserListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        res.data.results.map(item => {
          item.subsidy_balance = divide(item.subsidy_balance)
          item.balance = divide(item.balance)
        })
        this.memberOpts.tableData = res.data.results
        this.memberOpts.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
      this.setSelMemberData()
    },
    // 人员弹窗获取部门信息
    async getDepartmentList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardDepartmentGroupTreeListPost()
      this.isLoading = false
      if (res.code === 0) {
        this.memberOpts.departmentList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 设置人员选择table表格的选中状态
    setSelMemberData() {
      this.memberOpts.selkeyvaldata = {}
      this.memberOpts.allSelectData.map(item => {
        this.memberOpts.selkeyvaldata[item.person_no] = item
      })
      let seldata = []
      for (let i = 0; i < this.memberOpts.tableData.length; i++) {
        if (this.memberOpts.selkeyvaldata[this.memberOpts.tableData[i].person_no]) {
          seldata.push(this.memberOpts.tableData[i])
        }
      }
      // 注意。。。
      // 选中状态需要再当前页面获取数据更新显示之后操作
      this.$nextTick(() => {
        this.selectTultipleTable = this.$refs.selectTultipleTable
        if (seldata.length) {
          seldata.forEach(row => {
            this.selectTultipleTable.toggleRowSelection(row)
          })
        }
      })
    },
    changeGroupHandle(list) {
      this.currentPage = 1
      this.getCardUserList()
    },
    changePersonNo() {
      this.currentPage = 1
      this.getCardUserList()
    },
    // memberList 分页
    memberSizeChange(val) {
      this.memberOpts.pageSize = val
      this.getCardUserList()
      this.setSelMemberData()
    },
    memberCurrentChange(val) {
      this.memberOpts.currentPage = val
      this.getCardUserList()
      this.setSelMemberData()
    },
    // 人员选择dialog 多选框选中事件
    handleSelectionChange(val) {
      // 如果选中的数据条数与
      if (
        this.memberOpts.isall ||
        this.memberOpts.selectData.length === this.memberOpts.totalCount
      ) {
        // 全选
        this.memberOpts.isall = true
      } else {
        // 不全选
        this.memberOpts.isall = false
      }
    },
    // 点击全选时触发，通过toggleAllSelection进行全选也会触发当前事件。。。game over
    selectAlllist(row) {
      if (row.length) {
        this.memberOpts.isall = true
        this.memberOpts.selectData = Object.assign([], row) // this.memberOpts.selectData.concat(...row)
        this.uniqueMemberselData(this.memberOpts)
      } else {
        this.memberOpts.isall = false
        this.memberOpts.selectData = []
        this.memberOpts.selkeyvaldata = {}
      }
    },
    // 点击选着按钮都会触发
    selectDataList(val) {
      if (val.length) {
        this.memberOpts.selectData = Object.assign([], val) // this.memberOpts.selectData.concat(...val)
      }
      this.uniqueMemberselData(this.memberOpts)
    },
    uniqueMemberselData(data) {
      let keys = [] // 去重的key
      let result = [] // 去重后的结果
      // 去重前需重置
      data.selkeyvaldata = {}
      for (let i = 0; i < data.selectData.length; i++) {
        if (keys.indexOf(data.selectData[i].person_no) < 0) {
          keys.push(data.selectData[i].person_no)
          result.push(data.selectData[i])
        }
        data.selkeyvaldata[data.selectData[i].person_no] = data.selectData[i]
      }
      this.memberOpts.selectData = this.deepClone(result)
    },
    deepClone(source) {
      if (!source && typeof source !== 'object') {
        throw new Error('error arguments', 'deepClone')
      }
      const targetObj = source.constructor === Array ? [] : {}
      Object.keys(source).forEach(keys => {
        if (source[keys] && typeof source[keys] === 'object') {
          targetObj[keys] = this.deepClone(source[keys])
        } else {
          targetObj[keys] = source[keys]
        }
      })
      return targetObj
    },
    // 弹框确定按钮
    submitMemberDialog() {
      console.log(this.selectSubsidyData.memberseldata)
      this.memberDialog = false
      if (this.selectSubsidyData.memberseldata.length) {
        // 存在数据先做去重操作
        let keys = [] // 已选中的人员的编号，根据当前编号查重
        let result = [] // 最终合并结果
        keys = this.selectSubsidyData.memberseldata.map(item => {
          return item.person_no
        })
        result = result.concat(this.selectSubsidyData.memberseldata)
        this.memberOpts.selectData.map(item => {
          // item.sel_type = this.type // 设置当前数据是导进的类型，方便后面筛选数据
          if (keys.indexOf(item.person_no) < 0) {
            result.push(item)
            this.memberOpts.allSelectData.push(item) // 设置所有的选中的数据
          } else {
            keys.push(item.person_no)
          }
        })
        this.selectSubsidyData.memberseldata = result
      } else {
        // this.memberOpts.selectData.map(item => {
        //   item.sel_type = this.type // 设置当前数据是导进的类型，方便后面筛选数据
        // })
        this.memberOpts.allSelectData = this.memberOpts.selectData
        this.selectSubsidyData.memberseldata = this.memberOpts.selectData
      }
      // 完成后要清空
      this.memberOpts.selectData = []

      // 合并数据显示
      this.mergeUniqueData()
    },
    mergeUniqueData() {
      // 合并数据并去重
      let keys = [] // 已选的人员的编号，根据当前编号查重
      let result = [] // 最终合并结果
      if (this.selectSubsidyData.memberseldata.length) {
        keys = this.selectSubsidyData.memberseldata.map(item => {
          return item.person_no
        })
        result = result.concat(this.selectSubsidyData.memberseldata)
      }
      // this.selectSubsidyData.importseldata
      this.selectSubsidyData.importseldata.map(item => {
        if (keys.indexOf(item.person_no) < 0) {
          result.push(item)
          keys.push(item.person_no)
        }
      })
      this.selectSubsidyData.mergedata = result
      // 分页展示
      this.setSelCurrentPageData()
    },
    // 生成分页数据（总）人员+导入
    setSelCurrentPageData() {
      let start = (this.selectSubsidyData.currentPage - 1) * this.selectSubsidyData.pageSize
      let end =
        (this.selectSubsidyData.currentPage - 1) * this.selectSubsidyData.pageSize +
        this.selectSubsidyData.pageSize
      this.selectSubsidyData.totalCount = this.selectSubsidyData.mergedata.length
        ? this.selectSubsidyData.mergedata.length
        : 0
      this.selectSubsidyData.currentPageData = [].concat(
        this.selectSubsidyData.mergedata.slice(start, end)
      )
    },
    selhandleSizeChange(e) {
      this.selectSubsidyData.pageSize = e
      // 重新生成分页数据
      this.setSelCurrentPageData()
    },
    selhandleCurrentChange(e) {
      // 重新生成分页数据
      this.selectSubsidyData.currentPage = e
      this.setSelCurrentPageData()
    },
    // 删除数据
    deleteMergedata(row) {
      let data = this.selectSubsidyData.mergedata
      let index
      for (let i = 0; i < data.length; i++) {
        if (data[i].person_no === row.person_no) {
          index = i
          delete this.memberOpts.selkeyvaldata[row.person_no]
          this.selectSubsidyData.mergedata.splice(index, 1)
          break
        }
      }

      let data1 = this.selectSubsidyData.memberseldata
      let k
      for (let i = 0; i < data1.length; i++) {
        if (data1[i].person_no === row.person_no) {
          k = i
          delete this.memberOpts.selkeyvaldata[row.person_no]
          this.selectSubsidyData.memberseldata.splice(k, 1)
          break
        }
      }
      let data2 = this.selectSubsidyData.importseldata
      for (let i = 0; i < data2.length; i++) {
        if (data2[i].person_no === row.person_no) {
          this.selectSubsidyData.importseldata.splice(i, 1)
          break
        }
      }
      for (let i = 0; i < this.memberOpts.allSelectData.length; i++) {
        if (this.memberOpts.allSelectData[i].person_no === row.person_no) {
          this.memberOpts.allSelectData.splice(i, 1)
          this.memberOpts.isall = false
          delete this.memberOpts.selkeyvaldata[row.person_no]
          break
        }
      }

      this.checkedCurrentPage(this.selectSubsidyData.mergedata, this.selectSubsidyData)
      this.setSelCurrentPageData()
    },
    // 检查当前页码是否满足分页
    checkedCurrentPage(data, opts) {
      if (opts.currentPage > 1) {
        let lit = data.length % opts.pageSize
        if (lit < 1) {
          opts.currentPage--
        }
      }
    },
    // 取消按钮
    cancelClick() {
      this.$router.push({
        name: 'CardSubsidy'
      })
    },
    // 提交
    submitForm(e) {
      this.ruleForm.now_release = e
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          if (this.ruleForm.subsidy_money_type === 'batch' && this.selectSubsidyData.importseldata.length) {
            if (this.checkImportData(this.selectSubsidyData.mergedata, ['person_no', 'subsidy_balance']).length !== this.selectSubsidyData.mergedata.length) {
              return
            }
          }
          this.ruleForm.card_user_id = this.selectSubsidyData.mergedata.map(v => {
            return {
              person_no: v.person_no,
              subsidy_balance: Number(v.subsidy_balance ? times(v.subsidy_balance) : 0)
            }
          })
          if (this.ruleForm.card_user_id.length) {
            this.sendCardSubsidyData()
          } else {
            this.$message.error('请选择补贴人员')
          }
        } else {
          return false
        }
      })
    },
    // 发送数据
    async sendCardSubsidyData() {
      let params = {
        name: this.ruleForm.name,
        receive_way: this.ruleForm.receive_way,
        unreceived_effective: this.ruleForm.unreceived_effective,
        subsidy_type: this.ruleForm.subsidy_type, // 补贴类型
        now_release: this.ruleForm.now_release, // 立刻发放
        user_list: this.ruleForm.card_user_id, // 发放补贴的用户
        is_refresh: this.ruleForm.is_refresh // 是否清零
      }
      if (this.ruleForm.max_subsidy_balance) {
        params.max_subsidy_balance = Number(times(this.ruleForm.max_subsidy_balance))
      }
      if (this.ruleForm.subsidy_money_type === 'fixed') {
        params.money = Number(times(this.ruleForm.money))
      }
      if (this.ruleForm.is_refresh) {
        if (this.ruleForm.subsidy_type === 'ONE_RELEASE') {
          params.clear_time = this.ruleForm.clear_time + 'T00:00:00Z'
        } else if (this.ruleForm.subsidy_type !== 'DAY_RELEASE') {
          params.clear_day = this.ruleForm.clear_day
        }
      }
      if (this.ruleForm.subsidy_type === 'MONTH_RELEASE') {
        params.release_day = this.ruleForm.release_day
      }
      if (this.ruleForm.subsidy_type === 'WEEK_RELEASE') {
        params.release_day = this.ruleForm.release_day
      }
      if (this.ruleForm.subsidy_type === 'DAY_RELEASE') {
        // 按日发放参数……
        params.is_pass_saturday = this.ruleForm.saturday
        params.is_pass_sunday = this.ruleForm.sunday
        params.is_pass_holiday = this.ruleForm.holiday
      }
      if (this.ruleForm.receive_way === 'TOPUP') {
        params.top_up_money = times(this.ruleForm.top_up_money)
        params.is_top_up_money_refund = this.ruleForm.is_top_up_money_refund
      }
      this.subsidyLoading = true
      let res
      if (this.subsidyType === 'add') {
        res = await this.$apis.apiCardServiceCardSubsidyAddPost(params)
      } else {
        params.id = this.subsidyId
        res = await this.$apis.apiCardServiceCardSubsidyModifyPost(params)
      }
      this.subsidyLoading = false
      if (res.code === 0) {
        this.$message.success('创建补贴成功！')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 校验导入数据
    checkImportData(data, keys) {
      let result = []
      let errResult = {}
      const regNo = /^[a-zA-Z0-9-_]+$/i
      // const reg_money = /^-?(([0-9]*)|(([0]\.\d{1,2}|[0-9]*\.\d{1,2})))$/
      const regMoney = /^([0-9]+[\d]*(.[0-9]{1,2})?)$/
      const regPhone = /^1[3456789]\d{9}$/
      const keyList = {
        person_no: { reg: regNo, label: '人员编号' },
        card_no: { reg: regNo, label: '卡号' },
        phone: { reg: regPhone, label: '手机号' },
        balance: { reg: regMoney, label: '钱包金额' },
        subsidy_balance: { reg: regMoney, label: '补贴余额' }
      }
      if (keys.length > 0) {
        data.map((item, i) => {
          let isPass = true
          keys.map((k, j) => {
            if (item[k] !== undefined) {
              if (!keyList[k].reg.test(item[k])) {
                isPass = false
                // errResult.push(`导入报表第${i+3}行${keyList[k].label}格式错误`)
                if (errResult['第' + (i + 3) + '行']) {
                  errResult['第' + (i + 3) + '行'].push(
                    `导入报表第${i + 3}行${keyList[k].label}格式错误`
                  )
                } else {
                  errResult['第' + (i + 3) + '行'] = []
                  errResult['第' + (i + 3) + '行'].push(
                    `导入报表第${i + 3}行${keyList[k].label}格式错误`
                  )
                }
              } else if (isPass && j + 1 === keys.length) {
                result.push(item)
              }
            } else {
              // isPass && ((j+1)==keys.length)
              // result.push(item)
              if (k === 'person_no') {
                isPass = false
                if (errResult['第' + (i + 3) + '行']) {
                  errResult['第' + (i + 3) + '行'].push(
                    `导入报表第${i + 3}行${keyList[k].label}不能为空`
                  )
                } else {
                  errResult['第' + (i + 3) + '行'] = []
                  errResult['第' + (i + 3) + '行'].push(
                    `导入报表第${i + 3}行${keyList[k].label}不能为空`
                  )
                }
              }
              if (this.ruleForm.subsidy_money_type === 'batch') {
                if (k === 'subsidy_balance') {
                  if (errResult['第' + (i + 3) + '行']) {
                    errResult['第' + (i + 3) + '行'].push(
                      `导入报表第${i + 3}行${keyList[k].label}不能为空`
                    )
                  } else {
                    errResult['第' + (i + 3) + '行'] = []
                    errResult['第' + (i + 3) + '行'].push(
                      `导入报表第${i + 3}行${keyList[k].label}不能为空`
                    )
                  }
                }
              } else if (isPass && j + 1 === keys.length && k === 'subsidy_balance') {
                result.push(item)
              }
            }
          })
        })
      } else {
        result = data
      }
      if (Object.keys(errResult).length) {
        console.error('IMPORT_ERR', errResult)
        // let html = ''
        // for(let k in errResult) {
        //   html += `<p>${errResult[k]}</p>`
        // }
        // this.$message({
        //   dangerouslyUseHTMLString: true,
        //   message: html,
        //   duration: 5000
        // })
        this.$message.error('数据格式错误，请检查人员编号/补贴余额！')
      }
      return result
    },
    changeMoneyType(val) {
      if (val === 'batch' && this.selectSubsidyData.mergedata.length) {
        this.ruleForm.subsidy_money_type = 'fixed'
        this.$confirm(`固定金额切换到批量金额，将清空补贴人员列表，是否继续？`, `提示`, {
          confirmButtonText: this.$t('dialog.confirm_btn'),
          cancelButtonText: this.$t('dialog.cancel_btn'),
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              this.ruleForm.subsidy_money_type = 'batch'
              this.selectSubsidyData.currentPageData = []
              this.selectSubsidyData = {
                memberseldata: [], // 选择的人员数据
                importseldata: [], // 导入的人员数据
                mergedata: [], // 合并memberseldata和importseldata的数据，需去重，以memberseldata数据为准
                pageSize: 5, // 每页数量
                totalCount: 0, // 总条数
                currentPage: 1, // 第几页
                currentPageData: []
              }
              this.memberOpts.allSelectData = []
              done()
              instance.confirmButtonLoading = false
            } else {
              this.ruleForm.subsidy_money_type = 'fixed'
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        })
          .then(e => {
          })
          .catch(e => {})
      }
    },
    changeReceiveWay() {
      if (this.ruleForm.receive_way === 'MANUAL') {
        this.changeUnreceivedEffective()
      }
    },
    // 如选择不失效，则清零仅支持选择“否”
    changeUnreceivedEffective() {
      if (this.ruleForm.unreceived_effective) {
        this.ruleForm.is_refresh = ''
      } else {
        this.ruleForm.is_refresh = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.CardSubsidyDetail {
  .tips{
    margin-left: 40px;
    margin-bottom: 20px;
    color: #ff9b45;
    font-size: 14px;
  }
}
</style>

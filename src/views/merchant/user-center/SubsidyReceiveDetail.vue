<template>
  <div class="SubsidyReceiveDetail container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="handleExport">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column class-name="ps-checkbox" type="selection" width="37"></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="dept_name" label="部门" align="center"></el-table-column>
          <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
          <el-table-column prop="release_money" label="发放金额" align="center"></el-table-column>
          <el-table-column prop="receive_status_alias" label="领取状态" align="center"></el-table-column>
          <el-table-column prop="charge_trade_no" label="充值订单号" align="center"></el-table-column>
          <el-table-column prop="cur_received_money" label="已领取金额" align="center"></el-table-column>
          <el-table-column prop="unreceived_money" label="未领取金额" align="center"></el-table-column>
          <el-table-column prop="used_money_cycle" label="本轮使用补贴金额" align="center"></el-table-column>
          <el-table-column prop="release_time" label="发放时间" align="center"></el-table-column>
          <el-table-column prop="receive_time" label="领取时间" align="center"></el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, divide, getWeek, parseTime } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'SubsidyReceiveDetail',
  components: {},
  props: {},
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      subsidyId: '',
      subsidyType: '',
      organization: '',
      userId: '',
      searchFormSetting: {}
    }
  },
  created() {
    if (this.$route.query.id) {
      this.subsidyId = this.$route.query.id
      this.subsidyType = this.$route.query.type
      this.organization = Number(this.$route.query.organization)
      this.initLoad()
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      let info = {}
      if (this.subsidyType === 'MONTH_RELEASE') {
        info = {
          month: {
            type: 'month',
            label: '发放月份',
            value: parseTime(new Date(), '{y}-{m}'),
            placeholder: '请选择发放月份'
          }
        }
      } else if (this.subsidyType === 'WEEK_RELEASE') {
        info = {
          week: {
            type: 'week',
            label: '发放周',
            value: new Date(),
            placeholder: '请选择发放周',
            pickerOptions: {
              firstDayOfWeek: 1
            }
          }
        }
      } else if (this.subsidyType === 'DAY_RELEASE') {
        info = {
          day: {
            type: 'date',
            label: '发放日期',
            value: parseTime(new Date(), '{y}-{m}-{d}'),
            placeholder: '请选择发放日期'
          }
        }
      }
      this.searchFormSetting = {
        ...info,
        receive_statuses: {
          type: 'select',
          label: '领取状态',
          multiple: true,
          clearable: true,
          collapseTags: true,
          value: [],
          placeholder: '请选择领取状态',
          dataList: [{
            value: 'UNRECEIVED',
            label: '未领取'
          },
          {
            value: 'SUCCESS',
            label: '已领取'
          },
          {
            value: 'INVALID',
            label: '已失效'
          }]
        },
        card_department_group_id: {
          type: 'departmentSelect',
          multiple: false,
          isLazy: false,
          checkStrictly: true,
          label: '部门',
          value: '',
          placeholder: '请选择部门'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        }
      }
      this.getSubsidyReceiveDetail()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getSubsidyReceiveDetail()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        // console.log(key, data[key].value)
        if (data[key].value !== '' && data[key].value !== null) {
          if (key === 'month') {
            params.release_years = [data[key].value.split('-')[0]]
            params.release_months = [data[key].value.split('-')[1]]
          } else if (key === 'week') {
            params.release_years = [parseTime(data[key].value, '{y}')]
            params.release_weeks = [getWeek(data[key].value)]
          } else if (key === 'day') {
            params.release_dates = [data[key].value]
          } else {
            params[key] = data[key].value
          }
        }
      }
      return params
    },
    async getSubsidyReceiveDetail() {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardSubsidyGetUserSubsidyDetailsListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        card_subsidy_id: this.subsidyId,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        res.data.results.map(item => {
          item.release_money = divide(item.release_money)
          item.cur_received_money = divide(item.cur_received_money)
          item.unreceived_money = divide(item.unreceived_money)
          item.used_money_cycle = divide(item.used_money_cycle)
        })
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getSubsidyReceiveDetail()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getSubsidyReceiveDetail()
    },
    handleExport() {
      const option = {
        type: 'ExportSubsidyReceiveDetail',
        url: 'apiCardServiceCardSubsidyGetUserSubsidyDetailsListPost',
        params: {
          page: 1,
          page_size: 9999999,
          ...this.formatQueryParams(this.searchFormSetting),
          card_subsidy_id: this.subsidyId,
          is_export: true
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>

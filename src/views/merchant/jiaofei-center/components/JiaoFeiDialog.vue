<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="jiaofeiForm"
      @submit.native.prevent
      status-icon
      ref="jiaofeiForm"
      :rules="jiaofeiFormRules"
      label-width="80px"
      class="jiaofei-form"
      inline
    >
      <div v-if="type === 'choosePerson'">
        <el-form-item label="部门">
          <user-department-select
            class="w-180 ps-input"
            v-model="jiaofeiForm.department"
            :clearable="true"
            :multiple="true"
            :check-strictly="true"
            :isLazy="false"
            placeholder="请选择部门"
            :append-to-body="true"
            @change="searchHandle"
            >
          </user-department-select>
        </el-form-item>
        <el-form-item label="分组">
          <user-group-select
            :multiple="true"
            :collapse-tags="true"
            class="search-item-w ps-input w-180"
            v-model="jiaofeiForm.groupIds"
            placeholder="请下拉选择"
            @change="searchHandle"
          ></user-group-select>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="jiaofeiForm.name" placeholder="请输入" class="ps-input w-180" @input="searchHandle"></el-input>
        </el-form-item>
        <el-form-item label="人员编号">
          <el-input v-model="jiaofeiForm.personNo" placeholder="请输入" class="ps-input w-180" @input="searchHandle"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select
            v-model="jiaofeiForm.gender"
            placeholder="请下拉选择"
            class="ps-select w-180"
            popper-class="ps-popper-select"
            @change="searchHandle"
          >
            <el-option
              v-for="item in genderList"
              :key="item.value"
              :label="item.gender"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div class="person-table">
          <el-table
            ref="userListRef"
            :data="userList"
            max-height="350"
            :row-key="getRowKey"
            header-row-class-name="ps-table-header-row"
            @select="handleSelection"
            @select-all="handleAllSelection">
            <el-table-column type="selection" :reserve-selection="true" width="50" align="center" class-name="ps-checkbox"></el-table-column>
            <el-table-column prop="card_department_group_alias" label="部门" align="center"></el-table-column>
            <el-table-column prop="card_user_group_alias" label="分组" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" align="center"></el-table-column>
            <el-table-column prop="gender_alias" label="性别" align="center"></el-table-column>
            <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
            <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
          </el-table>
        </div>
        <div class="block ps-pagination person-table-bottom">
          <div style="width: 100px;">已选人数：{{selectList.length}}</div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            :pager-count="5"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
      <div v-if="type === 'refund'" class="refund">
        <div class="refund-info-wrap">
          <div class="refund-info">
            <div class="refund-info-item">订单号：{{selectInfo.trade_no}}</div>
            <div class="refund-info-item">创建时间：{{selectInfo.create_time}}</div>
            <div class="refund-info-item">缴费名称：{{selectInfo.jiaofei_name}}</div>
            <div class="refund-info-item">缴费类别：{{selectInfo.jiaofei_type_alias}}</div>
            <div class="refund-info-item">缴费金额：{{selectInfo.pay_fee}}</div>
            <div class="refund-info-item">支付类型：{{selectInfo.payway_alias}}</div>
            <div class="refund-info-item">支付方式：{{selectInfo.sub_payway_alias}}</div>
            <div class="refund-info-item">第三方订单号：{{selectInfo.provider_trade_no}}</div>
          </div>
          <div class="refund-info-border"></div>
          <div class="refund-info">
            <div class="refund-info-item">用户姓名：{{selectInfo.name}}</div>
            <div class="refund-info-item">人员编号：{{selectInfo.person_no}}</div>
            <div class="refund-info-item">部门：{{selectInfo.department_group_name}}</div>
            <div class="refund-info-item">手机号：{{selectInfo.phone}}</div>
          </div>
        </div>
        <div style="margin: 20px 0;">
          <el-radio-group v-model="refundType">
            <el-radio label="all" class="ps-radio">全额退款</el-radio>
            <el-radio label="part" class="ps-radio">部分退款</el-radio>
          </el-radio-group>
        </div>
        <div style="display: flex; line-height: 40px;">
          <div style="margin-right: 30px;">可退金额：￥{{selectInfo.pay_fee}}</div>
          <div v-if="refundType === 'all'">退款金额：￥{{selectInfo.pay_fee}}</div>
          <div v-else>
            <el-form-item label="退款金额：" prop="refundPrice" label-width="100px">
              <el-input v-model="jiaofeiForm.refundPrice" placeholder="请输入退款金额" class="ps-input w-180"></el-input>
            </el-form-item>
          </div>
        </div>
      </div>
      <div v-if="type==='name'">
        <el-form-item label-width="120px" label="缴费金额" prop="jiaofeiFee">
          <el-input placeholder="请输入缴费金额" v-model="jiaofeiForm.jiaofeiFee" class="ps-input w-180"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import UserDepartmentSelect from '@/components/UserDepartmentSelect'
import UserGroupSelect from '@/components/UserGroupSelect'
import { debounce, times } from '@/utils'
import { deepClone } from '@/assets/js/util'
export default {
  name: 'JiaoFeiDialog',
  components: { UserDepartmentSelect, UserGroupSelect },
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '400px'
    },
    personList: {
      type: Array,
      default() {
        return []
      }
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    let validataPrice = (rule, value, callback) => {
      if (value) {
        let reg = /^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else if (parseFloat(value) > parseFloat(this.selectInfo.pay_fee)) {
          callback(new Error('退款金额不能大于可退金额'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入金额'))
      }
    }
    return {
      isLoading: false,
      jiaofeiForm: {
        department: [],
        groupIds: [],
        name: '',
        personNo: '',
        gender: '',
        refundPrice: '',
        jiaofeiFee: ''
      },
      genderList: [
        { gender: '男', value: 'MAN' },
        { gender: '女', value: 'WOMEN' },
        { gender: '其他', value: 'OTHER' }
      ],
      jiaofeiFormRules: {
        refundPrice: [{ required: true, validator: validataPrice, trigger: "blur" }],
        jiaofeiFee: [{ required: true, validator: validataPrice, trigger: "blur" }]
      },
      userList: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      selectList: [],
      refundType: 'all',
      organizationId: this.$store.getters.organization
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        if (this.type === 'choosePerson' && this.isshow) {
          this.$nextTick(() => {
            this.$refs.userListRef.clearSelection()
          });
          this.currentPage = 1
          this.jiaofeiForm.department = []
          this.jiaofeiForm.groupIds = []
          this.jiaofeiForm.name = ''
          this.jiaofeiForm.personNo = ''
          this.jiaofeiForm.gender = ''
          this.selectList = deepClone(this.personList)
          this.getUserList()
        } else if (this.type === 'name') {
          this.jiaofeiForm.jiaofeiFee = this.selectInfo.jiaofei_fee
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getUserList()
    }, 300),
    clickConfirmHandle() {
      this.$refs.jiaofeiForm.validate(valid => {
        if (valid) {
          if (this.type === 'choosePerson') {
            this.$emit('confirmPerson', this.selectList)
            this.visible = false
          } else if (this.type === 'refund') {
            this.refundOperator()
          } else if (this.type === 'name') {
            this.changeJiaofeiFee()
          }
        } else {
        }
      })
    },
    async refundOperator() {
      const res = await this.$apis.apiBackgroundOrderOrderJiaofeiRefundPost({
        trade_no: this.selectInfo.trade_no,
        refund_fee: this.refundType === 'all' ? times(this.selectInfo.pay_fee) : times(this.jiaofeiForm.refundPrice)
      })
      this.showMessageDialog = false
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    async changeJiaofeiFee() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundJiaofeiJiaofeiSettingJiaofeiDetailModifyPost({
        jiaofei_detail_id: this.selectInfo.id,
        jiaofei_fee: times(this.jiaofeiForm.jiaofeiFee)
      })
      this.isLoading = false
      if (res.code === 0) {
        this.confirm()
        this.$message.success('修改成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.jiaofeiForm.resetFields()
    },
    async getUserList() {
      this.isLoading = true
      let data = {
        card_department_group_ids: this.jiaofeiForm.department,
        card_user_group_ids: this.jiaofeiForm.groupIds,
        person_name: this.jiaofeiForm.name,
        person_no: this.jiaofeiForm.personNo,
        gender: this.jiaofeiForm.gender
      }
      let params = {}
      for (let key in data) {
        if (data[key]) {
          params[key] = data[key]
        }
      }
      const res = await this.$apis.apiCardServiceCardUserListPost({
        ...params,
        org_ids: [this.organizationId],
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.userList = res.data.results
        this.totalCount = res.data.count
        this.userList.map(user => {
          user.card_user_group_alias = user.card_user_group_alias.join('，')
          this.personList.map(selectId => {
            if (user.id === selectId.id) {
              this.$refs.userListRef.toggleRowSelection(user, true);
              // this.handleSelection(user)
            }
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getUserList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getUserList()
    },
    handleSelection(val, row) {
      let index = this.selectList.findIndex(item => item.person_no === row.person_no)
      if (index === -1) {
        this.selectList.push(row)
      } else {
        this.selectList.splice(index, 1)
      }
    },
    handleAllSelection(selection) {
      let list = deepClone(selection)
      let flag = true
      this.userList.map(user => {
        let index = list.findIndex(item => item.person_no === user.person_no)
        if (index === -1) {
          flag = false
        }
      })
      if (flag) { // 全选
        this.userList.map(user => {
          let index = this.selectList.findIndex(item => item.person_no === user.person_no)
          if (index === -1) { // 把之前没有的加上
            this.selectList.push(user)
          }
        })
      } else { // 全不选
        this.userList.map(user => {
          let index = this.selectList.findIndex(item => item.person_no === user.person_no)
          if (index !== -1) { // 把之前有的去掉
            this.selectList.splice(index, 1)
          }
        })
      }
    },
    getRowKey(row) {
      return row.id;
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.person-table-bottom{
  display:flex;
  align-items:center;
  justify-content: space-between;
  padding-top:20px;
}
.refund{
  .refund-info-wrap{
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
    .refund-info{
      display: flex;
      flex-wrap: wrap;
      .refund-info-item{
        width: 50%;
        margin-bottom: 10px;
      }
    }
    .refund-info-border{
      border-top: 1px #e4e4e4 solid;
      margin: 10px 0;
    }
  }
}
</style>

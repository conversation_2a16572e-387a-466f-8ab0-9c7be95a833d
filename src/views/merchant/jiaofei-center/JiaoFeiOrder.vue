<template>
  <div class="JiaoFeiOrder container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="handleExport">导出Excel</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <template v-for="item in tableSetting">
            <el-table-column :key="item.key" :label="item.label" :prop="item.key" align="center">
            </el-table-column>
          </template>
          <el-table-column fixed="right" label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                :disabled="!scope.row.can_refund"
                @click="openRefundDialog(scope.row)"
                >退款</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="tips">
          缴费金额：￥{{totalJiaoFeiFee}}
        </div>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <jiao-fei-dialog
      :isshow.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :width="dialogWidth"
      :select-info="refundInfo"
      :confirm="searchHandle"/>
  </div>
</template>

<script>
import { divide, debounce, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import JiaoFeiDialog from './components/JiaoFeiDialog.vue'

export default {
  name: 'JiaoFeiOrder',
  components: { JiaoFeiDialog },
  mixins: [exportExcel],
  data() {
    const defaultdate = getSevenDateRange(7);
    return {
      isLoading: false,
      tableData: [],
      tableSetting: [
        { key: 'trade_no', label: '订单号' },
        { key: 'create_time', label: '创建时间' },
        { key: 'pay_time', label: '支付时间' },
        { key: 'finish_time', label: '到账时间' },
        { key: 'jiaofei_name', label: '缴费名称' },
        { key: 'jiaofei_type_alias', label: '缴费类别' },
        { key: 'pay_fee', label: '缴费金额' },
        { key: 'payway_alias', label: '支付类型' },
        { key: 'sub_payway_alias', label: '支付方式' },
        { key: 'name', label: '用户姓名' },
        { key: 'person_no', label: '人员编号' },
        { key: 'department_group_name', label: '部门' },
        { key: 'phone', label: '手机号' },
        { key: 'settle_status_alias', label: '对账情况' },
        { key: 'provider_trade_no', label: '第三方订单号' }
      ],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalJiaoFeiFee: 0,
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_time',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '支付时间',
              value: 'pay_time'
            },
            {
              label: '到账时间',
              value: 'finish_time'
            }
          ]
        },
        select_date: {
          type: 'daterange',
          value: [defaultdate[0], defaultdate[1]],
          clearable: false
        },
        jiaofei_name: {
          type: 'input',
          label: '缴费名称',
          value: '',
          maxlength: 20,
          placeholder: '请输入缴费名称'
        },
        jiaofei_type: {
          type: 'select',
          label: '缴费类别',
          value: '',
          clearable: true,
          placeholder: '请选择缴费类别',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        payway: {
          type: 'select',
          label: '支付类型',
          value: '',
          placeholder: '请选择',
          clearable: true,
          dataList: []
        },
        sub_payway: {
          type: 'select',
          label: '支付方式',
          value: '',
          placeholder: '请选择',
          clearable: true,
          dataList: []
        },
        department_ids: {
          type: 'organizationDepartmentSelect',
          multiple: true,
          isLazy: false,
          checkStrictly: true,
          label: '部门',
          value: [],
          placeholder: '请选择部门'
        },
        name: {
          type: 'input',
          label: '用户姓名',
          value: '',
          maxlength: 20,
          placeholder: '请输入用户姓名'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          maxlength: 20,
          placeholder: '请输入人员编号'
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          maxlength: 11,
          placeholder: '请输入手机号'
        },
        trade_no: {
          type: 'input',
          label: '订单号',
          value: '',
          maxlength: 25,
          placeholder: '请输入订单号'
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      dialogWidth: '',
      refundInfo: {}
    }
  },
  mounted() {
    this.initLoad()
  },
  watch: {
    'searchFormSetting.payway.value': function(val, old) {
      this.searchFormSetting.sub_payway.value = ''
      this.getJiaofeiMethod()
    }
  },
  methods: {
    async initLoad() {
      this.getJiaoFeiOrder()
      this.getJiaoFeiType()
      this.getChargeList()
      this.getJiaofeiMethod()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getJiaoFeiOrder()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getJiaoFeiOrder() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderJiaofeiListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        if (res.data.results) {
          this.tableData = res.data.results.map(item => {
            item.pay_fee = divide(item.pay_fee)
            item.pay_time = item.create_time
            return item
          })
        }
        this.totalJiaoFeiFee = divide(res.data.total_amount)
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getJiaoFeiOrder()
    },
    openRefundDialog(data) {
      this.dialogVisible = true
      this.dialogTitle = '退款'
      this.dialogType = 'refund'
      this.dialogWidth = '600px'
      this.refundInfo = data
      console.log(111, data)
    },
    async getJiaoFeiType() {
      const res = await this.$apis.apiBackgroundBaseMenuGetJiaofeiTypePost()
      if (res.code === 0) {
        let jiaofeiTypeList = []
        for (let key in res.data) {
          jiaofeiTypeList.push({
            key,
            name: res.data[key]
          })
        }
        this.searchFormSetting.jiaofei_type.dataList = jiaofeiTypeList
      } else {
        this.$message.error(res.msg)
      }
    },
    async getChargeList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeMethod({
        page: 1,
        page_size: 999,
        org_ids: []
      })
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.payway.dataList = [{ label: '全部', value: '' }, ...result]
      } else {
        this.$message.error(res.msg)
      }
    },
    async getJiaofeiMethod() {
      let params = {
        page: 1,
        page_size: 999,
        org_ids: []
      }
      if (this.searchFormSetting.payway.value) {
        params.payway = this.searchFormSetting.payway.value
      }
      const res = await this.$apis.apiBackgroundOrderOrderJiaofeiJiaofeiMethodPost(params)
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.sub_payway.dataList = [{ label: '全部', value: '' }, ...result]
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExport() {
      const option = {
        type: "ExportJiaoFeiOrder",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss">
.JiaoFeiOrder{
  .tips{
    margin-top: 10px;
    font-size: 14px;
  }
}

</style>

<template>
  <div class="refund-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchForm" @search="searchHandle">
      <template #perv>
        <div class="searchref_top">
          <el-button :class="{ active: current === 0 }" @click="tabHandler(0)">堂食订单</el-button>
          <el-button :class="{ active: current === 1 }" @click="tabHandler(1)">预约订单</el-button>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport">导出报表</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="currentTableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          :index="indexMethod"
          header-row-class-name="ps-table-header-row"/>
      </div>
      <!-- table end -->
      <ul class="total">
        <li>
          退款笔数:
          <span>{{ totalCount }}</span>
        </li>
        <li>
          合计退款金额:
          <span>￥{{ total_amount | formatMoney }}</span>
        </li>
        <li>
          手续费合计:￥
          <span>{{ total_rate_fee | formatMoney }}</span>
        </li>
      </ul>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import {
  REFUND_SCENE_TABLE,
  REFUND_RESERVATION_TABLE,
  REFUND_SCENE_SEARCH,
  REFUND_RESERVATION_SEARCH,
  getRequestParams
} from './constants'
export default {
  name: 'RefundOrder',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      current: 0,
      // 数据列表
      tableData: [],
      searchForm: {},
      sceneSearchForm: REFUND_SCENE_SEARCH,
      reservationSearchForm: REFUND_RESERVATION_SEARCH,
      isLoading: false, // 刷新数据

      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页
      total_amount: 0,
      total_rate_fee: 0,
      // 报表打印相关
      tableSetting: [],
      sceneTableSetting: [],
      reservationSableSetting: [],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'RefundOrderScene'
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
    this.getLevelNameList()
  },
  methods: {
    initLoad() {
      if (this.current === 1) {
        this.searchForm = this.reservationSearchForm
        this.getRefundReservationList()
      } else {
        this.searchForm = this.sceneSearchForm
        this.getRefundOnSceneList()
      }
    },
    tabHandler(type) {
      this.current = type
      this.page = 1
      this.$nextTick(() => {
        if (type === 0) {
          this.printType = 'RefundOrderScene'
          this.searchForm = this.sceneSearchForm
          this.tableSetting = this.sceneTableSetting
          this.initPrintSetting()
          this.getRefundOnSceneList()
        } else {
          this.printType = 'RefundOrderReservation'
          this.searchForm = this.reservationSearchForm
          this.tableSetting = this.reservationSableSetting
          this.initPrintSetting()
          this.getRefundReservationList()
        }
      })
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.initLoad()
    },
    // 获取退款预约订单列表
    async getRefundReservationList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderRefundReservationListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_amount = res.data.total_amount
        this.total_rate_fee = res.data.total_rate_fee
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取退款堂食订单列表
    async getRefundOnSceneList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderRefundOnSceneListPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_amount = res.data.total_amount
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    // 导出报表
    handleExport() {
      let type
      if (this.current === 1) {
        type = 'ExportRefundOrder'
      } else {
        type = 'ExportRefundOnSceneList'
      }
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const option = {
        type,
        params
      }
      this.exportHandle(option)
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      // 初始化每个tableSetting
      this.sceneTableSetting = deepClone(REFUND_SCENE_TABLE)
      this.reservationSableSetting = deepClone(REFUND_RESERVATION_TABLE)
      this.sceneTableSetting.splice(7, 0, ...arr2)
      this.reservationSableSetting.splice(7, 0, ...arr2)
      if (this.current === 0) {
        this.tableSetting = this.sceneTableSetting
      } else if (this.current === 1) {
        this.tableSetting = this.reservationSableSetting
      }
      this.initPrintSetting()
    }
  }
}
</script>

<style lang="scss" scoped>
// 日历
// .el-range-editor--mini.el-input__inner {
//   height: 32px !important;
//   width: 320px;
// }
.refund-order {
  .el-input {
    width: 180px;
  }
  .el-select {
    width: 180px;
  }
  .searchref_top {
    margin-bottom: 10px;
    .active {
      background-color: #ff9b45;
      color: #000;
    }
  }
  .el-table {
    font-size: 12px;
  }
  .searchref_top {
    .el-button {
      width: 120px;
    }
  }
  .total {
    padding: 0 20px;
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
}
</style>

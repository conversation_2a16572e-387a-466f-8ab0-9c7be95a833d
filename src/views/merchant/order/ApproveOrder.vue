<template>
  <div class="withdraw-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      label-width="105px"
      :loading="isLoading"
      :form-setting="searchSetting"
      @search="searchHandle"
    >
      <template slot="perv">
        <div class="m-b-15">
          <el-radio-group v-model="orderType" class="ps-radio-btn" @change="changeOrderStatus">
            <el-radio-button v-for="item in orderTypeList" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport">导出报表</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          border
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column
            v-for="item in currentTableSetting"
            :key="item.key"
            :col="item"
            :index="indexHandle"
          ></table-column>
          <el-table-column fixed="right" label="操作" width="130px" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.review_status === 'wait'"
                type="text"
                size="small"
                class="ps-text"
                @click="gotoDetail(scope.row, 1)"
              >
                处理
              </el-button>
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail(scope.row, 2)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to, camelToUnderline } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { APPROVE_ORDER } from './constants'
import report from '@/mixins/report' // 混入

export default {
  name: 'ApproveOrder',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      searchSetting: {},
      orderType: '',
      orderTypeList: [
        { label: '全部', value: '' },
        { label: '待审核', value: 'wait' },
        { label: '已同意', value: 'success' },
        { label: '已拒绝', value: 'reject' },
        { label: '已撤回', value: 'cancel' }
      ],

      // 报表设置相关
      tableSetting: [],
      tableInitSetting: [
        { label: '序号', key: 'index', type: 'index', width: '80px' },
        { label: '审核编号', key: 'review_no' },
        { label: '申请时间', key: 'apply_time' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '实收金额', key: 'real_fee', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
        { label: '储值动账', key: 'wallet_fee', type: 'money' },
        { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
        { label: '动账组织', key: 'organization_alias' },
        { label: '申请原因', key: 'review_reason_alias' },
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号', key: 'phone' },
        { label: '分组', key: 'payer_group_name' },
        { label: '部门', key: 'payer_department_group_name' },
        { label: '备注', key: 'remark' },
        { label: '审核状态', key: 'review_status_alias' }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'ApproveOrder',
      // 数据列表
      tableData: [],
      collect: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initSearchSetting()
    this.initLoad()
  },
  mounted() {},
  computed: {},
  methods: {
    initSearchSetting() {
      // 初始化表格以及数据
      if (this.orderType === '' || this.orderType === 'success' || this.orderType === 'reject') {
        this.tableSetting = [...this.tableInitSetting, { label: '操作员', key: 'account_alias' }]
        this.searchSetting = {
          ...APPROVE_ORDER,
          account_username: {
            type: 'input',
            value: '',
            label: '操作员',
            placeholder: '请输入操作员'
          },
          only_rate_fee: {
            type: 'checkbox',
            label: '',
            checkboxLabel: '只看手续费',
            value: false
          }
        }
      } else {
        this.tableSetting = this.tableInitSetting
        this.searchSetting = {
          ...APPROVE_ORDER,
          only_rate_fee: {
            type: 'checkbox',
            label: '',
            checkboxLabel: '只看手续费',
            value: false
          }
        }
      }
      this.initPrintSetting()
    },
    initLoad() {
      this.getReviewOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        let k = camelToUnderline(key)
        if (data[key].value !== '' && data[key].value !== null) {
          if (k !== 'select_time') {
            params[k] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 自定义序号
    indexHandle(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1
    },
    // 切换审核状态
    changeOrderStatus(e) {
      this.currentPage = 1
      this.initSearchSetting()
      this.getReviewOrderList()
    },
    // 获取数据列表
    async getReviewOrderList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.orderType) {
        params.review_status = this.orderType
      }
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderReviewListPost(params))
      this.isLoading = false
      if (err) {
        this.tableData = []
        this.collect = []
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.collect = []
        this.collect = [
          { key: 'wait_count', value: res.data.collect.wait_count, label: '待审核合计：', unit: '条' }
        ]
        if (this.orderType === 'success') {
          this.collect.push(
            { key: 'total_origin_fee', value: res.data.collect.total_origin_fee, label: '订单金额合计：', type: 'money' },
            { key: 'total_rate_fee', value: res.data.collect.total_rate_fee, label: '手续费合计：', type: 'money' }
          )
        }
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getReviewOrderList()
    },
    // 导出报表
    handleExport() {
      let params = {
        ...this.formatQueryParams(this.searchSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (this.orderType) {
        params.review_status = this.orderType
      }
      const option = {
        type: 'ApproveOrder',
        url: 'apiBackgroundOrderOrderReviewListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    gotoDetail(data, type) {
      this.$router.push({
        name: 'ApproveOrderDetail',
        query: {
          type,
          id: data.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.withdraw-order {
  .sumWrapper {
    padding-left: 20px;
    padding-bottom: 20px;
    ul,
    li {
      list-style: none;
    }
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 13px;
    }
  }
}
</style>

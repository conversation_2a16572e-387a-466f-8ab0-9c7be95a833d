<template>
  <div class="consumption-failure container-wrapper">
    <refresh-tool ref="searchRef" @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <!-- <button-icon color="plain" type="export" @click="handleExport" style="margin-right:20px">
            导出报表
          </button-icon>
          <el-button size="small">全部重新扣款</el-button> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row">
          <table-column :index="indexMethod" v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" @click="gotoDetail(row)">详情</el-button>
              <!-- v-if="scope.row.order_status === 'ORDER_FAILED'" -->
              <el-button type="text" size="small" @click="clickBtnHandle('repay', row)">重新扣款</el-button>
              <el-button type="text" size="small" class="ps-text" @click="clickBtnHandle('cancel', row)">取消订单</el-button>
              <el-button type="text" size="small" @click="clickBtnHandle('origin', row)">原价扣款</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
        <ul class="total">
          <li>
            合计笔数:
            <span>{{ totalData.total_count }}</span>
          </li>
          <li>
            合计订单金额:￥
            <span>{{ totalData.total_amount | formatMoney }}</span>
          </li>
          <li>
            合计实收金额:￥
            <span>{{ totalData.total_pay_amount | formatMoney }}</span>
          </li>
        </ul>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone } from '@/utils'
import { MEALTYPE, PICKEROPTIONS, PAYMENTSTATE } from './component/order'
import { CONSUMPTION_FAILURE } from './constants'
import report from '@/mixins/report' // 混入
export default {
  name: 'ConsumptionFailure',
  mixins: [report],
  components: {
  },
  data() {
    return {
      meal_type: MEALTYPE, // 餐段
      paymentstate: PAYMENTSTATE, // 支付状态
      searchSetting: CONSUMPTION_FAILURE,
      pickerOptions: PICKEROPTIONS,
      tableData: [],

      // 报表设置相关
      tableSetting: [
        { label: '序号', key: 'index', type: 'index', width: "80" },
        { label: '总单号', key: 'unified_out_trade_no', width: "150" },
        { label: '订单号', key: 'trade_no', width: "150" },
        { label: '创建时间', key: 'create_time', width: "150" },
        { label: '支付时间', key: 'pay_time', width: "150" },
        // { label: '扣款时间', key: 'deduction_time', width: "150" },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        // { label: '优惠金额', key: 'discount_fee', type: 'money' },
        // { label: '补贴消费', key: 'subsidy_fee', type: 'money' },
        // { label: '服务费', key: 'fuwu_fee', type: 'money' },
        // { label: '实收金额', key: 'pay_fee', type: 'money' },
        // { label: '支付状态', key: 'order_status_alias' },
        { label: '失败原因', key: 'error_reason' },
        // { label: '上传状态', key: 'upload_status_alias' },
        { label: '设备状态', key: 'pay_device_status_alias' },
        // { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
        // { label: '储值动账', key: 'wallet_fee', type: 'money' },
        // { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
        // { label: '补贴钱包余额', key: 'subsidy_balance', type: 'money' },
        // { label: '储值钱包余额', key: 'wallet_balance', type: 'money' },
        // { label: '赠送钱包余额', key: 'complimentary_balance', type: 'money' },
        { label: '餐段', key: 'meal_type_alias' },
        { label: '用户名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号', key: 'phone' },
        { label: '分组', key: 'payer_group_name' },
        { label: '部门', key: 'payer_department_group_name' },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "180" }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'failureOrder',

      columns: [], // 动态获取组织的层级
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      totalData: {
        total_count: 0,
        total_amount: 0,
        total_pay_amount: 0
      },
      orgKey: [] // 区分下组织和其它
    }
  },
  async created() {
    await this.getLevelNameList()
    this.initLoad()
  },
  mounted() {
  },
  filters: {
    capitalize: function(value) {
      return value.toFixed(2)
    }
  },
  computed: {
  },
  methods: {
    initLoad() {
      this.getFailureOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getFailureOrderList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 拉取脱机消费失败订单
    async getFailureOrderList() {
      this.isLoading = true
      let params = this.formatQueryParams(this.searchSetting)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineListPost({
        ...params,
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.totalData.total_count = res.data.total_count
        this.totalData.total_amount = res.data.total_amount
        this.totalData.total_pay_amount = res.data.total_pay_amount
        this.tableData = res.data.results.map(item => {
          let data = deepClone(item)
          let orderPaymen = deepClone(data.order_payment)
          delete data.order_payment
          for (const key in orderPaymen) {
            if (Object.hasOwnProperty.call(orderPaymen, key)) {
              if (this.orgKey.includes(key)) {
                data['org_' + key] = orderPaymen[key]
              } else {
                if (data[key] !== undefined) {
                  data['p_' + key] = orderPaymen[key]
                } else {
                  data[key] = orderPaymen[key]
                }
              }
            }
          }
          return data
        })
        console.log(this.tableData)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getFailureOrderList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getFailureOrderList()
    },
    // 导出报表
    handleExport() {},
    gotoDetail(row) {
      this.$router.push({
        name: 'ConsumptionFailureDetail',
        query: {
          id: row.p_id,
          trade_no: row.trade_no
          // data: JSON.stringify(row)
        }
      })
    },
    // 列表序号 补0
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      // let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      // let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      let list = res.data.map(v => {
        this.orgKey.push(v.level)
        return {
          label: v.name,
          key: 'org_' + v.level
        }
      })
      this.tableSetting.splice(6, 0, ...list)
      console.log(this.tableSetting)
      this.initPrintSetting()
    },
    clickBtnHandle(type, row) {
      let tipsText = `确定${type === 'repay' ? '重新发起扣款吗' : type === 'cancel' ? '取消订单吗' : '原价扣款'}？`
      this.$confirm(tipsText, '提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-origin-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            if (type === 'repay') {
              await this.repayOrder(row.p_id)
            }
            if (type === 'cancel') {
              await this.closeOrder(row.p_id)
            }
            if (type === 'origin') {
              await this.repayOrder(row.p_id, true) // 第二个参数为是否原价扣款
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 重新发起订单支付, isOrigin表示是否原价扣款
    async repayOrder(id, isOrigin) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      let params = {
        order_payment_id: id
      }
      if (isOrigin) params.is_original_price = isOrigin
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineOrderPayPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重新发起订单支付
    async closeOrder(id) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderOfflineOrderClosePost({
        order_payment_id: id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-failure {
  // // 日历
  // .el-range-editor--mini.el-input__inner {
  //   height: 32px !important;
  //   width: 320px;
  // }
  .el-input {
    width: 180px;
  }
  .el-select {
    width: 180px;
  }
  //数据报表头部
  .table-wrapper {
    .table-header {
      display: flex;
      justify-content: space-between;
      .el-button {
        margin-right: 10px;
        &:nth-of-type(3) {
          margin-right: 20px;
        }
      }
    }
  }
  .el-table {
    text-align: center;
    font-size: 12px;
  }
  .total {
    // padding: 0 20px;
    margin-top: 20px;
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
}
</style>

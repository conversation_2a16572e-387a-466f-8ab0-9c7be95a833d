<template>
  <div class="withdraw-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport">导出报表</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="currentTableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          :index="indexMethod"
          header-row-class-name="ps-table-header-row"/>
      </div>
      <div class="sumWrapper">
        <ul>
          <li>合计提现笔数：<span>{{ statisticsData.totalCount }}</span></li>
          <li>合计提现金额：<span>{{ statisticsData.totalAmount }}</span></li>
          <li>合计提现成功笔数：<span>{{ statisticsData.totalSuccessCount }}</span></li>
          <li>合计提现成功金额：<span>{{ statisticsData.totalSuccessAmount }}</span></li>
          <li>合计提现失败笔数：<span>{{ statisticsData.totalFailCount }}</span></li>
          <li>合计提现失败金额：<span>{{ statisticsData.totalFailAmount }}</span></li>
        </ul>
      </div>
       <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="page"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to, camelToUnderline, divide } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { WITHDRAW_ORDER_SEARCH } from './constants'
import report from '@/mixins/report' // 混入

export default {
  name: 'WithdrawOrder',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      searchSetting: WITHDRAW_ORDER_SEARCH,
      // 数据列表
      tableData: [],
      statisticsData: { // 统计
        totalAmount: 0,
        totalComplimentaryAmount: 0,
        totalCount: 0,
        totalFailAmount: 0,
        totalFailCount: 0,
        totalSuccessAmount: 0,
        totalSuccessCount: 0
      },

      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页

      // 报表设置相关
      tableSetting: [
        { label: '序号', key: 'index', type: 'index', width: "80" },
        { label: '提现单号', key: 'trade_no', width: "190" },
        { label: '第三方提现', key: 'out_trade_no', width: "160" },
        { label: '提现申请时间', key: 'create_time', width: "160" },
        { label: '提现到账时间', key: 'pay_time', width: "160" },
        { label: '提现金额', key: 'withdraw_fee', type: 'money' },
        { label: '储值钱包动账', key: 'wallet_fee', type: 'money' },
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号码', key: 'phone', width: "110" },
        { label: '提现类型', key: 'pay_scene_alias' },
        { label: '对账状态', key: 'settle_status_alias' },
        { label: '提现渠道', key: 'payway_alias' },
        { label: '提现状态', key: 'order_status_alias' },
        { label: '操作员', key: 'account_alias' }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'WithdrawOrder'
    }
  },
  created() {
    this.initLoad()
    this.initPrintSetting()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      this.getWithdrawOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        let k = camelToUnderline(key)
        if (data[key].value !== '') {
          if (k !== 'select_time') {
            params[k] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取数据列表
    async getWithdrawOrderList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderWithdrawListPost({
        ...this.formatQueryParams(this.searchSetting),
        page: this.page,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        this.tableData = []
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        // this.tableData = res.data.results.map(row => {
        //   row.withdraw_fee = divide(row.withdraw_fee)
        //   row.wallet_fee = divide(row.wallet_fee)
        //   return row
        // })
        let amountList = ['totalAmount', 'totalComplimentaryAmount', 'totalFailAmount', 'totalSuccessAmount']
        for (let k in this.statisticsData) {
          let key = camelToUnderline(k)
          if (amountList.includes(k)) {
            this.statisticsData[k] = divide(res.data[key])
          } else {
            this.statisticsData[k] = res.data[key]
          }
        }
        this.totalCount = res.data.count
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.page = val.current
      this.pageSize = val.pageSize
      this.getWithdrawOrderList()
    },
    // 导出报表
    handleExport() {
      let ReserParams = 1
      const option = {
        type: 'WithdrawOrder',
        params: {
          page: this.page,
          page_size: this.pageSize,
          ...ReserParams
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.withdraw-order {
  .sumWrapper{
    padding-left: 20px;
    padding-bottom: 20px;
    ul, li { list-style: none; }
    li{
      display: inline-block;
      margin-right: 20px;
      font-size: 13px;
    }
  }
}
</style>

<template>
  <div class="ConsumptionOrder container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" :form-setting="searchForm" label-width="105px" @search="searchHandle">
      <template #perv>
        <div class="searchref_top">
          <el-button :class="{ active: current === 0 }" @click="tabHandler(0)">堂食订单</el-button>
          <el-button :class="{ active: current === 1 }" @click="tabHandler(1)">预约订单</el-button>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="mulRefundHandle">批量退款</button-icon>
          <button-icon color="plain" type="export" @click="handleExportLive">导出实况</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['background_order.order_payment.list_export']">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog" v-if="current === 1">小票打印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
         <!-- :selectable="selectableHandle" -->
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" @selection-change="handleOrderSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <table-column  v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="gotoDetail(row)">详情</el-button>
              <el-button type="text" class="ps-text" size="small" :disabled="!row.can_refund" @click="openRefundDialog(row)">退款</el-button>
            </template>
          </table-column>
        </el-table>
        <ul class="total">
          <li>
            合计笔数:
            <span>{{ total_count }}</span>
          </li>
          <li>
            合计订单金额:￥
            <span>{{ total_amount | formatMoney }}</span>
          </li>
          <li>
            合计实收金额:￥
            <span>{{ total_origin_amount | formatMoney }}</span>
          </li>
          <li>
            手续费合计:￥
            <span>{{ total_rate_fee | formatMoney }}</span>
          </li>
        </ul>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[5, 10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 退款对话框 -->
    <div class="refund-confirm">
      <el-dialog top="200px" title="退款" :visible.sync="outerVisible" width="800px" customClass="ps-dialog">
        <el-table
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
          :data="refundData.food_list"
          border
          row-key="id"
          style="width: 100%;"
          ref="refundTable"
          @selection-change="handleRefundSelectionChange"
        >
          >
          <el-table-column
            type="selection"
            :show-overflow-tooltip="true"
            :reserve-selection="true"
            v-if="refundMethod === 'part'"
            class-name="ps-checkbox"
            width="55"
            :selectable="selectDisabled"
          ></el-table-column>
          <el-table-column prop="food_extra.food_img" label="图片">
            <template slot-scope="scope">
              <img style="width:60px" :src="scope.row.food_extra.food_img" alt="">
            </template>
          </el-table-column>
          <el-table-column prop="name" label="菜品名称"></el-table-column>
          <el-table-column prop="food_price" label="销售价格">
            <template slot-scope="scope">
              <!-- raw_fee -->
              <span>￥{{ scope.row.food_price | formatMoney}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="count" label="数量"></el-table-column>
          <el-table-column prop="weight" label="重量"></el-table-column>
          <el-table-column prop="buy_price" label="消费金额">
            <template slot-scope="scope">
              <span>￥{{ scope.row.real_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="food_status" label="退款状态">
            <template slot-scope="scope">
              <span>{{ scope.row.food_status === 'ORDER_REFUND_SUCCESS' ? '退款成功' : '未退款' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="refund-radio">
          <el-radio text-color="#FF9B45" v-model="refundMethod" @change="changeRefundType" label="all" class="ps-radio">全额退款</el-radio>
          <el-radio text-color="#FF9B45" v-model="refundMethod" @change="changeRefundType" label="part" class="ps-radio">部分退款</el-radio>
        </div>
        <div class="refund-info">
          <div class="refund-info-item">
            <span>可退款余额：</span>
            <span v-if="refundMethod === 'all'">{{ refundData.net_fee | formatMoney }}</span>
            <span v-if="refundMethod === 'part'">{{ refundData.part_net_fee | formatMoney }}</span>
          </div>
          <div v-if="refundMethod === 'all'" class="refund-info-item">
            <span>退款金额：</span>
            <span>{{ refundData.pay_fee | formatMoney }}</span>
          </div>
          <div v-if="refundMethod === 'part'" class="refund-info-item">
            <span>退款金额：</span>
             <el-input
              class="w-180 ps-input"
              placeholder="请输入退款金额"
              v-model="refundMoney"
            ></el-input>
          </div>
        </div>
        <el-dialog width="30%" title="温馨提示" customClass="ps-dialog" :visible.sync="innerVisible" append-to-body top="280px">
          <p class="twoRefund" v-if="refundMethod === 'all'" style="font-size: 20px;">
            确定要对该订单进行退款吗
          </p>
          <p class="twoRefund" v-else style="font-size: 20px;">
            确定要对该订单进行
            <span style="font-weight: bold;">部分退款吗?</span>
          </p>
          <p class="twoRefund" style="color:#E0364C;">温馨提示: 确定后不可撤销</p>
          <div slot="footer" class="footer-btn">
            <el-button class="ps-cancel-btn" @click="innerVisible = false">取消</el-button>
            <el-button class="ps-btn" :disabled="dialogLoading" @click="handleConfirm">确定</el-button>
          </div>
        </el-dialog>
        <div slot="footer">
          <el-button class="ps-cancel-btn" @click="outerVisible = false">取 消</el-button>
          <el-button class="ps-btn" @click="handleRefund">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <print-ticket
      :isshow.sync="printTicketVisible"
      type="order"
      title="小票打印"
      :select-list-id="selectListId"
      :confirm="searchHandle"
    ></print-ticket>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, deepClone, divide, times, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import PrintTicket from '@/components/PrintTicket'
import {
  DEVICE_STATUS,
  CONSUMPTION_SCENE_TABLE,
  CONSUMPTION_RESERVATION_TABLE,
  MEALTYPE,
  PAYMENTSTATE,
  GETMEALTYPE,
  getRequestParams,
  RECENTSEVEN
} from './constants'
import report from '@/mixins/report' // 混入
export default {
  name: 'ConsumptionOrder',
  components: { PrintTicket },
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  data() {
    return {
      current: 0, // 0代表堂食订单 1代表预约订单
      // 搜索筛选相关
      sceneSearchForm: {
        date_type: {
          type: 'select',
          value: 'create_time',
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '支付时间',
              value: 'pay_time'
            },
            {
              label: '扣费时间',
              value: 'deduction_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: RECENTSEVEN
        },
        order_status: {
          type: 'select',
          label: '支付状态',
          value: '',
          placeholder: '请选择',
          dataList: PAYMENTSTATE
        },
        payway: {
          type: 'select',
          label: '支付类型',
          value: '',
          placeholder: '请选择',
          dataList: []
        },
        sub_payway: {
          type: 'select',
          label: '支付方式',
          value: '',
          placeholder: '请选择',
          dataList: []
        },
        device_name: {
          type: 'select',
          label: '交易设备',
          value: '',
          listNameKey: 'device_name',
          listValueKey: 'device_name',
          placeholder: '请选择',
          clearable: true,
          dataList: []
        },
        pay_device_status: {
          type: 'select',
          label: '设备状态',
          value: '',
          placeholder: '请选择',
          dataList: DEVICE_STATUS
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        meal_type: {
          type: 'select',
          label: '餐段',
          value: '',
          placeholder: '请选择',
          dataList: MEALTYPE
        },
        wallet_org: {
          type: 'organizationSelect',
          value: [],
          label: '动账组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        consume_organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        payer_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          multiple: true,
          collapseTags: true
        },
        payer_department_group_ids: {
          type: 'organizationDepartmentSelect',
          value: [],
          label: '部门'
        },
        making_type: {
          type: 'select',
          label: '制作状态',
          value: '',
          placeholder: '请选择',
          dataList: [{
            label: '待制作',
            value: 'queues'
          }, {
            label: '已制作',
            value: 'finish'
          }]
        },
        name: {
          type: 'input',
          value: '',
          label: '用户姓名',
          placeholder: '请输入'
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号',
          placeholder: '请输入'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入'
        },
        third_and_trade_no: {
          type: 'input',
          value: '',
          labelWidth: '145px',
          label: '订单号/第三方订单号',
          placeholder: '请输入'
        },
        controller: {
          type: 'input',
          value: '',
          label: '操作员',
          placeholder: '请输入要搜索的操作员'
        },
        only_discount: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看优惠',
          value: false
        },
        only_rate_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看手续费',
          value: false
        }
      },
      reservationSearchForm: {
        date_type: {
          type: 'select',
          value: 'create_time',
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '报餐时间',
              value: 'report_date'
            },
            {
              label: '预约时间',
              value: 'reservation_date'
            },
            {
              label: '用餐时间',
              value: 'dining_time'
            },
            {
              label: '支付时间',
              value: 'pay_time'
            },
            {
              label: '扣费时间',
              value: 'deduction_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: RECENTSEVEN
        },
        take_meal_type: {
          type: 'select',
          label: '取餐方式',
          value: '',
          placeholder: '请选择',
          dataList: GETMEALTYPE
        },
        order_status: {
          type: 'select',
          label: '支付状态',
          value: '',
          placeholder: '请选择',
          dataList: PAYMENTSTATE
        },
        payway: {
          type: 'select',
          label: '支付类型',
          value: '',
          placeholder: '请选择',
          dataList: []
        },
        sub_payway: {
          type: 'select',
          label: '支付方式',
          value: '',
          placeholder: '请选择',
          dataList: []
        },
        meal_type: {
          type: 'select',
          label: '餐段',
          value: '',
          placeholder: '请选择',
          dataList: MEALTYPE
        },
        wallet_org: {
          type: 'organizationSelect',
          value: [],
          label: '动账组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        consume_organization_ids: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        payer_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          multiple: true,
          collapseTags: true
        },
        payer_department_group_ids: {
          type: 'organizationDepartmentSelect',
          value: [],
          label: '部门'
        },
        making_type: {
          type: 'select',
          label: '制作状态',
          value: '',
          placeholder: '请选择',
          dataList: [{
            label: '待制作',
            value: 'queues'
          }, {
            label: '已制作',
            value: 'finish'
          }]
        },
        name: {
          type: 'input',
          value: '',
          label: '用户姓名',
          placeholder: '请输入'
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号',
          placeholder: '请输入'
        },
        person_no: {
          type: 'input',
          value: '',
          label: '人员编号',
          placeholder: '请输入'
        },
        third_and_trade_no: {
          type: 'input',
          value: '',
          labelWidth: '145px',
          label: '订单号/第三方订单号',
          placeholder: '请输入'
        },
        only_discount: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看优惠',
          value: false
        },
        only_rate_fee: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看手续费',
          value: false
        }
      },
      searchForm: {},

      tableData: [],
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      page: 1, // 第几页
      total_count: 0,
      total_amount: 0,
      total_origin_amount: 0,
      total_rate_fee: 0,
      // 退款相关
      refundMethod: 'all', // all代表全额退款 part代表部分退款
      refundMoney: '', // 部分退款金额
      refundFoodId: [], // 部分退款菜品id
      outerVisible: false,
      innerVisible: false,
      dialogLoading: false,
      // 退款弹框的数组
      refundData: [],
      refundStatus: ['ORDER_REFUNDING', 'ORDER_REFUND_SUCCESS'],

      // 报表设置相关
      tableSetting: [],
      sceneTableSetting: CONSUMPTION_SCENE_TABLE,
      reservationSableSetting: CONSUMPTION_RESERVATION_TABLE,
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'ConsumptionOrderScene',
      printTicketVisible: false,
      refundOrderIds: [], // 可以退款的id
      selectListId: [] // 多选功能
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
    this.getLevelNameList() // 根据项目点获取公司的层级组织
    this.getpayList() // 支付方式 / 支付类型
    this.getDeviceList()
    this.getDeviceType()
  },
  methods: {
    initLoad() {
      if (this.current === 1) {
        this.searchForm = this.reservationSearchForm
        this.getConsumptionList()
      } else {
        this.searchForm = this.sceneSearchForm
        this.getOnSceneList()
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.printTicketVisible = false
      this.page = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.page = 1
      this.initLoad()
    },
    tabHandler(type) {
      this.current = type
      this.page = 1
      this.$nextTick(() => {
        if (type === 0) {
          this.printType = 'ConsumptionOrderScene'
          this.searchForm = this.sceneSearchForm
          this.tableSetting = this.sceneTableSetting
          this.initPrintSetting()
          this.getOnSceneList()
        } else {
          this.printType = 'ConsumptionOrderReservation'
          this.searchForm = this.reservationSearchForm
          this.tableSetting = this.reservationSableSetting
          this.initPrintSetting()
          this.getConsumptionList()
        }
      })
    },
    // 获取数据列表
    async getConsumptionList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_amount = res.data.total_amount
        this.total_count = res.data.count
        this.total_origin_amount = res.data.total_pay_amount
        this.total_rate_fee = res.data.total_rate_fee
      } else {
        this.$message.error(res.msg)
      }
    },
    async getOnSceneList() {
      this.isLoading = true
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderPaymentOnSceneListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        this.total_amount = res.data.total_amount
        this.total_count = res.data.count
        this.total_origin_amount = res.data.total_pay_amount
        this.total_rate_fee = res.data.total_rate_fee
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.initLoad()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.page = val
      this.initLoad()
    },
    // 动态获取组织的层级
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      let arr = JSON.parse(JSON.stringify(res.data).replace(/name/g, 'label'))
      let arr2 = JSON.parse(JSON.stringify(arr).replace(/level/g, 'key'))
      // 初始化每个tableSetting
      this.sceneTableSetting = deepClone(CONSUMPTION_SCENE_TABLE)
      this.reservationSableSetting = deepClone(CONSUMPTION_RESERVATION_TABLE)
      this.sceneTableSetting.splice(25, 0, ...arr2)
      this.reservationSableSetting.splice(22, 0, ...arr2)
      if (this.current === 0) {
        this.tableSetting = this.sceneTableSetting
      } else if (this.current === 1) {
        this.tableSetting = this.reservationSableSetting
      }
      this.initPrintSetting()
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost()
      if (res.code === 0) {
        const result = []
        const result2 = []
        res.data.result.payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        res.data.result.sub_payways.forEach(d => {
          Object.keys(d).forEach(key => result2.push({ label: d[key], value: key }))
        })
        this.sceneSearchForm.payway.dataList = result
        this.sceneSearchForm.sub_payway.dataList = result2
        this.reservationSearchForm.payway.dataList = result
        this.reservationSearchForm.sub_payway.dataList = result2
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取设备列表
    async getDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.sceneSearchForm.device_name.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转详情
    gotoDetail(row) {
      this.$router.push({
        // path: `/order/consumption_detail?id=${this.current}`,
        name: 'ConsumptionDetail',
        query: {
          id: row.id,
          type: this.current
        }
      })
    },
    // 导出报表
    handleExport() {
      let type
      if (this.current === 1) {
        type = 'ExportOrderConsumption'
      } else {
        type = 'ExportOnSceneList'
      }
      const params = getRequestParams(this.searchForm, this.page, this.pageSize)
      const option = {
        type,
        params
      }
      this.exportHandle(option)
    },
    // 导出实况
    handleExportLive() {
      let params = getRequestParams(this.searchForm, this.page, this.pageSize)
      if (this.current === 1) {
        params.export_order_types = 'reservation'
      } else {
        params.export_order_types = 'instore'
      }
      const option = {
        type: 'OrderPaymentLiveExport',
        url: 'apiBackgroundOrderOrderPaymentLiveExportPost',
        params
      }
      this.exportHandle(option)
    },
    // 退款start
    openRefundDialog(data) {
      this.refundData = data
      this.outerVisible = true
      this.refundMethod = 'all'
    },
    changeRefundType() {
      if (this.refundMethod === 'part') {
        this.$refs.refundTable.clearSelection()
      }
    },
    handleRefund() {
      if (this.refundMethod === 'part') {
        let reg = /^(([1]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(this.refundMoney)) {
          this.$message.error('输入金额有误')
          return
        }
      }
      this.innerVisible = true
    },
    // 退款弹出框 退款成功
    handleConfirm() {
      let params = {
        trade_no: this.refundData.trade_no
      }
      if (this.refundMethod === 'all') {
        params.refund_fee = this.refundData.pay_fee
      } else {
        params.refund_fee = times(this.refundMoney)
        params.payment_food_ids = this.refundFoodId
        if (parseFloat(params.refund_fee) > parseFloat(this.refundData.part_net_fee)) {
          return this.$message.error('退款金额不能大于可退金额！')
        }
      }
      this.orderRefund(params)
    },
    async orderRefund(params) {
      if (this.dialogLoading) return
      this.dialogLoading = true
      let res
      if (this.current) {
        // 预约退款
        res = await this.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(params)
      } else {
        // 堂食退款
        res = await this.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(params)
      }
      this.dialogLoading = false
      if (res.code === 0) {
        this.innerVisible = false
        this.outerVisible = false
        this.initLoad()
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 多选框  当选择项发生变化时会触发该事件
    handleRefundSelectionChange(val) {
      this.refundMoney = 0
      this.refundFoodId = []
      val.map(item => {
        this.refundMoney += item.real_fee
        this.refundFoodId.push(item.id)
      })
      this.refundMoney = divide(this.refundMoney)
    },
    // 退款end
    selectDisabled(row, index) {
      return !this.refundStatus.includes(row.refund_status)
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.sceneSearchForm.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // // 多选禁用
    // selectableHandle(row) {
    //   return row.can_refund
    // },
    // 订单的多选
    handleOrderSelectionChange(val) {
      this.refundOrderIds = []
      this.selectListId = val.map(item => {
        if (item.can_refund) {
          this.refundOrderIds.push(item.id)
        }
        return item.id
      })
    },
    async mulRefundHandle() {
      if (this.dialogLoading) return
      if (!this.selectListId.length) return this.$message.error('请选择要退款的订单！')
      if (!this.refundOrderIds.length) return this.$message.error('当前所选订单不存在可退款订单！')
      this.dialogLoading = true
      this.$confirm(`确定要将这些订单进行退款？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        // customClass: 'ps-confirm',
        // cancelButtonClass: 'ps-cancel-btn',
        // confirmButtonClass: 'ps-btn',
        closeOnClickModal: false,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {
              order_ids: this.refundOrderIds
            }
            let res
            if (this.current) {
              // 预约退款
              res = await this.$apis.apiBackgroundOrderOrderReservationReservationOrderRefundPost(params)
            } else {
              // 堂食退款
              res = await this.$apis.apiBackgroundOrderOrderPaymentInstoreRefundPost(params)
            }
            this.dialogLoading = false
            if (res.code === 0) {
              this.initLoad()
              this.$message.success(`操作成功，其中不可退款订单数${this.selectListId.length - this.refundOrderIds.length}笔`)
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              this.dialogLoading = false
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    openDialog() {
      if (!this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.printTicketVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
// 日历
// .el-range-editor--mini.el-input__inner {
//   height: 32px !important;
//   width: 320px;
// }
.container-wrapper {
  font-size: 12px !important;
  .active {
    background-color: #ff9b45;
    color: #000;
  }
  .searchref_top {
    margin-bottom: 10px;
    .el-button {
      width: 120px;
    }
  }
  .ps-small-box {
    .block {
      display: inline-block;
    }
    .el-select {
      width: 200px !important;
    }
    .el-input {
      width: 180px !important;
    }
  }
}
.table-wrapper {
  .el-table {
    text-align: center;
    font-size: 12px;
  }
  .total {
    margin-top: 10px;
    li {
      display: inline-block;
      margin-right: 20px;
      font-size: 14px;
    }
  }
}
.twoRefund {
  text-align: center;
}
.search-form-wrapper {
  overflow: hidden;
  // border-radius: 6px;
  .search-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 20px;
    &:after {
      content: '';
      position: absolute;
      left: 24px;
      right: 24px;
      bottom: 0;
      height: 1px;
      background-color: #e7ecf2;
    }
    .search-h-l {
      border-left: 4px solid #ff9b45;
      padding-left: 18px;
      font-size: 20px;
      color: #23282d;
    }
    .search-h-r {
      display: flex;
      align-items: center;
      .search-h-r-btn {
        margin-right: 10px;
        min-width: auto;
      }
      .search-collapse-btn {
        width: 121px;
        height: 16px;
        cursor: pointer;
        img {
          display: inline-block;
          width: 100%;
          vertical-align: middle;
        }
      }
    }
  }
  .collapse-wrapper {
    padding: 0 20px;
    // overflow: hidden;
  }
  .search-item-w {
    width: 200px;
  }
  .vue-treeselect__control {
    height: 40px;
  }
  .vue-treeselect__placeholder {
    line-height: 40px;
    font-size: 13px;
  }
}

.refund-confirm {
  .refund-radio{
    margin: 25px 0 10px;
  }
  .refund-info{
    display: flex;
    line-height: 40px;
    .refund-info-item{
      min-width: 150px;
    }
  }
}
.footer-btn{
  .ps-btn{
    background-color: #ff9b45;
    color: #fff;
    border: #ff9b45;
  }
}
</style>

<template>
  <div class="TableAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <!-- <div class="ps-flex-align-c"> -->
        <div class="table-title" style="min-width:130px;">数据列表</div>
        <!-- <div>
            <button-icon color="origin">列表全选</button-icon>
            <button-icon color="plain" type="export">导出存餐码</button-icon>
          </div> -->
        <!-- </div> -->
        <div class="align-r">
          <!-- <span class="p-r-20">全部格子数：188</span>
          <span class="p-r-20">已使用：188</span>
          <span class="p-r-20">未使用：188</span> -->
          <button-icon color="origin" @click="getSelectCupboardOrderAll">列表全选</button-icon>
          <button-icon color="origin" @click="clickStorageFood('batch')">批量存餐</button-icon>
          <!-- @click="gotoExport" -->
          <button-icon color="plain" type="export" @click="gotoExport('ExportPutMealNumber')">
            导出存餐码
          </button-icon>
          <button-icon color="plain" type="export" @click="gotoExport('ExportCupboardOrderList')">
            导出Excel
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
          ref="tableData"
          style="width: 100%"
          stripe
          row-key="id"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            :reserve-selection="true"
            class-name="ps-checkbox"
            :selectable="selectable"
          ></el-table-column>
          <el-table-column prop="unified_trade_no" label="总单号" align="center"></el-table-column>
          <el-table-column prop="trade_no" label="订单号" align="center"></el-table-column>
          <el-table-column prop="put_meal_number" label="存餐号" align="center"></el-table-column>
          <el-table-column prop="take_meal_number" label="取餐号" align="center"></el-table-column>
          <!-- <el-table-column prop="no_save_put_meal_number" label="未存餐号" align="center"></el-table-column> -->
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column
            prop="reservation_date"
            label="预约时间"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="consumption_name"
            label="预约消费点"
            align="center"
            width="100"
          ></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
          <!-- <el-table-column prop="reservation_take_meal_type_alias" label="取餐方式" align="center"></el-table-column> -->
          <el-table-column
            prop="meal_type_alias"
            label="预约餐段"
            align="center"
          ></el-table-column>
          <el-table-column prop="food_list" label="预约菜品" align="center">
            <template slot-scope="scope">
              <div
                v-if="scope.row.food_list && scope.row.food_list.length"
                class="ps-flex-align-c flex-wrap"
              >
                <div v-for="(item, index) in scope.row.food_list" :key="index">
                  <span class="p-t-10">
                    {{
                      (index === scope.row.food_list.length - 1 && item.name) || item.name + '、'
                    }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="payer_group_name" label="分组" align="center"></el-table-column>
          <el-table-column prop="is_save" label="是否已存餐" align="center" width="100">
            <template slot-scope="scope">
              <div v-if="scope.row.is_save">已存餐</div>
              <div v-else>未存餐</div>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="存餐区域" align="center"></el-table-column>
          <el-table-column prop="cupboard_name" label="餐柜名称" align="center"></el-table-column>
          <el-table-column prop="ceil_no" label="格子号" align="center"></el-table-column>
          <el-table-column
            prop="take_meal_status_alias"
            label="取餐状态"
            align="center"
          ></el-table-column>
          <el-table-column prop="dining_time" label="用餐时间" align="center"></el-table-column>
          <el-table-column align="center" width="180" label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                :disabled="!scope.row.can_save"
                @click="clickStorageFood('single', scope.row)"
              >
                存餐
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <!-- 分页 end -->
    <dialog-message
      :show.sync="storageFoodDialog"
      title="请选择所要存餐的取餐柜"
      customClass="ps-dialog"
      width="350px"
    >
      <div class="ps-center">
        <el-select
          v-if="batchSavetype === 'batch'"
          v-model="saveIdsDialog"
          multiple
          collapse-tags
          placeholder="请选择取餐柜"
          class="ps-select w-250 "
          popper-class="ps-popper-select"
          key="batch"
        >
          <el-option
            v-for="item in saveListDialog"
            :key="item.device_id"
            :label="item.device_name"
            :value="item.device_id"
          ></el-option>
        </el-select>
        <el-select
          v-if="batchSavetype === 'single'"
          v-model="singleSaveIdsDialog"
          collapse-tags
          placeholder="请选择取餐柜"
          class="ps-select w-250"
          popper-class="ps-popper-select"
          key="single"
        >
          <el-option
            v-for="item in saveListDialog"
            :key="item.device_id"
            :label="item.device_name"
            :value="item.device_id"
          ></el-option>
        </el-select>
      </div>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button :disabled="isLoading" class="ps-cancel-btn" @click="storageFoodDialog = false">
            取消
          </el-button>
          <el-button
            :disabled="isLoading"
            class="ps-btn"
            type="primary"
            @click="clickStorageFoodDialog"
          >
            确定
          </el-button>
        </div>
      </template>
    </dialog-message>
    <!-- 选择完取餐柜是否要提醒 -->
    <dialog-message
      :show.sync="storageTipsDialog"
      title="提示"
      customClass="ps-dialog"
      width="350px"
    >
      <div>
        <div>当前可用餐格不足以存完所选的订单，是否继续？</div>
        <div>如继续，则优先存入先下单的订单</div>
      </div>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button :disabled="isLoading" class="ps-cancel-btn" @click="storageTipsDialog = false">
            取消
          </el-button>
          <el-button
            :disabled="isLoading"
            class="ps-btn"
            type="primary"
            @click="clickTipsStorageFood"
          >
            继续
          </el-button>
        </div>
      </template>
    </dialog-message>

    <!-- 存餐成功 -->
    <dialog-message
      :show.sync="saveSuccessDialog"
      title="提示"
      customClass="ps-dialog"
      width="350px"
      @close="clickSaveSuccessDialog"
    >
      <div class="t-a-c">
        <div>存餐成功订单：{{ saveSuccessDialogData.save_success_num }}单</div>
        <div>未成功订单：{{ saveSuccessDialogData.no_save_num }}单</div>
        <el-button type="text" class="ps-blue" @click="gotoExport('ExportSaveCupboardOrderList')">
          下载存餐成功订单详情
        </el-button>
      </div>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
          <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickSaveSuccessDialog">
            关闭
          </el-button>
        </div>
      </template>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone } from '@/utils'
import { CUPBOARDORDER } from './constantsAndConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'TableAdmin',
  mixins: [exportExcel],
  data() {
    return {
      tableData: [],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: deepClone(CUPBOARDORDER),
      selectTableList: [],
      storageFoodDialog: false, // 选择取餐柜
      saveIdsDialog: [],
      singleSaveIdsDialog: '',
      saveListDialog: [],
      storageTipsDialog: false, // 存餐提示
      batchSavetype: '',
      saveSuccessDialog: false, // 存餐成功提示
      saveCupboardParams: {}, // 存餐需要的数据
      tableSingleRow: {}, // 单条存餐
      saveSuccessDialogData: {} // 存餐成功
    }
  },
  created() {
    this.searchFormSetting.organization_id.value = this.$store.getters.organization
    this.getCupboardList({ data_model: 1 })
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getCupboardOrderList()
    },
    // 刷新页面
    refreshHandle() {
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 取餐柜订单
    async getCupboardOrderList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderReservationCupboardOrderListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: 99999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$nextTick(() => {
          this.$refs.tableData.clearSelection()
        })
        this.tableData = res.data.results
        this.totalCount = res.data.results.length
      } else {
        this.$message.error(res.msg)
      }
    },
    getSelectCupboardOrderAll() {
      this.tableData.forEach(v => {
        if (v.can_save) {
          this.$nextTick(() => {
            this.$refs.tableData.toggleAllSelection(v)
          })
        }
      })
    },
    // 取餐柜订单 number 1筛选 2存餐的时候需要
    async getCupboardList(params) {
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderReservationGetCupboardListPost(params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (params.data_model === 1) {
          this.searchFormSetting.device_ids.dataList = res.data
        } else if (params.data_model === 2) {
          this.saveListDialog = res.data
          this.storageFoodDialog = true
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handleSelectionChange(e) {
      this.selectTableList = e
    },
    clickStorageFood(type, row) {
      if (type === 'batch') {
        this.saveIdsDialog = []
      } else if (type === 'single') {
        this.singleSaveIdsDialog = ''
      }
      this.batchSavetype = type
      this.tableSingleRow = row
      let params = {
        data_model: 2,
        organization_id: '',
        reservation_date: '',
        meal_type: ''
      }
      if (type === 'single') {
        params.organization_id = row.organization_id
        params.reservation_date = row.reservation_date
        params.meal_type = row.meal_type
      }
      if (type === 'batch' && !this.selectTableList.length) return this.$message.error('请勾选数据')
      if (type === 'batch' && this.selectTableList.length) {
        // 拿选择数据的第一个，批量
        params.organization_id = this.selectTableList[0].organization_id
        params.reservation_date = this.selectTableList[0].reservation_date
        params.meal_type = this.selectTableList[0].meal_type
      }
      this.getCupboardList(params)
    },
    // 复选框禁用
    selectable(row, rowIndex) {
      if (row.can_save) {
        return true // 不禁用
      } else {
        return false // 禁用
      }
    },
    clickStorageFoodDialog() {
      if (this.batchSavetype === 'batch' && !this.saveIdsDialog.length) return this.$message.error('请选择取餐柜')
      if (this.batchSavetype === 'single' && !this.singleSaveIdsDialog) return this.$message.error('请选择取餐柜')
      let totalFreeCellNum = 0
      this.saveListDialog.forEach(v => {
        if (this.batchSavetype === 'batch' && this.saveIdsDialog.includes(v.device_id)) {
          totalFreeCellNum += v.total_free_cell_num
        }
        if (this.batchSavetype === 'single' && this.singleSaveIdsDialog === v.device_id) {
          totalFreeCellNum = v.total_free_cell_num
        }
      })
      if (this.batchSavetype === 'single' && totalFreeCellNum <= 0) {
        return (this.storageTipsDialog = true)
      }
      if (this.batchSavetype === 'batch' && totalFreeCellNum < this.selectTableList.length) {
        return (this.storageTipsDialog = true)
      }
      this.getSaveCupboardOrder()
    },
    // 存餐的时候提示
    clickTipsStorageFood() {
      this.getSaveCupboardOrder()
    },
    async getSaveCupboardOrder() {
      let params = {
        order_payment_ids: [],
        device_ids: this.batchSavetype === 'batch' ? this.saveIdsDialog : [this.singleSaveIdsDialog],
        organization_id: '',
        meal_type: ''
      }
      if (this.batchSavetype === 'batch') {
        params.organization_id = this.selectTableList[0].organization_id
        params.meal_type = this.selectTableList[0].meal_type
        params.order_payment_ids = this.selectTableList.map(item => {
          return item.id
        })
      } else {
        params.order_payment_ids = [this.tableSingleRow.id]
        params.organization_id = this.tableSingleRow.organization_id
        params.meal_type = this.tableSingleRow.meal_type
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundOrderOrderReservationSaveCupboardOrderListPost(params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.saveSuccessDialogData = res.data
        this.saveSuccessDialog = true
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickSaveSuccessDialog() {
      this.saveSuccessDialog = false
      this.storageFoodDialog = false
      this.storageTipsDialog = false
    },
    gotoExport(type) {
      const option = {
        type: type,
        params: {}
      }
      if (type === 'ExportSaveCupboardOrderList') {
        option.params = {
          order_payment_save_ids: this.saveSuccessDialogData.order_payment_save_ids
        }
      } else if (type === 'ExportCupboardOrderList' || type === 'ExportPutMealNumber') {
        option.params = {
          ...this.formatQueryParams(this.searchFormSetting),
          page: 1,
          page_size: 99999
        }
      }
      this.exportHandle(option)
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value !== undefined) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>

<template>
  <div class="booking-meal-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
    ></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <el-button size="mini">打印</el-button> -->
          <el-button size="mini" @click="handleExport">导出</el-button>
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          :span-method="objectSpanMethod"
        >
          <el-table-column
            v-for="col in columns"
            :prop="col.column"
            :label="col.label"
            align="center"
            :key="col.column"
          >
            <template slot-scope="scope">
              <span v-if="col.column === 'raw_fee'">
                {{ scope.row[col.column] | formatMoney }}
              </span>
              <span v-else>{{ scope.row[col.column] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->

      <common-pagination
        ref="pagination"
        :total="total"
        :onPaginationChange="onPaginationChange"
      ></common-pagination>
    </div>
  </div>
</template>

<script>
import { recentSevenDay, dateTypes } from './constantsAndConfig'
import CommonPagination from './CommonPagination'
import { MEAL_TYPES, TAKE_MEAL_TYPES } from '@/utils/constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mergeHandle, mergeRowAction } from '@/utils/table'
export default {
  name: 'CategoryList',
  mixins: [exportExcel],
  components: {
    CommonPagination
  },
  mounted() {
    this.initLoad()
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_time',
          dataList: dateTypes
        },
        select_time: {
          clearable: false,
          type: 'daterange',
          // label: '预约时间',
          value: recentSevenDay
        },
        name: {
          type: 'input',
          label: '菜品名称',
          value: '',
          placeholder: '请输入菜品名称'
        },
        organization_id: {
          type: 'organizationSelect',
          value: [],
          label: '消费点',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: [],
          multiple: true,
          checkStrictly: true,
          collapseTags: true
        },
        payer_department_group_ids: {
          type: 'departmentSelect',
          multiple: true,
          flat: false,
          label: '部门',
          value: [],
          placeholder: '请选择部门',
          checkStrictly: true,
          dataList: [],
          limit: 1,
          level: 1
        },
        meal_type: {
          type: 'select',
          value: 'all',
          label: '餐段',
          dataList: [
            {
              label: '全部',
              value: 'all'
            },
            ...MEAL_TYPES
          ]
        },
        take_meal_type: {
          type: 'select',
          value: 'all',
          label: '取餐方式',
          dataList: [
            {
              label: '全部',
              value: 'all'
            },
            ...TAKE_MEAL_TYPES
          ]
        }
      },
      isLoading: false,
      tableData: [],
      mergeOpts: {
        useKeyList: {}, // 根据id去合并set_meal_id:[name]
        mergeKeyList: [
          'reservation_date',
          'organization__name',
          'payer_department_group_name',
          'meal_type_alias'
        ] // 通用的合并字段，根據值合并
      },
      rowMergeArrs: [], // 处理完的数据给表格合并
      columns: [
        { label: '日期', column: 'reservation_date' },
        { label: '组织', column: 'organization__name' },
        { label: '部门', column: 'payer_department_group_name' },
        { label: '餐段', column: 'meal_type_alias' },
        { label: '菜品', column: 'name' },
        { label: '规格', column: 'spec_name' },
        { label: '数量', column: 'food_count' },
        { label: '金额', column: 'raw_fee' }
      ]
    }
  },
  methods: {
    initLoad() {
      this.requestFoodCollectList()
      // this.userGroupList()
      // this.userConsumeList()
    },

    async requestFoodCollectList() {
      this.isLoading = true
      const params = {
        date_type: this.searchFormSetting.date_type.value,
        start_date: this.searchFormSetting.select_time.value[0],
        end_date: this.searchFormSetting.select_time.value[1],
        name: this.searchFormSetting.name.value,
        page: this.currentPage,
        page_size: this.pageSize,
        meal_type: this.searchFormSetting.meal_type.value,
        take_meal_type: this.searchFormSetting.take_meal_type.value,
        payer_department_group_ids: this.searchFormSetting.payer_department_group_ids.value
      }
      if (this.searchFormSetting.organization_id.value) {
        params.organization_id = this.searchFormSetting.organization_id.value
      }
      const res = await this.$apis.apiBackgroundOrderReservationOrderFoodCollectListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
        this.total = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 套餐合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },

    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },
    onPaginationChange(data) {
      this.pageSize = data.pageSize
      this.currentPage = data.current
      this.requestFoodCollectList()
    },
    searchHandle() {
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },
    // // 获取组织信息
    // async userGroupList() {
    //   const res = await this.$apis.apiBackgroundOrganizationOrganizationListPost({
    //     status: 'enable',
    //     page: 1,
    //     page_size: 9999999
    //   })
    //   if (res.code === 0) {
    //     this.searchFormSetting.organization_id.dataList = res.data.results
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },

    handleExport() {
      let params = {
        page: 1,
        page_size: 9999999,
        date_type: this.searchFormSetting.date_type.value,
        start_date: this.searchFormSetting.select_time.value[0],
        end_date: this.searchFormSetting.select_time.value[1],
        name: this.searchFormSetting.name.value,
        meal_type: this.searchFormSetting.meal_type.value,
        payer_department_group_ids: this.searchFormSetting.payer_department_group_ids.value
      }
      if (this.searchFormSetting.organization_id.value) {
        params.organization_id = this.searchFormSetting.organization_id.value
      }
      const option = {
        type: 'ExportCategoryList',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style scoped lang="scss">
.condition-wrapper {
  padding: 20px;
  // margin-top: 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .grid-content {
    display: flex;
    align-items: center;
    margin-right: 20px;
    .label {
      margin-right: 8px;
      font-size: 12px;
    }
  }
}
</style>

<template>
  <div class="booking-meal-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="menuManagerFormSetting"
    ></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-button size="mini" color="plain" icon="el-icon-plus" @click="editRecipes({})">
            新增菜谱
          </el-button>
          <el-button size="mini" color="plain" type="danger" @click="deleteHandler">
            批量删除
          </el-button>
          <!-- <el-button size="mini" color="plain">发布</el-button> -->
          <el-button size="mini" color="plain" @click="batchDownShelves">批量下架</el-button>
        </div>
      </div>
      <div style="padding: 10px 20px">
        <el-radio-group v-model="headerType" size="small" @change="switchFn">
          <el-radio-button label="week">周菜谱</el-radio-button>
          <el-radio-button label="month">月菜谱</el-radio-button>
        </el-radio-group>
      </div>
      <week-header :headerType="headerType" ref="weekHeaderRef"></week-header>
      <div class="radio-btn-wrapper">
        <div style="margin-left: 20px">
          <el-radio-group v-model="device" size="small" @change="switchFn" :disabled="isLoading">
            <el-radio-button v-for="item in deviceList" :label="item.key" :key="item.key">
              {{ item.name }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableRef"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column label="编号" align="center" prop="id"></el-table-column>
          <el-table-column label="菜谱名称" align="center" prop="name"></el-table-column>
          <el-table-column label="创建时间" align="center" prop="create_time"></el-table-column>
          <el-table-column label="可见范围" align="center" prop="use_user_groups">
            <template slot-scope="scope">
              <el-tag
                v-for="user in scope.row.use_user_groups"
                :key="user.id"
                style="margin-right: 6px"
              >
                {{ user.group_name }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="推荐人群" align="center">
            <template slot-scope="scope">
              {{ scope.row.apply_group && scope.row.apply_group.name }}
            </template>
          </el-table-column>
          <el-table-column label="应用组织" align="center" prop="use_organization">
            <template slot-scope="scope">
              <span>{{ scope.row.use_organization ? scope.row.use_organization.name : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="适用设备与页面" align="center" prop="device_types">
            <template slot-scope="scope">
              <el-tag v-for="d in scope.row.device_types" :key="d" style="margin-right: 6px">
                {{ d }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="绑定设备号" align="center" prop="devices">
            <template slot-scope="scope">
              <span class="link-text" @click="deviceDetail(scope.row.devices)">查看</span>
            </template>
          </el-table-column>
          <el-table-column
            label="创建组织"
            align="center"
            prop="organization_name"
          ></el-table-column>
          <el-table-column label="创建人" align="center" prop="account_username"></el-table-column>
          <el-table-column label="是否在用" align="center" prop="is_used">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.is_used"
                @change="handleChange($event, scope.row.id)"
              ></el-switch>
              <!-- key貌似还没上，不知道后端是在那个功能完善的，得等到这权限完善才能用，测试环境可以用 :disabled="!hasPermission(['background_food.menu_weekly.batch_used'])" -->
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="300" fixed="right">
            <template slot-scope="scope">
              <div class="operate-btn-wrapper">
                <el-button type="primary" size="mini" @click="editRecipes(scope.row)">
                  编辑菜谱信息
                </el-button>
                <!-- <el-button size="mini" type="primary">
                  设为当{{ headerType === 'week' ? '周' : '月' }}菜单
                </el-button> -->
                <el-button size="mini" @click="addRecipes(scope.row)">添加菜品</el-button>
                <el-button type="danger" size="mini" @click="deleteHandler(0, scope.row.id)">
                  删除
                </el-button>
              </div>
              <div class="operate-btn-wrapper">
                <el-button size="mini" @click="copyTo(scope.row.name, scope.row.id)">
                  复制
                </el-button>
                <el-button size="mini" @click="clickMenuPreview(scope.row)">
                  预览菜谱
                </el-button>
                <el-button size="mini" @click="clickMenuNutrition(scope.row)" v-if="scope.row.is_nutrition_guidance">
                  营养分析
                </el-button>
                <!-- <el-button size="mini" @click="copyToOrgs(scope.row.id)">复制到其他组织</el-button> -->
                <!-- <el-button size="mini">菜品排单</el-button> -->
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
        <common-pagination
          :onPaginationChange="onPaginationChange"
          :total="total"
          ref="pagination"
        ></common-pagination>

        <!-- 编辑菜谱信息 -->
        <div v-if="dialogVisible">
          <el-dialog
            title="菜谱信息"
            :visible.sync="dialogVisible"
            width="1000px"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
          >
            <edit-recipes :editData="editData" ref="editRecipesRef"></edit-recipes>
            <span slot="footer" class="dialog-footer">
              <el-button @click="handleCancelModify">取 消</el-button>
              <el-button type="primary" @click="handleModify">确 定</el-button>
            </span>
          </el-dialog>
        </div>
      </div>
    </div>

    <!-- 查看应用组织 -->
    <el-dialog
      title="查看"
      :visible.sync="showOrgDialog"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      width="400px"
    >
      <el-tag v-for="org in orgNames" :key="org.id">{{ org.name }}</el-tag>
    </el-dialog>

    <!-- 查看绑定设备号 -->
    <div v-if="showDeviceDialog">
      <el-dialog
        title="查看"
        :visible.sync="showDeviceDialog"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        width="400px"
      >
        <el-tag v-for="device in targetDevice" :key="device.id" style="margin-right: 12px">
          {{ device.device_type }}
        </el-tag>
      </el-dialog>
    </div>
    <!-- 复制到 -->
    <el-dialog
      title="复制"
      :visible.sync="showCopyDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :before-close="handleCopyDialogClose"
      :destroy-on-close="true"
      width="600px"
    >
      <div>
        <el-radio-group v-model="copeMenuStatus" class="ps-radio">
          <el-radio label="date">复制到其他日期</el-radio>
          <el-radio label="org">复制到其他组织</el-radio>
        </el-radio-group>
      </div>
      <div v-if="copeMenuStatus === 'date'">
        <p>
          <span style="margin-right: 48px">已选：{{ currentMenuName }}</span>
        </p>
        <div class="p-b-10">复制到{{ headerType === 'week' ? '周' : '月份' }}：</div>
        <el-date-picker
          v-if="headerType === 'month'"
          v-model="copyFormData.month"
          type="month"
          placeholder="选择月"
          size="small"
        ></el-date-picker>
        <el-date-picker
          v-if="headerType === 'week'"
          v-model="copyFormData.week"
          type="week"
          :picker-options="{ firstDayOfWeek: 1 }"
          placeholder="选择周"
          format="yyyy 第 WW 周"
          size="small"
        ></el-date-picker>
      </div>
      <div class="p-t-20" v-else>
        <tree-select-ingredients
          :structureTree="structureTree"
          :organizationDisabled="organizationDisabled"
          @inputTree="inputTree"
          :structureData="structureData"
        ></tree-select-ingredients>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleCopyDialogClose">取 消</el-button>
        <el-button size="small" type="primary" @click="handleCopy">确 定</el-button>
      </span>
    </el-dialog>

    <menu-preview-dialog
      v-if="menuPreviewDialogVisible"
      :isshow.sync="menuPreviewDialogVisible"
      :formDataDialog="dialogMenuPeviewInfo"
      width="900px"
      ref="menuPreviewDialog"
    />
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
import WeekHeader from '../components/WeekHeader.vue'
import EditRecipes from '../components/EditRecipes'
import CommonPagination from '../booking-setting/CommonPagination'
import treeSelectIngredients from '../components/TreeSelect'
import { menuManagerFormSetting } from '../booking-setting/constantsAndConfig'
import { getRequestParams, to } from '@/utils'
import menuPreviewDialog from '../components/menu/menuPreviewDialog'
export default {
  name: 'RecipesManage',
  props: {},
  components: {
    WeekHeader,
    EditRecipes,
    CommonPagination,
    treeSelectIngredients,
    menuPreviewDialog
  },
  mounted() {
    this.initLoad()
  },
  data() {
    return {
      editData: {},
      targetDevice: [],
      copyFormData: {
        copyId: -1,
        month: '',
        week: ''
      },
      orgNames: [],
      menuDevice: [],
      currentMenuName: '',
      dialogVisible: false,
      showCopyDialog: false,
      headerType: 'week',
      deviceList: [],
      device: '',
      showDeviceDialog: false,
      showOrgDialog: false,
      isLoading: false,
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      menuManagerFormSetting,
      currentDate: '',
      structureData: [],
      structureTree: [],
      organizationDisabled: false,
      copeMenuStatus: 'date',
      menuPreviewDialogVisible: false, // 预览菜谱
      dialogMenuPeviewInfo: {}
    }
  },
  methods: {
    initLoad() {
      this.requestMenuWeeklyList()
      this.requestDietGroupList()
      this.userGroupList()
      this.requestMenuDeviceList()
      this.getOrgDeviceList()
      this.getOrgData()
      this.getOrganizationTreeList()
    },
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationTreeListPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.structureTree = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },

    determineOrganization() {
      if (this.structureType !== 'see') {
        this.foodIngredientSync()
      } else {
        this.showDialogStructure = false
      }
    },

    inputTree(val) {
      this.structureData = val
    },

    // 日期切换，换取数据
    getDate(data) {
      this.currentDate = data.start
    },

    // 关闭时重置数据
    handleCopyDialogClose() {
      this.copyFormData = {
        copyId: -1,
        month: '',
        week: ''
      }
      this.structureData = []
      this.copeMenuStatus = 'date'
      this.showCopyDialog = false
    },

    handleCancelModify() {
      this.dialogVisible = false
    },

    // 获取设备数据
    async requestMenuDeviceList() {
      const params = {}
      const res = await this.$apis.apiBackgroundFoodMenuDeviceListPost(params)
      if (res.code === 0) {
        // const result = this.unique(res.data, 'device_type')
        // result.forEach(r => {
        //   r.children = res.data.filter(d => d.device_type === r.device_type)
        // })
        this.menuDevice = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取当前组织设备类型
    async getOrgDeviceList() {
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceTypePost({
        source: 'self'
      })
      if (res.code === 0) {
        this.deviceList = [
          { name: '全部', key: '' },
          { name: 'H5', key: 'H5' },
          { name: '小程序', key: 'MAPP' },
          ...res.data
        ]
      } else {
        this.$message.error(res.msg)
      }
    },

    // 修改菜谱信息
    async handleModify(isContinue, continueMenuId) {
      const data = this.$refs.editRecipesRef.form
      // 沿用菜谱参数
      if (isContinue === 'continue') {
        data.continue_menu_id = continueMenuId
      }
      delete data.use_organizations
      this.$refs.editRecipesRef.$refs.form.validate(async valid => {
        if (valid) {
          let res
          if (data.id > -1) {
            // 周修改
            if (this.headerType === 'week') {
              const dateString = this.$refs.weekHeaderRef.activeWeek
              data.start_date = dateString
              data.end_date = dayjs(dateString)
                .endOf('week')
                .add(1, 'd')
                .format('YYYY-MM-DD')
              res = await this.$apis.apiBackgroundFoodMenuWeeklyModifyPost(data)
            } else {
              // 月修改
              data.month = this.$refs.weekHeaderRef.activeMonth.replace('年', '').replace('月', '')
              res = await this.$apis.apiBackgroundFoodMenuMonthlyModifyPost(data)
            }
          } else {
            // 周新增
            if (this.headerType === 'week') {
              const dateString = this.$refs.weekHeaderRef.activeWeek
              data.start_date = dateString
              data.end_date = dayjs(dateString)
                .endOf('week')
                .add(1, 'd')
                .format('YYYY-MM-DD')
              res = await this.$apis.apiBackgroundFoodMenuWeeklyAddPost(data)
            } else {
              // 月新增
              data.month = this.$refs.weekHeaderRef.activeMonth.replace('年', '').replace('月', '')
              res = await this.$apis.apiBackgroundFoodMenuMonthlyAddPost(data)
            }
          }
          if (res.code === 0) {
            this.$message.success('操作成功！')
            this.dialogVisible = false
            this.requestMenuWeeklyList()
          } else if (res.code === 2) {
            this.$confirm(`${res.msg}`, `提示`, {
              confirmButtonText: this.$t('dialog.confirm_btn'),
              cancelButtonText: this.$t('dialog.cancel_btn'),
              closeOnClickModal: false,
              customClass: 'ps-confirm',
              cancelButtonClass: 'ps-cancel-btn',
              confirmButtonClass: 'ps-btn',
              center: true,
              beforeClose: async (action, instance, done) => {
                if (action === 'confirm') {
                  instance.confirmButtonLoading = true
                  this.handleModify('continue', res.data.continue_menu_id)
                  done()
                  instance.confirmButtonLoading = false
                } else {
                  if (!instance.confirmButtonLoading) {
                    done()
                  }
                }
              }
            })
              .then(e => {})
              .catch(e => {})
          } else {
            this.$message.error(res.msg)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    // 处理复制
    async handleCopy() {
      let res
      if (this.copeMenuStatus === 'date') {
        const params = { id: this.copyFormData.copyId }
        let dateString = ''
        if (this.headerType === 'week') {
          dateString = dayjs(new Date(this.copyFormData.week).getTime() - 8.64e7).format(
            'YYYY-MM-DD'
          )
          params.start_dates = [dateString]
          res = await this.$apis.apiBackgroundFoodMenuWeeklyWeeklyCopyPost(params)
        } else {
          dateString = dayjs(this.copyFormData.month).format('YYYY-MM')
          params.months = [dateString.replace('-', '')]
          res = await this.$apis.apiBackgroundFoodMenuMonthlyMonthlyCopyPost(params)
        }
      } else if (this.copeMenuStatus === 'org') {
        const data = {
          id: this.copyFormData.copyId,
          use_organizations: this.structureData
        }
        if (this.headerType === 'week') {
          res = await this.$apis.apiBackgroundFoodMenuWeeklyCopyToOrganization(data)
        } else {
          res = await this.$apis.apiBackgroundFoodMenuMonthlyCopyToOrganization(data)
        }
      }
      if (res.code === 0) {
        this.$message.success('操作成功！')
        this.requestMenuWeeklyList()
        this.showCopyDialog = false
      } else {
        this.$message.error(res.msg)
      }
    },

    copyTo(name, id) {
      this.copyFormData.copyId = id
      this.currentMenuName = name
      this.showCopyDialog = true
    },
    // 预览菜谱
    clickMenuPreview(data) {
      this.menuPreviewDialogVisible = true
      this.dialogMenuPeviewInfo = {
        menuId: data.id,
        menuType: this.headerType
      }
    },
    // copyToOrgs(id) {
    //   this.copyOrgId = id
    //   this.showCopayOrgDialog = true
    // },

    // 获取周菜谱列表
    async requestMenuWeeklyList() {
      if (this.$refs.switchActiveWeek || this.$refs.switchActiveMonth) {
        this.page = 1
        this.pageSize = 10
      }
      const params = getRequestParams(this.menuManagerFormSetting, this.page, this.pageSize)
      if (this.device !== '') {
        params.device_types = this.device
      }
      this.isLoading = true
      let res
      if (this.headerType === 'week') {
        params.start_date = this.$refs.weekHeaderRef.activeWeek
        res = await this.$apis.apiBackgroundFoodMenuWeeklyListPost(params)
      } else {
        // 获取月菜谱列表
        params.month = this.$refs.weekHeaderRef.activeMonth.replace('年', '').replace('月', '')
        res = await this.$apis.apiBackgroundFoodMenuMonthlyListPost(params)
      }
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.results
      } else {
        this.tableData = []
        this.$message.error(res.msg)
      }
    },

    // 获取使用人群
    async requestDietGroupList() {
      const param = {
        page: 1,
        page_size: 9999
      }
      const res = await this.$apis.apiBackgroundFoodDietGroupListPost(param)
      if (res.code === 0) {
        this.menuManagerFormSetting.apply_groups.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },

    // 获取可见范围
    async userGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: 9999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.groupList = res.data.results
        this.menuManagerFormSetting.use_user_groups.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },

    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },

    // 获取组织数据
    async getOrgData() {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationTreeListPost({
        page: 1,
        page_size: 9999
      })
      if (res.code === 0) {
        const handledResult = this.deleteEmptyGroup(res.data)
        this.menuManagerFormSetting.organization.dataList = handledResult
        this.menuManagerFormSetting.use_organization.dataList = handledResult
      } else {
        this.$message.error(res.msg)
      }
    },
    changeTime() {
      if (this.headerType === 'week') {
        return this.$apis.apiBackgroundFoodMenuWeeklyDeletePost
      } else {
        return this.$apis.apiBackgroundFoodMenuMonthlyDeletePost
      }
    },
    // 删除数据
    async deleteHandler(type, id) {
      let delId
      if (type === 0) {
        delId = id
      } else {
        const selectData = this.$refs.tableRef.selection
        if (selectData.length === 0) {
          this.$message({ message: '请选择需要删除的数据', type: 'warning' })
          return
        }
        delId = selectData.map(d => d.id)
      }

      this.$confirm('是否删除此数据？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true

            const [err, res] = await to(
              this.changeTime()({
                ids: typeof delId === 'number' ? [delId] : delId
              })
            )
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success('删除成功')
              this.$refs.pagination.handleCurrentChange(1)
              this.$refs.pagination.handleSizeChange(10)
              // this.requestMenuWeeklyList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            done()
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },

    refreshHandle() {
      // 搜索重置
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },

    searchHandle() {
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    },

    // 查看绑定设备号
    deviceDetail(data) {
      const res = this.menuDevice.filter(d => data.includes(d.id))
      this.targetDevice = res
      this.showDeviceDialog = true
    },

    // 批量下架
    async batchDownShelves() {
      const ids = this.$refs.tableRef.selection.map(d => d.id)
      const res = await this.$apis.apiBackgroundFoodMenuWeeklyBatchUsedPost({ ids, is_used: false })
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.requestMenuWeeklyList()
      } else {
        this.$message.error(res.msg)
      }
    },

    // 新增菜品
    addRecipes(data) {
      this.$router.push({
        name: this.headerType === 'week' ? 'AddWeekRecipesSeparate' : 'AddMonthRecipesSeparate',
        query: {
          id: data.id,
          menu_type: this.headerType
        }
      })
    },
    clickMenuNutrition(data) {
      this.$router.push({
        name: 'NutritionAnalysis',
        query: {
          id: data.id,
          menu_type: this.headerType
        }
      })
    },
    // 翻页
    onPaginationChange(data) {
      this.page = data.current
      this.pageSize = data.pageSize
      this.requestMenuWeeklyList()
    },

    // 编辑
    async editRecipes(data) {
      this.dialogVisible = true
      if (data.id) {
        let deviceModel = []
        data.device_model.map(item => {
          for (let key in item) {
            deviceModel.push(key)
          }
        })
        this.editData = {
          id: data.id,
          name: data.name,
          apply_group: data.apply_group ? data.apply_group.id : '',
          device_types: data.device_types,
          device_model: deviceModel,
          use_user_groups: data.use_user_groups.map(d => d.id),
          devices: data.devices,
          is_continue_to_used: data.is_continue_to_used,
          is_intent_menu: data.is_intent_menu,
          applied_to_visitor: data.applied_to_visitor
          // use_organizations: data.use_organizations.map(d => d.id)
        }
      } else {
        this.editData = {}
        if (this.$refs.editRecipesRef) {
          this.$refs.editRecipesRef.$refs.form.resetFields()
        }
      }
    },

    // 查看应用组织
    organizationDetail(data) {
      this.orgNames = data.map(d => ({ id: d.id, name: d.name }))
      this.showOrgDialog = true
    },

    // 修改状态
    handleChange(status, id) {
      this.$confirm(`是否${status === false ? '关闭' : '开启'}该菜谱`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        center: true,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.cancelButtonLoading = true
            let res = []
            if (this.headerType === 'week') {
              res = await to(
                this.$apis.apiBackgroundFoodMenuWeeklyBatchUsedPost({
                  ids: [id],
                  is_used: status
                })
              )
            } else {
              res = await to(
                this.$apis.apiBackgroundFoodMenuMonthlyBatchUsedPost({
                  ids: [id],
                  is_used: status
                })
              )
            }
            instance.confirmButtonLoading = false
            instance.cancelButtonLoading = false
            if (res[0]) {
              this.$message.error(res[0].message)
              return
            }
            if (res[1].code === 0) {
              done()
              this.requestMenuWeeklyList()
              this.$message.success('修改成功')
            } else {
              this.$message.error(res[1].msg)
            }
          } else {
            const idx = this.tableData.findIndex(d => d.id === id)
            if (idx > -1) {
              this.tableData[idx].is_used = !this.tableData[idx].is_used
            }
            done()
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    switchFn() {
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.onPaginationChange({ current: 1, pageSize: 10 })
    }
  }
}
</script>

<style scoped lang="scss">
$primary: #f59a23;

.link-text {
  color: $primary;
  cursor: pointer;
}

.operate-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 8px;
}

.radio-btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.week-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 20px;
  .arrow-direction {
    margin-right: 12px;
    font-weight: bold;
    font-size: 24px;
    cursor: pointer;
  }
  .item {
    box-sizing: border-box;
    margin-right: 12px;
    font-size: 14px;
    cursor: pointer;

    .active-week {
      color: $primary;
    }
  }
}
</style>

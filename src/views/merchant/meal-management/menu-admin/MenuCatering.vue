<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="menu-catering">
      <!-- 组织结构 start -->
      <div class="organization-tree">
        <el-input
          class="tree-search ps-input"
          placeholder="请选择分类"
          v-model="treeFilterText"
        ></el-input>
        <div
          :class="['all-tree', searchForm.category_id ? '' : 'is-current']"
          @click="treeHandleNodeClick('', 'all')"
        >
          <i class="tree-search-icon"><img src="@/assets/img/icon all.png" alt="" /></i>
          <span>{{ tabType === 'food' ? '全部菜品' : '全部套餐' }}</span>
          <span v-if="tabType === 'food'">（已选{{ selectFoodIds.length }}）</span>
          <span v-if="tabType === 'setMeal'">（已选{{ setMealSelectFoodIds.length }}）</span>
        </div>
        <el-tree
          v-loading="treeLoading"
          :data="treeList"
          :props="treeProps"
          :filter-node-method="filterTreeNode"
          :check-on-click-node="true"
          :default-expand-all="true"
          :highlight-current="true"
          :current-node-key="searchForm.category_id"
          :class="{ 'tree-box': searchForm.category_id }"
          node-key="id"
          ref="tree"
          @node-click="treeHandleNodeClick"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span class="ellipsis tree-lable">{{ node.label }}</span>
            <span style="color:#fda04d;">
              <span v-if="data.number">（已选{{ data.number }}）</span>
            </span>
          </span>
        </el-tree>
      </div>
      <!-- end -->
      <!--  -->
      <div class="account-list">
        <search-form
          class="search-lt-shadow"
          ref="searchRef"
          :form-setting="searchFormSetting"
          @search="searchHandle"
        >
          <template slot="perv">
            <div class="tab">
              <div
                :class="['tab-item', tabType === 'food' ? 'active' : '']"
                @click="tabClick('food')"
              >
                菜品
              </div>
              <div
                :class="['tab-item', tabType === 'setMeal' ? 'active' : '']"
                @click="tabClick('setMeal')"
              >
                套餐
              </div>
            </div>
          </template>
        </search-form>
        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="align-r">
              <button-icon color="plain" type="Import" @click="importHandler('import')">
                导入菜品/商品
              </button-icon>
            </div>
          </div>
          <div class="table-content ps-flex-align-c flex-align-c">
            <el-checkbox
              class="ps-checkbox"
              v-model="setMealSelectAll"
              @change="setMealhandleCheckChange"
            >
              全选
            </el-checkbox>
            <el-select
              v-if="setMealAllValue === -1"
              class="ps-select"
              popper-class="ps-popper-select"
              v-model="setMealAllValue"
              placeholder="请选择"
              style="width:120px;margin: 0 10px"
              :disabled="!setMealSelectAll"
            >
              <el-option label="无数量限制" :value="-1"></el-option>
              <el-option label="限制数量" :value="0"></el-option>
            </el-select>
            <el-input
              v-else
              v-model="setMealAllValue"
              size="small"
              style="width: 120px;margin: 0 10px"
              :disabled="!setMealSelectAll"
            >
              <i slot="suffix" class="el-input__icon el-icon-circle-close" style="cursor: pointer;" @click="setMealAllValue = -1"></i>
            </el-input>
            <!-- <el-input-number
              style="width: 130px;margin: 0 10px"
              size="small"
              :min="0"
              placeholder="请输入库存"
              :disabled="!setMealSelectAll"
              v-model="setMealAllValue"
            ></el-input-number> -->
            <span>限制单人可点数量：</span>
            <el-input
              :disabled="!setMealSelectAll"
              v-model="setMealAllFoodBuyLimitNum"
              size="small"
              style="width: 100px;margin: 0 10px"
            ></el-input>
            <el-button
              class="ps-origin-btn"
              :disabled="!setMealSelectAll"
              size="small"
              type="primary"
              @click="setMealSetAll(setMealSelectAll)"
            >
              确定
            </el-button>
            <div class="p-l-20" v-if="tabType === 'setMeal'">
              <el-checkbox class="ps-checkbox" v-model="setMealFoodDisplay">
                套餐中包含菜品不单独显示
              </el-checkbox>
            </div>
            <div class="p-l-20" v-if="isNutritionGuidance && tabType === 'food'">
              <i class="el-icon-success" style="color:#5dbf6e;"></i>
              <span>推荐菜品</span>
            </div>
            <div class="p-l-20" v-if="isNutritionGuidance && tabType === 'food'">
              <i class="el-icon-warning" style="color:#fd953c;"></i>
              <span>不推荐菜品</span>
            </div>
          </div>

          <div
            class="table-content"
            :class="{ 'ps-flex-bw': isNutritionGuidance && tabType === 'food' }"
          >
            <!-- table start -->
            <div :class="{ 'table-box': isNutritionGuidance && tabType === 'food' }">
              <el-table
                v-loading="isLoading"
                :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
                ref="tableData"
                style="width: 100%"
                height="550"
                row-key="id"
                stripe
                header-row-class-name="ps-table-header-row"
                class="ps-table"
                @select="handleSelectionSelect"
                @select-all="handleSelectionAll"
              >
                <el-table-column
                  type="selection"
                  width="55"
                  class-name="ps-checkbox"
                  align="center"
                  :reserve-selection="true"
                ></el-table-column>
                <el-table-column prop="name" label="菜品名" align="center">
                  <template slot-scope="scope">
                    <!-- <div v-if="isNutritionGuidance && tabType === 'food'"> -->
                    <i
                      v-if="scope.row.recommend_food"
                      class="el-icon-success"
                      style="color:#5dbf6e;"
                    ></i>
                    <i
                      v-if="scope.row.recommend_food === 0"
                      class="el-icon-warning"
                      style="color:#fd953c;"
                    ></i>
                    <!-- </div> -->
                    <div :style="{ color: scope.row.food_nutrition_status ? 'red' : '' }">
                      <!-- <span
                        class="recommend-food-wrapp"
                         :style="{ backgroundColor: scope.row.recommend_food ? border : '' }"
                      ></span> -->
                      {{ scope.row.name }}
                      <span v-if="tabType === 'food'">({{ scope.row.weight }})g</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="ingredient_info"
                  v-if="tabType === 'food'"
                  key="ingredient_info"
                  label="食材组成"
                  align="center"
                  width="200"
                >
                  <template slot-scope="scope">
                    <div
                      v-if="scope.row.ingredient_info && scope.row.ingredient_info.length"
                      class="ps-flex-align-c flex-wrap flex-justify-c"
                      :style="{ color: scope.row.food_nutrition_status ? 'red' : '' }"
                    >
                      <div v-for="(item, index) in scope.row.ingredient_info" :key="index">
                        <span class="p-t-10">
                          {{
                            (index === scope.row.ingredient_info.length - 1 &&
                              item.ingredient_name) ||
                              item.ingredient_name + '、'
                          }}
                        </span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="ingredient_info"
                  v-if="tabType === 'setMeal'"
                  key="ingredient_info"
                  label="菜品组成"
                  align="center"
                  width="200"
                >
                  <template slot-scope="scope">
                    <div>{{ scope.row.food_group_name }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="mobile" label="营养元素" align="center" width="200">
                  <template slot-scope="scope">
                    <div
                      class="ps-flex-align-c flex-wrap flex-justify-c"
                      :style="{ color: scope.row.food_nutrition_status ? 'red' : '' }"
                    >
                      {{ scope.row.ingredient_category_count + '类' }}/
                      <div v-for="(item, index) in mainNutritionList" :key="index">
                        {{
                          (index === mainNutritionList.length - 1 &&
                            item.name + ':' + scope.row.main_nutrition[item.key] + item.unit) ||
                            item.name + ':' + scope.row.main_nutrition[item.key] + item.unit + '/'
                        }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="settingStocksNum" label="库存" align="center" width="150">
                  <template slot-scope="scope">
                    <el-select
                      v-if="scope.row.settingStocksNum === -1"
                      class="ps-select"
                      popper-class="ps-popper-select"
                      v-model="scope.row.settingStocksNum"
                      placeholder="请选择"
                      style="width:120px;"
                      @change="changeFoodNum(scope.row)"
                      :disabled="tableDisableSelect(scope.row)"
                    >
                      <el-option label="无数量限制" :value="-1"></el-option>
                      <el-option label="限制数量" :value="0"></el-option>
                    </el-select>
                    <el-input
                      v-else
                      v-model="scope.row.settingStocksNum"
                      size="small"
                      style="width: 120px;margin: 0 10px"
                      @change="changeFoodNum(scope.row)"
                      :disabled="tableDisableSelect(scope.row)"
                    >
                      <i slot="suffix" class="el-input__icon el-icon-circle-close" style="cursor: pointer;" @click="inputClearStock(scope.row)"></i>
                    </el-input>
                    <!-- <el-input-number
                      style="width: 130px;margin: 0 10px"
                      size="small"
                      :min="0"
                      placeholder="请输入库存"
                      :disabled="tableDisableSelect(scope.row)"
                      v-model="scope.row.settingStocksNum"
                      @change="changeFoodNum(scope.row)"
                    ></el-input-number> -->
                    <!-- <span class="warn-text">{{ scope.row.result }}</span> -->
                  </template>
                </el-table-column>
                <el-table-column
                  prop="selectedFoodBuyLimitNum"
                  label="限制单人可点数量"
                  align="center"
                  width="150"
                >
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.selectedFoodBuyLimitNum"
                      size="small"
                      style="width: 100px;margin: 0 10px"
                      @change="changeFoodBuyLimitNum(scope.row)"
                      :disabled="tableDisableSelect(scope.row)"
                    ></el-input>
                    <!-- <span class="warn-text">{{ scope.row.result }}</span> -->
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 start -->
              <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage"
                  :page-size="pageSize"
                  layout="prev, pager, next, total, jumper"
                  :total="tableData.length"
                  background
                  class="ps-text"
                  popper-class="ps-popper-select"
                ></el-pagination>
              </div>
              <!-- 分页 end -->
            </div>
            <!-- table end -->
            <!-- 开启营养分析 -->
            <div class="nutrition-box" v-if="isNutritionGuidance && tabType === 'food'">
              <div>营养摄入统计</div>
              <div class="percent_box">
                <el-progress
                  type="circle"
                  class="percent"
                  :percentage="
                    totleNutritionInfo.percentsEnergyKcal >= 100
                      ? 100
                      : totleNutritionInfo.percentsEnergyKcal
                  "
                  color="#ff9b45"
                  :stroke-width="7"
                  :show-text="false"
                ></el-progress>
                <div class="text_box">
                  <div class="text">
                    <span style="color:#ff9b45;">{{ totleNutritionInfo.reportEnergyKcal }}</span>
                    /{{ needNutritionInfo.energy_kcal }}
                  </div>
                  <div class="tips">总能量kacal</div>
                </div>
              </div>
              <div class="analysis-box">
                <div class="analysis-strip">
                  <span
                    class="tag"
                    :style="{
                      backgroundColor: `${
                        percentColorName(totleNutritionInfo.percentsCarbohydrate).color
                      }`
                    }"
                  >
                    {{ percentColorName(totleNutritionInfo.percentsCarbohydrate).name }}
                  </span>

                  <span class="tips">总碳水</span>
                  <el-progress
                    :percentage="
                      totleNutritionInfo.percentsCarbohydrate >= 100
                        ? 100
                        : totleNutritionInfo.percentsCarbohydrate
                    "
                    :show-text="false"
                    :color="percentColorName(totleNutritionInfo.percentsCarbohydrate).color"
                    class="progress-content"
                  ></el-progress>
                  <span class="progress-data">
                    <span>{{ totleNutritionInfo.reportCarbohydrate }}</span>
                    /{{ needNutritionInfo.carbohydrate }}g
                  </span>
                </div>
                <div class="analysis-strip">
                  <span
                    class="tag"
                    :style="{
                      backgroundColor: `${
                        percentColorName(totleNutritionInfo.percentsProtein).color
                      }`
                    }"
                  >
                    {{ percentColorName(totleNutritionInfo.percentsProtein).name }}
                  </span>
                  <span class="tips">总蛋白质</span>
                  <el-progress
                    :percentage="
                      totleNutritionInfo.percentsProtein >= 100
                        ? 100
                        : totleNutritionInfo.percentsProtein
                    "
                    :show-text="false"
                    :color="percentColorName(totleNutritionInfo.percentsProtein).color"
                    class="progress-content"
                  ></el-progress>
                  <span class="progress-data">
                    <span>{{ totleNutritionInfo.reportProtein }}</span>
                    /{{ needNutritionInfo.protein }}g
                  </span>
                </div>
                <div class="analysis-strip">
                  <span
                    class="tag"
                    :style="{
                      backgroundColor: `${
                        percentColorName(totleNutritionInfo.percentsAxunge).color
                      }`
                    }"
                  >
                    {{ percentColorName(totleNutritionInfo.percentsAxunge).name }}
                  </span>
                  <span class="tips">总脂肪</span>
                  <el-progress
                    :percentage="
                      totleNutritionInfo.percentsAxunge >= 100
                        ? 100
                        : totleNutritionInfo.percentsAxunge
                    "
                    :color="percentColorName(totleNutritionInfo.percentsAxunge).color"
                    :show-text="false"
                    class="progress-content"
                  ></el-progress>
                  <span class="progress-data">
                    <span>{{ totleNutritionInfo.reportAxunge }}</span>
                    /{{ needNutritionInfo.axunge }}g
                  </span>
                </div>
              </div>
              <div class="title-table">食物多样性</div>
              <el-table
                v-if="totleNutritionInfo.foodDiversityNutrition"
                :data="totleNutritionInfo.foodDiversityNutrition"
                ref="progressTableData"
                style="width: 100%"
                stripe
                header-row-class-name="ps-table-header-row"
                class="ps-table"
              >
                <el-table-column prop="name" label="类别" align="center"></el-table-column>
                <el-table-column
                  prop="needValue"
                  label="推荐摄入量"
                  align="center"
                ></el-table-column>
                <el-table-column prop="value" label="当前摄入量" align="center"></el-table-column>
              </el-table>
            </div>
          </div>

          <div class="p-r-20 p-l-20" v-if="tabType === 'food'">
            <span class="p-r-20">已选菜品：{{ selectFoodIds.length }}</span>
            <span>总人数：{{ collectiveNumber }}人</span>
          </div>
          <div class="ps-flex-bw p-t-10 p-r-20 p-l-20 p-b-40">
            <div v-if="tabType === 'food'" style="flex:1">
              <span>菜品提醒：</span>
              <el-tag
                class="m-r-10 m-b-10"
                size="small"
                type="warning"
                v-for="(item, index) in foodsTips"
                :key="index"
              >
                <el-popover placement="top" trigger="hover">
                  <div>
                    {{ item.tips }}
                  </div>
                  <i slot="reference" class="el-icon-warning  ps-i"></i>
                </el-popover>
                {{ item.name }}
              </el-tag>
            </div>
            <!-- 位置不同 -->
            <div class="p-r-20 p-l-20" v-if="tabType === 'setMeal'">
              <span class="p-r-20">已选菜品：{{ selectFoodIds.length }}</span>
              <span>总人数：{{ collectiveNumber }}人</span>
            </div>
            <div>
              <el-button class="ps-cancel-btn" size="small" @click="closeHandler">返 回</el-button>
              <el-button
                class="ps-btn"
                size="small"
                type="primary"
                @click="confirmEditMeal('determine')"
              >
                确 定
              </el-button>
              <el-button
                class="ps-green-btn"
                size="small"
                type="primary"
                v-if="showMealContinue()"
                @click="confirmEditMeal('continue')"
              >
                继续配餐
              </el-button>
              <!-- {{currentEditMealType}}
              {{currentEditDate}}
              {{mealDailyDataList[mealDailyDataList.length -1]}}

              {{mealDailyDataList}} -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, deepClone, to } from '@/utils'
import { MENUCATERINGFOOD, MENUCATERINGSETMEAL } from './constants'
import merge from 'webpack-merge'
import NP from 'number-precision'
export default {
  name: 'MenuCatering',
  inject: ['reload'],
  data() {
    return {
      tabType: 'food',
      treeLoading: false,
      treeList: [],
      treeFilterText: '',
      treeProps: {
        children: 'food_category',
        label: 'name'
      },
      searchForm: {
        category_id: ''
      },
      checkList: [],
      tableData: [], // 列表数据
      pageSize: 8, // 每页数量
      totalCount: 0, // 总条数
      totalPageSize: 0, // 总页数
      currentPage: 1, // 第几页
      isLoading: false,
      searchFormSetting: deepClone(MENUCATERINGFOOD),
      setMealSelectAll: false, // 全选
      setMealAllValue: -1,
      setMealAllFoodBuyLimitNum: '',
      progressTableData: [],
      isNutritionGuidance: false, // 是否开启分析
      currentEditData: {}, // 传进来的数据
      selectFoodIds: [], // 菜品选择后的id
      allFoodList: {}, // 选中的数据
      // selectFoodInfoList: [], // 目前这个字段做健康的 每一条数据 勾选
      setMealSelectFoodIds: [], // 套餐选择后的id
      setMealAllFoodList: {}, // 套餐选中的数据
      setMealFoodDisplay: false, // 套餐包含菜品不单独显示
      regNum: /^\d+$/,
      foodsTips: [], // 菜品提醒
      mainNutritionList: [
        {
          name: '能量',
          key: 'energy_kcal',
          value: 0,
          unit: 'kcal'
        },
        {
          name: '脂肪',
          key: 'axunge',
          value: 0,
          unit: 'g'
        },
        {
          name: '碳水化物',
          key: 'carbohydrate',
          value: 0,
          unit: 'g'
        },
        {
          name: '蛋白质',
          key: 'protein',
          value: 0,
          unit: 'g'
        }
      ],
      setMealList: [],
      foodsList: [],
      collectiveNumber: 0, // 总人数
      needNutritionInfo: {},
      mealDailyData: {}, // 请求所有接口的日期
      menuId: this.$route.query.menuId, // 菜品id
      menuType: this.$route.query.menuType, // 月份，周
      currentEditDate: this.$route.query.currentEditDate, // 日期
      currentEditMealType: this.$route.query.currentEditMealType, // 餐段
      analysisFoodId: '', // 营养分析 明细 修改跳过来 需要高亮 单条
      isFirst: true // 判断是否第一次进入
    }
  },
  computed: {
    totleNutritionInfo() {
      return this.inNutrition()
    },
    selectFoodInfoList() {
      let list = []
      this.tableData.forEach(item => {
        if (this.selectFoodIds.includes(item.id)) {
          list.push(item)
        }
      })
      return list
    }
  },
  watch: {
    treeFilterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    // 营养分析 明细 修改跳过来 需要高亮 单条
    if (this.$route.query.food_id) {
      this.analysisFoodId = this.$route.query.food_id
    }
    if (this.$route.query.isNutritionGuidance === 'true') {
      this.isNutritionGuidance = true
      this.getMealTypeNutrition()
    }
    // 判断继续配餐是否显示 最后一天 日期
    this.mealDailyData = JSON.parse(sessionStorage.getItem('mealDailyData'))
    this.currentEditData = this.$decodeQuery(this.$route.query.data)
    this.initSelectData()
    this.initSetMealSelectData()
    this.initLoad()
    this.getMenuFoodList()
    this.setMealAllSetMealList()
  },
  mounted() {},
  methods: {
    inNutrition() {
      let totalData = {
        reportEnergyKcal: 0,
        reportCarbohydrate: 0,
        reportProtein: 0,
        reportAxunge: 0,
        // 百分比
        percentsEnergyKcal: 0,
        percentsCarbohydrate: 0,
        percentsProtein: 0,
        percentsAxunge: 0,
        // 食物多样性
        foodDiversityNutrition: [
          {
            name: '谷物',
            needValue: this.needNutritionInfo.cereals,
            value: 0,
            key: 'cereals'
          },
          {
            name: '鱼禽肉蛋',
            needValue: this.needNutritionInfo.eggsandmeat,
            value: 0,
            key: 'eggsandmeat'
          },
          {
            name: '水果',
            needValue: this.needNutritionInfo.fruit,
            value: 0,
            key: 'fruit'
          },
          {
            name: '蔬菜',
            needValue: this.needNutritionInfo.vegetable,
            value: 0,
            key: 'vegetable'
          }
        ]
      }
      if (this.selectFoodInfoList && this.selectFoodInfoList.length) {
        //  100 * 数量 * 对应菜品营养字段 100 暂时不乘
        this.selectFoodInfoList.forEach(item => {
          let stocksNum = item.settingStocksNum > 0 ? item.settingStocksNum : 0 // 库存无限制（-1）按照0来算
          // 总数
          totalData.reportEnergyKcal = NP.plus(
            totalData.reportEnergyKcal,
            NP.times(item.main_nutrition.energy_kcal, stocksNum)
          )
          totalData.reportAxunge = NP.plus(
            totalData.reportAxunge,
            NP.times(item.main_nutrition.axunge, stocksNum)
          )
          totalData.reportCarbohydrate = NP.plus(
            totalData.reportCarbohydrate,
            NP.times(item.main_nutrition.carbohydrate, stocksNum)
          )
          totalData.reportProtein = NP.plus(
            totalData.reportProtein,
            NP.times(item.main_nutrition.protein, stocksNum)
          )
          // 食物类别
          totalData.foodDiversityNutrition.forEach(foodDiversityNutritionItem => {
            foodDiversityNutritionItem.value += parseInt(
              item.food_diversity_nutrition[foodDiversityNutritionItem.key] * stocksNum
            )
          })
        })
        if (Object.keys(this.needNutritionInfo) && Object.keys(this.needNutritionInfo).length) {
          totalData.percentsEnergyKcal = parseInt(
            (totalData.reportEnergyKcal / this.needNutritionInfo.energy_kcal) * 100
          )
          totalData.percentsCarbohydrate = parseInt(
            (totalData.reportCarbohydrate / this.needNutritionInfo.carbohydrate) * 100
          )
          totalData.percentsProtein = parseInt(
            (totalData.reportProtein / this.needNutritionInfo.protein) * 100
          )
          totalData.percentsAxunge = parseInt(
            (totalData.reportAxunge / this.needNutritionInfo.axunge) * 100
          )
        }
      }
      return totalData
    },
    //  营养样式
    percentColorName(nutritionData) {
      let params = {
        color: '',
        name: ''
      }
      // 不足：小于80%；
      // 适宜：80%~120%；
      // 过量：大于120%
      if (parseInt(nutritionData) >= 120) {
        //  红色
        params.color = '#ea5b55'
        params.name = '过量'
      } else if (parseInt(nutritionData) >= 80) {
        params.color = '#5dbf6e'
        params.name = '适宜'
      } else if (parseInt(nutritionData) < 80) {
        params.color = '#e89e42'
        params.name = '不足'
      }
      return params
    },
    // 初始化
    initLoad() {
      this.$nextTick(() => {
        this.$refs.tableData.clearSelection()
      })
      if (this.tabType === 'food') {
        // 拿到菜品id
        this.searchFormSetting = deepClone(MENUCATERINGFOOD)
        // tree树结构
        this.getMenuFoodSortList()
        // 获取菜品标签
        this.getAllLabelGroupList()
        // 菜品列表数据
        // this.getMenuFoodList()
      } else {
        // 套餐
        this.searchFormSetting = deepClone(MENUCATERINGSETMEAL)
        // 套餐初始化下上次选中的数据
        this.getSetMealCategoryList()
        // 套餐数据
        // this.setMealAllSetMealList()
      }
    },
    // 加载接口
    initFunLoad() {
      this.currentPage = 1
      this.$nextTick(() => {
        this.$refs.tableData.clearSelection()
      })
      if (this.searchForm.category_id) {
        this.resetTreeFoodNumber(this.searchForm.category_id)
      } else {
        this.resetTreeFoodNumber()
      }
      if (this.tabType === 'food') {
        // 菜品列表数据
        this.getMenuFoodList()
      } else {
        // 套餐数据
        this.setMealAllSetMealList()
      }
    },
    initSelectData() {
      if (this.currentEditData && this.currentEditData.food_data) {
        // this.selectFoodInfoList = this.currentEditData.food_data
        this.selectFoodIds = this.currentEditData.food_data.map(food => {
          return food.id
        })
      }
    },
    // 套餐初始化下上次选中的数据
    initSetMealSelectData() {
      if (this.currentEditData && this.currentEditData.set_meal_data) {
        this.setMealSelectFoodIds = this.currentEditData.set_meal_data.map(food => {
          return food.id
        })
      }
      this.setMealFoodDisplay = this.currentEditData.set_meal_food_display
    },
    // 剔除已删除菜品的id
    setCurrentHasFoodIds(ids) {
      let nowSelectIds = this.selectFoodIds.filter(v => ids.includes(v))
      this.selectFoodIds = nowSelectIds
    },
    // 剔除已删除套餐的id
    setCurrentHasMealIds(ids) {
      let nowSelectIds = this.setMealSelectFoodIds.filter(v => ids.includes(v))
      this.setMealSelectFoodIds = nowSelectIds
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.searchForm.category_id = ''
      this.currentPage = 1
      this.$refs.tableData.clearSelection()
      this.initLoad()
      this.initFunLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initFunLoad()
    }, 300),
    // 获取菜品列表
    async getMenuFoodList() {
      const params = {
        page: 1,
        page_size: 99999,
        id: this.menuId,
        menu_type: this.menuType,
        ...this.formatQueryParams(this.searchFormSetting)
      }
      if (this.searchForm.category_id) {
        params.category = this.searchForm.category_id
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodMenuFoodListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 确保第一次进来才调用这接口
        if (
          !this.foodsList.length &&
          !this.searchForm.category_id &&
          !this.searchFormSetting.name.value &&
          !this.searchFormSetting.count_type.value &&
          !this.searchFormSetting.label_filter.value &&
          !this.searchFormSetting.label_list.value.length
        ) {
          this.isFirst = false
          let currentIds = res.data.foods.map(v => v.id)
          this.setCurrentHasFoodIds(currentIds)
        }
        let num = 0
        this.collectiveNumber = res.data.collective_number
        this.foodsTips = res.data.foods_tips
        this.foodsList = res.data.foods
          .map(d => {
            let data = {
              category: d.category,
              name: d.food_name,
              selected: this.selectFoodIds.includes(d.id),
              id: d.id,
              // eslint-disable-next-line camelcase
              settingStocksNum: this.currentEditData?.setting_stocks?.[d.id] >= 0 ? this.currentEditData?.setting_stocks?.[d.id] : -1,
              // eslint-disable-next-line camelcase
              selectedFoodBuyLimitNum: this.currentEditData?.food_buy_limit?.[d.id] || 0, // 单个限制全选可点
              // eslint-disable-next-line camelcase
              stock: this.currentEditData?.current_stocks?.[d.id] || 0,
              status: 1,
              ingredient_category_count: d.ingredient_category_count,
              ingredient_info: d.ingredient_info,
              main_nutrition: d.main_nutrition,
              food_diversity_nutrition: d.food_diversity_nutrition,
              weight: d.weight,
              recommend_food: d.recommend_food,
              // 179 从营养明细点进来,需要标示出来
              food_nutrition_status: 0
            }
            // 从营养明细点进来,需要标示出来
            if (d.id === Number(this.analysisFoodId)) {
              data.food_nutrition_status = 1
            }
            let selectItem = this.allFoodList[data.id.toString()]
            if (data.selected && selectItem) {
              // 同步下库存
              if (selectItem.settingStocksNum > data.settingStocksNum) {
                data.settingStocksNum = selectItem.settingStocksNum
              }
              if (selectItem.selectedFoodBuyLimitNum > data.selectedFoodBuyLimitNum) {
                data.selectedFoodBuyLimitNum = selectItem.selectedFoodBuyLimitNum
              }
            }
            // 勾选table
            if (this.selectFoodIds.includes(d.id)) {
              num += 1
              this.$nextTick(() => {
                // this.categorySelectNumber('all')
                this.$refs.tableData.toggleRowSelection(data)
              })
            }
            return data
          })
          .sort((a, b) => {
            // 要先排序已经选择过的
            let min = a.selected.toString().charCodeAt() - b.selected.toString().charCodeAt()
            return min > 0 ? -1 : 1
          })
          .sort((a, b) => {
            // 再排序从营养明细那边点过来修改的
            let min =
              a.food_nutrition_status.toString().charCodeAt() -
              b.food_nutrition_status.toString().charCodeAt()
            return min > 0 ? -1 : 1
          })
        if (this.tabType === 'food') {
          this.tableData = this.foodsList
          setTimeout(() => {
            this.categorySelectNumber('all')
          }, 200)
        }
        // 全选
        if (this.tabType === 'food' && num && num === this.foodsList.length) {
          this.setMealSelectAll = true
        } else {
          this.setMealSelectAll = false
        }
        // 第一次获取所有数据时，保存下备用
        //  !this.keyword &&
        // !this.cateVal &&
        if (!this.allFoodList.length && this.foodsList.length) {
          Object.freeze(deepClone(this.foodsList)).forEach(v => {
            this.allFoodList[v.id.toString()] = v
          })
        }
        // this.setMealSelectAll = false
        this.setMealAllValue = -1
        this.setMealAllFoodBuyLimitNum = ''
      } else {
        this.$message.error(res.msg)
      }
    },
    // 套餐列表
    async setMealAllSetMealList() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting)
      }
      if (this.searchForm.category_id) {
        params.category_id = this.searchForm.category_id
      }
      const [err, res] = await to(this.$apis.apiBackgroundFoodSetMealAllSetMealListPost(params))
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (
          !this.setMealList.length &&
          !this.searchForm.category_id &&
          !this.searchFormSetting.name.value
        ) {
          this.isFirst = false
          let currentIds = res.data.map(v => v.id)
          this.setCurrentHasMealIds(currentIds)
        }
        let num = 0
        this.setMealList = res.data
          .map(d => {
            let data = {
              category: d.category,
              name: d.name,
              selected: this.setMealSelectFoodIds.includes(d.id),
              id: d.id,
              // eslint-disable-next-line camelcase
              settingStocksNum: this.currentEditData?.set_meal_setting_stocks?.[d.id] || -1,
              // eslint-disable-next-line camelcase
              selectedFoodBuyLimitNum: this.currentEditData?.set_meal_buy_limit?.[d.id] || 0, // 单个限制全选可点
              // eslint-disable-next-line camelcase
              stock: this.currentEditData?.set_meal_current_stocks?.[d.id] || 0,
              status: 1,
              ingredient_category_count: d.ingredient_category_count,
              main_nutrition: d.main_nutrition,
              food_group_name: ''
            }
            if (d.food_group && d.food_group.length) {
              data.food_group_name = d.food_group.join('、')
            }
            let selectItem = this.setMealAllFoodList[data.id.toString()]
            if (data.selected && selectItem) {
              // 同步下库存
              if (selectItem.settingStocksNum > data.settingStocksNum) {
                data.settingStocksNum = selectItem.settingStocksNum
              }
              if (selectItem.selectedFoodBuyLimitNum > data.selectedFoodBuyLimitNum) {
                data.selectedFoodBuyLimitNum = selectItem.selectedFoodBuyLimitNum
              }
            }
            // 勾选table
            if (this.setMealSelectFoodIds.includes(d.id) && this.tabType === 'setMeal') {
              num += 1
              this.$nextTick(() => {
                this.$refs.tableData.toggleRowSelection(data)
              })
            }
            return data
          })
          .sort((a, b) => {
            let min = a.selected.toString().charCodeAt() - b.selected.toString().charCodeAt()
            return min > 0 ? -1 : 1
          })
        if (this.tabType === 'setMeal') {
          this.tableData = this.setMealList
          setTimeout(() => {
            this.categorySelectNumber('all')
          }, 200)
        }
        // 全选
        if (this.tabType === 'setMeal' && num && num === this.setMealList.length) {
          this.setMealSelectAll = true
        } else {
          this.setMealSelectAll = false
        }
        // !this.setMealCategoryName &&
        // !this.setMealCategoryId &&
        if (
          !this.searchForm.category_id &&
          !this.setMealAllFoodList.length &&
          this.setMealList.length
        ) {
          Object.freeze(deepClone(this.setMealList)).forEach(v => {
            this.setMealAllFoodList[v.id.toString()] = v
          })
        }
        // this.setMealSelectAll = false
        this.setMealAllValue = -1
        this.setMealAllFoodBuyLimitNum = ''
      } else {
        this.$message.error(res.msg)
      }
    },
    // 输入内容禁用
    tableDisableSelect(data) {
      let status = false
      if (this.tabType === 'food') {
        if (!this.selectFoodIds.includes(data.id)) {
          status = true
        } else {
          status = false
        }
      } else if (this.tabType === 'setMeal') {
        if (!this.setMealSelectFoodIds.includes(data.id)) {
          status = true
        } else {
          status = false
        }
      }

      return status
    },
    // 获取组织结构tree表
    async getMenuFoodSortList() {
      this.treeLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodMenuFoodSortListPost())
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let list = res.data.map(v => {
          v.id = v.id + '_' + '1'
          if (v.food_category && v.food_category.length) {
            v.food_category.forEach(item => {
              item.number = 0
            })
          }
          return v
        })
        this.treeList = this.deleteEmptyGroup(list)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分类列表
    async getSetMealCategoryList() {
      this.treeLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodSetMealCategoryListPost({
          page: 1,
          page_size: 9999
        })
      )
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let list = res.data.results.map(v => {
          v.number = 0
          return v
        })
        this.treeList = this.deleteEmptyGroup(list)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.food_category) {
            if (item.food_category.length > 0) {
              traversal(item.food_category)
            } else {
              _that.$delete(item, 'food_category')
            }
          } else {
            _that.$delete(item, 'food_category')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 点击tree node
    treeHandleNodeClick(e, type) {
      // this.$nextTick(() => {
      //   this.$refs.tableData.clearSelection()
      // })
      if (type === 'all') {
        this.searchForm.category_id = ''
        this.initFunLoad()
      } else if (!e.food_category && e.id) {
        if (e.number) {
          e.number = 0
        }
        this.searchForm.category_id = e.id
        this.initFunLoad()
      }
      // this.searchHandle()
    },
    // 过滤tree数据
    filterTreeNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 列表选择单个
    handleSelectionSelect(e, row) {
      const data = this.tableData.find(d => d.id === row.id)
      data.selected = !data.selected
      if (this.tabType === 'food') {
        if (data.selected && !this.selectFoodIds.includes(row.id)) {
          this.selectFoodIds.push(data.id)
          // this.selectFoodInfoList.push(data)
        } else {
          this.selectFoodIds.splice(this.selectFoodIds.indexOf(data.id), 1)
          // 目前这个字段做健康的 删除某条数据
          // this.selectFoodInfoList.splice(
          //   this.selectFoodInfoList.findIndex(item => item.id === data.id),
          //   1
          // )
        }
        if (this.selectFoodIds.length === this.tableData.length) {
          this.setMealSelectAll = true
        } else {
          this.setMealSelectAll = false
        }
      } else if (this.tabType === 'setMeal') {
        if (data.selected && !this.setMealSelectFoodIds.includes(row.id)) {
          this.setMealSelectFoodIds.push(data.id)
        } else {
          this.setMealSelectFoodIds.splice(this.setMealSelectFoodIds.indexOf(data.id), 1)
        }
        if (this.setMealSelectFoodIds.length === this.tableData.length) {
          this.setMealSelectAll = true
        } else {
          this.setMealSelectAll = false
        }
      }
      this.categorySelectNumber('', data, true)
    },
    // 分类返显
    // cancel 单个取消
    categorySelectNumber(type, data, cancel) {
      let list = []
      if (type === 'all') {
        list = this.tableData
      } else {
        list = [data]
      }
      let treeListClone = deepClone(this.treeList)
      treeListClone.forEach(v => {
        if (v.food_category && v.food_category.length) {
          this.updateTreeChildren(v.id, v.food_category)
          v.food_category.forEach(categoryItem => {
            // 已经选择的数量
            // v.id //菜品分类id
            list.forEach(item => {
              if (this.tabType === 'food') {
                if (this.selectFoodIds.includes(item.id) && categoryItem.id === item.category) {
                  categoryItem.number += 1
                  this.updateTreeChildren(v.id, v.food_category)
                } else if (
                  cancel &&
                  !this.selectFoodIds.includes(item.id) &&
                  categoryItem.id === item.category
                ) {
                  categoryItem.number -= 1
                }
              }
              // 已经选择的数量
            })
          })
        } else if (this.tabType === 'setMeal') {
          list.forEach(item => {
            if (this.setMealSelectFoodIds.includes(item.id) && v.id === item.category) {
              v.number += 1
              // this.updateTreeChildren(v.id, [])
            } else if (
              cancel &&
              !this.setMealSelectFoodIds.includes(item.id) &&
              v.id === item.category
            ) {
              v.number -= 1
            }
            // 已经选择的数量
          })
        }
      })
      if (this.tabType === 'setMeal') {
        this.treeList = treeListClone
        if (this.searchForm.category_id) {
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.searchForm.category_id)
          })
        }
      }
    },
    // 更新tree数据，只能更新子级
    updateTreeChildren(parentId, childrens) {
      this.$nextTick(() => {
        let treeFRef = this.$refs.tree
        treeFRef.updateKeyChildren(parentId, childrens)
      })
      // 更新数据时需要手动设置当前高亮选项
      if (this.searchForm.category_id) {
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.searchForm.category_id)
        })
      }
    },
    // 列表选择 全选
    // 如果要 放开 要处理 selected 和单个选择一样 隐藏了样式
    handleSelectionAll(e) {
      // this.selectFoodIds = e.map(item => {
      //   return item.id
      // })
    },
    // tab 栏点击事件
    tabClick(type) {
      this.searchForm.category_id = ''
      this.tabType = type
      this.tableData = []
      this.initLoad()
      this.initFunLoad()
    },
    setMealhandleCheckChange(status) {
      if (this.tabType === 'food') {
        this.tableData.forEach(d => {
          d.selected = status
          if (status) {
            if (this.allFoodList[d.id.toString()]) {
              this.allFoodList[d.id.toString()] = deepClone(d)
            } else {
              this.allFoodList[d.id.toString()].settingStocksNum = d.settingStocksNum
              this.allFoodList[d.id.toString()].selectedFoodBuyLimitNum = d.selectedFoodBuyLimitNum
            }
            if (!this.selectFoodIds.includes(d.id)) {
              this.selectFoodIds.push(d.id)
              // this.selectFoodInfoList.push(d)
              this.$refs.tableData.toggleRowSelection(d)
            }
          } else {
            // 取消勾选
            this.selectFoodIds.splice(this.selectFoodIds.indexOf(d.id), 1)
            // this.selectFoodInfoList.splice(
            //   this.selectFoodInfoList.findIndex(item => item.id === d.id),
            //   1
            // )
            this.allFoodList[d.id.toString()].selected = false
            this.$refs.tableData.clearSelection()
          }
        })
      } else if (this.tabType === 'setMeal') {
        this.tableData.forEach(d => {
          d.selected = status
          if (status) {
            if (this.setMealAllFoodList[d.id.toString()]) {
              this.setMealAllFoodList[d.id.toString()] = deepClone(d)
            } else {
              this.setMealAllFoodList[d.id.toString()].settingStocksNum = d.settingStocksNum
              this.setMealAllFoodList[d.id.toString()].selectedFoodBuyLimitNum =
                d.selectedFoodBuyLimitNum
            }
            if (!this.setMealSelectFoodIds.includes(d.id)) {
              this.setMealSelectFoodIds.push(d.id)
              this.$refs.tableData.toggleRowSelection(d)
            }
          } else {
            this.setMealSelectFoodIds.splice(this.setMealSelectFoodIds.indexOf(d.id), 1)
            this.setMealAllFoodList[d.id.toString()].selected = false
            this.$refs.tableData.clearSelection()
          }
        })
      }

      if (status) {
        if (this.searchForm.category_id) {
          this.resetTreeFoodNumber(this.searchForm.category_id)
        } else {
          this.resetTreeFoodNumber()
        }
        this.categorySelectNumber('all')
      } else {
        this.resetTreeFoodNumber(this.searchForm.category_id)
      }
    },
    setMealSetAll(isAll) {
      if (this.setMealAllFoodBuyLimitNum && !this.regNum.test(this.setMealAllFoodBuyLimitNum)) {
        return this.$message.error('数量有误，请输入正整数')
      }
      if (this.setMealAllValue !== -1 && (this.setMealAllValue % 1 !== 0 || this.setMealAllValue < 0)) {
        return this.$message.error('数量有误，请输入正整数')
      }
      if (this.setMealAllValue !== -1 && this.setMealAllFoodBuyLimitNum > this.setMealAllValue) {
        return this.$message.error('菜品限制单人数量，不能大于菜品份数')
      }
      this.tableData.forEach(f => {
        f.settingStocksNum = this.setMealAllValue
        f.selectedFoodBuyLimitNum = this.setMealAllFoodBuyLimitNum
        // 一个菜品 一个套餐
        if (this.tabType === 'food') {
          if (!this.selectFoodIds.includes(f.id)) {
            this.selectFoodIds.push(f.id)
          }
          if (this.allFoodList[f.id.toString()]) {
            this.allFoodList[f.id.toString()] = deepClone(f)
          } else {
            this.allFoodList[f.id.toString()].settingStocksNum = f.settingStocksNum
            this.allFoodList[f.id.toString()].selectedFoodBuyLimitNum = f.selectedFoodBuyLimitNum
          }
        } else if (this.tabType === 'setMeal') {
          if (!this.setMealSelectFoodIds.includes(f.id)) {
            this.setMealSelectFoodIds.push(f.id)
          }
          if (this.setMealAllFoodList[f.id.toString()]) {
            this.setMealAllFoodList[f.id.toString()] = deepClone(f)
          } else {
            this.setMealAllFoodList[f.id.toString()].selectedNum = f.selectedNum
            this.setMealAllFoodList[f.id.toString()].selectedFoodBuyLimitNum =
              f.selectedFoodBuyLimitNum
          }
        }
      })
    },
    // 同步下菜品数量
    changeFoodNum(e) {
      if (this.tabType === 'food') {
        this.allFoodList[e.id.toString()].settingStocksNum = e.settingStocksNum
      } else if (this.tabType === 'setMeal') {
        this.setMealAllFoodList[e.id.toString()].settingStocksNum = e.settingStocksNum
      }
    },
    inputClearStock(e) {
      e.settingStocksNum = -1
      if (this.tabType === 'food') {
        this.allFoodList[e.id.toString()].settingStocksNum = -1
      } else if (this.tabType === 'setMeal') {
        this.setMealAllFoodList[e.id.toString()].settingStocksNum = -1
      }
    },
    changeFoodBuyLimitNum(e) {
      if (this.tabType === 'food') {
        this.allFoodList[e.id.toString()].selectedFoodBuyLimitNum = e.selectedFoodBuyLimitNum
      } else if (this.tabType === 'setMeal') {
        this.setMealAllFoodList[e.id.toString()].selectedFoodBuyLimitNum = e.selectedFoodBuyLimitNum
      }
    },
    confirmEditMeal(type) {
      const postData = {
        use_date: this.currentEditDate,
        setting_stock: {},
        set_meal_setting_stock: {}, // 套餐
        food_buy_limit: {},
        set_meal_buy_limit: {}, // 套餐限购
        id: Number(this.$route.query.menuId),
        meal_type: this.currentEditMealType
      }
      if (this.selectFoodIds.length) {
        this.selectFoodIds.forEach(d => {
          postData.setting_stock[d] = Number(this.allFoodList[d].settingStocksNum)
          postData.food_buy_limit[d] = this.allFoodList[d].selectedFoodBuyLimitNum
        })
      }
      if (this.tabType === 'setMeal') {
        postData.set_meal_food_display = this.setMealFoodDisplay
      }
      // 套餐
      if (this.setMealSelectFoodIds.length) {
        this.setMealSelectFoodIds.forEach(d => {
          postData.set_meal_setting_stock[d] = Number(this.setMealAllFoodList[d].settingStocksNum)
          postData.set_meal_buy_limit[d] = this.setMealAllFoodList[d].selectedFoodBuyLimitNum
        })
      }
      postData.setting_stock = JSON.stringify(postData.setting_stock)
      postData.food_buy_limit = JSON.stringify(postData.food_buy_limit)
      // 套餐
      postData.set_meal_setting_stock = JSON.stringify(postData.set_meal_setting_stock)
      postData.set_meal_buy_limit = JSON.stringify(postData.set_meal_buy_limit)
      let data = this.selectFoodIds.every(f => {
        if (this.allFoodList[f].selectedFoodBuyLimitNum && !this.regNum.test(this.allFoodList[f].selectedFoodBuyLimitNum)) {
          this.$message.error('数量有误，请输入正整数')
          return false
        }
        if (this.allFoodList[f].settingStocksNum === -1) {
          return true
        } else if (!this.regNum.test(this.allFoodList[f].settingStocksNum)) {
          this.$message.error('数量有误，请输入正整数')
          return false
        } else if (this.allFoodList[f].settingStocksNum !== -1 && this.allFoodList[f].selectedFoodBuyLimitNum > this.allFoodList[f].settingStocksNum) {
          this.$message.error('菜品限制单人数量，不能大于菜品份数')
          return false
        } else {
          return true
        }
      })
      let setMealdata = this.setMealSelectFoodIds.every(f => {
        if (this.setMealAllFoodList[f].selectedFoodBuyLimitNum && !this.regNum.test(this.setMealAllFoodList[f].selectedFoodBuyLimitNum)) {
          this.$message.error('数量有误，请输入正整数')
          return false
        }
        if (this.setMealAllFoodList[f].settingStocksNum === -1) {
          return true
        } else if (!this.regNum.test(this.setMealAllFoodList[f].settingStocksNum)) {
          this.$message.error('数量有误，请输入正整数')
          return false
        } else if (this.setMealAllFoodList[f].settingStocksNum !== -1 && this.setMealAllFoodList[f].selectedFoodBuyLimitNum > this.setMealAllFoodList[f].settingStocksNum) {
          this.$message.error('套餐限制单人数量，不能大于套餐份数')
          return false
        } else {
          return true
        }
      })
      if (data && setMealdata) {
        this.getFoodMenuMenuFoodModify(postData, type)
      }
    },
    // 最后一个日期 最后一个餐段 不显示继续配餐
    showMealContinue() {
      let dateList = Object.keys(this.mealDailyData)
      let status = true
      if (this.currentEditMealType === 'morning') {
        if (dateList.indexOf(this.currentEditDate) === dateList.length - 1) {
          status = false
        }
      }
      return status
    },
    // 继续配餐
    initContinueCatering(menuDailyData) {
      // 当前餐段
      // let menuType = this.$route.query.menuType
      // let menuId = this.$route.query.menuId
      // let currentEditMealType = this.currentEditMealType
      // let currentEditDate = this.$route.query.currentEditDate
      let meal = ['breakfast', 'lunch', 'afternoon', 'dinner', 'supper', 'morning']
      let dateList = Object.keys(menuDailyData)
      let foodsData = {}
      // 餐段最后一个
      if (this.currentEditMealType === 'morning') {
        if (dateList.indexOf(this.currentEditDate) === dateList.length - 1) {
          return this.$message.error('已经是最后一天了')
        } else {
          this.currentEditMealType = 'breakfast'
          this.currentEditDate = dateList[this.currentEditDate.indexOf(this.currentEditDate) + 1]
        }
      } else {
        this.currentEditMealType = meal[meal.indexOf(this.currentEditMealType) + 1]
      }
      if (
        menuDailyData[this.currentEditDate].foods &&
        menuDailyData[this.currentEditDate].foods.length
      ) {
        menuDailyData[this.currentEditDate].foods.forEach(v => {
          if (v.meal_type === this.currentEditMealType) {
            foodsData = v
          }
        })
      }
      // 修改id
      this.$router.replace({
        query: merge(this.$route.query, {
          isNutritionGuidance: this.isNutritionGuidance ? 'true' : 'false',
          currentEditDate: this.currentEditDate,
          currentEditMealType: this.currentEditMealType,
          data: this.$encodeQuery(foodsData)
        })
      })
      if (Object.keys(foodsData) && Object.keys(foodsData).length) {
        this.reload()
      }
    },
    clickContinueCatering() {
      if (this.$route.query.menuType === 'week') {
        this.getweekDetail()
      } else {
        this.getMonthlyDetail()
      }
    },
    // 获取单个月份
    async getMonthlyDetail(currentEditMealType, currentEditDate) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodMenuMonthlyMonthlyDetailPost({
          id: this.menuId
          // meal_type: currentEditMealType,
          // use_date: currentEditDate
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.initContinueCatering(res.data.daily_data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取周
    async getweekDetail(currentEditMealType, currentEditDate) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodMenuWeeklyWeeklyDetailPost({
          id: this.menuId
          // meal_type: currentEditMealType,
          // use_date: currentEditDate
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.initContinueCatering(res.data.daily_data)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getFoodMenuMenuFoodModify(data, type) {
      this.isLoading = true
      let [err, res] = ''
      if (this.$route.query.menuType === 'week') {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodMenuWeeklyMenuFoodModifyPost(data))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodMenuMonthlyMenuFoodModifyPost(data))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('操作成功！')
        // 继续配餐 不用返回
        if (type === 'continue') {
          this.clickContinueCatering()
        } else {
          this.$closeCurrentTab(this.$route.path)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取所有的标签
    async getAllLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyLabelGroupAllLabelGroupListPost({
          is_admin: true,
          type: 'food',
          page: 1,
          page_size: 999999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(v => {
          v.id = `${v.id}_1`
          if (!v.label_list.length) {
            v.isDisabled = true
          }
          return v
        })
        this.searchFormSetting.label_list.dataList = res.data.results
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 重置tree food number 数据
    resetTreeFoodNumber(categoryId) {
      this.treeList.forEach(v => {
        if (this.tabType === 'food' && v.food_category && v.food_category.length) {
          v.food_category.forEach(item => {
            if (categoryId) {
              if (item.id === categoryId) {
                item.number = 0
              }
            } else {
              item.number = 0
            }
          })
        } else if (this.tabType === 'setMeal') {
          // 套餐的时候
          if (categoryId) {
            if (v.id === categoryId) {
              v.number = 0
            }
          } else {
            v.number = 0
          }
        }
      })
    },
    // 所需要的营养
    async getMealTypeNutrition() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodMenuMealTypeNutritionPost({
          id: this.menuId,
          menu_type: this.menuType,
          date: this.currentEditDate,
          meal_type: this.currentEditMealType
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.needNutritionInfo = res.data.need_nutrition
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 导入菜品/商品
    importHandler(type) {
      this.$router.push({
        name: 'MerchantImportCommodity',
        params: {
          type: type
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      // this.getAccountList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      // this.getAccountList()
    }
    // format(){
    //   return 123
    // },
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

#menu-catering {
  display: flex;
  .account-list {
    flex: 1;
    min-width: 0;
    .status-s {
      border-radius: 25px;
      display: inline-block;
      padding: 2px 10px 2px 15px;
      color: #ffffff;
      position: relative;
      font-size: 12px;
      line-height: 1.2;
      &.yellow {
        background-color: #49d498;
      }
      &.red {
        background-color: #fd594e;
      }
      &::before {
        content: ' ';
        position: absolute;
        left: 7px;
        top: 50%;
        transform: translateY(-50%);
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: #ffffff;
      }
    }
  }
  .custom-tree-node {
    font-size: 14px;
  }
  .tab {
    margin-bottom: 20px;
    .tab-item {
      display: inline-block;
      width: 90px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 14px;
      border: solid 1px #dae1ea;
      font-size: 16px;
      color: #7b7c82;
      vertical-align: middle;
      cursor: pointer;
      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }
    }
  }
  .table-box {
    width: 60%;
  }
  .nutrition-box {
    flex: 1;
    padding: 0px 10px;
    .percent_box {
      position: relative;
      text-align: center;
    }
    .text_box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .text {
        font-size: 18px;
      }
      .tips {
        font-size: 12px;
        color: #7b7c82;
      }
    }
    .analysis-box {
      .analysis-strip {
        display: flex;
        align-items: center;
        padding-top: 15px;
        .progress-content {
          flex: 1;
        }
        .tag {
          font-size: 12px;
          color: #fff;
          background-color: #ef9a50;
          padding: 2px 5px;
          border-radius: 10px;
          margin-right: 10px;
        }
        .tips {
          font-size: 13px;
          width: 65px;
        }
        .progress-data {
          margin-left: 5px;
          font-size: 13px;
        }
      }
    }
    .title-table {
      padding: 10px 0;
    }
  }

  .el-table__header-wrapper .el-checkbox {
    display: none;
  }
}
</style>

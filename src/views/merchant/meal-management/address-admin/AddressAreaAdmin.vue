<template>
  <div class="AddressAreaAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('add')">新增</button-icon>
          <button-icon color="plain" @click="openDialog('modify')">区域编辑</button-icon>
          <button-icon color="plain" type="del" @click="mulOperation('mulDel')">批量删除</button-icon>
          <button-icon color="plain" @click="openDialog('PrintTicket')">小票打印</button-icon>
          <button-icon color="plain" type="mul" @click="openDialog('copy')">批量复制</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :row-class-name="tableRowClassName"
          @selection-change="handleSelectionChange"
          :span-method="objectSpanMethod"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column prop="name" label="配送区域" align="center"></el-table-column>
          <el-table-column prop="organization_name" label="组织" align="center"></el-table-column>
          <el-table-column prop="l0" label="一级" align="center"></el-table-column>
          <el-table-column prop="l1" label="二级" align="center"></el-table-column>
          <el-table-column prop="l2" label="三级" align="center"></el-table-column>
          <el-table-column prop="l3" label="四级" align="center"></el-table-column>
          <el-table-column prop="l4" label="五级" align="center"></el-table-column>
          <el-table-column width="80" label="操作" align="center" fixed="right">
            <template slot-scope="scope">
              <!-- <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDialog('modify', scope.row)"
              >
                编辑
              </el-button> -->
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="mulOperation('del', scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <!-- 分页 end -->
    <address-area-dialog
      :isshow.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :width="dialogwidth"
      :address-info="addressInfo"
      :select-list-id="selectListId"
      :confirm="searchHandle"
    />
    <print-ticket
      :isshow.sync="dialogPrintVisible"
      type="addressArea"
      title="小票打印"
      :select-list-id="selectListId"
      :confirm="searchHandle"
    ></print-ticket>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
import AddressAreaDialog from './components/AddressAreaDialog.vue'
import PrintTicket from '@/components/PrintTicket'
export default {
  name: 'AddressAreaAdmin',
  components: { AddressAreaDialog, PrintTicket },
  // mixins: [activatedLoadData],
  data() {
    return {
      tableData: [],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: {
        organization: {
          type: 'organizationSelect',
          multiple: false,
          isLazy: false,
          clearable: true,
          checkStrictly: true,
          label: '组织',
          value: this.$store.getters.organization,
          placeholder: '请选择组织'
        },
        name: {
          type: 'input',
          label: '配送区域',
          value: '',
          placeholder: '请输入配送区域',
          dataList: []
        }
      },
      dialogVisible: false,
      dialogType: '',
      dialogTitle: '',
      dialogwidth: '',
      addressInfo: {}, // 弹窗数据
      selectListId: [],
      selectList: [],
      dialogPrintVisible: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      // this.getAreaList()
      this.getAddressAreaList()
    },
    // 刷新页面
    refreshHandle() {
      this.currentPage = 1
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.dialogPrintVisible = false
      this.currentPage = 1
      this.getAddressAreaList()
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // async getAreaList() {
    //   const res = await this.$apis.apiAddressAddersAreaAllPost({})
    //   console.log(res)
    //   if (res.code === 0) {
    //   }
    // },
    async getAddressAreaList() {
      const res = await this.$apis.apiAddressAddersAreaListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      if (res.code === 0) {
        this.tableData = []
        this.totalCount = res.data.count
        if (!res.data.results) return
        res.data.results.forEach((item, index) => {
          let rowColor // 斑马纹样式
          if (index % 2) {
            rowColor = true
          } else {
            rowColor = false
          }
          for (let i = 0; i < item.adders_centers.length; i++) {
            let itemRowspan = 0 // 合并表格
            if (item.adders_centers.length === 1) {
              itemRowspan = -1
            }
            if (i === 0 && item.adders_centers.length !== 1) {
              itemRowspan = item.adders_centers.length
            }
            this.tableData.push({
              id: item.id,
              name: item.name,
              organization_id: item.organization_id,
              organization_name: item.organization_name,
              address_id: item.adders_centers[i].id,
              l0: item.adders_centers[i].l0,
              l1: item.adders_centers[i].l1,
              l2: item.adders_centers[i].l2,
              l3: item.adders_centers[i].l3,
              l4: item.adders_centers[i].l4,
              rowspan: itemRowspan,
              rowColor
            })
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 合并表格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        if (row.rowspan !== 1) {
          return {
            rowspan: row.rowspan,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getAddressAreaList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getAddressAreaList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      this.selectList = Object.freeze(val) // 解除下监听吧，节约点资源
      this.selectList.map(item => {
        this.selectListId.push(item.id)
      })
    },
    // 操作
    mulOperation(type, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case 'mulDel':
          content = '确定批量删除所选配送区域吗？'
          break
        case 'del':
          content = '确定移除该配送点吗？'
          break
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {}
            switch (type) {
              case 'mulDel':
                params.ids = this.selectListId
                break
              case 'del':
                params.ids = [data.id]
                params.addr_center_id = data.address_id
                break
            }
            this.delAddressArea(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    async delAddressArea(params) {
      const res = await this.$apis.apiAddressAddersAreaDeletePost(params)
      if (res.code === 0) {
        this.$message.success('删除成功')
        this.getAddressAreaList()
      } else {
        this.$message.error(res.msg)
      }
    },
    openDialog(type, data) {
      this.addressInfo = data
      this.dialogType = type
      switch (type) {
        case 'add':
          this.dialogTitle = '新增配送区域'
          this.dialogwidth = '500px'
          this.dialogVisible = true
          break
        case 'modify': {
          if (this.selectListId.length === 0) {
            return this.$message.error("请选择需要编辑的配送区域");
          } else if (this.selectListId.length > 1) {
            return this.$message.error("仅支持编辑单个配送区域");
          }
          let ids = []
          this.tableData.forEach(item => {
            if (this.selectListId[0] === item.id) {
              ids.push(item.address_id)
            }
          })
          this.addressInfo = this.selectList[0]
          this.addressInfo.address_ids = ids
          this.dialogTitle = '编辑配送区域'
          this.dialogwidth = '500px'
          this.dialogVisible = true
          break
        }
        case 'PrintTicket':
          if (!this.selectListId.length) {
            return this.$message.error('请先选择数据！')
          }
          this.dialogPrintVisible = true
          break
        case 'copy':
          this.dialogTitle = '批量复制'
          if (!this.selectListId.length) {
            return this.$message.error('请先选择数据！')
          }
          this.dialogVisible = true
          break
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.AddressAreaAdmin {
  .header-box {
    padding: 10px 0 25px;
    text-align: right;
  }
  .ps-table-tree {
    .ps-table-header-row {
      background: #f5f6fa;
      th {
        background: #f5f6fa;
      }
    }
    .el-tree-node__expand-icon.expanded {
      // transform: rotate(0deg);
    }
    .el-icon-arrow-right {
      vertical-align: middle;
    }
    //有子节点 且未展开
    .el-icon-arrow-right:before {
      background: url('~@/assets/img/zz2.png') no-repeat 0 0px;
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      font-size: 16px;
      background-size: 16px;
    }
    // 0级
    .el-table__row--level-0 .el-icon-arrow-right:before {
      background: url('~@/assets/img/zz4.png') no-repeat 0 0px;
    }
    .el-table__row--level-1 .el-icon-arrow-right:before {
      background: url('~@/assets/img/zz3.png') no-repeat 0 0px;
    }
    //有子节点 且已展开
    .el-table__expand-icon--expanded {
      .el-icon-arrow-right:before {
        background: url('~@/assets/img/zz2.png') no-repeat 0 0;
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        font-size: 16px;
        background-size: 16px;
      }
    }
    .el-table__row--level-0 {
      .el-table__expand-icon--expanded {
        .el-icon-arrow-right:before {
          background: url('~@/assets/img/zz1.png') no-repeat 0 0;
        }
      }
    }
    //没有子节点
    // .el-tree-node__expand-icon.is-leaf::before
    // .el-table__placeholder::before {
    //   // background: url('./images/file.png') no-repeat 0 0;
    //   content: '';
    //   display: block;
    //   width: 16px;
    //   height: 18px;
    //   font-size: 16px;
    //   background-size: 16px;
    // }
    .el-table__expand-icon--expanded {
      transform: rotate(180deg);
    }
  }
  .dialog-select {
    width: 100%;
  }
  .dialog-form {
    max-width: 400px;
  }
  .el-table .cell.el-tooltip {
    text-overflow: ellipsis;
  }
  .no-children {
    .th-row {
      .cell {
        padding-left: 32px;
      }
    }
  }
  .el-table__body-wrapper .tools-row {
    .cell {
      text-align: left;
    }
    .el-button {
      position: relative;
      &:not(:last-child) {
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          right: -8px;
          width: 1px;
          height: 10px;
          background: #ebebeb;
          transform: translateY(-50%);
        }
      }
    }
    .delete {
      color: #ff0000;
    }
  }
  .dataTemplate {
    padding: 0px 0px 20px 40px;
  }
  .importValue {
    padding: 15px 0 10px 40px;
    display: flex;
  }
}
.dialog-footer {
  width: 100%;
  text-align: right;
}
</style>

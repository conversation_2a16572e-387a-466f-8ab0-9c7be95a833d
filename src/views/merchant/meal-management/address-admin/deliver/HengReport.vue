<template>
  <div class="HengReport container-wrapper">
    <!-- search start -->
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
      @reset="resetHandle"
    ></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="handleExport">导出报表</button-icon>
          <button-icon color="plain" @click="openDialog">小票打印</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          show-summary
          :summary-method="getSummaries"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column v-for="(item, index) in tableSetting" :key="index" :label="item" :prop="String(index)" align="center"></el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <print-ticket
      :isshow.sync="dialogPrintVisible"
      type="order"
      title="小票打印"
      :select-list-id="selectOrderId"
      :confirm="searchHandle"
    ></print-ticket>
  </div>
</template>

<script>
import { debounce, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { MEAL_TYPES } from '@/utils/constants'
import PrintTicket from '@/components/PrintTicket'
export default {
  name: 'HengReport',
  components: { PrintTicket },
  mixins: [exportExcel],
  data() {
    return {
      tableData: [],
      totalData: {},
      tableSetting: [''],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: {
        select_date: {
          clearable: false,
          label: '取餐时间',
          type: 'daterange',
          value: getSevenDateRange(1)
        },
        area_ids: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '配送区域',
          dataList: []
        },
        l1_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '一级地址',
          dataList: []
        },
        l2_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '二级地址',
          dataList: []
        },
        l3_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '三级地址',
          dataList: []
        },
        l4_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '四级地址',
          dataList: []
        },
        l5_addr: {
          type: 'select',
          value: [],
          multiple: true,
          clearable: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          label: '五级地址',
          dataList: []
        },
        take_meal_time: {
          type: 'select',
          value: '',
          clearable: true,
          label: '取餐餐段',
          dataList: MEAL_TYPES
        },
        name: {
          type: 'input',
          value: '',
          label: '取餐人',
          placeholder: '请输入'
        },
        phone: {
          type: 'input',
          value: '',
          label: '手机号码',
          placeholder: '请输入手机号码'
        },
        org_ids: {
          type: 'organizationSelect',
          value: [this.$store.getters.organization],
          label: '消费点',
          clearable: true,
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        is_visitor: {
          type: 'checkbox',
          label: '',
          checkboxLabel: '只看游客',
          value: false
        }
      },
      dialogPrintVisible: false,
      selectOrderId: [],
      otherInfo: []
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    'searchFormSetting.org_ids.value': function() {
      this.getAddressAreaList()
    },
    'searchFormSetting.area_ids.value': function() {
      this.searchFormSetting.l1_addr.dataList = []
      this.searchFormSetting.l1_addr.value = []
      this.loadAddress(1, this.searchFormSetting.area_ids.value)
    },
    'searchFormSetting.l1_addr.value': function() {
      this.searchFormSetting.l2_addr.dataList = []
      this.searchFormSetting.l2_addr.value = []
      if (this.searchFormSetting.l1_addr.value.length) {
        this.loadAddress(2)
      }
    },
    'searchFormSetting.l2_addr.value': function() {
      this.searchFormSetting.l3_addr.dataList = []
      this.searchFormSetting.l3_addr.value = []
      if (this.searchFormSetting.l2_addr.value.length) {
        this.loadAddress(3)
      }
    },
    'searchFormSetting.l3_addr.value': function() {
      this.searchFormSetting.l4_addr.dataList = []
      this.searchFormSetting.l4_addr.value = []
      if (this.searchFormSetting.l3_addr.value.length) {
        this.loadAddress(4)
      }
    },
    'searchFormSetting.l4_addr.value': function() {
      this.searchFormSetting.l5_addr.dataList = []
      this.searchFormSetting.l5_addr.value = []
      if (this.searchFormSetting.l4_addr.value.length) {
        this.loadAddress(5)
      }
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getHengList()
      this.getAddressAreaList()
      this.loadAddress(1)
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogPrintVisible = false
      this.currentPage = 1
      this.getHengList()
    }, 300),
    resetHandle() {
      this.searchFormSetting.org_ids.value = [this.$store.getters.organization]
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' || data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.reservation_date_start = data[key].value[0]
            params.reservation_date_end = data[key].value[1]
          }
        }
      }
      return params
    },
    async getHengList() {
      const res = await this.$apis.apiBackgroundOrderOrderReservationGetDeliveryCollectByYPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      if (res.code === 0) {
        if (res.data.results.length) {
          this.totalData = ['', ...res.data.results[0]] // 有个勾选框，合计往后退一个
          this.tableSetting = res.data.results[1] // 后端直接返回了表头数据
          this.tableData = res.data.results.splice(2, res.data.results.length - 2) // 用数据的index做prop
          this.otherInfo = res.data.other_info
        } else {
          this.tableData = []
        }
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getHengList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getHengList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectOrderId = []
      val.map(item => {
        let index = this.tableData.indexOf(item)
        if (this.selectOrderId.indexOf(this.otherInfo[index].order_payment_id) === -1) {
          this.selectOrderId.push(this.otherInfo[index].order_payment_id)
        }
      })
    },
    async getAddressAreaList() {
      const res = await this.$apis.apiAddressAddersAreaAllPost({
        used_org_ids: this.searchFormSetting.org_ids.value
      })
      if (res.code === 0) {
        this.searchFormSetting.area_ids.dataList = [
          {
            name: '未命名区域',
            id: 0
          },
          ...res.data
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态配送点数据
    async loadAddress(level, areaIds) {
      // 这里的level是这样的：一级组织的level=1，传给后端需要-1
      let params = {
        page: 1,
        page_size: 99999,
        level: level - 1,
        used_for_web: true
      }
      if (level === 2) {
        params.parent_id = this.searchFormSetting.l1_addr.value
      } else if (level === 3) {
        params.parent_id = this.searchFormSetting.l2_addr.value
      } else if (level === 4) {
        params.parent_id = this.searchFormSetting.l3_addr.value
      } else if (level === 5) {
        params.parent_id = this.searchFormSetting.l4_addr.value
      }
      if (areaIds) {
        params.area_id = areaIds
      }
      const res = await this.$apis.apiAddressAddersCenterListPost(params)
      if (res.code === 0) {
        if (level === 1) {
          this.searchFormSetting.l1_addr.dataList = res.data.results
        } else if (level === 2) {
          this.searchFormSetting.l2_addr.dataList = res.data.results
        } else if (level === 3) {
          this.searchFormSetting.l3_addr.dataList = res.data.results
        } else if (level === 4) {
          this.searchFormSetting.l4_addr.dataList = res.data.results
        } else if (level === 5) {
          this.searchFormSetting.l5_addr.dataList = res.data.results
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 合计
    getSummaries(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        sums[index] = this.totalData[index]
      });
      return sums;
    },
    // 导出报表
    handleExport() {
      const option = {
        type: 'HengReport',
        url: 'apiBackgroundOrderOrderReservationGetDeliveryCollectByYPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          is_export: true
        }
      }
      this.exportHandle(option)
    },
    openDialog() {
      if (!this.selectOrderId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.dialogPrintVisible = true
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.HengReport {
  // 将合计放到表格最上方
  .el-table {
    display: flex;
    flex-direction: column;
  }
  // order默认值为0，order越大，位置越往后，这样合计行就上移到表格上方
  .el-table__footer-wrapper{
    order: 0;
    overflow-y: scroll;
  }
  .el-table__header-wrapper{
    order: 1;
    overflow-y: scroll;
  }
  .el-table__body-wrapper {
    order: 2;
    overflow-y: scroll;
    max-height: 500px;
  }
}
</style>

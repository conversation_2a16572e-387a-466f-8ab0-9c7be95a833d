<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
    :destroy-on-close="false"
  >
    <el-form
      v-if="visible"
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      ref="dialogForm"
      :rules="dialogFormRules"
      label-width="120px"
    >
      <div v-if="type==='add' || type==='modify'">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入配送点名称" class="ps-input w-250"></el-input>
        </el-form-item>
        <el-form-item label="组织：" prop="org">
          <organization-select
            class="search-item-w ps-input w-250"
            placeholder="请选择所属组织"
            :isLazy="false"
            :multiple="false"
            :check-strictly="true"
            v-model="dialogForm.org"
            :append-to-body="true"
            :disabled="type === 'modify'"
            >
            <!-- @change="getOrganization($event)" -->
          </organization-select>
        </el-form-item>
        <el-form-item label="配送点：" prop="address">
          <address-select
            class="search-item-w ps-input w-250"
            placeholder="请选择配送点"
            :multiple="true"
            v-model="dialogForm.address"
            :orgId="dialogForm.org"
            :append-to-body="true"
            >
          </address-select>
        </el-form-item>
      </div>
      <div v-if="type==='copy'" style="margin-left:90px;">
        <div style="margin-bottom: 20px;font-weight:bold;">请选择需要复制的组织:</div>
        <el-form-item label="" prop="orgs" label-width="0px">
          <organization-select
            class="search-item-w ps-input w-250"
            placeholder="请选择需要复制的组织"
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            v-model="dialogForm.orgs"
            :append-to-body="true"
            >
            <!-- @change="getOrganization($event)" -->
          </organization-select>
        </el-form-item>
        <div style="color: #ff9b45;">非对应组织使用地址会在复制成功后相应剔除</div>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button :disabled="isLoading" class="ps-cancel-btn" @click="clickCancleHandle">取消</el-button>
        <el-button :disabled="isLoading" class="ps-btn" type="primary" @click="clickConfirmHandle">确定</el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import OrganizationSelect from '@/components/OrganizationSelect'
import AddressSelect from '@/components/AddressSelect'
export default {
  name: 'AddressAreaDialog',
  components: {
    AddressSelect,
    OrganizationSelect
  },
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '500px'
    },
    isshow: Boolean,
    confirm: Function,
    addressInfo: {
      type: Object,
      default: () => {}
    },
    selectListId: {
      type: Array,
      default: () => []
    }
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      time: new Date().getTime(),
      dialogForm: {
        name: '',
        org: null,
        address: [],
        orgs: []
      },
      allChildId: [],
      dialogFormRules: {
        name: [{ required: true, message: '请输入组织', trigger: 'change' }],
        org: [{ required: true, message: '请选择所属组织', trigger: 'blur' }],
        address: [{ required: true, message: '请选择配送点', trigger: 'blur' }],
        orgs: [{ required: true, message: '请选择组织', trigger: 'blur' }]
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      this.initLoad()
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        this.dialogForm.name = this.addressInfo.name
        this.dialogForm.org = this.addressInfo.organization_id
        this.dialogForm.address = this.addressInfo.address_ids
      }
    },
    clickConfirmHandle(isWithdrawal) {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          if (this.type === 'add' || this.type === 'modify') {
            this.getAllId()
          } else if (this.type === 'copy') {
            this.cloneAddressArea()
          }
        } else {
        }
      })
    },
    async getAllId() {
      // let queue = []
      let params = {}
      params.organization = this.dialogForm.org
      params.name = this.dialogForm.name
      params.use_points = this.dialogForm.address
      if (this.type === 'add') {
        this.confirmAdd(params)
      } else {
        this.modifyAddress(params)
      }
      // return
      // this.dialogForm.address.map(id => {
      //   queue.push(this.getAllChildren(id))
      // })
      // this.allChildId = []
      // Promise.all(queue).then(() => {
      //   this.allChildId = Array.from(new Set(this.allChildId))
      //   let params = {}
      //   params.organization = this.dialogForm.org
      //   params.name = this.dialogForm.name
      //   params.use_points = this.allChildId
      //   if (this.type === 'add') {
      //     this.confirmAdd(params)
      //   } else {
      //     this.modifyAddress(params)
      //   }
      // }).catch(err => {
      //   console.log(err)
      // });
    },
    // 获取所有的子孙id
    async getAllChildren(id) {
      const res = await this.$apis.apiAddressAddersCenterGetAllChildrenPost({ id })
      if (res.code === 0) {
        this.allChildId = this.allChildId.concat(res.data)
        this.allChildId.push(id)
      } else {
        this.$message.error(res.msg)
      }
    },
    async confirmAdd(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiAddressAddersAreaAddPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.confirm()
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    async modifyAddress(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiAddressAddersAreaModifyPost({
        id: this.addressInfo.id,
        ...params
      })
      this.isLoading = false
      if (res.code === 0) {
        this.confirm()
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    async cloneAddressArea(params) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await this.$apis.apiAddressAddersAreaClonePost({
        ids: this.selectListId,
        used_orgs: this.dialogForm.orgs
      })
      this.isLoading = false
      if (res.code === 0) {
        this.confirm()
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      // this.$refs.dialogForm.resetFields()
      this.dialogForm = {
        name: '',
        org: null,
        address: []
      }
      this.isLoading = false
      this.visible = false
    },
    getOrganization(value) {
      this.dialogForm.org = value
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.addLower{
  padding-left: 30px;
  .addLower_tree {
    .el-tree-node__content{
      height: auto;
      margin-bottom: 20px;
    }
    .el-tree-node__content:hover {
      background-color: transparent;
    }
    .el-tree-node:focus > .el-tree-node__content {
      background-color: transparent !important;
    }
  }
  .label{
    line-height: 40px;
    width: 60px;
    background-color: #f2f2f2;
    border-radius: 0 4px 4px 0;
    text-align: center;
    margin-right: 15px;
    display: inline-block;
    padding: 0 5px;
  }
  .textarea-w{
    width: 350px;
    margin-left: 24px;
  }
}

</style>

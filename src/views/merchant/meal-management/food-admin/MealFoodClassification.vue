<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div id="classification-container">
      <div class="organization-tree">
        <el-input
          v-model="primaryName"
          placeholder="请输入一级分类名称"
          class="tree-search ps-input"
        ></el-input>
        <div v-loading="isLoadingFoodFoodSor">
          <!-- <button-icon color="origin" type="" @click="openImport">批量导入分类</button-icon> -->
          <button-icon color="origin" type="" @click="clickShowDialogClassification('addSort')">
            新建一级分类
          </button-icon>
          <ul
            class="infinite-list"
            :style="{ overflow: 'auto', height: `${classificationBoxHeight}px` }"
          >
            <li v-for="(item, index) in foodFoodSortPrimaryList" :key="index">
              <div class="primary-classification">
                <span>{{ item.name }}</span>
                <div v-if="!item.is_admin" class="ps-flex flex-align-c">
                  <el-button
                    type="text"
                    size="small"
                    class="ps-text"
                    @click="clickShowDialogClassification('editSort', item)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="ps-red"
                    @click="deleteHaldler('delSort', item)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="classification-list">
        <search-form
          ref="searchRef"
          :form-setting="searchFormSetting"
          @search="searchHandle"
        ></search-form>
        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="ps-flex flex-align-c align-r">
            <div class="p-r-20">
              <span class="p-r-10" style="font-size: 15px;font-weight: 600;">系统分类隐藏</span>
              <el-switch v-model="categoryIsEnable" active-color="#ff9b45" @change="changeCategoryIsEnable" inactive-color="#ffcda2"></el-switch>
            </div>
              <button-icon
                color="origin"
                type="add"
                @click="clickShowDialogClassification('addCategory')"
              >
                创建二级分类
              </button-icon>
              <button-icon color="origin" type="mul" @click="deleteHaldler('delBatchCategory')">
                批量删除
              </button-icon>
            </div>
          </div>
          <div class="table-content">
            <!-- table start -->
            <el-table
              v-loading="isLoading"
              :data="tableData"
              ref="tableData"
              style="width: 100%"
              row-key="id"
              stripe
              :row-class-name="tableRowClassName"
              header-row-class-name="ps-table-header-row"
              class="ps-table"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                type="selection"
                :selectable="selectableHandle"
                width="55"
                class-name="ps-checkbox"
              ></el-table-column>
              <el-table-column prop="sort_name" label="一级分类" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.is_admin" style="margin-right: 10px; border: 1px solid #2b2b2b; border-radius: 3px;">系统</span>
                  <span>{{ scope.row.sort_name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="二级分类" align="center">
                <template slot-scope="scope">
                  <span class="status-point" :style="{ backgroundColor: scope.row.color }"></span>
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
              <el-table-column fixed="right" label="操作" width="180" align="center">
                <template slot-scope="scope">
                  <el-button
                    v-if="!scope.row.is_admin"
                    type="text"
                    size="small"
                    class="ps-text"
                    @click="clickShowDialogClassification('editCategory', scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-if="!scope.row.is_admin"
                    type="text"
                    size="small"
                    class="ps-red"
                    @click="deleteHaldler('delCategory', scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- table end -->
          </div>
          <!-- 分页 start -->
          <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="pageSize"
              layout="total, prev, pager, next,sizes,jumper"
              :total="totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- 批量导入 -->
    <el-dialog
      title="批量导入"
      :visible.sync="importShowDialog"
      width="600px"
      custom-class="ps-dialog"
    >
      <import-upload-file
        @publicUrl="publicUrl"
        uploadFormItemLabel="导入分类"
        file-type="zip"
        link="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/9d831119671ac5dd0f34398007cd4b1a1617760590210.zip"
      ></import-upload-file>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="importShowDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="mulImortFace">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 一级分类 -->
    <el-dialog
      :title="dialogClassificationTitle"
      :visible.sync="showDialogSort"
      width="400px"
      custom-class="ps-dialog"
    >
      <el-form
        :model="dialogSortForm"
        @submit.native.prevent
        status-icon
        ref="dialogSortForm"
        :rules="dialogSortFormRules"
        v-loading="formSortLoading"
        label-width="80px"
      >
        <el-form-item label="一级分类" prop="primarySortName">
          <el-input
            class="ps-input"
            style="width: 190px"
            placeholder="请输入一级分类名称"
            v-model="dialogSortForm.primarySortName"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDialogSort = false">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          :loading="formSortLoading"
          @click="determineSortDialog"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
    <!-- 二级分类 -->
    <el-dialog
      :title="dialogClassificationTitle"
      :visible.sync="showDialogCategory"
      width="400px"
      custom-class="ps-dialog"
    >
      <el-form
        :model="dialogCategoryForm"
        @submit.native.prevent
        status-icon
        ref="dialogCategoryForm"
        :rules="dialogCategoryRules"
        v-loading="formCategoryLoading"
        label-width="80px"
      >
        <el-form-item label="一级分类" prop="primarySortId">
          <el-select
            v-model="dialogCategoryForm.primarySortId"
            placeholder="请下拉选择一级分类"
            class="ps-select"
            popper-class="ps-popper-select"
            collapse-tags
            filterable
            clearable
          >
            <el-option
              v-for="item in this.foodFoodSortPrimaryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="二级分类" prop="categoryName">
          <el-input
            v-model="dialogCategoryForm.categoryName"
            class="ps-input"
            style="width: 190px"
          ></el-input>
        </el-form-item>
        <el-form-item label="添加颜色" prop="color">
          <el-color-picker v-model="dialogCategoryForm.color"></el-color-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDialogCategory = false">取 消</el-button>
        <el-button
          class="ps-btn"
          type="primary"
          :loading="formCategoryLoading"
          @click="determineCategoryDialog"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import ImportUploadFile from '@/components/ImportUploadFile'
export default {
  name: 'MealFoodClassification',
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      classificationBoxHeight: '',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '二级分类',
          value: '',
          placeholder: '请输入二级分类名称'
        }
      },
      primaryName: '',
      importShowDialog: false,
      dialogClassificationTitle: '',
      showDialogClassificationType: '',
      showDialogCategory: false,
      showDialogSort: false,
      selectList: [
        { name: '特价', id: '1' },
        { name: '折扣', id: '2' }
      ],
      dialogCategoryForm: {
        primarySortId: '',
        categoryName: '',
        color: '#409EFF'
      },
      dialogCategoryRules: {
        primarySortId: [{ required: true, message: '请选择一级分类', trigger: 'change' }],
        categoryName: [{ required: true, message: '请输入一级分类名称', trigger: 'blur' }]
      },
      dialogSortForm: {
        primarySortName: ''
      },
      dialogSortFormRules: {
        primarySortName: [{ required: true, message: '请输入一级分类名称', trigger: 'blur' }]
      },
      isLoadingFoodFoodSor: false,
      formSortLoading: false,
      formCategoryLoading: false,
      foodFoodSortPrimaryList: [],
      showDialogClassificationRow: {},
      selectListId: [],
      delType: '',
      categoryIsEnable: false
    }
  },
  components: { ImportUploadFile },
  created() {
    this.initLoad()
  },
  watch: {
    tableData: function () {
      this.$nextTick(function () {
        this.classificationBoxHeight =
        document.getElementsByClassName('search-form-wrapper')[0].offsetHeight +
        document.getElementsByClassName('table-wrapper')[0].offsetHeight - 140
      })
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.foodFoodSortList()
      this.foodFoodCategoryList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      // this.tableData = []
      this.initLoad()
    },
    changeCategoryIsEnable() {
      this.getModifyCategoryDisplay()
    },
    // 修改系统菜品分类显示
    async getModifyCategoryDisplay() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryModifyCategoryDisplayPost({
          is_enable: this.categoryIsEnable
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodFoodCategoryList()
        this.foodFoodSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 一级列表
    async foodFoodSortList() {
      this.isLoadingFoodFoodSor = true
      let params = {
        page: 1,
        page_size: 999999
      }
      if (this.primaryName) {
        params.name = this.primaryName
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodSortListPost(params)
      )
      this.isLoadingFoodFoodSor = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.foodFoodSortPrimaryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 一级分类新增
    async foodFoodSortAdd() {
      this.formSortLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodSortAddPost({
          name: this.dialogSortForm.primarySortName
        })
      )
      this.formSortLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogSort = false
        this.foodFoodSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 一级分类编辑
    async foodFoodSortModify() {
      this.formSortLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodSortModifyPost({
          name: this.dialogSortForm.primarySortName,
          id: this.showDialogClassificationRow.id
        })
      )
      this.formSortLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogSort = false
        this.foodFoodSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 一级分类删除
    async foodFoodSorttDelete(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodSortDeletePost({
          ids: [row.id]
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.foodFoodSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 二级列表
    async foodFoodCategoryList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.categoryIsEnable = res.data.category_display
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 二级分类新增
    async foodFoodCategoryAdd() {
      this.formCategoryLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryAddPost({
          sort: this.dialogCategoryForm.primarySortId,
          name: this.dialogCategoryForm.categoryName,
          color: this.dialogCategoryForm.color
        })
      )
      this.formCategoryLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogCategory = false
        this.searchHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 二级分类编辑
    async foodFoodCategoryModify() {
      this.formCategoryLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryModifyPost({
          id: this.showDialogClassificationRow.id,
          status: this.showDialogClassificationRow.status,
          organization: this.showDialogClassificationRow.organization,
          sort: this.dialogCategoryForm.primarySortId,
          name: this.dialogCategoryForm.categoryName,
          color: this.dialogCategoryForm.color
        })
      )
      this.formCategoryLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogCategory = false
        this.foodFoodCategoryList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 二级分类删除
    async foodFoodCategoryDelete(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryDeletePost({
          ids: this.delType === 'delCategory' ? [row.id] : this.selectListId
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.searchHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.foodFoodCategoryList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.foodFoodCategoryList()
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    addAndEditMealFood() {},
    deleteHaldler(type, row) {
      if (type === 'delBatchCategory' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let _this = this
      let delText = ''
      this.delType = type
      switch (type) {
        case 'delSort':
          delText = ''
          break
        case 'delCategory':
          delText = ''
          break
        case 'delBatchCategory':
          delText = '批量'
          break
        default:
          break
      }
      this.$confirm(`是否${delText}删除该分类？`, `${delText}删除`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            switch (type) {
              case 'delSort':
                _this.foodFoodSorttDelete(row)
                break
              default:
                _this.foodFoodCategoryDelete(row)
                break
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    publicUrl(data) {
      console.log(data)
    },
    mulImortFace() {
      if (!this.uploadUrl) {
        this.$message.error('食材还没上传完毕或未上传')
        return
      }
      // const { href } = this.$router.resolve({
      //   name: 'Excel',
      //   query: {
      //     type: 'mul_import_face',
      //     face_zip_url: this.uploadUrl
      //   }
      // })
      // window.open(href, '_blank')
    },
    clickShowDialogClassification(type, row) {
      this.showDialogClassificationRow = {}
      this.showDialogClassificationType = type
      if (type === 'addSort') {
        this.dialogClassificationTitle = '新增一级分类'
        this.dialogSortForm.primarySortName = ''
        this.showDialogSort = true
      } else if (type === 'editSort') {
        this.dialogClassificationTitle = '编辑一级分类'
        this.dialogSortForm.primarySortName = row.name
        this.showDialogClassificationRow = row
        this.showDialogSort = true
      } else if (type === 'addCategory') {
        this.dialogClassificationTitle = '新增二级分类'
        this.showDialogCategory = true
        this.dialogCategoryForm = {
          primarySortId: '',
          categoryName: '',
          color: '#409EFF'
        }
      } else if (type === 'editCategory') {
        this.dialogClassificationTitle = '编辑二级分类'
        this.showDialogCategory = true
        this.dialogCategoryForm = {
          primarySortId: row.sort,
          categoryName: row.name,
          color: row.color
        }
        this.showDialogClassificationRow = row
      }
    },
    determineSortDialog() {
      this.$refs.dialogSortForm.validate(valid => {
        if (valid) {
          if (this.showDialogClassificationType === 'addSort') {
            this.foodFoodSortAdd()
          } else if (this.showDialogClassificationType === 'editSort') {
            this.foodFoodSortModify()
          }
        } else {
          return false
        }
      })
    },
    determineCategoryDialog() {
      this.$refs.dialogCategoryForm.validate(valid => {
        if (valid) {
          if (this.showDialogClassificationType === 'addCategory') {
            this.foodFoodCategoryAdd()
          } else if (this.showDialogClassificationType === 'editCategory') {
            this.foodFoodCategoryModify()
          }
        } else {
          return false
        }
      })
    },
    openImport() {
      this.$message.error('暂无导入')
      return
      this.importShowDialog = true
    },
    // 禁止选中超管添加的分类
    selectableHandle(row, index) {
      return !row.is_admin
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
#classification-container {
  display: flex;
  .classification-list {
    flex: 1;
    min-width: 0;
    // background-color: ;
  }
  .classification-left {
    height: 648px;
    overflow: auto;
  }
  .status-point {
    // 小圆点在这里
    // border: 1px solid red;
    display: inline-block; // 此句为css样式展示重点🏁
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 2px;
  }
}
.infinite-list {
  .primary-classification {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 32px;
  }
}
</style>

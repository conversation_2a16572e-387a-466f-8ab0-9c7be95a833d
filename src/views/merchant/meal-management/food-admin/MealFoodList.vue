<template>
  <div class="meal-food-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="110px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <span style="font-size: 12px;" v-if="userInfo.account_type !== 2">
            <el-popover
              placement="top"
              title=""
              width="200"
              trigger="click"
              content="允许上级下发，重名的菜品会直接覆盖"
            >
              <i slot="reference" class="el-icon-question pointer"></i>
            </el-popover>
            允许上级下发
            <el-switch
              v-model="updateSetting"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
              @change="updateSettingHandler"
            ></el-switch>
          </span>
          <button-icon color="plain" type="del" @click="batchLabelClick('batchLabelDel')">
            批量移除标签
          </button-icon>
          <button-icon color="plain" type="mul" @click="batchLabelClick('batchLabelAdd')">
            批量打标签
          </button-icon>
          <button-icon color="origin" type="add" @click="gotoCopyFoods">
            添加菜品
          </button-icon>
          <button-icon color="origin" type="Import" @click="importImgHandler">
            批量导入图片
          </button-icon>
          <button-icon color="origin" type="Import" @click="importHandler('modify_import')">
            导入编辑
          </button-icon>
          <!-- <button-icon color="origin" type="add" @click="clickShowDiscountDialog('add')">
            创建优惠
          </button-icon> -->
          <!-- <button-icon color="origin" type="add" @click="addAndEditMealFood('add')">
            新建菜品/商品
          </button-icon> -->
          <button-icon color="plain" type="Import" @click="importHandler('import')">
            导入菜品/商品
          </button-icon>
          <!-- <button-icon color="origin" type="mul" @click="addAndEditMealFood('batchAdd')">
            批量设置
          </button-icon> -->
          <!-- <button-icon color="origin" type="mul" @click="clickOnSale('batchOnSale', 1)">
            批量上架
          </button-icon>
          <button-icon color="origin" type="mul" @click="clickOnSale('batchOnSale', 0)">
            批量下架
          </button-icon> -->
          <button-icon color="plain" type="del" @click="deleteHaldler('delBatch')">
            批量删除
          </button-icon>
          <button-icon color="plain" type="export" @click="gotoExport">导出菜品/商品</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          row-key="id"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
            :reserve-selection="true"
          ></el-table-column>
          <!-- <el-table-column type="index" label="序号" align="center"></el-table-column>
          <el-table-column prop="id" label="菜品编号" align="center"></el-table-column> -->
          <el-table-column prop="" label="菜品 / 商品图片" align="center" width="200px">
            <template slot-scope="scope">
              <el-image
                :src="scope.row.image? scope.row.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"
                style="width: 150px; height: 100px"
                fit="contain"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="菜品 / 商品名称" align="center"></el-table-column>
          <el-table-column prop="all_alias_name" label="菜品 / 商品别名" width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column prop="attributes" label="属性" align="center">
            <template slot-scope="scope">
              {{ scope.row.attributes === 'goods' ? '商品' : '菜品' }}
            </template>
          </el-table-column>
          <el-table-column prop="category_name" label="分类" align="center"></el-table-column>
          <!-- 新版不需要哦 -->
          <!-- <el-table-column prop="category_name" label="上新" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.new_start_date">
                {{ `${scope.row.new_start_date} ~ ${scope.row.new_end_date}` }}
              </div>
            </template>
          </el-table-column> -->
          <el-table-column prop="price_info.origin_price" label="成本价" align="center">
            <template slot-scope="scope">
              <span>{{ `￥${scope.row.price_info.origin_price}` }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="price_info.food_price" label="菜品 / 商品价格" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.price_info.count_type === 2">
                {{ `￥${scope.row.price_info.weight_price}` }}
              </span>
              <span v-else-if="scope.row.price_info.count_type === 3">
                {{ `￥${scope.row.price_info.food_price}` }}
              </span>
              <div v-else>
                <p v-for="(item, index) in scope.row.spec_list" :key="index">
                  {{ `￥${(item.food_price / 100).toFixed(2)}` }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="price_info.pack_price" label="打包费" align="center">
            <template slot-scope="scope">
              <span>{{ `￥${scope.row.price_info.pack_price}` }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="barcode" label="条形码" align="center"></el-table-column>
          <el-table-column prop="" label="食材组成" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogIngredients(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogNutrition(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="xx" label="标签" align="center" width="220px">
            <template slot-scope="scope">
              <div class="collapse-wrapper">
                <div class="collapse-list hide">
                  <el-tag
                    class="m-r-5 m-t-5 collapse-data"
                    v-for="(item, index) in scope.row.labelList"
                    :key="index"
                    size="medium"
                    effect="plain"
                    :type="item.type === 'ingredients' ? 'light' : 'danger'"
                    :closable="item.type !== 'ingredients'"
                    @close="closeTag(item, scope.row)"
                  >
                    {{ item.name }}
                  </el-tag>
                  <template v-if="scope.row.labelList && scope.row.labelList.length > 3">
                    <span class="collapse-more" @click="showMoreHandler">
                      查看更多
                      <i class="el-icon-arrow-down"></i>
                    </span>
                    <span class="collapse-hide" @click="hideMoreHandler">
                      收起
                      <i class="el-icon-arrow-up"></i>
                    </span>
                  </template>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="" label="口味" align="center" width="150px">
            <template slot-scope="scope">
              <div class="tast-wrapper">
                <el-tag v-for="(item, index) in scope.row.taste_list" :key="index">
                  {{ item.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="organization_name" label="应用组织" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickOrganization('see', scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="create_source_name" label="来源" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
          <!-- <el-table-column prop="sale_status_alias" label="状态" align="center"></el-table-column> -->
          <!-- <el-table-column prop="" label="支持预约" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.is_support_reservation">支持</span>
              <span v-else>不支持</span>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" width="180" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="addAndEditMealFood('edit', scope.row)"
              >
                编辑
              </el-button>
              <!-- <el-button
                type="text"
                size="small"
                class="ps-text"
                v-if="scope.row.sale_status"
                @click="clickOnSale('singleOnSale', 0, scope.row)"
              >
                下架
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                v-if="!scope.row.sale_status"
                @click="clickOnSale('singleOnSale', 1, scope.row)"
              >
                上架
              </el-button> -->
              <el-button
                type="text"
                size="small"
                class="ps-del"
                @click="clickOrganization('editOperation', scope.row)"
              >
                应用组织
              </el-button>
              <!-- <el-button
                type="text"
                size="small"
                class="ps-del"
                @click="clickShowDiscountDialog('history', scope.row)"
              >
                历史优惠
              </el-button> -->
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="deleteHaldler('del', scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 查看应用组织 -->
      <organization-dialog
        v-if="showDialogStructure"
        :structureTree="structureTree"
        :organizationDisabled="organizationDisabled"
        :showDialogStructure.sync="showDialogStructure"
        :structureType="structureType"
        :dialogDataRow="dialogDataRow"
        :confirm="searchHandle"
      />
      <!-- 表中营养信息 -->
      <el-dialog
        title="营养信息"
        :visible.sync="showDialogNutrition"
        width="800px"
        custom-class="ps-dialog"
      >
        <nutrition-data :tableDataNutrition="tableDataNutrition" :readonly="true" />
      </el-dialog>
      <!-- 查看食材组成 -->
      <el-dialog
        title="食材组成"
        :visible.sync="showDialogIngredients"
        width="650px"
        custom-class="ps-dialog"
      >
        <el-table :data="tableDataIngredients" style="width: 100%">
          <el-table-column prop="ingredient_name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="ingredient_scale" label="占比" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.ingredient_scale }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      <!-- 新增或编辑 -->
      <add-and-edit-mealFood
        v-if="showDialogMealFood"
        :showDialogMealFood.sync="showDialogMealFood"
        :showDialogMealFoodType="showDialogMealFoodType"
        :formFoodDataDialog="formFoodDataDialog"
        :foodDialogTitle="foodDialogTitle"
        :foodCategoryList="searchFormSetting.category_id.dataList"
        :selectListId="selectListId"
        :confirm="searchHandle"
      />
      <!-- 优惠 -->
      <food-discount-dialog
        v-if="showFoodDiscountDialog"
        :showFoodDiscountDialog.sync="showFoodDiscountDialog"
        :showDiscountDialogType="showDiscountDialogType"
        :selectListId="selectListId"
        :formFoodDataDialog="formFoodDataDialog"
        :confirm="searchHandle"
      />

      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      :title="titleSelectLaber"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
      ref="selectLaber"
    >
      <div slot="append">
        <div class="tab">
          <div :class="['tab-item', ruleSingleInfo.isAdmin ? 'active' : '']" @click="tabClick">
            平台标签
          </div>
          <div
            :class="['tab-item', ruleSingleInfo.isAdmin === false ? 'active' : '']"
            @click="tabClick"
          >
            自有标签
          </div>
        </div>
      </div>
    </select-laber>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, to, divide, isCurrentOrg, replaceSingleQuote } from '@/utils'
// import treeSelectIngredients from '../components/TreeSelect' // 菜品管理统一用一个tree
import addAndEditMealFood from '../components/mealFoodList/AddAndEditMealFoodDialog' // 弹框用的新增或者编辑
import foodDiscountDialog from '../components/mealFoodList/FoodDiscountDialog' // 优惠
import nutritionData from '../components/mealFoodList/NutritionTable'
import organizationDialog from '../components/mealFoodList/OrganizationDialog'
import { NUTRITION_LIST } from './constants'
import selectLaber from '../components/selectLaber.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'MealFoodList',
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        create_source_id: {
          type: 'organizationSelect',
          label: '创建来源',
          multiple: false,
          checkStrictly: true,
          value: '',
          placeholder: '请选择',
          dataList: [
            {
              name: '全部',
              id: ''
            },
            {
              name: '系统',
              id: 1
            }
          ]
        },
        category_id: {
          type: 'select',
          label: '分类',
          value: '',
          listNameKey: 'name',
          listValueKey: 'id',
          placeholder: '请选择分类',
          collapseTags: true,
          dataList: []
        },
        attributes: {
          type: 'select',
          label: '属性',
          value: '',
          listNameKey: 'name',
          listValueKey: 'id',
          placeholder: '请选择属性',
          collapseTags: true,
          dataList: [
            {
              name: '商品',
              id: 'goods'
            },
            {
              name: '菜品',
              id: 'foods'
            }
          ]
        },
        food_name: {
          type: 'input',
          label: '菜品 / 商品名称',
          value: '',
          placeholder: '请输入菜品 / 商品名称'
        },
        user_name: {
          type: 'input',
          label: '操作人',
          value: '',
          placeholder: '请输入操作人'
        },
        label_filter: {
          type: 'select',
          label: '', // 空格就是占位
          value: '',
          placeholder: '',
          dataList: [
            {
              label: '请选择',
              value: 'select',
              disabled: true
            },
            {
              label: '包含',
              value: 'Include'
            },
            {
              label: '不包含',
              value: 'Exclude'
            }
          ]
        },
        label_list: {
          type: 'treeselect',
          label: '',
          value: [],
          placeholder: '选择标签',
          multiple: true,
          limit: 1,
          level: 1,
          valueConsistsOf: 'LEAF_PRIORITY',
          normalizer: node => ({
            id: node.id,
            label: node.name,
            children: node.label_list
          }),
          dataList: []
        }
      },
      structureTree: [],
      showDialogStructure: false,
      organizationDisabled: false,
      // structureData: [],
      showDialogNutrition: false,
      tableDataNutrition: [],
      showDialogIngredients: false,
      tableDataIngredients: [],
      showDialogMealFood: false,
      showDialogMealFoodType: '',
      showFoodDiscountDialog: false,
      showDiscountDialogType: '',
      formFoodDataDialog: {},
      foodDialogTitle: '',
      delType: '',
      selectListId: [],
      dialogDataRow: {},
      structureType: '',
      updateSetting: false,
      // 批量打标签
      selectLaberDialogVisible: false,
      titleSelectLaber: '',
      batchLabelType: '', // 批量标签type
      ruleSingleInfo: {
        isAdmin: true, // 是否获取平台标签
        labelType: 'food'
      } // 标签
    }
  },
  components: {
    addAndEditMealFood,
    foodDiscountDialog,
    nutritionData,
    organizationDialog,
    selectLaber
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.initLoad()
    this.getAllLabelGroupList()
    this.getOrganizationTreeList()
    this.foodFoodCategoryList()
  },
  mounted() {
    // 获取食材库数据
    this.$nextTick(() => {
      setTimeout(() => {
        this.getSystemIngredientList()
        this.getFoodIngredientList()
      }, 60)
    })
  },
  methods: {
    initLoad() {
      this.getFoodList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 二级列表
    async foodFoodCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodCategoryListPost({
          page: 1,
          page_size: 9999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.category_id.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 菜品列表列表
    async getFoodList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        res.data.results.map(v => {
          v.price_info.origin_price = divide(v.price_info.origin_price)
          v.price_info.food_price = divide(v.price_info.food_price)
          v.price_info.pack_price = divide(v.price_info.pack_price)
          v.price_info.weight_price = divide(v.price_info.weight_price)
          v.ingredientsLabel = []
          v.labelList = []
          v.ingredients_list.forEach(k => {
            if (k.label.length) {
              k.label.forEach(m => {
                m.type = 'ingredients'
                v.ingredientsLabel.push(m)
              })
            }
          })
          let uniqueArr = this.fn2(v.ingredientsLabel)
          v.labelList.push(...v.label, ...uniqueArr)
        })
        this.tableData = res.data.results.map(item => {
          if (item.alias_name !== null) {
            item.all_alias_name = item.alias_name.join(',')
          } else {
            item.alias_name = []
          }
          return item
        })
        this.updateSetting = res.data.is_food_copy
      } else {
        this.$message.error(res.msg)
      }
    },
    fn2(arr) {
      const res = new Map()
      return arr.filter(arr => !res.has(arr.id) && res.set(arr.id, arr.id))
    },
    // 菜品列表删除
    async getFoodListDelete(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodDeletePost({
          ids: this.delType === 'del' ? [row.id] : this.selectListId
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.$message.success(res.msg)
        this.searchHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 菜品列表设置上下架
    async getFoodListOnSale(type, status, row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodBatchOnSalePost({
          ids: type === 'singleOnSale' ? [row.id] : this.selectListId,
          sale_status: status
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.$message.success(res.msg)
        this.getFoodList()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickOrganization(type, row) {
      console.log(222222, type, row)
      // this.structureData = []
      this.showDialogStructure = true
      this.structureType = type
      this.dialogDataRow = row
      if (type === 'see') {
        this.organizationDisabled = true
        // this.structureData = row.use_organizations
      } else if (type === 'editOperation') {
        // this.structureData = row.use_organizations
        this.organizationDisabled = false
      } else {
        this.organizationDisabled = false
      }
    },
    // inputTree(val) {
    //   this.structureData = val
    // },
    // determineOrganization() {
    //   if (this.structureType !== 'see') {
    //     this.foodIngredientSync()
    //   } else {
    //     this.showDialogStructure = false
    //   }
    // },
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      // this.treeLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationTreeListPost())
      // this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.structureTree = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    addAndEditMealFood(type, row) {
      if (type === 'batchAdd' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      if (type === 'add') {
        this.foodDialogTitle = '新增'
      } else if (type === 'edit') {
        this.foodDialogTitle = '编辑'
      } else if (type === 'batchAdd') {
        this.foodDialogTitle = '批量设置'
      }
      this.formFoodDataDialog = row
      this.showDialogMealFoodType = type
      this.showDialogMealFood = true
    },
    deleteHaldler(type, row) {
      if (type === 'delBatch' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let delText = ''
      if (type === 'delBatch') {
        delText = '是否批量删除已选'
      } else {
        delText = '是否删除该'
      }
      let _this = this
      this.delType = type
      this.$confirm(
        `${delText}菜品/商品？删除后会同时清空菜谱中对应的菜品/商品。`,
        `${type === 'delBatch' ? '批量' : ''}删除`,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-warn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              console.log(123)
              _this.getFoodListDelete(row)
              done()
              instance.confirmButtonLoading = false
            } else {
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        }
      )
        .then(e => {})
        .catch(e => {})
    },
    clickOnSale(type, status, row) {
      if (type === 'batchOnSale' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let _this = this
      let onSaleText = ''
      if (type === 'batchOnSale' && status) {
        onSaleText = '批量上架'
      } else if (type === 'batchOnSale' && !status) {
        onSaleText = '批量下架'
      } else if (type === 'singleOnSale' && status) {
        onSaleText = '批量上架'
      } else if (type === 'singleOnSale' && !status) {
        onSaleText = '批量下架'
      }
      this.$confirm(`是否${onSaleText}该菜品？`, `${onSaleText}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            _this.getFoodListOnSale(type, status, row)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    clickShowDialogIngredients(row) {
      this.showDialogIngredients = true
      this.tableDataIngredients = row.ingredients_list
    },
    clickShowDialogNutrition(row) {
      this.showDialogNutrition = true
      this.tableDataNutrition = {}
      if (!row.nutrition_info) row.nutrition_info = {}
      let element = row.nutrition_info.element
        ? JSON.parse(replaceSingleQuote(row.nutrition_info.element))
        : {}
      let vitamin = row.nutrition_info.vitamin
        ? JSON.parse(replaceSingleQuote(row.nutrition_info.vitamin))
        : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(
            this.tableDataNutrition,
            nutrition.key,
            row.nutrition_info[nutrition.key] ? row.nutrition_info[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'element') {
          this.$set(
            this.tableDataNutrition,
            nutrition.key,
            element[nutrition.key] ? element[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'vitamin') {
          this.$set(
            this.tableDataNutrition,
            nutrition.key,
            vitamin[nutrition.key] ? vitamin[nutrition.key] : 0
          )
        }
      })
    },
    clickShowDiscountDialog(type, row) {
      if (type === 'add' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      if (type === 'history') {
        this.formFoodDataDialog = row
      }
      this.showFoodDiscountDialog = true
      this.showDiscountDialogType = type
    },
    showMoreHandler(e) {
      e.target.parentNode.classList.remove('hide')
    },
    hideMoreHandler(e) {
      e.target.parentNode.classList.add('hide')
    },
    batchLabelClick(type) {
      this.batchLabelType = type
      if (type === 'batchLabelDel') {
        this.titleSelectLaber = '批量移除标签'
      } else if (type === 'batchLabelAdd') {
        this.titleSelectLaber = '批量打标签'
      }
      // 保存一下 选择标签后 需要返显
      this.ruleSingleInfo = {
        isAdmin: true, // 是否获取平台标签
        labelType: 'food'
      }
      if (!this.selectListId.length) return this.$message.error(`请先选择要${this.titleSelectLaber}的数据！`)
      this.selectLaberDialogVisible = true
    },
    // 移除标签
    closeTag(data, row) {
      this.batchLabelType = 'delSingleTag'
      this.titleSelectLaber = '删除该标签'
      let params = {
        selectLabelIdList: [data.id]
      }
      this.selectListId = [row.id]
      this.selectLaberData(params)
    },
    tabClick() {
      this.ruleSingleInfo.isAdmin = !this.ruleSingleInfo.isAdmin
      this.$refs.selectLaber.currentPage = 1
      this.$refs.selectLaber.getLabelGroupList()
    },
    gotoExport() {
      const option = {
        type: 'ExoprtMealFoodList',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    selectLaberData(params) {
      this.$confirm(`是否${this.titleSelectLaber}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            let labelParams = {
              ids: this.selectListId,
              label_list: params.selectLabelIdList
            }
            let [err, res] = ''
            if (this.batchLabelType === 'batchLabelAdd') {
              ;[err, res] = await to(this.$apis.apiBackgroundFoodFoodBatchAddLabelPost(labelParams))
            } else {
              ;[err, res] = await to(
                this.$apis.apiBackgroundFoodFoodBatchDeleteLabelPost(labelParams)
              )
            }
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.$refs.tableData.clearSelection()
              this.getFoodList()
              this.selectListId = []
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 获取所有的标签
    async getAllLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyLabelGroupAllLabelGroupListPost({
          is_admin: true,
          type: 'food',
          page: 1,
          page_size: 999999
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(v => {
          v.id = `${v.id}_1`
          if (!v.label_list.length) {
            v.isDisabled = true
          }
          return v
        })
        this.searchFormSetting.label_list.dataList = res.data.results
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getFoodList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getFoodList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    // 导入菜品/商品
    importHandler(type) {
      this.$router.push({
        name: 'MerchantImportCommodity',
        params: {
          type: type
        }
      })
    },
    // 导入菜品/商品图片
    importImgHandler() {
      this.$router.push({
        name: 'MerchantImportCommodityImage'
      })
    },
    gotoCopyFoods(type) {
      this.$router.push({
        name: 'MerchantCopyFoods'
      })
    },
    // 是否允许商户上传信息
    updateSettingHandler(e) {
      let title = '是否允许上级下发?'
      if (!this.updateSetting) {
        title = '是否关闭上级下发?'
      }
      this.$confirm(title, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundFoodFoodChangeFoodCopyPost({
                is_enable: this.updateSetting
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getFoodList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              this.updateSetting = !this.updateSetting
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    isCurrentOrg,
    // 系统食材库列表
    async getSystemIngredientList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSystemIngredientPost({
          is_copy: 'all'
        })
      )
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        sessionStorage.setItem('systemIngredientsList', res.data ? JSON.stringify(res.data) : '[]')
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 食材管理列表
    async getFoodIngredientList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientIngredientNamePost({
          page: 1,
          page_size: 99999
        })
      )
      if (err) {
        // this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 所有的食材列表
        sessionStorage.setItem('foodIngredientList', res.data ? JSON.stringify(res.data) : '[]')
      } else {
        // this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
.meal-food-list {
  .el-tag {
    margin: 0 10px 10px 0;
  }
  .input-new-tag {
    width: 100px;
  }
  .collapse-wrapper {
    .collapse-list {
      // text-align: left;
      .collapse-data {
        display: inline-block;
        height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .collapse-more {
        display: none;
      }
      .food-ellipsis {
        display: none;
      }
      .collapse-hide {
        display: block;
        color: #f3b687;
        font-size: 12px;
        cursor: pointer;
      }
      &.hide {
        .collapse-data:nth-child(n + 4) {
          display: none;
        }
        .collapse-more {
          text-align: center;
          display: block;
          color: #f3b687;
          font-size: 12px;
          cursor: pointer;
        }
        .food-ellipsis {
          display: inline-block;
          width: 100px;
        }
        .collapse-hide {
          display: none;
        }
      }
    }
  }
  .tab {
    margin: 10px 0;
    .tab-item {
      display: inline-block;
      width: 90px;
      height: 26px;
      line-height: 26px;
      margin-right: 10px;
      text-align: center;
      border-radius: 14px;
      border: solid 1px #dae1ea;
      font-size: 14px;
      color: #7b7c82;
      vertical-align: middle;
      cursor: pointer;
      &.active {
        color: #ffffff;
        background-color: #fd953c;
        border: solid 1px #fd953c;
      }
    }
  }
}
</style>

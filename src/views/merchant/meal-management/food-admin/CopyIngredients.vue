<template>
  <div class="ingredients-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="110px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="copySystemIngredients('multi')">
            批量复制
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column prop="name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="all_alias_name" label="食材别名" align="center" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="category_name" label="一级分类" align="center"></el-table-column>
          <el-table-column prop="sort_name" label="二级分类" align="center"></el-table-column>
          <el-table-column prop="is_copy" label="是否已复制" align="center">
            <template slot-scope="scope">
              {{ scope.row.is_copy ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="copySystemIngredients('single', scope.row.id)"
              >
                复制
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- tree -->
        <!-- table end -->
      </div>
    </div>
    <dialog-message width="415px" top="30vh" title="复制成功" :show.sync="showDialog" center>
      <div class="import-food m-b-50">
        前往添加菜品，可快速创建菜品库和食材库，选择系统菜品相关食材会自动创建。
      </div>
      <div slot="tool" class="text-center">
        <el-button class="ps-btn w-110" type="primary" style="margin-right: 20px;" @click="clickConfirmHandle">添加菜品</el-button>
        <el-button class="ps-cancel-btn w-110" @click="clickCancleHandle">我知道了</el-button>
      </div>
    </dialog-message>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import { confirm } from '@/utils/message'

export default {
  name: 'CopyIngredients',
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        ingredient_name: {
          type: 'input',
          label: '食材名称',
          value: '',
          placeholder: '请输入食材名称'
        },
        sort_id: {
          type: 'treeselect',
          multiple: true,
          flat: false,
          label: '分类',
          value: null,
          placeholder: '请选择分类',
          dataList: [],
          limit: 1,
          level: 1,
          normalizer: node => ({
            id: node.level + '_' + node.id,
            label: node.name,
            children: node.sort_list
          })
        },
        is_copy: {
          type: 'select',
          label: '是否已复制',
          value: 'all',
          placeholder: '请选择',
          collapseTags: true,
          dataList: [
            {
              label: '全部',
              value: 'all'
            },
            {
              label: '是',
              value: 'finish_copy'
            },
            {
              label: '否',
              value: 'not_copy'
            }
          ]
        }
      },
      selectListId: [], // 多选选中的id
      copyModel: '',
      showDialog: false
    }
  },
  created() {
    // this.initLoad()
    this.getCategoryCategoryNameList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSystemIngredientList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.getSystemIngredientList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.getSystemIngredientList()
    },
    // 获取所有一级食材以及下面的二级食材
    async getCategoryCategoryNameList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost()
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.sort_id.dataList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },

    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.sort_list) {
            if (item.sort_list.length > 0) {
              traversal(item.sort_list)
            } else {
              _that.$delete(item, 'sort_list')
            }
          } else {
            _that.$delete(item, 'sort_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value || (data[key].value && data[key].value.length)) {
          if (key !== 'select_time') {
            // 因为要区分一级和二级id 字段不同 如果是一级就用category_id 二级sort_id 目前只有二级分类
            if (key === 'sort_id') {
              let categoryIds = []
              let sortIds = []
              data[key].value.map(v => {
                if (v.split('_')[0] === '1') {
                  categoryIds.push(Number(v.split('_')[1]))
                } else if (v.split('_')[0] === '2') {
                  sortIds.push(Number(v.split('_')[1]))
                }
              })
              if (categoryIds.length) {
                params.category_id = categoryIds
              }
              if (sortIds.length) {
                params.sort_id = sortIds
              }
              //   data[key].dataList.map(v => {
              //     if (data[key].value.split('_')[0] === '1') {
              //       params.category_id = Number(data[key].value.split('_')[1])
              //     } else {
              //       if (data[key].value.split('_')[0] === '2') {
              //         params.sort_id = data[key].value.split('_')[1]
              //       }
              //     }
              //   })
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 系统食材库列表
    async getSystemIngredientList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSystemIngredientPost({
          ...this.formatQueryParams(this.searchFormSetting)
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        this.tableData = []
        return
      }
      console.log('res', res)
      if (res.code === 0) {
        this.tableData = res.data.map(item => {
          if (item.alias_name !== null) {
            item.all_alias_name = item.alias_name.join(',')
          } else {
            item.alias_name = []
          }
          return item
        })
      } else {
        this.$message.error(res.msg)
        this.tableData = []
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      val.map(item => {
        this.selectListId.push(item.id)
      })
    },
    async copySystemIngredients(type, id, model) {
      let ids = []
      if (type === 'multi') {
        ids = this.selectListId
      } else {
        ids = [id]
      }
      let params = {
        ids: ids
      }
      if (model) {
        params.copy_model = model
      }
      if (!ids.length) return this.$message.error('请先选择复制的数据！')
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSystemIngredientCopyPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('res', res)
      if (res.code === 0) {
        this.selectListId = []
        this.$message.success(res.msg)
        this.getSystemIngredientList()
        this.showDialog = true
      } else if (res.code === 2) {
        confirm({ content: res.msg, confirmButtonText: '覆盖', cancelButtonText: '去重后保存' })
          .then(_ => {
            this.copySystemIngredients(type, id, 'cover')
          })
          .catch(e => {
            if (e === 'cancel') {
              this.copySystemIngredients(type, id, 'deduplication')
            }
          })
      } else {
        this.$message.error(res.msg)
      }
    },
    async clickConfirmHandle(e) {
      this.clickCancleHandle()
      await this.$sleep(100)
      this.$router.push({
        name: 'MerchantCopyFoods'
      })
    },
    clickCancleHandle(e) {
      this.$closeCurrentTab(this.$route.path)
      this.showDialog = false
    }
  }
}
</script>
<style lang="scss">
</style>

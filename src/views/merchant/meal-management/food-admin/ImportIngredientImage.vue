<template>
  <div class="import-ingredientimgs-wrapper">
    <zip :template-url="templateUrl" :api-url="apiUrl"/>
  </div>
</template>

<script>
import zip from '@/views/public/import/zip'
export default {
  name: 'ImportIngredientImage',
  components: {
    zip
  },
  data() {
    return {
      templateUrl: 'https://cashier-v4.debug.packertec.com/api/temporary/template_excel/ingredient_image.zip',
      apiUrl: 'apiBackgroundFoodIngredientIngredientImageBatAddPost'
    }
  },
  created() {
    // this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
    }
  }
}
</script>

<style lang="scss" scoped>
</style>

<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="super-ingredients-library container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="del" @click="batchLabelClick('batchLabelDel')">批量移除标签</button-icon>
          <button-icon color="plain" type="mul" @click="batchLabelClick('batchLabelAdd')">批量打标签</button-icon>
          <button-icon color="origin" type="add" @click="addIngredients('add')">添加食材</button-icon>
          <button-icon color="plain" type="Import" @click="gotoCopyIngredients()">复制系统食材</button-icon>
          <button-icon color="plain" type="Import" @click="importHandler('import')">导入食材</button-icon>
          <button-icon color="plain" type="Import" v-permission="['background_food.ingredient.ingredient_image_bat_add']" @click="importHandler('importImage')">导入食材图片</button-icon>
          <button-icon color="plain" type="menu" @click="gotoCategory">分类列表</button-icon>
          <button-icon color="origin" type="export" @click="gotoExport">导出EXCEL</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          row-key="id"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox" :reserve-selection="true"></el-table-column>
          <!-- <el-table-column type="index" width="50" label="序号" align="center"></el-table-column> -->
          <el-table-column prop="" label="图片" align="center">
            <template slot-scope="scope">
              <el-image class="column-image" :lazy="true" :src="scope.row.image?scope.row.image:require('@/assets/img/ingredients_default.png')" :preview-src-list="[scope.row.image?scope.row.image:require('@/assets/img/ingredients_default.png')]">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="食材ID" align="center"></el-table-column>
          <el-table-column prop="name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="all_alias_name" label="食材别名" align="center" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="category_name" label="一级分类" align="center"></el-table-column>
          <el-table-column prop="sort_name" label="二级分类" align="center"></el-table-column>
          <el-table-column prop="xx" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.is_enable_nutrition"
                type="text"
                size="small"
                class="ps-text"
                @click="showDialogHandler('nutrition', scope.row)"
              >
                查看
              </el-button>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="xx" label="标签" align="center" width="220px">
            <template slot-scope="scope">
              <div class="collapse-wrapper">
                <div class="collapse-list hide">
                  <el-tag
                    class="m-r-5 m-t-5 collapse-data"
                    v-for="(item, index) in scope.row.label"
                    :key="index"
                    size="medium"
                    effect="plain"
                    type="light"
                    closable
                    @close="closeTag(item, scope.row)"
                  >
                    {{ item.name }}
                  </el-tag>
                  <template v-if="scope.row.label && scope.row.label.length > 3">
                    <span class="collapse-more" @click="showMoreHandler">
                      查看更多
                      <i class="el-icon-arrow-down"></i>
                    </span>
                    <span class="collapse-hide" @click="hideMoreHandler">
                      收起
                      <i class="el-icon-arrow-up"></i>
                    </span>
                  </template>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="is_enable_nutrition" label="营养录人" align="center">
            <template slot-scope="scope">
              {{ scope.row.is_enable_nutrition ? '是' : '否' }}
            </template>
          </el-table-column> -->
          <el-table-column prop="create_source_name" label="创建来源" align="center"></el-table-column>
          <!-- <el-table-column prop="organization" label="应用组织" align="center">
            <template slot-scope="scope">
              <el-popover
                v-if="scope.row.use_organizations.length"
                placement="right"
                width="200"
                trigger="click"
              >
                <div v-for="item in scope.row.use_organizations" :key="item.id">
                  {{ item.name }}
                </div>
                <el-button slot="reference" type="text" size="small" class="ps-text">
                  查看
                </el-button>
              </el-popover>
              <span v-else>--</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
          <el-table-column  label="操作" width="180" align="center">
            <template slot-scope="scope">
              <!-- <el-button
                type="text"
                size="small"
                class="ps-text"
                :disabled="scope.row.create_source === -1"
                @click="showDialogHandler('organizations', scope.row)"
              >应用组织</el-button> -->
              <!-- <span style="margin:0 10px; color: #e2e8f0;">|</span> -->
              <el-button
                type="text"
                size="small"
                class=""
                :disabled="scope.row.create_source === -1"
                @click="addIngredients('modify', scope.row)"
              >
                编辑
              </el-button>
              <span style="margin: 0 10px; color: #e2e8f0">|</span>
              <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                :disabled="scope.row.create_source === -1"
                @click="deleteIngredients('single', scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 start -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="dialogWidth"
      :top="dialogTop"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      @closed="dialogHandleClose"
    >
      <el-form v-loading="isLoading" :model="formData" class="" size="small">
        <!-- 营养 start -->
        <div v-if="dialogType === 'nutrition'">
          <template v-for="nutrition in nutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
              <el-form-item :prop="nutrition.key">
                <el-input
                  style="width: 120px"
                  readonly
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                ></el-input>
                <span style="margin-left: 10px">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
        </div>
        <!-- 营养 end -->
        <!-- 下发菜品 start -->
        <div v-if="dialogType === 'organizations'">
          <el-form-item prop="organizations" label="">
            <organization-select
              :only-child="true"
              :isLazy="false"
              :multiple="true"
              :checkStrictly="true"
              v-model="formData.organizations"
            />
          </el-form-item>
        </div>
        <!-- 下发菜品 end -->
      </el-form>
      <span slot="footer" v-if="dialogType !== 'nutrition'" class="dialog-footer">
        <el-button size="small" class="ps-cancel-btn" @click="dialogVisible = false">
          取消
        </el-button>
        <el-button class="ps-origin-btn" type="primary" size="small" @click="clickDialogHandler">
          确定
        </el-button>
      </span>
    </el-dialog>
    <!-- 弹窗 end -->
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      :title="titleSelectLaber"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
      ref="selectLaber"
      >
        <div slot="append">
          <div class="tab">
            <div
              :class="['tab-item', ruleSingleInfo.isAdmin ? 'active' : '']"
              @click="tabClick"
            >
              平台标签
            </div>
            <div
              :class="['tab-item', ruleSingleInfo.isAdmin === false ? 'active' : '']"
              @click="tabClick"
            >
              自有标签
            </div>
          </div>
        </div>
    </select-laber>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, to, isCurrentOrg, replaceSingleQuote } from '@/utils'
import { NUTRITION_LIST, LIBRARY_SEARCH_SETTING_MERCHANT } from './constants'
import organizationSelect from '@/components/OrganizationSelect'
import selectLaber from '../components/selectLaber.vue'
export default {
  name: 'IngredientsAdmin',
  mixins: [exportExcel], // activatedLoadData
  components: {
    organizationSelect,
    selectLaber
  },
  data() {
    return {
      selectListId: [],
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [], // 报表数据
      searchFormSetting: LIBRARY_SEARCH_SETTING_MERCHANT, // 搜索条件
      dialogData: {}, // 弹窗原数据
      dialogTitle: '营养', // 弹窗title
      dialogType: '', // 弹窗类型
      dialogVisible: false, // 是否开启弹窗
      dialogLoading: false, // 弹窗的状态
      nutritionList: NUTRITION_LIST, // 营养列表
      formData: {
        id: '',
        organizations: []
      }, // 弹窗
      dialogTop: '20vh',
      dialogWidth: '700px',
      // 批量打标签
      selectLaberDialogVisible: false,
      titleSelectLaber: '',
      batchLabelType: '', // 批量标签type
      ruleSingleInfo: {
        isAdmin: true, // 是否获取平台标签
        labelType: 'food'
      } // 标签
    }
  },
  created() {
    this.initLoad()
    this.getAllLabelGroupList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getCategoryCategoryNameList()
      this.foodIngredientList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      this.currentPage = 1
      this.foodIngredientList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 获取所有一级食材以及下面的二级食材
    async getCategoryCategoryNameList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        LIBRARY_SEARCH_SETTING_MERCHANT.sort_id.dataList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.sort_list) {
            if (item.sort_list.length > 0) {
              traversal(item.sort_list)
            } else {
              _that.$delete(item, 'sort_list')
            }
          } else {
            _that.$delete(item, 'sort_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value || (data[key].value && data[key].value.length)) {
          if (key !== 'select_time') {
            // 因为要区分一级和二级id 字段不同 如果是一级就用category_id 二级sort_id 目前只有二级分类
            if (key === 'sort_id') {
              data[key].dataList.map(v => {
                if (data[key].value.split('_')[0] === '1') {
                  params.category_id = Number(data[key].value.split('_')[1])
                } else {
                  if (data[key].value.split('_')[0] === '2') {
                    params.sort_id = Number(data[key].value.split('_')[1])
                  }
                }
              })
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 食材管理列表
    async foodIngredientList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          if (item.alias_name !== null) {
            item.all_alias_name = item.alias_name.join(',')
          } else {
            item.alias_name = []
          }
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳转添加食材页面
    addIngredients(type, data = {}) {
      let querys = {
        type: type
      }
      if (type === 'modify') {
        querys.data = this.$encodeQuery(data)
      }
      this.$router.push({
        name: 'MerchantAddIngredients',
        query: querys,
        params: {
          type
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.foodIngredientList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.foodIngredientList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    // 显示营养
    showDialogHandler(type, row) {
      this.dialogType = type
      if (type === 'nutrition') {
        let rowNutrition = row.nutrition
        if (!rowNutrition) {
          // 防止nutrition没值的情况
          rowNutrition = {}
        }
        let element = rowNutrition.element
          ? JSON.parse(replaceSingleQuote(rowNutrition.element))
          : {}
        let vitamin = rowNutrition.vitamin
          ? JSON.parse(replaceSingleQuote(rowNutrition.vitamin))
          : {}
        NUTRITION_LIST.forEach(nutrition => {
          if (nutrition.type === 'default') {
            this.$set(this.formData, nutrition.key, rowNutrition[nutrition.key])
          }
          if (nutrition.type === 'element') {
            this.$set(this.formData, nutrition.key, element[nutrition.key])
          }
          if (nutrition.type === 'vitamin') {
            this.$set(this.formData, nutrition.key, vitamin[nutrition.key])
          }
        })
        this.dialogTitle = '营养信息'
        this.dialogWidth = '700px'
      } else {
        this.dialogData = row
        this.formData.id = row.id
        this.formData.organizations = row.use_organizations.map(item => {
          return item.id
        })
        this.dialogTitle = '应用组织'
        this.dialogWidth = '450px'
        this.dialogTop = '35vh'
      }
      this.dialogVisible = true
    },
    dialogHandleClose() {
      this.formData = {
        id: '',
        organizations: []
      }
      this.dialogData = {}
      this.dialogType = ''
    },
    // 弹窗点击事件
    clickDialogHandler() {
      if (this.dialogType === 'organizations') {
        if (this.formData.organizations.length > 0) {
          this.setIssueHandler()
        } else {
          this.$message.error('请先选择一个组织！')
        }
      }
    },
    // 删除食材
    async deleteIngredients(type, data) {
      let params = {}
      if (type === 'single') {
        params.ids = [data.id]
      } else {
      } // 删除多个，原型没有
      if (this.isLoading) return this.$message.error('请勿重复提交！')
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (action === 'confirm') {
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundFoodIngredientDeletePost(params))
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.foodIngredientList()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 下发菜品
    async setIssueHandler(data) {
      if (this.isLoading) return this.$message.error('请勿重复提交！')
      this.$confirm(`确定应用组织？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (action === 'confirm') {
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundFoodIngredientDistributePost({
                id: this.formData.id,
                organizations: this.formData.organizations
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.foodIngredientList()
              this.dialogVisible = false
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    showMoreHandler(e) {
      // this.$refs.tableData.doLayout();
      // console.log(2222)
      e.target.parentNode.classList.remove('hide')
    },
    hideMoreHandler(e) {
      // this.$refs.tableData.doLayout();
      // console.log(2222)
      e.target.parentNode.classList.add('hide')
    },
    batchLabelClick(type) {
      this.batchLabelType = type
      if (type === 'batchLabelDel') {
        this.titleSelectLaber = '批量移除标签'
      } else if (type === 'batchLabelAdd') {
        this.titleSelectLaber = '批量打标签'
      }
      // 保存一下 选择标签后 需要返显
      this.ruleSingleInfo = {
        isAdmin: true, // 是否获取平台标签
        labelType: 'ingredient'
      }
      if (!this.selectListId.length) return this.$message.error(`请先选择要${this.titleSelectLaber}的数据！`)
      this.selectLaberDialogVisible = true
    },
    // 移除标签
    closeTag(data, row) {
      this.batchLabelType = 'delSingleTag'
      this.titleSelectLaber = '删除该标签'
      let params = {
        selectLabelIdList: [data.id]
      }
      this.selectListId = [row.id]
      this.selectLaberData(params)
    },
    tabClick() {
      this.ruleSingleInfo.isAdmin = !this.ruleSingleInfo.isAdmin
      this.$refs.selectLaber.currentPage = 1
      this.$refs.selectLaber.getLabelGroupList()
    },
    selectLaberData(params) {
      this.$confirm(`是否${this.titleSelectLaber}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            let labelParams = {
              ids: this.selectListId,
              label_list: params.selectLabelIdList
            }
            let [err, res] = ''
            if (this.batchLabelType === 'batchLabelAdd') {
              ;[err, res] = await to(
                this.$apis.apiBackgroundFoodIngredientBatchAddLabelPost(labelParams)
              )
            } else {
              ;[err, res] = await to(
                this.$apis.apiBackgroundFoodIngredientBatchDeleteLabelPost(labelParams)
              )
            }
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.$refs.tableData.clearSelection();
              this.foodIngredientList()
              this.selectListId = []
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 获取所有的标签
    async getAllLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundHealthyLabelGroupAllLabelGroupListPost({
        is_admin: true,
        type: 'ingredient',
        page: 1,
        page_size: 999999
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(v => {
          if (!v.label_list.length) {
            v.isDisabled = true
          }
          return v
        })
        this.searchFormSetting.label_list.dataList = res.data.results
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    // 导入食材
    importHandler(type) {
      if (type === 'import') {
        this.$router.push({
          name: 'MerchantImportIngredients',
          params: {
            type: type
          }
        })
      } else if (type === 'importImage') {
        this.$router.push({
          name: 'MerchantImportIngredientImage',
          params: {
            type: type
          }
        })
      }
    },
    // 分类
    gotoCategory() {
      this.$router.push({
        name: 'MerchantIngredientsCategory'
      })
    },
    // 复制系统食材
    gotoCopyIngredients() {
      this.$router.push({
        name: 'MerchantCopyIngredients'
      })
    },
    // 导出 IngredientsAdmin
    gotoExport() {
      const option = {
        type: 'IngredientsAdmin',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    isCurrentOrg
  }
}
</script>

<style lang="scss" scoped>
@import './styles/commont.scss';
.super-ingredients-library {
  .tab {
    margin-bottom: 20px;
    .tab-item {
      display: inline-block;
      width: 90px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 14px;
      border: solid 1px #dae1ea;
      font-size: 16px;
      color: #7b7c82;
      vertical-align: middle;
      cursor: pointer;
      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }
    }
  }
  .column-image{
    max-width: 80px;
    max-height: 80px;
  }
}
.ps-dialog {
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
}
.collapse-wrapper {
  .collapse-list {
    // text-align: left;
    .collapse-data {
      display: inline-block;
      height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .collapse-more {
      display: none;
    }
    .food-ellipsis {
      display: none;
    }
    .collapse-hide {
      display: block;
      color: #f3b687;
      font-size: 12px;
      cursor: pointer;
    }
    &.hide {
      .collapse-data:nth-child(n + 4) {
        display: none;
      }
      .collapse-more {
        text-align: center;
        display: block;
        color: #f3b687;
        font-size: 12px;
        cursor: pointer;
      }
      .food-ellipsis {
        display: inline-block;
        width: 100px;
      }
      .collapse-hide {
        display: none;
      }
    }
  }
}
.tab {
  margin:10px 0;
  .tab-item {
    display: inline-block;
    width: 90px;
    height: 26px;
    line-height: 26px;
    margin-right: 10px;
    text-align: center;
    border-radius: 14px;
    border: solid 1px #dae1ea;
    font-size: 14px;
    color: #7b7c82;
    vertical-align: middle;
    cursor: pointer;
    &.active {
      color: #ffffff;
      background-color: #fd953c;
      border: solid 1px #fd953c;
    }
  }
  }
</style>

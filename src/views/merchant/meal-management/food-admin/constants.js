export const DEFAULT_NUTRITION = [
  // { name: '千焦耳', key: 'energy_mj', unit: 'kj', type: 'default' },
  { name: '千卡', key: 'energy_kcal', unit: 'Kcal', type: 'default' },
  { name: '碳水化合物', key: 'carbohydrate', unit: 'g', type: 'default' },
  { name: '蛋白质', key: 'protein', unit: 'g', type: 'default' },
  { name: '脂肪', key: 'axunge', unit: 'g', type: 'default' }
]

export const ELEMENT_NUTRITION = [
  { key: 'Ca', name: '钙', unit: 'mg', type: 'element' },
  { key: 'P', name: '磷', unit: 'mg', type: 'element' },
  { key: 'K', name: '钾', unit: 'mg', type: 'element' },
  { key: 'Na', name: '钠', unit: 'mg', type: 'element' },
  { name: '镁', key: 'Mg', unit: 'mg', type: 'element' },
  { key: 'Fe', name: '铁', unit: 'mg', type: 'element' },
  { key: 'I', name: '碘', unit: 'μg', type: 'element' },
  { key: 'Se', name: '硒', unit: 'μg', type: 'element' },
  { key: 'Zn', name: '锌', unit: 'mg', type: 'element' },
  { key: 'Cu', name: '铜', unit: 'mg', type: 'element' },
  { key: 'F', name: '氟', unit: 'mg', type: 'element' },
  { key: 'Cr', name: '铬', unit: 'μg', type: 'element' },
  { key: 'Mo', name: '钼', unit: 'μg', type: 'element' },
  { key: 'Mn', name: '锰', unit: 'mg', type: 'element' }
]

export const VITAMIN_NUTRITION = [
  { key: 'VA', name: '维生素A', unit: 'μg', type: 'vitamin' },
  { key: 'VD', name: '维生素D', unit: 'μg', type: 'vitamin' },
  { key: 'VE', name: '维生素E', unit: 'mg', type: 'vitamin' },
  { key: 'VK', name: '维生素K', unit: 'μg', type: 'vitamin' },
  { key: 'VB1', name: '维生素B1', unit: 'mg', type: 'vitamin' },
  { key: 'VB2', name: '维生素B2', unit: 'mg', type: 'vitamin' },
  { key: 'VB6', name: '维生素B6', unit: 'mg', type: 'vitamin' },
  { key: 'VB12', name: '维生素B12', unit: 'μg', type: 'vitamin' },
  { key: 'VC', name: '维生素C', unit: 'mg', type: 'vitamin' },
  { key: 'VB5', name: '泛酸', unit: 'mg', type: 'vitamin' },
  { key: 'VM', name: '叶酸', unit: 'μg', type: 'vitamin' },
  { key: 'VB3', name: '烟酸', unit: 'mg', type: 'vitamin' },
  { key: 'Choline', name: ' 胆碱', unit: 'mg', type: 'vitamin' },
  { key: 'Nicotinamide', name: '烟酰胺', unit: 'mg', type: 'vitamin' },
  { key: 'VH', name: '生物素', unit: 'mg', type: 'vitamin' }
]

export const NUTRITION_LIST = [...DEFAULT_NUTRITION, ...ELEMENT_NUTRITION, ...VITAMIN_NUTRITION]

// 食材管理搜索条件 商户食材
export const LIBRARY_SEARCH_SETTING_MERCHANT = {
  select_time: {
    type: 'datetimerange',
    label: '创建时间',
    format: 'yyyy-MM-dd HH:mm:ss',
    value: []
  },
  sort_id: {
    type: 'treeselect',
    multiple: false,
    flat: false,
    label: '分类',
    value: null,
    placeholder: '请选择分类',
    dataList: [],
    limit: 1,
    level: 1,
    normalizer: node => ({
      id: node.level + '_' + node.id,
      label: node.name,
      children: node.sort_list
    })
  },
  create_source: {
    type: 'organizationSelect',
    label: '创建来源',
    multiple: false,
    checkStrictly: true,
    isLazy: false,
    value: '',
    placeholder: '请选择创建来源',
    dataList: [
      {
        name: '全部',
        id: ''
      },
      {
        company: 0,
        create_time: '2022-01-25 09:49:26',
        has_children: false,
        id: 1,
        level: -1,
        level_name: '',
        level_tag: -1,
        name: '系统',
        parent: null,
        status: 'enable',
        status_alias: '正常',
        tree_id: -1
      }
    ]
  },
  is_enable_nutrition: {
    type: 'select',
    label: '营养录入',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '是',
        value: '1'
      },
      {
        label: '否',
        value: '0'
      }
    ]
  },
  ingredient_name: {
    type: 'input',
    label: '食材名称',
    value: '',
    placeholder: '请输入食材名称'
  },
  label_filter: {
    type: 'select',
    label: '', // 空格就是占位
    value: '',
    placeholder: '',
    dataList: [
      {
        label: '请选择',
        value: 'select',
        disabled: true
      },
      {
        label: '包含',
        value: 'Include'
      },
      {
        label: '不包含',
        value: 'Exclude'
      }
    ]
  },
  label_list: {
    type: 'treeselect',
    label: '',
    value: [],
    placeholder: '选择标签',
    multiple: true,
    limit: 1,
    level: 1,
    valueConsistsOf: 'LEAF_PRIORITY',
    normalizer: node => ({
      id: node.id,
      label: node.name,
      children: node.label_list
    }),
    dataList: []
  },
  is_entering: {
    type: 'select',
    label: '录入图片',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '已录入',
        value: '1'
      },
      {
        label: '未录入',
        value: '0'
      }
    ]
  }
}

// 食材分类搜索条件
export const LIBRARY_SEARCH_SETTING_CATEORY = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '修改时间',
    value: []
  },
  category: {
    type: 'select',
    label: '分类',
    value: [],
    placeholder: '请选择分类',
    multiple: true,
    collapseTags: true,
    dataList: [
      {
        label: '挂失',
        value: 'LOSS'
      },
      {
        label: '退卡',
        value: 'QUIT'
      },
      {
        label: '使用中',
        value: 'ENABLE'
      },
      {
        label: '未发卡',
        value: 'UNUSED'
      }
    ]
  },
  person_no: {
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入操作人'
  }
}

// 菜品商品搜索条件 商户菜品/商品
export const COMMODITY_SEARCH_SETTING_MERCHANT = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '创建时间',
    value: []
  },
  nutrition: {
    type: 'select',
    label: '营养录入',
    value: [],
    placeholder: '请选择',
    multiple: true,
    collapseTags: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '是',
        value: '1'
      },
      {
        label: '否',
        value: '0'
      }
    ]
  },
  perso_no: {
    type: 'input',
    label: '菜品/商品名称',
    value: '',
    placeholder: '请输入菜品/商品名称'
  },
  hasx: {
    type: 'select',
    label: '已有菜品/商品',
    value: '',
    placeholder: '请选择已有菜品/商品',
    dataList: [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]
  },
  attributes: {
    type: 'select',
    label: '属性',
    value: '',
    placeholder: '请选择属性',
    dataList: [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]
  }
}

// 规格
export const SPEC_LIST = [
  { name: '份' },
  { name: '碗' },
  { name: '瓶' },
  { name: '碟' },
  { name: '盅' },
  { name: '盆' },
  { name: '件' },
  { name: '串' },
  { name: '例' },
  { name: '只' },
  { name: '边' }
]

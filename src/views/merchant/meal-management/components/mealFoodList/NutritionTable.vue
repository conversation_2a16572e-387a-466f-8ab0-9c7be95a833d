<template>
  <div>
    <el-form
      :model="formData"
      :rules="formRuls"
      class=""
      ref="formIngredients"
      size="small"
    >
      <template v-for="nutrition in currentNutritionList">
        <div class="nutrition-item" :key="nutrition.key">
          <!-- <div class="nutrition-label">{{nutrition.name+'：'}}</div> -->
          <el-form-item :prop="nutrition.key" :label="nutrition.name+'：'" :rules="formRuls.nutrition">
            <el-input style="width: 120px;" :readonly="readonly" disabled v-model="formData[nutrition.key]" @change="changeNutrition" class="ps-input" placeholder="请输入"></el-input><span style="margin-left: 10px;">{{nutrition.unit}}</span>
          </el-form-item>
        </div>
      </template>
      <div class="text-center pointer">
        <span @click="showAll = !showAll" style="color:#027DB4;">{{ showAll ? '收起' : '查看更多营养信息' }}</span>
      </div>
    </el-form>
  </div>
</template>

<script>
import { NUTRITION_LIST } from '../../food-admin/constants'
export default {
  props: {
    tableDataNutrition: Object,
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let validataNutrition = (rule, value, callback) => {
      if (value) {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('营养数据有误，仅支持两位小数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      formData: {},
      nutritionList: NUTRITION_LIST,
      formRuls: {
        nutrition: [{ validator: validataNutrition, trigger: 'change' }]
      },
      showAll: false
    }
  },
  watch: {
    tableDataNutrition: {
      handler(val) {
        this.initData()
      },
      deep: true
    }
  },
  computed: {
    currentNutritionList: function () {
      let result = []
      if (!this.showAll) {
        result = this.nutritionList.slice(0, 4)
      } else {
        result = this.nutritionList
      }
      return result
    }
  },
  mounted() {
    console.log(this.tableDataNutrition)
    this.initData()
  },
  methods: {
    initData(data) {
      this.nutritionList.forEach(item => {
        let val = this.tableDataNutrition[item.key] ? this.tableDataNutrition[item.key] : 0
        this.$set(this.formData, item.key, val)
      })
    },
    // 修改营养信息
    changeNutrition(e) {
      this.$emit('update:tableDataNutrition', this.formData)
    }
  }
}
</script>

<style lang='scss'>
.nutrition-item{
  // display: flex;
  // justify-content: space-around;
  // flex-wrap: wrap;
  display: inline-block;
  width: 200px;
  .nutrition-label{
    margin-bottom: 3px;
    font-size: 14px;
    letter-spacing: 1px;
    color: #23282d;
  }
  .el-form-item__label{
    display: block;
    text-align: left;
    line-height: 1.5;
    float: none;
  }
}
</style>

<template>
  <div>
    <el-form ref="form" :model="form" label-width="120px" :rules="rules">
      <el-form-item label="日期">
        <span>{{ dateString }}</span>
      </el-form-item>

      <el-form-item label="菜谱名称" prop="name">
        <el-input v-model="form.name"></el-input>
      </el-form-item>

      <el-form-item label="推荐人群" prop="apply_group">
        <el-select v-model="form.apply_group" class="ps-select w-350">
          <el-option
            v-for="user in apply_groups"
            :label="user.name"
            :key="user.id"
            :value="user.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="设备类型：" prop="device_types">
        <el-select
          multiple
          v-model="form.device_types"
          placeholder="请选择设备类型"
          class="ps-select w-350"
          popper-class="ps-popper-select"
          @change="deviceTypeChange"
        >
          <el-option
            v-for="item in deviceArr"
            :key="item.key"
            :label="item.name"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备型号：" :prop="isDisabledModel?'':'device_model'">
        <el-select
          multiple
          v-model="form.device_model"
          placeholder="请选择设备型号"
          class="ps-select w-350"
          popper-class="ps-popper-select"
          :disabled="isDisabledModel"
        >
          <el-option
            v-for="item in deviceModelList"
            :key="item.key"
            :label="item.key"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="应用组织" prop="use_organizations">
        <el-select v-model="form.use_organizations" multiple collapse-tags>
          <el-option v-for="org in organizations" :key="org.id" :value="org.id" :label="org.name"></el-option>
        </el-select>
      </el-form-item>-->

      <el-form-item label="可见范围" prop="use_user_groups">
        <el-select :disabled="isDisabledGroup" v-model="form.use_user_groups" multiple collapse-tags class="ps-select w-350">
          <el-option
            v-for="group in groupList"
            :key="group.id"
            :value="group.id"
            :label="group.group_name"
          ></el-option>
        </el-select>
        <el-checkbox :disabled="isDisabledGroup" v-model="form.applied_to_visitor" class="ps-checkbox m-l-20">游客</el-checkbox>
      </el-form-item>
      <el-form-item label="是否沿用菜谱" prop="is_continue_to_used">
        <el-switch v-model="form.is_continue_to_used" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
      </el-form-item>
      <el-form-item label="是否作为意向菜谱展示" prop="is_intent_menu" label-width="160px">
        <el-switch v-model="form.is_intent_menu" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
// import { deviceList } from '../booking-setting/constantsAndConfig'
export default {
  name: 'EditRecipes',
  props: {
    editData: {
      type: Object
    }
  },
  data() {
    return {
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        // apply_group: [{ required: true, message: '请选择推荐人群', trigger: 'change' }]
        device_types: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
        device_model: [{ required: true, message: '请选择设备型号', trigger: 'change' }]
      },
      dateString: '',
      deviceArr: [],
      apply_groups: [],
      deviceModelList: [],
      groupList: [],
      organizations: [],
      form: {
        id: -1,
        name: '',
        apply_group: '',
        device_types: [],
        device_model: [],
        use_organizations: '',
        use_user_groups: '',
        applied_to_visitor: false,
        is_continue_to_used: false,
        is_intent_menu: false
      },
      isDisabledModel: false,
      isDisabledGroup: true
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.handleDateString()
      await this.requestDietGroupList()
      await this.userGroupList()
      await this.getOrganizationList()
      await this.getOrgDeviceList()
      this.setRawData()
    },

    deviceTypeChange() {
      this.form.device_model = []
      this.$refs.form.clearValidate()
      this.getDeviceModel()
    },

    // // 获取设备类型
    // async getDeviceType() {
    //   const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
    //   if (res.code === 0) {
    //     this.deviceArr = res.data
    //   } else {
    //     this.$message.error(res.msg)
    //   }
    // },

    async getDeviceModel() {
      // 可见范围选项只有在设备类型为H5、小程序、智能秤的时候才能选
      let status = ['H5', 'MAPP', 'ZNC']
      this.isDisabledGroup = true
      status.map(item => {
        if (this.form.device_types.includes(item)) {
          this.isDisabledGroup = false
        }
      })
      // H5和小程序不需要选择设备型号（置灰），也不需要获取设备型号
      let deviceTypes = this.form.device_types.filter(t => { return t !== "H5" && t !== "MAPP" })
      if (!deviceTypes.length) {
        this.isDisabledModel = true;
        return
      } else {
        this.isDisabledModel = false
      }
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceModelPost({
        device_types: deviceTypes
      })
      if (res.code === 0) {
        this.deviceModelList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },

    // 获取当前组织设备类型
    async getOrgDeviceList() {
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceTypePost({
        source: 'self'
      })
      if (res.code === 0) {
        this.deviceArr = [
          { name: 'H5', key: 'H5' },
          { name: '小程序', key: 'MAPP' },
          ...res.data
        ]
      } else {
        this.$message.error(res.msg)
      }
    },

    handleDateString() {
      const parentData = this.$parent.$parent
      if (parentData.headerType === 'week') {
        const weekStart = parentData.$refs.weekHeaderRef.activeWeek
        const weekEnd = dayjs(weekStart)
          .endOf('week')
          .add(1, 'd')
          .format('YYYY-MM-DD')
        this.dateString = `${weekStart} 至 ${weekEnd}`
      } else {
        this.dateString = parentData.$refs.weekHeaderRef.activeMonth
      }
    },

    setRawData() {
      if (!this.editData.id) {
        return
      }
      const target = this.deviceArr
        .filter(d => this.editData.device_types.includes(d.name))
        .map(r => r.key)
      this.form = {
        id: this.editData.id,
        name: this.editData.name,
        device_types: target || [],
        device_model: this.editData.device_model,
        apply_group: this.editData.apply_group,
        use_user_groups: this.editData.use_user_groups,
        applied_to_visitor: this.editData.applied_to_visitor,
        is_continue_to_used: this.editData.is_continue_to_used,
        is_intent_menu: this.editData.is_intent_menu
      }
      this.getDeviceModel()
    },

    // 获取使用人群
    async requestDietGroupList() {
      const param = {
        page: 1,
        page_size: 9999
      }
      const res = await this.$apis.apiBackgroundFoodDietGroupListPost(param)
      if (res.code === 0) {
        this.apply_groups = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },

    // 获取组织信息
    async getOrganizationList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrganizationOrganizationListPost({
        status: 'enable',
        page: 1,
        page_size: 9999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.organizations = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },

    // // 对象数组去重
    // unique(arr, key) {
    //   if (!arr) return arr
    //   if (key === undefined) return [...new Set(arr)]
    //   const map = {
    //     'string': e => e[key],
    //     'function': e => key(e),
    //   }
    //   const fn = map[typeof key]
    //   const obj = arr.reduce((o, e) => (o[fn(e)] = e, o), {})
    //   return Object.values(obj)
    // },

    // 获取设备数据
    async requestMenuDeviceList() {
      const params = {}
      const res = await this.$apis.apiBackgroundFoodMenuDeviceListPost(params)
      if (res.code === 0) {
        // const result = this.unique(res.data, 'device_type')
        // result.forEach(r => {
        //   r.children = res.data.filter(d => d.device_type === r.device_type)
        // })
        this.menuDevice = res.data
      } else {
        this.$message.error(res.msg)
      }
    },

    // 获取分组信息
    async userGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        page: 1,
        page_size: 9999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.groupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

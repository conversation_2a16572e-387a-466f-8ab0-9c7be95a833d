<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        label-width="120px"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <!-- <div class="align-r">
            <el-button size="mini" @click="gotoExport">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div> -->
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item">
              <template #userGroups="{ row }">
                <span>
                  {{ row.card_user_groups_alias.join(',') }}
                </span>
              </template>
              <template #operation="{ row }">
                <el-button
                  type="text"
                  size="small"
                  @click="clickCorrelationOrder(row)"
                  v-if="row.coupon_order.length"
                >
                  查看
                </el-button>
              </template>
            </table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <!-- <print-setting
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting> -->
    <detail-correlation-order-dialog
      v-if="correlationOrderDialogShow"
      :isshow.sync="correlationOrderDialogShow"
      :correlationOrderInfo="correlationOrderInfo"
    />
    <!-- :paramsInfo="paramsInfo" -->
  </div>
</template>

<script>
// import { THIRD_RECONCILIATION } from './constantsConfig'
// import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
// import { deepClone } from '@/utils'
import DetailCorrelationOrderDialog from './components/DetailCorrelationOrderDialog.vue'
import { debounce, to } from '@/utils'
export default {
  components: { DetailCorrelationOrderDialog },
  name: 'ThirdReconciliation',
  mixins: [report],
  data() {
    return {
      isLoading: false,
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: {
        org_ids: {
          type: 'organizationSelect',
          multiple: true,
          checkStrictly: true,
          isLazy: false,
          label: '适用组织',
          value: [],
          placeholder: '请选择适用组织'
        },
        receive_time: {
          type: 'daterange',
          label: '领取时间',
          value: [],
          clearable: true
        },
        name: {
          type: 'input',
          label: '姓名',
          labelWidth: '100px',
          value: '',
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          labelWidth: '100px',
          value: '',
          placeholder: '请输入人员编号'
        },
        use_time: {
          type: 'daterange',
          label: '使用时间',
          value: [],
          clearable: true
        },
        card_department_group_id: {
          type: 'departmentSelect',
          multiple: false,
          isLazy: false,
          checkStrictly: true,
          label: '部门',
          value: '',
          placeholder: '请选择部门'
        },
        card_user_group_ids: {
          type: 'groupSelect',
          label: '分组',
          value: [],
          placeholder: '请选择分组',
          multiple: true,
          collapseTags: true,
          filterable: true
        }
      },
      correlationOrderDialogShow: false,
      correlationOrderInfo: {},
      tableSettings: [
        { label: '所属组织', key: 'organization_name' },
        { label: '部门', key: 'card_department_group_name' },
        { label: '分组', key: 'card_user_groups_alias', type: 'slot', slotName: 'userGroups' },
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '领取时间', key: 'receive_time' },
        { label: '使用时间', key: 'use_time' },
        { label: '关联订单', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right' }
      ],
      collect: [
        // 统计
        { key: 'receive_count', value: 0, label: '发放数目:' },
        { key: 'receive_coupon_count', value: 0, label: '领取数目:' },
        { key: 'used_coupon_count', value: 0, label: '使用数目:' }
      ]
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
    // this.getSearchQuery()
  },
  methods: {
    initLoad() {
      // if (!isFirst) {
      // this.currentTableSetting = this.tableSetting
      this.getWithdrawList()
      // }
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.getWithdrawList()
    },

    // 节下流咯
    searchHandle: debounce(function (e) {
      // if (e && e === 'search') {
      this.currentPage = 1
      this.getWithdrawList()
      // }
    }, 300),
    // 获取搜索的数据
    // async getSearchQuery() {
    //   const res = await this.$apis.apiBackgroundReportCenterSelectDataSelectListPost({
    //     request_report_path: 'background_report_center/data_report/third_order_list'
    //   })
    //   if (res.code === 0) {
    //     if (res.data && res.data.OrderType) {
    //       this.searchFormSetting.order_type.dataList = res.data.OrderType
    //     }
    //   } else {}
    // },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'receive_time' && key !== 'use_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            if (key === 'receive_time') {
              params.start_receive_time = data[key].value[0]
              params.end_receive_time = data[key].value[1]
            }
            if (key === 'use_time') {
              params.start_use_time = data[key].value[0]
              params.end_use_time = data[key].value[1]
            }
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getWithdrawList() {
      const params = this.formatQueryParams(this.searchFormSetting)
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundCouponCouponListPost({
          ...params,
          coupon_manage_id: this.$route.query.id,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getWithdrawList()
    },
    clickCorrelationOrder(row) {
      this.correlationOrderInfo = row
      this.correlationOrderDialogShow = true
    }
    // 导出
    // gotoExport() {
    //   const option = {
    //     type: 'ExportThirdReconciliation',
    //     params: {
    //       ...this.formatQueryParams(this.searchFormSetting),
    //       page: this.currentPage,
    //       page_size: this.pageSize
    //     }
    //   }
    //   this.exportHandle(option)
    // }
    // gotoPrint() {
    //   const params = this.formatQueryParams(this.searchFormSetting)
    //   const { href } = this.$router.resolve({
    //     name: "Print",
    //     query: {
    //       print_date_state: true,
    //       print_type: this.printType,
    //       print_title: '第三方对账表',
    //       result_key: 'result', // 返回的数据处理的data keys
    //       api: 'apiBackgroundReportCenterDataReportThirdOrderListPost', // 请求的api
    //       show_summary: false, // 合计
    //       show_print_header_and_footer: true, // 打印页头页尾
    //       table_setting: JSON.stringify(this.tableSetting),
    //       current_table_setting: JSON.stringify(this.currentTableSetting),
    //       collect: JSON.stringify(this.collect),
    //       push_summary: false, // 合计添加到到table数据最后
    //       params: JSON.stringify({
    //         ...params,
    //         page: 1,
    //         page_size: this.totalCount || 10
    //       })
    //     }
    //   });
    //   window.open(href, "_blank");
    // }
  }
}
</script>
<style lang="scss" scoped></style>

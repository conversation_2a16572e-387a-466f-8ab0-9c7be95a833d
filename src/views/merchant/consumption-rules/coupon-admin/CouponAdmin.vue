<template>
  <div class="acticity-recharges container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="80px"
      @search="searchHandle"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon
            color="origin"
            type="add"
            v-permission="['background_coupon.coupon_manage.add']"
            @click="gotoAddOrEditCoupon('add')"
          >
            新增优惠券
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="name" label="优惠券名称" align="center"></el-table-column>
          <el-table-column
            prop="use_organization_name"
            label="适用组织"
            align="center"
          ></el-table-column>
          <el-table-column prop="coupon_type_alias" label="券类型" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center" width="180"></el-table-column>
          <el-table-column prop="use_end_time" label="失效时间" align="center" width="180"></el-table-column>
          <el-table-column prop="used_time" label="使用时间" align="center" width="200"></el-table-column>
          <el-table-column prop="meal_types" label="适用餐段" align="center" width="100"></el-table-column>
          <el-table-column prop="is_enable" label="状态" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.is_enable"
                @change="mulOperation('status', scope.row)"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
                :disabled="!allPermissions.includes('background_coupon.coupon_manage.modify')"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="receive_count" label="发放券数" align="center"></el-table-column>
          <el-table-column
            prop="receive_coupon_count"
            label="领取券数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="used_coupon_count"
            label="使用券数"
            align="center"
          ></el-table-column>
          <el-table-column prop="operator_name" label="创建人" align="center"></el-table-column>
          <el-table-column prop="remark" label="备注" align="center">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.remark" placement="top">
                <div>{{textFormat(scope.row.remark, 7)}}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="140" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="clickCouponDetail(scope.row)">
                详情
              </el-button>
              <el-button
                type="text"
                size="small"
                v-permission="['background_coupon.coupon_manage.modify']"
                @click="gotoAddOrEditCoupon('edit',scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                class="ps-warn-text"
                size="small"
                v-permission="['background_coupon.coupon_manage.delete']"
                @click="mulOperation('del', scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce, to, textFormat } from '@/utils'
import { MEAL_LIST } from '../constants.js'
import { mapGetters } from 'vuex'
export default {
  name: 'MerchantCouponAdmin',
  computed: {
    ...mapGetters(['allPermissions'])
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        use_organization: {
          type: 'organizationSelect',
          multiple: true,
          checkStrictly: true,
          isLazy: false,
          label: '适用组织',
          value: [],
          placeholder: '请选择适用组织'
        },
        name: {
          type: 'input',
          label: '优惠券名称',
          labelWidth: '100px',
          value: '',
          placeholder: '请输入优惠券名称'
        },
        coupon_type: {
          type: 'select',
          label: '优惠类型',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择优惠类型',
          dataList: [
            {
              label: '满减券',
              value: 'FULL_DISCOUNT'
            },
            {
              label: '代金券',
              value: 'INSTANT_DISCOUNT'
            },
            {
              label: '折扣券',
              value: 'DISCOUNT'
            },
            {
              label: '兑换券',
              value: 'EXCHANGE'
            }
          ]
        },
        is_enable: {
          type: 'select',
          label: '状态',
          value: '',
          placeholder: '请选择状态',
          dataList: [
            {
              label: '使用',
              value: true
            },
            {
              label: '禁用',
              value: false
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '创建时间',
          value: [],
          clearable: true
        },
        use_time: {
          type: 'daterange',
          label: '有效期',
          value: [],
          clearable: true
        },
        meal_type: {
          type: 'select',
          label: '适用餐段',
          value: '',
          listNameKey: 'name',
          listValueKey: 'value',
          placeholder: '请选择使用餐段',
          clearable: true,
          // multiple: true,
          collapseTags: true,
          dataList: MEAL_LIST
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDiscountCouponList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      // if (e && e === 'search') {
      this.currentPage = 1
      this.getDiscountCouponList()
      // }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time' && key !== 'use_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            if (key === 'select_time') {
              params.start_create_time = data[key].value[0]
              params.end_create_time = data[key].value[1]
            }
            if (key === 'use_time') {
              params.use_start_time = data[key].value[0]
              params.use_end_time = data[key].value[1]
            }
          }
        }
      }
      return params
    },
    // 获取列表
    async getDiscountCouponList() {
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      const [err, res] = await to(this.$apis.apiBackgroundCouponCouponManageListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          if (item.use_start_time && item.use_end_time) {
            item.used_time = item.use_start_time + '至' + item.use_end_time
          }
          item.meal_types = item.meal_type_list_alias.join('、')
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDiscountCouponList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDiscountCouponList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    mulOperation(type, data) {
      let title = '提示'
      let content = ''
      switch (type) {
        case 'del':
          content = '删除优惠券后数据不可恢复，是否删除？'
          break
        case 'status':
          if (data.is_enable) {
            content = '确定启用该优惠券规则？'
          } else {
            content = '关闭后优惠券不可使用，是否确认关闭？'
          }
          break
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {}
            switch (type) {
              case 'del':
                params.ids = [data.id]
                this.getCouponDel(params)
                break
              case 'status':
                params = data
                this.getCouponManageModify(params)
                break
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              if (type === 'status') {
                data.is_enable = !data.is_enable
              }
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 优惠券状态
    async getCouponManageModify(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundCouponCouponManageModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.getDiscountCouponList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getCouponDel(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundCouponCouponManageDeletePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (this.currentPage > 1 && this.tableData.length === 1) {
          this.currentPage--
        }
        this.getDiscountCouponList()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCouponDetail(row) {
      this.$router.push({
        name: 'MerchantCouponAdminDetail',
        query: {
          id: row.id
        }
      })
    },
    gotoAddOrEditCoupon(type, data) {
      let query = {}
      if (type === 'edit') {
        query = { data: encodeURIComponent(JSON.stringify(data)) }
      }
      this.$router.push({
        name: 'MerchantAddCoupon',
        params: {
          type
        },
        query
      })
    },
    textFormat
  }
}
</script>

<style lang="scss" scoped>
</style>

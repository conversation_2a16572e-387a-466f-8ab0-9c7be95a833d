<template>
  <div class="MerchantAddCoupon container-wrapper">
    <div class="table-wrapper">
      <div class="table-header" style="display: flex; justify-content: space-between;">
        <div class="table-title">{{type==='add'?'新建':'编辑'}}优惠券</div>
      </div>
      <el-form
        v-loading="isLoading"
        :model="couponForm"
        ref="couponFormRef"
        :rules="couponFormRule"
        style="padding: 0 25px;"
        label-width="130px"
      >
        <el-form-item label="优惠券名称" prop="couponName">
          <el-input v-model="couponForm.couponName" :disabled="type==='edit'" maxlength="30" class="ps-input w-300"></el-input>
        </el-form-item>
        <el-form-item label="券类型" prop="couponType">
          <el-select popper-class="ps-popper-select w-300" v-model="couponForm.couponType" :disabled="type==='edit'">
            <el-option label="满减券" value="FULL_DISCOUNT"></el-option>
            <el-option label="代金券" value="INSTANT_DISCOUNT"></el-option>
            <el-option label="折扣券" value="DISCOUNT"></el-option>
            <el-option label="兑换券" value="EXCHANGE"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="适用组织" prop="userOrg" key="userOrgKey">
          <organization-select
            class="search-item-w ps-input w-300"
            placeholder="请选择所属组织"
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            v-model="couponForm.userOrg"
            :append-to-body="true"
            :disabled="type==='edit'"
            >
          </organization-select>
        </el-form-item>
        <el-form-item label="使用条件" v-if="couponForm.couponType === 'FULL_DISCOUNT'" key="FULL_DISCOUNT">
          <span>订单金额满</span>
          <el-form-item class="form-content-inline" prop="maxPrice" key="maxPrice">
            <el-input v-model="couponForm.maxPrice" :disabled="type==='edit'" class="margin-input w-180 ps-input"></el-input>
          </el-form-item>
          <span>元减去</span>
          <el-form-item class="form-content-inline" prop="minPrice" key="minPrice">
            <el-input v-model="couponForm.minPrice" :disabled="type==='edit'" class="margin-input w-180 ps-input"></el-input>
          </el-form-item>
          <span>元</span>
        </el-form-item>
        <el-form-item label="使用条件" v-if="couponForm.couponType === 'INSTANT_DISCOUNT'"  key="INSTANT_DISCOUNT">
          <span>优惠金额</span>
          <el-form-item class="form-content-inline" prop="preferentialPrice" key="preferentialPrice">
            <el-input v-model="couponForm.preferentialPrice" :disabled="type==='edit'" class="margin-input w-180 ps-input"></el-input>
          </el-form-item>
          <span>元</span>
        </el-form-item>
        <el-form-item label="使用条件" v-if="couponForm.couponType === 'DISCOUNT'" key="DISCOUNT">
          <span>优惠券折扣</span>
          <el-form-item class="form-content-inline" prop="preferentialDiscount"  key="preferentialDiscount">
            <el-input v-model="couponForm.preferentialDiscount" :disabled="type==='edit'" class="margin-input w-180 ps-input"></el-input>
            <span>折</span>
          </el-form-item>
          <el-form-item class="m-t-10">
            <span>是否设置每人最大折扣金额</span>
            <el-switch v-model="couponForm.isSetMaxDiscount" :disabled="type==='edit'" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            <el-form-item prop="userMaxDiscount" class="form-content-inline" v-if="couponForm.isSetMaxDiscount" key="userMaxDiscount">
              <el-input v-model="couponForm.userMaxDiscount" :disabled="type==='edit'" class="margin-input w-180 ps-input"></el-input>
            </el-form-item>
            <span>元</span>
          </el-form-item>
          <el-form-item v-if="couponForm.isSetMaxDiscount" class="m-t-10">
            <span>每人最大折扣金额上限：</span>
            <el-radio-group class="ps-radio" v-model="couponForm.maxDiscountType" :disabled="type==='edit'">
              <el-radio label="all">整单原价扣款</el-radio>
              <el-radio label="beyond">超出部分原价扣款</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form-item>
        <el-form-item v-if="couponForm.couponType === 'EXCHANGE'">
          <el-form-item>
            <span>优惠券是否需要满足订单金额使用</span>
            <el-switch v-model="couponForm.isOrderFee" :disabled="type==='edit'" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            <span class="margin-l-20">订单金额</span>
            <el-form-item prop="orderFee" class="form-content-inline" key="orderFee">
              <el-input v-model="couponForm.orderFee" :disabled="type==='edit'" class="margin-input w-180 ps-input"></el-input>
            </el-form-item>
            <span>元</span>
          </el-form-item>
          <el-form-item prop="foodMaxCount" key="foodMaxCount">
            <span>兑换券兑换菜品/套餐上限数量</span>
            <el-input v-model="couponForm.foodMaxCount" :disabled="type==='edit'" class="margin-input w-180 ps-input"></el-input>
          </el-form-item>
        </el-form-item>
        <el-form-item v-if="couponForm.couponType === 'EXCHANGE'" key="EXCHANGE">
          <el-button type="primary" size="mini" :disabled="type==='edit'" class="ps-origin-btn" @click="openChooseDialog('food')">去选择</el-button>
          <el-table
          :data="couponForm.foodList"
          ref="tableDataRef"
          header-row-class-name="ps-table-header-row"
          style="width: 500px;"
          >
            <el-table-column prop="name" label="菜品名称" align="center"></el-table-column>
            <el-table-column prop="xx" label="操作" width="100px" align="center">
              <template slot-scope="scope">
                <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                @click="deleteFoodHandle(scope.row.index)"
                :disabled="type==='edit'"
              >移除</el-button>
              </template>
            </el-table-column>
            <el-table-column label="" prop="" width="80px" align="center">
              <template>
                <img class="drop-img" src="@/assets/img/drop.png" alt="" />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="状态" prop="couponStatus">
          <el-radio-group class="ps-radio" v-model="couponForm.couponStatus">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="适用餐段" prop="userMeal">
          <el-checkbox-group v-model="couponForm.userMeal" :disabled="type==='edit'">
            <el-checkbox v-for="item in mealList" :key="item.value" :label="item.value" class="ps-checkbox">{{item.name}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="优惠券领取方式" prop="receiveType">
          <el-radio-group class="ps-radio" :disabled="type==='edit'" v-model="couponForm.receiveType">
            <el-radio label="MANUAL">手动领取</el-radio>
            <el-radio label="AUTOMATIC">自动领取</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="领取时间" :prop="type==='edit'?'':'receiveTime'" v-if="couponForm.receiveType === 'MANUAL'" key="receiveTime">
          <el-date-picker
            v-model="couponForm.receiveTime"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            clearable
            class="ps-poper-picker"
            :disabled="type==='edit'"
          ></el-date-picker>
        </el-form-item>
        <el-form-item :label="couponForm.receiveType === 'MANUAL'?'发放数目':'每人发放数目'" class="form-content-inline" prop="sendCount"  key="sendCount">
          <el-input v-model="couponForm.sendCount" :disabled="type==='edit'&&couponForm.receiveType=='AUTOMATIC'" class="ps-input"></el-input>
        </el-form-item>
        <el-form-item label="每人最多可领取"  prop="receiveCount" class="form-content-inline" v-if="couponForm.receiveType === 'MANUAL'" key="receiveCount">
          <el-input v-model="couponForm.receiveCount" :disabled="type==='edit'&&couponForm.receiveType=='AUTOMATIC'" class="ps-input"></el-input>
        </el-form-item>
        <el-form-item label="优惠券是否限制使用时间" prop="isLimitTime" label-width="180px">
          <el-switch v-model="couponForm.isLimitTime" :disabled="type==='edit'" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          <el-form-item label="优惠券有效期" :prop="type==='edit'?'':'limitTime'" class="form-content-inline">
            <el-date-picker
              v-model="couponForm.limitTime"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              clearable
              class="ps-poper-picker"
              :disabled="type==='edit'"
            ></el-date-picker>
          </el-form-item>
        </el-form-item>
        <el-form-item label="领取条件">
          <el-radio-group class="ps-radio" v-model="couponForm.receiveFactor">
            <el-radio label="ALL">所有人</el-radio>
            <el-radio label="GROUP">指定分组</el-radio>
            <el-radio label="PERSON">指定人员</el-radio>
          </el-radio-group>
          <el-form-item prop="receiveGroup" v-if="couponForm.receiveFactor === 'GROUP'">
            <user-group-select
              class="search-item-w ps-input w-250"
              v-model="couponForm.receiveGroup"
              :multiple="true"
              placeholder="请下拉选择"
            ></user-group-select>
          </el-form-item>
        </el-form-item>
        <el-form-item label="" prop="personList" v-if="couponForm.receiveFactor === 'PERSON'">
          <el-button type="primary" size="mini" class="ps-origin-btn" @click="openChooseDialog('choosePerson')">去选择</el-button>
          <span class="margin-l-20 m-r-20">已选人数：{{personList.length}}人</span>
          <el-button type="primary" size="mini" class="ps-origin-btn" @click="openImport()">导入用户</el-button>
          <div>
            <el-table :data="personList" max-height="400" header-row-class-name="ps-table-header-row">
              <el-table-column prop="organization_alias" label="所属组织" align="center"></el-table-column>
              <el-table-column prop="department_group_name" label="部门" align="center"></el-table-column>
              <el-table-column prop="card_user_group_alias" label="分组" align="center"></el-table-column>
              <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
              <el-table-column prop="name" label="姓名" align="center"></el-table-column>
              <el-table-column prop="importStatus" label="导入状态" align="center"></el-table-column>
              <el-table-column width="180" label="操作" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    class="ps-warn"
                    @click="delOperation(scope.$index)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            maxlength="200"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
            v-model="couponForm.remark"
            :disabled="type==='edit'">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" class="ps-origin-btn w-150" @click="checkForm">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
    <person-dialog
      :isshow.sync="dialogPersonVisible"
      :personList="chooseList"
      @confirmPerson="confirmPerson"/>
    <food-dialog
      :isshow.sync="dialogFoodVisible"
      :choose-list="chooseList"
      @confirmPerson="confirmFood"/>
      <!-- 导入用户 -->
      <import-dialog
      v-if="importShowDialog"
      :templateUrl="templateUrl"
      :tableSetting="tableSetting"
      :show.sync="importShowDialog"
      :title="'导入用户'"
      importType="data"
      @confirm="confirmImportData"
      :loading="isImportLoading"
    ></import-dialog>
  </div>
</template>

<script>
import OrganizationSelect from '@/components/OrganizationSelect'
import UserGroupSelect from '@/components/UserGroupSelect'
import PersonDialog from './components/PersonDialog'
import FoodDialog from './components/FoodDialog'
import { MEAL_LIST } from '../constants'
import { divide, parseTime, times } from '@/utils'
import Sortable from 'sortablejs'
export default {
  name: 'MerchantAddCoupon',
  components: {
    OrganizationSelect,
    UserGroupSelect,
    PersonDialog,
    FoodDialog
  },
  // mixins: [activatedLoadData],
  data() {
    let validataFee = (rule, value, callback) => {
      if (value) {
        let reg = /(^[1-9](\d+)?(\.\d{1,2})?$)|(^\d\.\d{1,2}$)/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else if (Number(value) >= 99999.99) {
          callback(new Error('金额单位上限为‘万’'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入金额'))
      }
    }
    let validatorReceiveTime = (rule, value, callback) => {
      if (value.length) {
        let now = new Date(parseTime(new Date(), '{y}-{m}-{d}')).getTime()
        let start = new Date(this.couponForm.receiveTime[0]).getTime()
        let end = new Date(this.couponForm.receiveTime[1]).getTime()
        if (start < now) {
          callback(new Error('领取开始时间不能小于当前时间'))
        } else if (this.couponForm.limitTime.length && start > new Date(this.couponForm.limitTime[0]).getTime()) {
          callback(new Error('领取开始时间必须小于使用开始时间'))
        } else if (this.couponForm.limitTime.length && end >= new Date(this.couponForm.limitTime[1]).getTime()) {
          callback(new Error('领取结束时间必须小于使用结束时间'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请选择领取时间'))
      }
    }
    let validatorLimitTime = (rule, value, callback) => {
      if (value.length) {
        let now = new Date(parseTime(new Date(), '{y}-{m}-{d}')).getTime()
        let start = new Date(this.couponForm.limitTime[0]).getTime()
        let end = new Date(this.couponForm.limitTime[1]).getTime()
        if (start < now) {
          callback(new Error('开始时间不能小于当前时间'))
        } else if (this.couponForm.receiveTime.length && start < new Date(this.couponForm.receiveTime[0]).getTime()) {
          callback(new Error('开始时间必须小于领取开始时间'))
        } else if (this.couponForm.receiveTime.length && end < new Date(this.couponForm.receiveTime[1]).getTime()) {
          callback(new Error('结束时间必须大于领取结束时间'))
        } else {
          callback()
        }
      } else if (this.couponForm.isLimitTime) {
        callback(new Error('请选择发放时间'))
      } else {
        callback()
      }
    }
    let validatorSendCount = (rule, value, callback) => {
      if (value) {
        let reg = /^[1-9][0-9]*$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正整数'))
        } else if (value < 1) {
          callback(new Error('数量必须大于0'))
        } else if (value > 100000) {
          callback(new Error('数量必须不能大于99999'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请填写发放张数'))
      }
    }
    let validatorReceiveCount = (rule, value, callback) => {
      if (value) {
        let reg = /^[1-9][0-9]*$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正整数'))
        } else if (value < 1) {
          callback(new Error('数量必须大于0'))
        } else if (value > 10000) {
          callback(new Error('数量必须不能大于9999'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请填写数量'))
      }
    }
    let validataDiscount = (rule, value, callback) => {
      if (value) {
        let reg = /^\d+(\.\d{1})?$/
        if (value < 1 || !reg.test(value)) {
          callback(new Error('数量必须大于0,且输入一位小数'))
        } else if (value >= 10 || !reg.test(value)) {
          callback(new Error('数量必须不能大于9,且输入一位小数'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请填写折扣数'))
      }
    }
    return {
      isLoading: false, // 刷新数据
      type: 'add',
      progress: '',
      couponId: '',
      couponForm: {
        couponName: '', // 优惠券名称
        couponType: 'FULL_DISCOUNT', // 券类型
        userOrg: [], // 使用组织
        maxPrice: '', // 满减金额
        minPrice: '', // 满减优惠金额
        preferentialPrice: '', // 优惠金额
        preferentialDiscount: '', // 优惠折扣
        isSetMaxDiscount: false, // 是否设置每人最大折扣金额
        userMaxDiscount: '', // 每人最大折扣金额
        maxDiscountType: 'all', // 每人最大折扣金额上限类型
        isOrderFee: false, // 优惠券是否需要满足订单金额使用
        orderFee: '', // 订单金额
        foodMaxCount: '', // 兑换券兑换菜品/套餐上限数量
        foodList: [],
        couponStatus: '', // 状态
        userMeal: [], // 适用餐段
        receiveType: 'MANUAL', // 优惠券领取方式
        receiveTime: [], // 手动领取时间
        sendCount: '', // 发放数目
        receiveCount: '', // 每人最多可领取数目
        isLimitTime: false, // 是否限制时间
        limitTime: [], // 限制时间
        receiveFactor: 'ALL', // 限制条件
        receiveGroup: [], // 领取限制分组
        receivePersonList: [], // 领取限制人
        remark: '' // 备注
      },
      couponFormRule: {
        couponName: [{ required: true, message: '请输入优惠券名称', trigger: ['change', 'blur'] }],
        couponType: [{ required: true, message: '请选择券类型', trigger: ['change', 'blur'] }],
        userOrg: [{ required: true, message: '请选择适用组织', trigger: ['change', 'blur'] }],
        maxPrice: [{ required: true, validator: validataFee, trigger: ['change', 'blur'] }],
        minPrice: [{ required: true, validator: validataFee, trigger: ['change', 'blur'] }],
        preferentialPrice: [{ required: true, validator: validataFee, trigger: ['change', 'blur'] }],
        preferentialDiscount: [{ required: true, validator: validataDiscount, trigger: ['change', 'blur'] }],
        userMaxDiscount: [{ required: true, validator: validataFee, trigger: ['change', 'blur'] }],
        foodMaxCount: [{ required: true, validator: validatorReceiveCount, trigger: "blur" }],
        couponStatus: [{ required: true, message: '请输入状态', trigger: ['change', 'blur'] }],
        userMeal: [{ required: true, message: '请选择适用餐段', trigger: ['change', 'blur'] }],
        receiveType: [{ required: true, message: '请选择优惠券领取方式', trigger: ['change', 'blur'] }],
        receiveTime: [{ required: true, validator: validatorReceiveTime, trigger: ['change', 'blur'] }],
        sendCount: [{ required: true, validator: validatorSendCount, trigger: ['change', 'blur'] }],
        receiveCount: [{ required: true, validator: validatorReceiveCount, trigger: ['change', 'blur'] }],
        limitTime: [{ validator: validatorLimitTime, trigger: ['change', 'blur'] }],
        receiveGroup: [{ required: true, message: '请选择分组', trigger: ['change', 'blur'] }]
      },
      mealList: MEAL_LIST,
      chooseList: [],
      personList: [],
      sortList: [],
      SortWrap: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - 24 * 60 * 60 * 1000);
        }
      },
      dialogPersonVisible: false,
      dialogFoodVisible: false,
      tableSetting: [],
      templateUrl: '',
      importShowDialog: false,
      isImportLoading: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.$route.query.data) {
        let info = JSON.parse(decodeURIComponent(this.$route.query.data))
        console.log(info)
        this.couponId = info.id
        this.couponForm.couponName = info.name
        this.couponForm.couponType = info.coupon_type
        this.couponForm.userOrg = info.use_organization
        this.couponForm.couponStatus = info.is_enable
        this.couponForm.userMeal = info.meal_type_list
        this.couponForm.receiveType = info.receive_type
        // this.couponForm.sendCount = info.issued_number
        this.couponForm.isLimitTime = info.is_use_time
        this.couponForm.limitTime = info.use_start_time && info.use_end_time ? [info.use_start_time, info.use_end_time] : []
        this.couponForm.receiveFactor = info.receive_condition
        this.couponForm.remark = info.remark
        if (this.couponForm.couponType === 'FULL_DISCOUNT') {
          this.couponForm.maxPrice = divide(info.use_condition.full_money)
          this.couponForm.minPrice = divide(info.use_condition.reduce)
        } else if (this.couponForm.couponType === 'INSTANT_DISCOUNT') {
          this.couponForm.preferentialPrice = divide(info.use_condition.reduce)
        } else if (this.couponForm.couponType === 'DISCOUNT') {
          this.couponForm.preferentialDiscount = info.use_condition.discount
          this.couponForm.isSetMaxDiscount = info.use_condition.is_max
          if (this.couponForm.isSetMaxDiscount) {
            this.couponForm.userMaxDiscount = divide(info.use_condition.max_money)
          }
          this.couponForm.maxDiscountType = info.use_condition.max_discount
        } else if (this.couponForm.couponType === 'EXCHANGE') {
          this.couponForm.isOrderFee = info.use_condition.is_satisfy
          if (this.couponForm.isOrderFee) {
            this.couponForm.orderFee = divide(info.use_condition.satisfy_money)
          }
          this.couponForm.foodMaxCount = info.use_condition.max_num
          this.couponForm.foodList = info.use_condition.exchange_list.map((v, index) => {
            return {
              id: v.food_id,
              name: v.food_name
            }
          })
        }
        if (this.couponForm.receiveType === 'MANUAL') {
          this.couponForm.sendCount = info.issued_number
          this.couponForm.receiveTime = info.receive_start_time && info.receive_end_time ? [info.receive_start_time, info.receive_end_time] : []
          this.couponForm.receiveCount = info.manual_receive_number
        } else if (this.couponForm.receiveType === 'AUTOMATIC') {
          this.couponForm.sendCount = info.automatic_receive_number
        }
        if (this.couponForm.receiveFactor === 'GROUP') {
          this.couponForm.receiveGroup = info.card_user_group
        } else if (this.couponForm.receiveFactor === 'PERSON') {
          this.personList = info.card_info_list.map((item, index) => {
            return {
              id: item.id,
              organization_alias: item.organization_alias,
              department_group_name: item.department_group_name,
              card_user_group_alias: item.card_user_group_alias,
              person_no: item.person_no,
              name: item.name,
            }
          })
          console.log('info.card_info', this.personList)
        }
      }
      if (this.$route.params.type) {
        this.type = this.$route.params.type
      }
    },
    checkForm() {
      this.$refs.couponFormRef.validate(valid => {
        if (valid) {
          if (!this.personList.length && this.couponForm.receiveFactor === 'PERSON') {
            return this.$message.error('请选择人员')
          }
          let params = {
            name: this.couponForm.couponName,
            coupon_type: this.couponForm.couponType,
            use_organization: this.couponForm.userOrg,
            is_enable: this.couponForm.couponStatus,
            meal_type_list: this.couponForm.userMeal,
            receive_type: this.couponForm.receiveType,
            issued_number: this.couponForm.sendCount,
            is_use_time: this.couponForm.isLimitTime,
            use_start_time: this.couponForm.limitTime[0],
            use_end_time: this.couponForm.limitTime[1],
            receive_condition: this.couponForm.receiveFactor,
            remark: this.couponForm.remark
          }
          let useCondition = {}
          if (this.couponForm.couponType === 'FULL_DISCOUNT') {
            useCondition.full_money = times(this.couponForm.maxPrice)
            useCondition.reduce = times(this.couponForm.minPrice)
          } else if (this.couponForm.couponType === 'INSTANT_DISCOUNT') {
            useCondition.reduce = times(this.couponForm.preferentialPrice)
          } else if (this.couponForm.couponType === 'DISCOUNT') {
            useCondition.discount = this.couponForm.preferentialDiscount
            useCondition.is_max = this.couponForm.isSetMaxDiscount
            if (this.couponForm.isSetMaxDiscount) {
              useCondition.max_money = times(Number(this.couponForm.userMaxDiscount))
            }
            useCondition.max_discount = this.couponForm.maxDiscountType
          } else if (this.couponForm.couponType === 'EXCHANGE') {
            useCondition.is_satisfy = this.couponForm.isOrderFee
            if (useCondition.is_satisfy) {
              useCondition.satisfy_money = times(Number(this.couponForm.orderFee))
            }
            useCondition.max_num = this.couponForm.foodMaxCount
            useCondition.exchange_list = this.couponForm.foodList.map((item, index) => {
              return {
                food_id: item.id,
                food_name: item.name,
                seq: index + 1
              }
            })
          }
          params.use_condition = useCondition
          if (this.couponForm.receiveType === 'MANUAL') {
            params.issued_number = this.couponForm.sendCount
            params.receive_start_time = this.couponForm.receiveTime[0]
            params.receive_end_time = this.couponForm.receiveTime[1]
            params.manual_receive_number = this.couponForm.receiveCount
          } else if (this.couponForm.receiveType === 'AUTOMATIC') {
            params.automatic_receive_number = this.couponForm.sendCount
          }
          if (this.couponForm.receiveFactor === 'GROUP') {
            params.card_user_group = this.couponForm.receiveGroup
          } else if (this.couponForm.receiveFactor === 'PERSON') {
            params.card_info = this.personList.map((item, index) => {
              return item.id
            })
          }
          let api
          if (this.type === 'add') {
            api = this.$apis.apiBackgroundCouponCouponManageAddPost
          } else {
            params.id = this.couponId
            api = this.$apis.apiBackgroundCouponCouponManageModifyPost
          }
          this.saveSetting(params, api)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveSetting(params, api) {
      const res = await api(params)
      if (res.code === 0) {
        this.$message.success('保存成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    openChooseDialog(type) {
      if (type === 'choosePerson') {
        this.chooseList = this.personList
        console.log(this.chooseList, 123)
        this.dialogPersonVisible = true
      } else if (type === 'food') {
        this.chooseList = this.couponForm.foodList
        this.dialogFoodVisible = true
      }
    },
    confirmPerson(val) {
      this.personList = []
      val.map(item => {
        item.department_group_name = item.card_department_group_alias
        item.gender_name = item.gender_alias
      })
      this.personList = val
    },
    confirmFood(val) {
      this.couponForm.foodList = val
      this.initSortable()
    },
    delOperation(index) {
      this.personList.splice(index, 1)
    },
    initSortable() {
      this.sortList = this.couponForm.foodList.map(item => item.id)
      const menuEl = this.$refs.tableDataRef.$el.querySelector(
        '.el-table__body-wrapper > table > tbody'
      )
      this.sortWrap = Sortable.create(menuEl, {
        ghostClass: 'sort-active',
        animation: 300,
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const currentRow = this.couponForm.foodList.splice(evt.oldIndex, 1)[0]
          this.couponForm.foodList.splice(evt.newIndex, 0, currentRow)
          const tempIndex = this.sortList.splice(evt.oldIndex, 1)[0]
          this.sortList.splice(evt.newIndex, 0, tempIndex)
        }
      })
    },
    deleteFoodHandle(index) {
      this.couponForm.foodList.splice(index, 1)
    },
    openImport() {
      this.importShowDialog = true
      this.templateUrl = `${location.origin}/api/temporary/template_excel/卡务模板/导入用户.xls`
      this.tableSetting = [
        { key: 'name', label: '姓名' },
        { key: 'person_no', label: '人员编号' },
        // { key: 'card_no', label: '卡号' },
        // { key: 'phone', label: '手机号' },
        { key: 'card_user_group_alias', label: '用户分组' },
        { key: 'card_department_group_alias', label: '部门分组' }
        // { key: 'gender', label: '性别' },
        // { key: 'crowd', label: '人群' },
        // { key: 'effective_time', label: '生效时间' },
        // { key: 'expiration_time', label: '失效时间' }
      ]
    },
    confirmImportData(data) {
      // 提取两个数组中的id属性值到Map中，以便于查找
      let mapPersonList = new Set(this.personList.map(item => item.person_no));
      // 过滤相同的person_no
      let filterList = data.allData.filter(item => !mapPersonList.has(item.person_no));
      let personNoIds = filterList.map(v => v.person_no)
      // 如果都有了就不请求
      if (personNoIds.length) {
        this.getUserList(personNoIds)
      } else {
        this.importShowDialog = false
      }
    },
    // 指定人员 导入用户需要用到
    async getUserList(personNoIds) {
      this.isImportLoading = true
      const res = await this.$apis.apiCardServiceCardUserListPost({
        org_ids: [this.$store.getters.organization],
        person_nos: personNoIds,
        page: 1,
        page_size: 99999
      })
      this.isImportLoading = false
      if (res.code === 0) {
        this.importShowDialog = false
        res.data.results.map(user => {
          user.card_user_group_alias = user.card_user_group_alias.join('，')
        })
        this.personList.push(...res.data.results)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
.MerchantAddCoupon{
  .margin-input{
    margin: 0 5px;
  }
  .margin-l-20{
    margin-left: 20px;
  }
  .form-content-inline{
    display: inline-block;
    .el-form-item__content{
      display: inline-block;
      margin-left: 0px!important;
    }
  }
}
</style>

<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="couponDialogForm"
      @submit.native.prevent
      status-icon
      ref="couponDialogForm"
      label-width="80px"
      class="jiaofei-form"
      inline
    >
      <el-form-item>
        <el-radio-group class="ps-radio" v-model="couponDialogForm.foodType" @change="changeFoodType">
          <el-radio label="food">菜品</el-radio>
          <el-radio label="taocan">套餐</el-radio>
        </el-radio-group>
      </el-form-item>
      <br>
      <el-form-item :label="couponDialogForm.foodType === 'food' ? '菜品分类' : '套餐分类'">
        <el-select
          v-model="couponDialogForm.categoryId"
          placeholder="请下拉选择"
          class="ps-select w-180"
          popper-class="ps-popper-select"
          @change="searchHandle"
        >
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="couponDialogForm.foodType === 'food' ? '菜品名称' : '套餐名称'">
        <el-input v-model="couponDialogForm.foodName" placeholder="请输入" class="ps-input w-180" @input="searchHandle"></el-input>
      </el-form-item>
      <div class="person-table">
        <el-table
          ref="foodListRef"
          :data="foodList"
          max-height="350"
          :row-key="getRowKey"
          header-row-class-name="ps-table-header-row"
          @select="handleSelection"
          @select-all="handleAllSelection">
            <el-table-column type="selection" :reserve-selection="true" width="50" align="center" class-name="ps-checkbox"></el-table-column>
            <el-table-column prop="name" :label="couponDialogForm.foodType === 'food' ? '菜品名称' : '套餐名称'" align="center"></el-table-column>
            <el-table-column prop="category_name" :label="couponDialogForm.foodType === 'food' ? '菜品分类' : '套餐分类'" align="center"></el-table-column>
        </el-table>
      </div>
      <div class="block ps-pagination person-table-bottom">
        <div style="width: 100px;">已选人数：{{selectList.length}}</div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          :pager-count="5"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { debounce, to } from '@/utils'
import { deepClone } from '@/assets/js/util'
export default {
  name: 'FoodDialog',
  props: {
    loading: Boolean,
    title: {
      type: String,
      default: '选择菜品/套餐'
    },
    width: {
      type: String,
      default: '700px'
    },
    chooseList: {
      type: Array,
      default() {
        return []
      }
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    return {
      isLoading: false,
      couponDialogForm: {
        foodType: 'food',
        categoryId: '',
        foodName: ''
      },
      foodList: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      selectList: [],
      organizationId: this.$store.getters.organization,
      categoryList: [],
      foodList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.currentPage = 1
        this.totalCount = 0
        if (this.isshow) {
          this.$nextTick(() => {
            this.$refs.foodListRef.clearSelection()
          });
          this.couponDialogForm.foodType = 'food'
          this.couponDialogForm.categoryId = ''
          this.couponDialogForm.foodName = ''
          this.selectList = deepClone(this.chooseList)
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.foodFoodCategoryList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.foodFoodList()
    }, 300),
    clickConfirmHandle() {
      console.log('this.selectList', this.selectList)
      this.$refs.couponDialogForm.validate(valid => {
        if (valid) {
          this.$emit('confirmPerson', this.selectList)
          this.visible = false
        } else {
        }
      })
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.couponDialogForm.resetFields()
    },
    
    // 菜品列表
    async foodFoodList() {
      let api
      let params = {}
      if (this.couponDialogForm.foodType === 'food') {
        api = this.$apis.apiBackgroundFoodFoodListPost
        if (this.couponDialogForm.categoryId) {
          params.category_id = this.couponDialogForm.categoryId
        }
        if (this.couponDialogForm.foodName) {
          params.food_name = this.couponDialogForm.foodName
        }
      } else {
        api = this.$apis.apiBackgroundFoodSetMealListPost
        if (this.couponDialogForm.categoryId) {
          params.category = [this.couponDialogForm.categoryId]
        }
        if (this.couponDialogForm.foodName) {
          params.name = this.couponDialogForm.foodName
        }
      }
      this.isLoading = true
      const [err, res] = await to(
        api({
          page: 1,
          page_size: 9999,
          ...params
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodList = res.data.results
        this.totalCount = res.data.count
        this.foodList.map(user => {
          this.chooseList.map(selectId => {
            if (user.id === selectId.id) {
              this.$refs.foodListRef.toggleRowSelection(user, true);
            }
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      if (this.type === 'choosePerson') {
        this.getfoodList()
      } else if (this.type === 'food') {
        this.foodFoodList()
      }
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      if (this.type === 'choosePerson') {
        this.getfoodList()
      } else if (this.type === 'food') {
        if (!this.couponDialogForm.categoryId && !this.couponDialogForm.foodName) return
        this.foodFoodList()
      }
    },
    handleSelection(val, row) {
      let index = this.selectList.findIndex(item => item.id === row.id)
      if (index === -1) {
        this.selectList.push(row)
      } else {
        this.selectList.splice(index, 1)
      }
    },
    handleAllSelection(selection) {
      let list = deepClone(selection)
      let flag = true
      this.foodList.map(user => {
        let index = list.findIndex(item => item.id === user.id)
        if (index === -1) {
          flag = false
        }
      })
      if (flag) { // 全选
        this.foodList.map(user => {
          let index = this.selectList.findIndex(item => item.id === user.id)
          if (index === -1) { // 把之前没有的加上
            this.selectList.push(user)
          }
        })
      } else { // 全不选
        this.foodList.map(user => {
          let index = this.selectList.findIndex(item => item.id === user.id)
          if (index !== -1) { // 把之前有的去掉
            this.selectList.splice(index, 1)
          }
        })
      }
    },
    getRowKey(row) {
      return row.id;
    },
    foodSelection() {

    },
    changeFoodType() {
      this.foodFoodCategoryList()
      this.foodList = []
      this.currentPage = 1
      this.totalCount = 0
      this.couponDialogForm.foodName = ''
      this.couponDialogForm.categoryId = ''
    },
    // 菜品分类
    async foodFoodCategoryList() {
      let api
      if (this.couponDialogForm.foodType === 'food') {
        api = this.$apis.apiBackgroundFoodFoodCategoryListPost
      } else {
        api = this.$apis.apiBackgroundFoodSetMealCategoryListPost
      }
      const [err, res] = await to(
        api({
          page: 1,
          page_size: 9999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.categoryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.person-table-bottom{
  display:flex;
  align-items:center;
  justify-content: space-between;
  padding-top:20px;
}
.refund{
  .refund-info-wrap{
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
    .refund-info{
      display: flex;
      flex-wrap: wrap;
      .refund-info-item{
        width: 50%;
        margin-bottom: 10px;
      }
    }
    .refund-info-border{
      border-top: 1px #e4e4e4 solid;
      margin: 10px 0;
    }
  }
}
</style>

<template>
  <dialog-message
    :show.sync="visible"
    title="关联订单"
    @close="handleClose"
    customClass="ps-dialog"
    width="900px"
    :destroy-on-close="false"
  >
    <div class="table-content">
      <!-- table start -->
      <el-table
        v-loading="isLoading"
        :data="correlationOrderInfo.coupon_order"
        ref="tableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
        <table-column v-for="item in tableSettings" :key="item.key" :col="item">
          <template #extra="{ row }">
            <!-- 折扣 -->
            <span v-if="correlationOrderInfo.coupon_type === 'DISCOUNT'">
              {{ row.discount_fee | formatMoney }}
            </span>
            <!-- 满减-立减 -->
            <span
              v-if="
                correlationOrderInfo.coupon_type === 'FULL_DISCOUNT' ||
                correlationOrderInfo.coupon_type === 'INSTANT_DISCOUNT'
              "
            >
              {{ row.deduction_fee | formatMoney }}
            </span>
            <!-- 兑换 -->
            <span v-else>{{ couponExtra(row) }}</span>
          </template>
          <template #is_refund="{ row }">
            <!-- 折扣 -->
              {{ row.is_refund ? '是' : '否' }}
          </template>
        </table-column>
      </el-table>
      <!-- table end -->
    </div>
    <!-- 分页 start -->
    <!-- <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[5, 10, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="totalCount"
        background
        class="ps-text"
        popper-class="ps-popper-select"
      ></el-pagination>
    </div> -->
    <!-- 分页 end -->
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right">
        <el-button class="ps-cancel-btn" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
export default {
  name: 'HistoryDialog',
  props: {
    correlationOrderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    isshow: Boolean
  },
  data() {
    return {
      isLoading: false, // 刷新数据
      // pageSize: 10, // 每页数量
      // totalCount: 0, // 总条数
      // currentPage: 1, // 第几页
      tableSettings: [
        { label: '子订单号', key: 'trade_no' },
        { label: '订单金额', key: 'origin_fee', type: 'money' },
        { label: '优惠内容', key: 'extra', type: 'slot', slotName: 'extra' },
        { label: '是否退款', key: 'is_refund', type: 'slot', slotName: 'is_refund' }
      ]
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleClose(e) {
      this.visible = false
    },
    couponExtra(row) {
      let nameList = row.coupon_extra.map(v => {
        return v.name
      })
      return nameList.join(',')
    }
    // // 分页页数change事件
    // handleSizeChange(val) {
    //   this.pageSize = val
    //   this.getRuleHistory()
    // },
    // // 分页页码change事件
    // handleCurrentChange(val) {
    //   this.currentPage = val
    //   this.getRuleHistory()
    // }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>

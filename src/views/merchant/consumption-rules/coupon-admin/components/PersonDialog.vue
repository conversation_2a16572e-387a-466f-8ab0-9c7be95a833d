<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="couponDialogForm"
      @submit.native.prevent
      status-icon
      ref="couponDialogForm"
      label-width="80px"
      class="jiaofei-form"
      inline
    >
      <el-form-item label="部门">
        <user-department-select
          class="w-180 ps-input"
          v-model="couponDialogForm.department"
          :clearable="true"
          :multiple="true"
          :check-strictly="true"
          :isLazy="false"
          placeholder="请选择部门"
          :append-to-body="true"
          @change="searchHandle"
          >
        </user-department-select>
      </el-form-item>
      <el-form-item label="分组">
        <user-group-select
          :multiple="true"
          :collapse-tags="true"
          class="search-item-w ps-input w-180"
          v-model="couponDialogForm.groupIds"
          placeholder="请下拉选择"
          @change="searchHandle"
        ></user-group-select>
      </el-form-item>
      <el-form-item label="姓名">
        <el-input v-model="couponDialogForm.name" placeholder="请输入" class="ps-input w-180" @input="searchHandle"></el-input>
      </el-form-item>
      <el-form-item label="人员编号">
        <el-input v-model="couponDialogForm.personNo" placeholder="请输入" class="ps-input w-180" @input="searchHandle"></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select
          v-model="couponDialogForm.gender"
          placeholder="请下拉选择"
          class="ps-select w-180"
          popper-class="ps-popper-select"
          @change="searchHandle"
        >
          <el-option
            v-for="item in genderList"
            :key="item.value"
            :label="item.gender"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="person-table">
        <el-table
          ref="userListRef"
          :data="userList"
          max-height="350"
          :row-key="getRowKey"
          header-row-class-name="ps-table-header-row"
          @select="handleSelection"
          @select-all="handleAllSelection">
          <el-table-column type="selection" :reserve-selection="true" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="card_department_group_alias" label="部门" align="center"></el-table-column>
          <el-table-column prop="card_user_group_alias" label="分组" align="center"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="gender_alias" label="性别" align="center"></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
          <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
        </el-table>
      </div>
      <div class="block ps-pagination person-table-bottom">
        <div style="width: 100px;">已选人数：{{selectList.length}}</div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          :pager-count="5"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import UserDepartmentSelect from '@/components/UserDepartmentSelect'
import UserGroupSelect from '@/components/UserGroupSelect'
import { debounce, to } from '@/utils'
import { deepClone } from '@/assets/js/util'
export default {
  name: 'JiaoFeiDialog',
  components: { UserDepartmentSelect, UserGroupSelect },
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '选择人员'
    },
    width: {
      type: String,
      default: '900px'
    },
    personList: {
      type: Array,
      default() {
        return []
      }
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    return {
      isLoading: false,
      couponDialogForm: {
        department: [],
        groupIds: [],
        name: '',
        personNo: '',
        gender: '',
      },
      genderList: [
        { gender: '男', value: 'MAN' },
        { gender: '女', value: 'WOMEN' },
        { gender: '其他', value: 'OTHER' }
      ],
      userList: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      selectList: [],
      organizationId: this.$store.getters.organization,
      categoryList: [],
      foodList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.currentPage = 1
        this.totalCount = 0
        if (this.isshow) {
          this.$nextTick(() => {
            this.$refs.userListRef.clearSelection()
          });
          this.couponDialogForm.department = []
          this.couponDialogForm.groupIds = []
          this.couponDialogForm.name = ''
          this.couponDialogForm.personNo = ''
          this.couponDialogForm.gender = ''
          this.selectList = deepClone(this.personList)
          this.getUserList()
        }
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getUserList()
    }, 300),
    clickConfirmHandle() {
      this.$refs.couponDialogForm.validate(valid => {
        if (valid) {
          this.$emit('confirmPerson', this.selectList)
          this.visible = false
        } else {
        }
      })
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.couponDialogForm.resetFields()
    },
    async getUserList() {
      this.isLoading = true
      let data = {
        card_department_group_ids: this.couponDialogForm.department,
        card_user_group_ids: this.couponDialogForm.groupIds,
        person_name: this.couponDialogForm.name,
        person_no: this.couponDialogForm.personNo,
        gender: this.couponDialogForm.gender
      }
      let params = {}
      for (let key in data) {
        if (data[key]) {
          params[key] = data[key]
        }
      }
      const res = await this.$apis.apiCardServiceCardUserListPost({
        ...params,
        org_ids: [this.organizationId],
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      console.log("res", res)
      if (res.code === 0) {
        this.userList = res.data.results
        this.totalCount = res.data.count
        console.log(this.personList)
        this.userList.map(user => {
          user.card_user_group_alias = user.card_user_group_alias.join('，')
          this.personList.map(selectId => {
            if (user.id === selectId.id) {
              this.$refs.userListRef.toggleRowSelection(user, true);
              // this.handleSelection(user)
            }
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getUserList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getUserList()
    },
    handleSelection(val, row) {
      let index = this.selectList.findIndex(item => item.id === row.id)
      if (index === -1) {
        this.selectList.push(row)
      } else {
        this.selectList.splice(index, 1)
      }
    },
    handleAllSelection(selection) {
      let list = deepClone(selection)
      let flag = true
      this.userList.map(user => {
        let index = list.findIndex(item => item.id === user.id)
        if (index === -1) {
          flag = false
        }
      })
      if (flag) { // 全选
        this.userList.map(user => {
          let index = this.selectList.findIndex(item => item.id === user.id)
          if (index === -1) { // 把之前没有的加上
            this.selectList.push(user)
          }
        })
      } else { // 全不选
        this.userList.map(user => {
          let index = this.selectList.findIndex(item => item.id === user.id)
          if (index !== -1) { // 把之前有的去掉
            this.selectList.splice(index, 1)
          }
        })
      }
    },
    getRowKey(row) {
      return row.id;
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.person-table-bottom{
  display:flex;
  align-items:center;
  justify-content: space-between;
  padding-top:20px;
}
.refund{
  .refund-info-wrap{
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
    .refund-info{
      display: flex;
      flex-wrap: wrap;
      .refund-info-item{
        width: 50%;
        margin-bottom: 10px;
      }
    }
    .refund-info-border{
      border-top: 1px #e4e4e4 solid;
      margin: 10px 0;
    }
  }
}
</style>

<template>
  <dialog-message
    :show.sync="visible"
    title="历史记录"
    @close="handleClose"
    customClass="ps-dialog"
    width="900px"
    :destroy-on-close="false"
  >
    <div class="table-content">
      <!-- table start -->
      <el-table
         v-loading = isLoading
        :data="historytableData"
        ref="historytableData"
        style="width: 100%"
        stripe
        header-row-class-name="ps-table-header-row"
      >
        <el-table-column prop="time" label="操作时间" align="center"></el-table-column>
        <el-table-column prop="content" label="操作内容" align="center"></el-table-column>
        <el-table-column prop="details" label="操作详情" align="center"></el-table-column>
        <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      </el-table>
      <!-- table end -->
    </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right">
        <el-button class="ps-cancel-btn" @click="handleClose">关闭</el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { to } from '@/utils'
export default {
  name: 'HistoryDialog',
  props: {
    paramsInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    isshow: Boolean
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      historytableData: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {},
  created() {
    this.getRuleHistory()
  },
  mounted() {},
  methods: {
    handleClose(e) {
      this.visible = false
    },
    // 获取规则的历史记录
    async getRuleHistory() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeGetRuleHistoryPost({
          ...this.paramsInfo,
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.historytableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getRuleHistory()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getRuleHistory()
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';
</style>

<template>
  <div class="ChargeConsumeService container-wrapper">
    <div class="table-type">
      <div
        :class="['table-type-btn', tableType === 'consume' ? 'active-btn' : '']"
        @click="changeTableType('consume')"
      >
        扣款手续费
      </div>
      <div
        :class="['table-type-btn', tableType === 'charge' ? 'active-btn' : '']"
        @click="changeTableType('charge')"
      >
        充值手续费
      </div>
    </div>
    <consume-service v-if="tableType === 'consume'" ref="consumeService" />
    <charge-service v-if="tableType === 'charge'" ref="chargeService" />
  </div>
</template>
<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
import ConsumeService from './components/ConsumeService'
import ChargeService from './components/ChargeService'
export default {
  // mixins: [activatedLoadData],
  components: { ConsumeService, ChargeService },
  name: 'ChargeConsumeService',
  data() {
    return {
      tableType: 'consume'
    }
  },
  created() {
    if (this.$route.query.tableType) {
      this.tableType = this.$route.query.tableType
    }
  },
  mounted() {},
  methods: {
    initLoad() {},
    changeTableType(type) {
      this.tableType = type
      this.changeHash()
    },
    changeHash() {
      this.$router.replace({
        name: 'MerchantRechargeDeductionService',
        query: {
          tableType: this.tableType
        }
      })
    },

    // 节下流咯
    searchHandle: debounce(function () {}, 300)
  }
}
</script>
<style lang="scss" scoped>
.ChargeConsumeService {
  .table-type {
    padding: 20px 0;
    display: flex;
    font-size: 16px;
    .table-type-btn {
      width: 120px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      color: #ff9b45;
      background-color: #e8f0f8;
      border-radius: 40px;
      margin-right: 20px;
      border: 1px #ff9b45 solid;
      cursor: pointer;
    }
    .active-btn {
      color: #fff;
      background-color: #ff9b45;
      border: none;
    }
  }
}
</style>

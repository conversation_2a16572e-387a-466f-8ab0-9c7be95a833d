<template>
  <div class="container-wrapper MessagesSetting" v-loading="isLoading">
    <el-form
      ref="messagesSetting"
      :rules="formDataRuls"
      :model="formData"
      size="small"
      v-if="dataLoading"
    >
      <el-form-item>
        <el-radio-group class="ps-radio-btn" v-model="senderType" size="mini" prop="couponType">
          <el-radio-button v-for="item in templateList" :key="item.sender_type" :label="item.sender_type">{{item.sender_type_alias}}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="适用组织" prop="orgIds" label-width="70px">
        <organization-select
          class="ps-input w-250"
          placeholder="请选择适用组织"
          :isLazy="false"
          :multiple="true"
          :check-strictly="true"
          v-model="formData[senderType].organizations"
          :append-to-body="true"
          >
        </organization-select>
      </el-form-item>
      <template v-for="tempItem in formSettingList">
        <div v-if="tempItem.setting_type === 'common'" class="l-title" :key="tempItem.setting_type">{{tempItem.setting_type_alias}}</div>
        <div v-else :key="tempItem.setting_type" class="checkbox-title">
          <el-checkbox class="ps-checkbox m-r-10" v-model="formData[senderType][tempItem.setting_type].enable"></el-checkbox>
          {{tempItem.setting_type_alias}}
        </div>
        <template v-for="item in tempItem.template">
          <el-form-item :key="item.key + tempItem.setting_type" :prop="item.key" :label="item.name" class="m-l-25">
            <el-input size="small" v-if="!item.type || item.type==='input'" :type="item.type" v-model="formData[senderType][tempItem.setting_type][item.key]" class="ps-input w-250"></el-input>
            <el-input size="small" v-if="item.type==='textarea'" type="textarea" :rows="3"  v-model="formData[senderType][tempItem.setting_type][item.key]" class="ps-input w-250"></el-input>
            <el-select ref="forRef" size="small" v-if="item.type==='select'" class="search-item-w ps-input w-250"  v-model="formData[senderType][tempItem.setting_type][item.key]" placeholder="">
              <el-option v-for="option in item.value" :key="option.value" :label="option.name" :value="option.value"></el-option>
            </el-select>
            <el-radio-group class="ps-radio" v-if="item.type==='radio'" v-model="formData[senderType][tempItem.setting_type][item.key]">
              <el-radio v-for="option in item.value" :key="option.value" :label="option.value">{{option.name}}</el-radio>
            </el-radio-group>
            <el-checkbox-group v-if="item.type==='checkbox'" v-model="formData[senderType][tempItem.setting_type][item.key]">
              <el-checkbox v-for="option in item.value" :key="option.value" :label="option.value" class="ps-checkbox">{{option.name}}</el-checkbox>
            </el-checkbox-group>
            <el-input-number v-if="item.type==='number'" class="ps-input-number" step-strictly v-model="formData[senderType][tempItem.setting_type][item.key]" label=""></el-input-number>
            <el-tooltip v-if="item.help_text" class="item" effect="dark" :content="item.help_text" placement="top-start">
              <i class="el-icon-info"></i>
            </el-tooltip>
          </el-form-item>
        </template>
      </template>
      <el-form-item>
        <el-button type="primary" size="small" class="ps-origin-btn w-180" v-permission="['background_messages.third_messages_settings.modify']" @click="saveSettingHandle">保存</el-button>
      </el-form-item>
    </el-form>
    <div v-else>暂无配置</div>
  </div>
</template>

<script>
import { to } from '@/utils'
import OrganizationSelect from '@/components/OrganizationSelect'
import { deepClone } from '@/assets/js/util'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'MessagesSetting',
  components: { OrganizationSelect },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      dataLoading: false,
      templateList: [],
      settingList: [],
      senderType: 'psbc',
      formData: {},
      formDataRuls: {
      }
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  computed: {
    formSettingList() {
      let currentTypeList = this.templateList.filter(item => item.sender_type === this.senderType)
      if (currentTypeList.length) {
        return currentTypeList[0].settings_template
      } else {
        return []
      }
    }
  },
  methods: {
    initLoad() {
      this.getMessagestemplateList()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 获取模板
    async getMessagestemplateList() {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundMessagesThirdMessagesSettingsTemplateListPost())
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.templateList = res.data
        if (this.templateList.length) {
          this.senderType = this.templateList[0].sender_type
        }
        this.getMessagesSettingsList()
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 获取配置
    async getMessagesSettingsList() {
      this.isLoading = true
      let params = {
        company: this.$store.getters.userInfo.company_id
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundMessagesThirdMessagesSettingsListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.settingList = res.data
        this.loadFormData()
      } else {
        // this.$message.error(res.msg)
      }
    },
    loadFormData() {
      if (!this.templateList.length) return
      this.templateList.map(item => {
        let list = this.settingList.filter(setting => setting.id !== null && setting.sender_type === item.sender_type) // 有数据的要赋值
        // 整体处理数据
        this.$set(this.formData, item.sender_type, {})
        this.$set(this.formData[item.sender_type], 'organizations', list.length ? list[0].organizations : [])
        item.settings_template.map(setting => {
          this.$set(this.formData[item.sender_type], setting.setting_type, {})
          if (!setting.template) return
          // eslint-disable-next-line camelcase
          let settingType = list[0]?.event_msg_config?.[setting.setting_type] ? list[0].event_msg_config[setting.setting_type] : {} // 已有的数据，setting.setting_type可能也许不一定存在，加多一层判断
          setting.template.map(temp => {
            if (temp.type === 'checkbox') {
              let defaultWeek = JSON.parse(temp.default).map(item => {
                return String(item)
              })
              this.$set(this.formData[item.sender_type][setting.setting_type], temp.key, settingType[temp.key] ? settingType[temp.key] : defaultWeek)
            } else {
              this.$set(this.formData[item.sender_type][setting.setting_type], temp.key, settingType[temp.key] ? settingType[temp.key] : temp.default)
            }
          })
          this.$set(this.formData[item.sender_type][setting.setting_type], 'enable', settingType.enable ? settingType.enable : false)
        })
      })
      this.dataLoading = true
    },
    // 发送请求
    async saveSettingHandle() {
      if (this.isLoading) {
        return
      }
      this.$refs.messagesSetting.validate(valid => {
        if (valid) {
          this.modifySetting()
        }
      })
    },
    async modifySetting() {
      let config = deepClone(this.formData[this.senderType])
      delete config.organizations
      let params = {
        sender_type: this.senderType,
        organizations: this.formData[this.senderType].organizations,
        event_msg_config: config,
        company: this.$store.getters.userInfo.company_id
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMessagesThirdMessagesSettingsModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.formOperate = 'detail'
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.MessagesSetting {
  padding: 20px;
  margin-top: 20px;
  background-color: #fff;
  .m-t-20{
    margin-top: 20px;
  }
  .m-b-20{
    margin-bottom: 20px;
  }
  .m-l-25{
    margin-left: 25px;
  }
  .m-r-10{
    margin-right: 10px;
  }
  .ps-radio-btn .is-active .el-radio-button__inner{
    background-color: #ff9b45;
  }
  .checkbox-title{
    padding: 10px 0;
    font-size: 16px;
    font-weight: bold;
    color: #23282d;
  }
  .add-wrapper{
    margin: 60px 0 60px 150px;
    .el-button{
      width: 120px;
    }
  }
}
</style>

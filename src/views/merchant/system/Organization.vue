<template>
  <div class="container-wrapper has-organization">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="super-organization">
      <!-- 组织结构 start -->
      <div class="organization-tree">
        <el-input
          class="tree-search ps-input"
          type="primary"
          :placeholder="$t('placeholder.role_tree_search')"
          clearable
          v-model="treeFilterText"
        ></el-input>
        <!-- <div v-if="!treeFilterText" :class="['all-tree tree-flex', !selectId?'is-current':'']" @click="treeHandleNodeClick(rootTreeData, 'admin')">
          <span>
            <i class="tree-search-icon"><img src="@/assets/img/icon all.png" alt="" /></i>朴食科技
          </span>
          <span>
            <el-popover
              popper-class="custon-tree-popper"
              placement="right-start"
              width="auto"
              trigger="hover">
              <div class="popover-btn-box">
                <el-button type="text" @click.stop="addRootTreeHandle(rootTreeData, 'root', 'add')">新建组织</el-button>
              </div>
              <i slot="reference" class="el-icon-more tree-icon"></i>
            </el-popover>
          </span>
        </div> -->
        <!-- :load="load"
          lazy -->
        <el-tree
          v-loading="treeLoading"
          :data="treeList"
          :props="treeProps"
          :load="loadTree"
          :lazy="isLazy"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :highlight-current="true"
          :current-node-key="selectId"
          :class="{ 'tree-box': selectId }"
          ref="treeRef"
          node-key="id"
          @node-click="treeHandleNodeClick($event, 'tree')"
        >
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <!-- <el-tooltip class="item" effect="dark" :content="data.level_name + '-' +data.name" placement="top"> -->
            <span class="ellipsis tree-lable">
              {{ data.level_name + '-' + node.label }}
              <!-- <i class="el-icon-edit tree-icon" v-permission="['background.admin.organization.modify_level_name']" @click.stop="openDialogHaldler('level_name', data)"></i> -->
              <span class="stop-box" v-if="data.status === 'disable'">停</span>
            </span>
            <!-- </el-tooltip> -->
            <span>
              <el-popover
                placement="right-start"
                width="auto"
                popper-class="custon-tree-popper"
                trigger="hover"
              >
                <div :class="['popover-btn-box', treeLoading ? 'no-pointer' : '']">
                  <!-- <el-button :disabled="data.status === 'disable'" type="text" @click="openDialogHaldler('level_name', data)">层级名称修改</el-button> -->
                  <el-button
                    v-if="data.level_tag < 5"
                    :disabled="data.status === 'disable'"
                    type="text"
                    @click="addChildTreeHandle('child', data, 'add')"
                    v-permission="['background_organization.organization.add']"
                  >
                    添加组织层级
                  </el-button>
                  <el-button
                    v-if="data.level === 0"
                    type="text"
                    @click="openDialogHaldler('allow_register', data)"
                  >
                    自注册设置
                  </el-button>
                  <el-button
                    type="text"
                    class="popper-del"
                    @click="deleteOrganization(data)"
                    v-if="data.deep !== 1"
                    v-permission="['background_organization.organization.delete']"
                  >
                    删除
                  </el-button>
                </div>
                <i slot="reference" class="el-icon-more tree-icon"></i>
              </el-popover>
            </span>
          </div>
        </el-tree>
        <div v-if="treeCount > treeSize" class="" style="text-align:right; margin-top: 20px;">
          <el-pagination
            @current-change="treePaginationChange"
            :current-page="treePage"
            :page-size="treeSize"
            layout="total, prev, pager, next"
            popper-class="ps-popper-select"
            :total="treeCount"
          ></el-pagination>
        </div>
      </div>
      <!-- end -->
      <!--  -->
      <div class="organization-r" v-loading="isLoading">
        <!-- tab start -->
        <div v-if="operate !== 'add'" key="tab" class="organization-tab-group">
          <div
            v-for="item in tabList"
            :key="item.value"
            :class="[
              'organization-tab',
              item.value === tabType ? 'is-checked' : '',
              item.disable ? 'is-disable' : ''
            ]"
            @click="clickTabHandle(item)"
            :label="item.value"
          >
            <span class="tab-label">{{ item.name }}</span>
          </div>
        </div>
        <!-- tab end -->
        <transition-group :name="slideTransition">
          <!-- 详情or添加组织 start -->
          <template v-if="tabType === 'detail' || operate === 'add'">
            <div v-if="type === 'child' && !isLoading" key="child">
              <add-organization
                :type="type"
                :id="selectId"
                :operate.sync="operate"
                :info-data="organizationInfo"
                :parent-data="parentTreeData"
                :tree-data="selectTree"
                :restore-handle="restoreHandle"
              />
            </div>
          </template>
          <!-- 详情or添加组织 end -->

          <!-- 支付设置 start -->
          <div v-if="tabType === 'paySetting'" key="paySetting" class="">
            <pay-setting
              v-if="!isLoading"
              :type="type"
              :info-data="organizationInfo"
              :organization-data="selectTree"
            />
          </div>
          <div v-if="tabType === 'rechargeSetting'" key="rechargeSetting" class="">
            <recharge-setting
              v-if="!isLoading"
              :type="type"
              :info-data="organizationInfo"
              :organization-data="selectTree"
            />
          </div>
          <div v-if="tabType === 'deductSetting'" key="deductSetting" class="">
            <deduct-setting
              v-if="!isLoading"
              :type="type"
              :info-data="organizationInfo"
              :organization-data="selectTree"
            />
          </div>
          <div v-if="tabType === 'seniorSetting'" key="seniorSetting" class="">
            <senior-setting
              v-if="!isLoading"
              :type="type"
              :info-data="organizationInfo"
              :organization-data="selectTree"
            />
          </div>
          <div v-if="tabType === 'bindAppid'" key="bindAppid" class="">
            <appid-setting
              v-if="!isLoading"
              :type="type"
              :info-data="organizationInfo"
              :organization-data="selectTree"
              :restore-handle="restoreHandle"
            />
          </div>
          <div v-if="tabType === 'downloadQrCode'" key="downloadQrCode" class="">
            <download-qr-code
              v-if="!isLoading"
              :type="type"
              :info-data="organizationInfo"
              :organization-data="selectTree"
              :restore-handle="restoreHandle"
            />
          </div>
          <!-- 支付设置 end -->
        </transition-group>
      </div>
    </div>
    <!-- 层级名称修改 弹窗 start -->
    <dialog-message
      :title="dialogTitle"
      :show.sync="dialogVisible"
      :width="dialogWidth"
      :loading="dialogLoading"
      top="20vh"
      custom-class="ps-dialog"
      @close="dialogHandleClose"
    >
      <el-form
        ref="dialogFormRef"
        v-loading="dialogLoading"
        :rules="dialogFormDataRuls"
        :model="dialogFormData"
        class="dialog-form"
        :label-width="dialogFormLabelw"
        size="small"
      >
        <div v-if="dialogType === 'level_name'" class="">
          <el-form-item label="组织名称" prop="level_name">
            <el-input class="ps-input" v-model="dialogFormData.level_name"></el-input>
          </el-form-item>
        </div>
        <div v-if="dialogType === 'allow_register'" style="margin-bottom: 35px;">
          <el-form-item label="" prop="allow_register">
            <el-radio-group class="ps-radio" v-model="dialogFormData.allow_register">
              <el-radio :label="true">开启自注册</el-radio>
              <el-radio :label="false">关闭自注册</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-form>
      <template slot="tool">
        <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: center;">
          <el-button
            :disabled="dialogLoading"
            class="ps-cancel-btn"
            size="mini"
            @click="clickCancleHandle"
          >
            取消
          </el-button>
          <el-button
            :disabled="dialogLoading"
            class="ps-btn"
            type="primary"
            size="mini"
            @click="submitDialogHandler('dialogFormRef')"
          >
            确定
          </el-button>
        </div>
      </template>
    </dialog-message>
    <!-- 弹窗 end -->
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
import { hasClass } from '@/utils/dom'
import { regionData } from 'element-china-area-data'
import industryType from '@/assets/data/industryType.json'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import addOrganization from './components/addOrganization'
import paySetting from './components/paySetting'
import rechargeSetting from './components/rechargeSetting'
import deductSetting from './components/deductSetting'
import seniorSetting from './components/seniorSetting'
import appidSetting from './components/appidSetting'
import downloadQrCode from './components/downloadQrCode'

export default {
  name: 'OrganizationList',
  components: {
    addOrganization,
    paySetting,
    rechargeSetting,
    deductSetting,
    seniorSetting,
    appidSetting,
    downloadQrCode
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      treeLoading: false,
      treeList: [],
      isLazy: true,
      treeFilterText: '',
      treeProps: {
        children: 'children_list',
        label: 'name',
        isLeaf: (data, node) => {
          return !data.has_children
        }
      },
      treeSize: 10, // 每页数量
      treeCount: 0, // 总条数
      treePage: 1, // 第几页
      selectTree: {}, // 选中的tree数据
      selectId: '', // 点击选中的tree id
      parentTreeData: {},
      type: '', // addRoot, root, child
      organizationInfo: null, // 详情
      operate: '', // 操作类型，仅用于左侧的添加
      tabType: 'detail', // tab类型
      tabList: [
        { name: '基本信息', value: 'detail', index: 1 }, // disable: true
        { name: '支付配置', value: 'paySetting', index: 2 },
        { name: '充值配置', value: 'rechargeSetting', index: 3 },
        { name: '扣款设置', value: 'deductSetting', index: 4 },
        { name: '高级设置', value: 'seniorSetting', index: 5 },
        // { name: '交易渠道', value: 'tradingSetting', index: 6 },
        // { name: '绑定公众号', value: 'bindAppid', index: 7 },
        { name: '下载二维码', value: 'downloadQrCode', index: 8 }
      ],
      dialogData: {},
      dialogTitle: '',
      dialogType: '',
      dialogVisible: false,
      dialogLoading: false,
      dialogWidth: '',
      dialogFormLabelw: '80px',
      dialogFormData: {
        id: '',
        name: '',
        level_name: '',
        allow_register: false
      },
      dialogFormDataRuls: {
        name: [{ required: true, message: '组织名称不能为空', trigger: 'blur' }],
        level_name: [{ required: true, message: '层级名称不能为空', trigger: 'blur' }]
      },
      time: new Date().getTime(),
      addrOptions: regionData,
      levelList: [],
      industryTypeList: industryType,
      permissionTree: [],
      permission: [],
      isLoading: false,
      slideTransition: 'slide-left' // 默认切换动画
    }
  },
  watch: {
    treeFilterText(val) {
      this.filterHandle()
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.$route.query.type) {
        this.type = this.$route.query.type
      }
      if (this.$route.query.id) {
        this.selectId = Number(this.$route.query.id)
      }
      this.getOrganizationList()
      if (this.selectId) {
        this.getSelectOrganizationInfo(this.selectId)
      }
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      // this.$refs.searchRef.resetForm()
      this.treeFilterText = ''
      this.treePage = 1
      this.treeList = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {}, 300),
    // z
    permissionNormalizer(node) {
      return {
        id: node.key,
        label: node.verbose_name,
        children: node.children
      }
    },
    // 懒加载组织结构
    async loadTree(tree, resolve) {
      // 0级直接退出执行
      if (tree.level === 0) {
        return
      }
      let params = {
        status__in: ['enable', 'disable'],
        page: 1,
        page_size: 99999
      }
      if (tree.data && tree.data.id) {
        params.parent__in = tree.data.id
      } else {
        // params.parent__is_null = '1'
        this.treeLoading = true
      }
      // 强制睡眠
      // await this.$sleep(1000);
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationListPost(params))
      this.treeLoading = false
      if (err) {
        resolve([])
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        resolve(res.data.results)
      } else {
        resolve([])
        this.$message.error(res.msg)
      }
    },
    // 名称搜索
    filterHandle: debounce(function() {
      this.treePage = 1
      this.getOrganizationList(this.treeFilterText)
    }, 300),
    // 获取组织，用于顶级的获取or搜索
    async getOrganizationList(name, parentId, callback, refresh) {
      let params = {
        status__in: ['enable', 'disable'],
        page: this.treePage,
        page_size: this.treeSize
      }
      if (parentId) {
        params.parent__in = parentId
      } else {
        // params.parent__is_null = '1'
      }
      if (name) {
        params.name__contains = name
      }
      // if (name) {
      //   params.name__contains = name
      // } else {
      //   params.parent__is_null = '1'
      // }
      this.treeLoading = true
      // 强制睡眠
      // await this.$sleep(1000);
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationListPost(params))
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let results = res.data.results
        if (name && !true) {
          results = res.data.results.map(v => {
            if (name) {
              v.has_children = false
            }
            return v
          })
        } else {
          if (results.length && results.length < 6 && results[0].has_children) {
            this.$nextTick(() => {
              // 只能通过class去触发click事件了
              const expandEls = document.querySelectorAll('.el-tree-node__expand-icon')
              if (!hasClass(expandEls[0], 'expanded')) {
                expandEls[0] && expandEls[0].click()
              }
            })
          }
        }
        if (!this.treeList.length || refresh) {
          this.tabType = 'detail'
          this.operate = ''
          this.type = 'child'
          if (results.length) {
            this.selectId = results[0].id
            this.selectTree = results[0]
            this.getSelectOrganizationInfo(this.selectId)
          }
        }
        if (!parentId) {
          this.treeList = results.map(tree => {
            tree.deep = 1
            return tree
          })
          this.treeCount = res.data.count
        } else {
          // 有parentid和callback才能进行数据更新
          if (callback) {
            callback(parentId, results)
          }
        }

        // 更新数据时需要手动设置当前高亮选项
        if (this.selectId) {
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.selectId)
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 组织树的分页
    treePaginationChange(e) {
      this.treePage = e
      this.getOrganizationList(this.treeFilterText)
    },
    // 处理下没有children_list
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取指定公司的层级列
    async getLevelList(companyId) {
      // await this.$sleep(1000);
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationGetLevelNameMapPost({
          company_id: companyId
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.levelList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取权限树状结构
    async getPermissionTreeList(id) {
      // await this.$sleep(1000);
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationGetMerchantPermissionsPost({
          // company_id: id
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.permissionTree = this.deleteEmptyChildren(res.data, 'children')
        // this.permission = getTreeDeepkeyList(this.permissionTree, 'key')
      } else {
        this.$message.error(res.msg)
      }
    },
    // tab 点击事件
    clickTabHandle(tab) {
      if (!tab.disable) {
        let oldTab = this.tabList.filter(item => item.value === this.tabType)[0]
        this.slideTransition = oldTab.index < tab.index ? 'slide-left' : 'slide-right'
        this.tabType = tab.value
      }
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if ((rowIndex + 1) % 2 === 0) {
        str += 'table-header-row'
      }
      return str
    },
    // 点击tree node click
    async treeHandleNodeClick(e, type, operate) {
      if (e.id === this.selectTree.id && this.operate === operate) return
      this.type = ''
      this.tabType = 'detail'
      if (type === 'tree') {
        // 如果是tree类型则加载详情
        await this.getSelectOrganizationInfo(e.id)
      }
      // 延时300ms用于动画效果
      await this.$sleep(300)
      if (type === 'tree') {
        this.type = 'child'
        this.operate = 'detail'
      } else {
        this.type = type
      }
      this.selectTree = e
      this.selectId = e.id
      // this.searchHandle()
      // this.changeHash(this.type, e)
    },
    // 添加root级
    async addRootTreeHandle(e, type, operate) {
      this.type = ''
      this.tabType = 'detail'
      // 延时300ms用于动画效果
      await this.$sleep(300)
      this.type = type
      this.selectId = e.id
      if (operate) {
        this.operate = operate
      }
    },
    // 添加子层级
    async addChildTreeHandle(type, data, operate) {
      this.type = ''
      this.tabType = 'detail'
      this.organizationInfo = {}
      // 添加子层级要加载父级的详情，根据父级详情进行配置
      // await this.getSelectOrganizationInfo(data.id)
      this.isLoading = true
      // 延时300ms用于动画效果
      await this.$sleep(100)
      this.isLoading = false
      this.type = type
      // this.selectTree = {}
      // this.selectId = ''
      this.parentTreeData = data
      this.operate = operate
    },
    // 还原下上次显示的内容
    async restoreHandle(type, operate) {
      this.operate = ''
      if (operate === 'add') {
        // 直接还原成默认吧
        this.showAdminData()
        if (this.parentTreeData.parent) {
          // 如果有父级，则从父级中拉取下级数据
          if (this.parentTreeData.has_children) {
            // 当前父级有下级数据
            await this.getOrganizationList(
              this.treeFilterText,
              this.parentTreeData.id,
              this.updateTreeChildren
            )
          } else if (this.parentTreeData.parent) {
            // 当前父级没有下级数据，则要从父级的父级进行更新
            await this.getOrganizationList(
              this.treeFilterText,
              this.parentTreeData.parent,
              this.updateTreeChildren
            )
          } else {
          } // 按理说不回有这种情况
        } else {
          await this.getOrganizationList()
        }
        // this.getSelectOrganizationInfo(this.selectId)
      } else {
        await this.getOrganizationList(
          this.treeFilterText,
          this.selectTree.parent,
          this.updateTreeChildren
        )
        await this.getSelectOrganizationInfo(this.selectId)
      }
    },
    // 获取当前选中的组织的详情
    async getSelectOrganizationInfo(id) {
      if (!id) {
        return
      }
      this.isLoading = true
      // await this.$sleep(1000);
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationGetInfoPost({
          id: id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.organizationInfo = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // change Hash
    changeHash(type, data) {
      this.$router.push({
        name: 'SuperCompany',
        query: {
          type: type,
          id: data.id
        }
      })
    },
    // 打开弹窗
    async openDialogHaldler(type, data) {
      this.dialogType = type
      this.dialogData = deepClone(data)
      this.dialogVisible = true
      // await this.getLevelList()
      // await this.getPermissionTreeList()
      switch (type) {
        case 'modify':
          this.dialogTitle = this.$t('dialog.edit_title')
          this.dialogFormData.id = data.id
          if (data.organization.length) {
            this.dialogFormData.organization = data.organization
          }
          // this.dialogFormData.organization = data.organization
          this.dialogFormData.name = data.name
          break
        case 'add':
          this.dialogTitle = this.$t('dialog.add_title')
          break
        case 'addRoot':
          this.dialogTitle = this.$t('dialog.add_title')
          this.dialogFormData.level_name = 0
          break
        case 'level_name':
          this.dialogTitle = '层级名称修改'
          this.dialogWidth = '400px'
          this.dialogFormData.level_name = data.level_name
          break
        case 'allow_register':
          this.dialogFormData.id = data.id
          this.dialogTitle = '自注册设置开关'
          this.dialogWidth = '400px'
          this.dialogFormLabelw = '50px'
          this.getSettingInfo(data.id)
          break
      }
    },
    // 弹窗取消事件
    clickCancleHandle() {
      this.$refs.dialogFormRef.resetFields()
      this.dialogVisible = false
    },
    // 弹窗确定事件
    submitDialogHandler(refType) {
      if (this.dialogType === 'account') {
        this.dialogVisible = false
      } else {
        this.$refs[refType].validate(valid => {
          if (valid) {
            if (this.dialogLoading) {
              return this.$message.error('请勿重复提交!')
            }
            switch (this.dialogType) {
              case 'level_name':
                this.modifyNameHandle(this.dialogType)
                break
              case 'allow_register':
                this.allowRegisterHandle(this.dialogType)
                break
            }
          }
        })
      }
    },
    // tree 开启or关闭
    async changeStatus(data) {
      let tipsText = ''
      let disable = true
      switch (data.status) {
        case 'enable':
          tipsText = '是否对禁用该层级以及下级层级软件使用权限，点击确定后，账号无法登录'
          break
        default:
          tipsText = '是否启用？'
          disable = false
          break
      }
      this.$confirm(tipsText, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: disable ? 'ps-warn' : 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.treeLoading = true
            // await this.$sleep(1000);
            const [err, res] = await to(
              this.$apis.apiBackgroundOrganizationEnablePost({
                id: data.id,
                enable: data.status !== 'enable'
              })
            )
            this.treeLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              // this.$set(data, 'status', data.status === 'enable' ? 'disable' : 'enable')
              this.getOrganizationList(this.treeFilterText, data.parent, this.updateTreeChildren)
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 编辑组织
    async modifyNameHandle(e) {
      if (this.dialogLoading) {
        this.$message.error('请勿重复提交!')
        return
      }
      this.dialogLoading = true
      this.treeLoading = true
      // await this.$sleep(1000);
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationModifyLevelNamePost({
          id: this.dialogData.id,
          name: this.dialogFormData.level_name
        })
      )
      this.dialogLoading = false
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogData.level_name = this.dialogFormData.level_name
        this.getOrganizationList(
          this.treeFilterText,
          this.dialogData.parent,
          this.updateTreeChildren
        )
        this.dialogVisible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 更新tree数据，只能更新子级
    updateTreeChildren(parentId, childrens) {
      // updateKeyChildren
      let treeFRef = this.$refs.treeRef
      treeFRef.updateKeyChildren(parentId, childrens)
    },
    deleteOrganization(data) {
      let deleteIds = []
      deleteIds = [data.id]
      if (!deleteIds.length) {
        return this.$message.error(this.$t('message.role_select_empty'))
      }
      this.$confirm(`确定删除${data.name}吗?`, this.$t('message.delete'), {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.treeLoading = true
            // await this.$sleep(2222)
            const [err, res] = await to(
              this.$apis.apiBackgroundOrganizationOrganizationDeletePost({
                ids: deleteIds
              })
            )
            this.treeLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.$refs.treeRef.remove(data.id)
              if (this.selectId === data.id) {
                this.showAdminData()
              }
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // show Admin Data
    showAdminData() {
      this.tabType = ''
      this.operate = ''
      this.type = 'detail'
      this.selectId = this.rootTreeData.id
      this.selectTree = this.rootTreeData
    },
    // 移出当前数组中的数据
    removeArrayData(id, list) {
      const len = list.length
      for (let index = 0; index < len; index++) {
        const current = list[index]
        if (current.id === id) {
          list.splice(index, 1)
          break
        }
      }
    },
    // 关闭弹窗回调
    dialogHandleClose(e) {
      this.dialogData = {}
      this.dialogTitle = ''
      this.dialogType = ''
    },
    // 获取自注册信息
    async getSettingInfo(id) {
      this.dialogLoading = true
      // this.$sleep(2000)
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGetSettingsPost({
          id: id
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogFormData.allow_register = res.data.allow_register
      } else {
        this.$message.error(res.msg)
      }
    },
    // 自注册设置
    async allowRegisterHandle() {
      this.dialogLoading = true
      // this.$sleep(2000)
      let params = {
        id: this.dialogFormData.id,
        allow_register: this.dialogFormData.allow_register
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationModifySettingsPost(params)
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogVisible = false
        this.$message.success(res.msg)
        await this.getOrganizationList(
          this.treeFilterText,
          this.selectTree.parent,
          this.updateTreeChildren,
          true
        )
        await this.getSelectOrganizationInfo(this.selectId)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.super-organization {
  display: flex;
  // height: 100%;
  .organization-r {
    position: relative;
    min-height: calc(100vh - 128px - 76px - 5px);
    // height: calc(100vh - 128px - 76px - 5px);
    // overflow-y: scroll;
    flex: 1;
    min-width: 0;
    background-color: #f8f9fa;
    box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3), inset 2px 2px 0px 0px#ffffff;
    border-radius: 0px 12px 12px 0;
    padding: 20px;
    .organization-tab-group {
      .organization-tab {
        display: inline-block;
        padding: 5px 10px;
        margin: 5px 10px 5px 0;
        font-size: 13px;
        letter-spacing: 1px;
        color: #7b7c82;
        border: solid 1px #dae1ea;
        border-radius: 15px;
        cursor: pointer;
        &.is-checked {
          color: #fff;
          border: solid 1px #dae1ea;
          background-color: #fd953c;
        }
        .is-checked + .tab-label {
          color: #fff;
        }
        &.el-radio:last-child {
          margin-right: 0;
        }
        &.is-disable {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
    .item-box {
      // display: flex;
      padding: 10px 0;
      .item-b-l {
        // display: flex;
        // justify-content: center;
        // align-items: center;
        float: left;
        width: 56px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        vertical-align: middle;
        background-color: #ff9b45;
        border-radius: 8px;
        font-size: 30px;
        letter-spacing: 2px;
        color: #ffffff;
      }
      .item-b-r {
        margin-left: 76px;
      }
      .item-text-box {
        display: flex;
        padding: 5px 0;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #23282d;
        .item-label {
          opacity: 0.7;
        }
        .item-text {
          flex: 1;
        }
      }
    }
  }
  .organization-tree {
    width: auto;
    max-width: 320px;
  }
  .custom-tree-node {
    width: 100%;
    min-width: 0;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #23282d;
    .el-icon-more {
      transform: rotate(90deg);
      color: #a1a1a1;
    }
    .tree-icon {
      width: 15px;
      text-align: center;
      &.el-icon-edit {
        color: #ff9b45;
      }
    }
    .stop-box {
      display: inline-block;
      text-align: center;
      color: red;
      border: 1px solid #ff5450;
      border-radius: 50%;
      font-size: 12px;
      padding: 2px 3px;
      transform: scale(0.7);
    }
  }
  .tree-flex {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #23282d;
    .el-icon-more {
      transform: rotate(90deg);
      color: #a1a1a1;
    }
    .tree-icon {
      width: 15px;
      text-align: center;
    }
  }
}
.ps-dialog {
  .dialog-form {
    .flex {
      display: flex;
      align-items: center;
    }
    .form-img {
      display: inline-block;
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-left: 10px;
      cursor: pointer;
      opacity: 0.8;
      &:hover {
        opacity: 1;
      }
    }
  }
}
.custon-tree-popper {
  min-width: 50px;
  padding: 0;
  .popover-btn-box {
    display: flex;
    flex-direction: column;
    .el-button {
      display: block;
      margin: 0 !important;
      padding: 8px 15px;
      color: #23282d;
      &.popper-del {
        color: #ff5450;
      }
      &:hover {
        color: #fd953c;
        background-color: #edeff5;
      }
      &.is-disabled {
        opacity: 0.5;
        &:hover {
          color: #23282d;
          background-color: unset;
        }
      }
    }
  }
}
</style>

<template>
  <div class="ModuleAdmin container-wrapper">
    <div class="wrapper">
      <div class="title">已选功能:</div>
      <div class="tips">注：点击图标，切换至已选/可选功能</div>
      <div ref="menuListRef" class="has-menu-list">
        <div v-for="item in hasMenuList" :key="item.key" class="has-menu-item" @click="remove(item.permission)">
          <img :src="item.icon" alt="">
          <div>{{item.name}}</div>
        </div>
      </div>
      <div class="title">可选功能:</div>
      <div ref="" class="has-menu-list">
        <div v-for="item in nohasMenuList" :key="item.key" class="has-menu-item" @click="add(item.permission)">
         <img :src="item.icon" alt="">
          <div>{{item.name}}</div>
        </div>
      </div>
      <div>
        <el-button size="small" type="primary" class="ps-origin-btn w-150" @click="saveMenu">保存</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import Sortable from 'sortablejs'
import { to } from '@/utils'
export default {
  name: 'ModuleAdmin',
  data() {
    return {
      menuKey: 0,
      allMenuList: [
        {
          icon: require('@/assets/img/h5/canteen_chongzhi.png'),
          name: '充值',
          key: 'charge',
          permission: 'charge'
        },
        {
          icon: require('@/assets/img/h5/canteen_dingdan.png'),
          name: '订单',
          key: 'order',
          permission: 'order'
        },
        {
          icon: require('@/assets/img/h5/canteen_yuyue.png'),
          name: '预约点餐',
          key: 'reservation',
          permission: 'reservation'
        },
        {
          icon: require('@/assets/img/h5/canteen_baocan.png'),
          name: '报餐',
          key: 'report',
          permission: 'report'
        },
        {
          icon: require('@/assets/img/h5/canteen_zahnghu.png'),
          name: '账户信息',
          key: 'account_info',
          permission: 'account_info'
        },
        {
          icon: require('@/assets/img/h5/buffet.png'),
          name: '托盘绑定',
          key: 'tray_bind',
          permission: 'tray_bind'
        },
        {
          icon: require('@/assets/img/h5/my_appoint.png'),
          name: '我的预约',
          key: 'myReservation',
          permission: 'reservation'
        },
        {
          icon: require('@/assets/img/h5/jiaofei.png'),
          name: '缴费中心',
          key: 'jiaofei',
          permission: 'jiaofei'
        },
        {
          icon: require('@/assets/img/h5/control_attendance.png'),
          name: '门禁考勤',
          key: 'attendance',
          permission: 'attendance'
        },
        {
          icon: require('@/assets/img/h5/canteen_caipu.png'),
          name: '意向菜谱',
          key: 'intent_food',
          permission: 'intent_food'
        },
        {
          icon: require('@/assets/img/h5/icon_review.png'),
          name: '审核查询',
          key: 'order_review',
          permission: 'order_review'
        },
        {
          icon: require('@/assets/img/h5/sign_icon.png'),
          name: '免密支付',
          key: 'free_payment_setting',
          permission: 'free_payment_setting'
        },
        {
          icon: require('@/assets/img/h5/feedback.png'),
          name: '食堂建议',
          key: 'shop_feedback',
          permission: 'shop_feedback'
        },
        {
          icon: require('@/assets/img/h5/face_collect.png'),
          name: '人脸采集',
          key: 'face_collect',
          permission: 'face_collect'
        },
        {
          icon: require('@/assets/img/h5/coupon.png'),
          name: '卡券中心',
          key: 'coupon',
          permission: 'coupon'
        }
      ],
      hasMenuList: [],
      nohasMenuList: [],
      sortList: [],
      SortWrap: null
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getMenuList()
      this.$nextTick(() => {
        this.initSortable()
      })
    },
    async getMenuList () {
      if (this.isLoading) return;
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationGetInfoPost({
          id: this.$store.getters.organization
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.hasMenuList = []
        res.data.already_permission.map(item => {
          this.allMenuList.map(menuItem => {
            if (item === menuItem.permission) {
              this.hasMenuList.push(menuItem)
            }
          })
        })
        this.nohasMenuList = []
        res.data.not_app_permission.map(item => {
          this.allMenuList.map(menuItem => {
            if (item === menuItem.permission) {
              this.nohasMenuList.push(menuItem)
            }
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    initSortable() {
      this.sortList = this.hasMenuList.map(item => item.id)
      const menuEl = this.$refs.menuListRef
      this.sortWrap = Sortable.create(menuEl, {
        ghostClass: 'sort-active',
        animation: 300,
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const currentRow = this.hasMenuList.splice(evt.oldIndex, 1)[0]
          this.hasMenuList.splice(evt.newIndex, 0, currentRow)
          const tempIndex = this.sortList.splice(evt.oldIndex, 1)[0]
          this.sortList.splice(evt.newIndex, 0, tempIndex)
        }
      })
    },
    remove(permission) {
      // 预约点餐和我的预约权限相同，所以用了这种写法
      this.nohasMenuList.push(...this.hasMenuList.filter(item => item.permission === permission))
      this.hasMenuList = this.hasMenuList.filter(item => item.permission !== permission)
    },
    add(permission) {
      this.hasMenuList.push(...this.nohasMenuList.filter(item => item.permission === permission))
      this.nohasMenuList = this.nohasMenuList.filter(item => item.permission !== permission)
    },
    async saveMenu () {
      if (this.isLoading) return;
      this.isLoading = true
      let has = []
      this.hasMenuList.map(item => {
        if (has.indexOf(item.permission) === -1) {
          has.push(item.permission)
        }
      })
      let nohas = []
      this.nohasMenuList.map(item => {
        if (nohas.indexOf(item.permission) === -1) {
          nohas.push(item.permission)
        }
      })
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationModifyPost({
          already_permission: has,
          not_app_permission: nohas,
          id: this.$store.getters.organization
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('成功')
        this.getMenuList()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.ModuleAdmin{
  .wrapper{
    padding: 20px;
    margin-top: 20px;
    border-radius: 10px;
    background-color: #FFF;
  }
  .has-menu-list{
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 50px;
    .has-menu-item{
      width: 100px;
      height: 100px;
      margin-right: 20px;
      text-align: center;
      cursor: pointer;
      img{
        width: 40px;
        height: 40px;
      }
    }
  }
  .title{
    font-size: 20px;
    margin-bottom: 20px;
  }
  .tips{
    color: red;
    margin-bottom: 20px;
  }
}
</style>

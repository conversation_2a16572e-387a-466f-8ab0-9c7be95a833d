<template>
  <div class="deductsetting-wrapper" v-loading="isLoading">
    <div class="m-b-10">
      <span class="p-r-10" style="font-size:14px">手续费生效方式</span>
      <el-radio-group class="ps-radio" v-model="commissionsChargeType" @change="changeCommissionsChargeType">
        <el-radio :label="0">订单实收金额+手续费</el-radio>
        <!-- <el-radio :label="1">（ 订单原金额+手续费）*优惠</el-radio> -->
      </el-radio-group>
    </div>
    <div class="wrapper-title">注：优先使用【营销活动-手续费】中的设置，当用户无分组或所在分组无规则时，生效当前【扣款设置】的手续费规则</div>
    <div class="l-title">
      <span>线上扣款顺序</span>
      <!-- <span class="float-r" style="font-weight: normal;">
        是否以用户设定的扣款钱包顺序进行扣款
        <el-switch style="margin-left: 5px;" v-model="isOpen" active-color="#ff9b45"></el-switch>
      </span> -->
    </div>
    <div class="table-box">
      <el-table
        ref="onlineWalletRef"
        width="100%"
        row-key="id"
        :data="onlineWalletList"
        tooltip-effect="dark"
        header-row-class-name="ps-table-header-row"
        stripe
      >
        <el-table-column type="index" label="优先级" width="80" align="center"></el-table-column>
        <el-table-column label="扣款钱包" prop="sub_payway_alias" align="center"></el-table-column>
        <el-table-column label="商户名称" prop="merchant_name" align="center"></el-table-column>
        <el-table-column label="商户号" prop="merchant_id" align="center"></el-table-column>
        <el-table-column
          show-overflow-tooltip
          label="备注"
          prop="remark"
          align="center"
        ></el-table-column>
        <el-table-column show-overflow-tooltip label="手续费" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" class="ps-text" :disabled="scope.row.payway === 'CashPay' || scope.row.payway === 'PushiPay'?true:false" v-if="!scope.row.service_fee_value" @click="serviceSetting(scope.row)">
              设置
            </el-button>
            <span v-else-if="scope.row.service_fee_value" @click="serviceSetting(scope.row)" class="ps-origin" style="cursor: pointer;">
              {{ servicePirceFormat(scope.row) }} <span>{{scope.row.service_fee_type === 1?'元':'%'}}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <!-- eslint-disable-next-line vue/no-unused-vars -->
          <template slot-scope="scope">
            <img class="drop-img" src="@/assets/img/drop.png" alt="" />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="add-wrapper">
      <el-button
        v-permission="['background_payment.pay_info.set_order_payinfos']"
        type="primary"
        size="small"
        class="ps-origin-btn"
        @click="saveWalletWeightHandle('online')"
      >
        保存
      </el-button>
    </div>
    <div class="l-title">
      <span>线下扣款顺序</span>
      <!-- <span class="float-r" style="font-weight: normal;">
        是否以用户设定的扣款钱包顺序进行扣款
        <el-switch style="margin-left: 5px;" v-model="isOpen" active-color="#ff9b45"></el-switch>
      </span> -->
    </div>
    <div class="table-box">
      <el-table
        ref="instoreWalletRef"
        width="100%"
        row-key="id"
        :data="instoreWalletList"
        tooltip-effect="dark"
        header-row-class-name="ps-table-header-row"
        stripe
      >
        <el-table-column type="index" label="优先级" width="80" align="center"></el-table-column>
        <el-table-column label="扣款钱包" prop="sub_payway_alias" align="center"></el-table-column>
        <el-table-column label="商户名称" prop="merchant_name" align="center"></el-table-column>
        <el-table-column label="商户号" prop="merchant_id" align="center"></el-table-column>
        <el-table-column
          show-overflow-tooltip
          label="备注"
          prop="remark"
          align="center"
        ></el-table-column>
        <el-table-column show-overflow-tooltip label="手续费" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" class="ps-text" :disabled="scope.row.payway === 'CashPay' || scope.row.payway === 'PushiPay'?true:false"  v-if="!scope.row.service_fee_value" @click="serviceSetting(scope.row)">
              设置
            </el-button>
            <span v-else-if="scope.row.service_fee_value" @click="serviceSetting(scope.row)" class="ps-origin" style="cursor: pointer;">
              {{ servicePirceFormat(scope.row) }} <span>{{scope.row.service_fee_type === 1?'元':'%'}}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="" align="center">
          <!-- eslint-disable-next-line vue/no-unused-vars -->
          <template slot-scope="scope">
            <img class="drop-img" src="@/assets/img/drop.png" alt="" />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="add-wrapper">
      <el-button
        v-permission="['background_payment.pay_info.set_order_payinfos']"
        type="primary"
        size="small"
        class="ps-origin-btn"
        @click="saveWalletWeightHandle('instore')"
      >
        保存
      </el-button>
    </div>
    <div class="l-title">
      <span>扣款限制</span>
    </div>
    <div class="form-wrapper">
      <el-form
        :model="walletFormData"
        ref="walletFormRef"
        :rules="walletFormRuls"
        label-width="180px"
      >
        <el-form-item prop="pushiPayTime" label="重复支付限制">
          <el-switch
            class="wallet-margin"
            v-model="walletFormData.isDuplicatePayLimit"
            active-color="#ff9b45"
          ></el-switch>
          <el-input-number
            :disabled="!walletFormData.isDuplicatePayLimit"
            v-model="walletFormData.duplicatePaySecondLimit"
            :min="0"
          ></el-input-number>
          <span class="wallet-margin-l">秒内不能重复支付</span>
        </el-form-item>
      </el-form>
    </div>
    <div class="add-wrapper">
      <el-button
        v-permission="['background_organization.organization.modify_settings']"
        type="primary"
        size="small"
        class="ps-origin-btn"
        @click="setSeniorSettingHandle"
      >
        保存
      </el-button>
    </div>
    <el-dialog
      title="手续费设置"
      :visible.sync="serviceSettingDialog"
      width="400px"
      custom-class="ps-dialog"
    >
      <el-form
        ref="serviceSettingForm"
        :rules="serviceSettingDialogRuls"
        :model="serviceSettingDialogFormData"
      >
        <div class="form-flex">
          <el-form-item class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="1"
            >
              定额
            </el-radio>
          </el-form-item>
          <el-form-item prop="quota" v-if="serviceSettingDialogFormData.service_fee_type !== 2">
            <div class="form-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.quota"
              ></el-input>
              <span>元</span>
            </div>
            <span>实收金额=订单金额+定额</span>
          </el-form-item>
        </div>
        <div class="form-flex">
          <el-form-item label="" class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="2"
            >
              百分比
            </el-radio>
          </el-form-item>
          <el-form-item prop="discount" v-if="serviceSettingDialogFormData.service_fee_type !== 1">
            <div class="form-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.discount"
              ></el-input>
              <span>%</span>
            </div>
            <span>实收金额=订单金额+（订单金额*折扣）</span>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="serviceSettingDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="determineServiceSettingDialog">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to, debounce } from '@/utils'
import Sortable from 'sortablejs'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { validataPrice, validateStock } from '@/assets/js/validata'
import NP from 'number-precision'
export default {
  name: 'DeductSetting',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: {
      // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  data() {
    // var validatePass = (rule, value, callback) => {
    //   if (value === '') {
    //     callback(new Error('请输入密码'))
    //   } else {
    //     if (this.ruleForm.checkPass !== '') {
    //       this.$refs.ruleForm.validateField('checkPass')
    //     }
    //     callback()
    //   }
    // }
    return {
      isOpen: false,
      isLoading: false,
      commissionsChargeType: 0,
      formOperate: 'detail',
      onlineSortable: null,
      instoreSortable: null,
      pageSize: 10,
      currentPage: 1,
      totalCount: 0,
      onlineWalletList: [],
      instoreWalletList: [],
      onlineSortList: [],
      instoreSortList: [],
      walletFormData: {
        isDuplicatePayLimit: false,
        duplicatePaySecondLimit: 0
      },
      walletFormRuls: {
        merchantId: [{ required: true, message: '商户号不能为空', trigger: 'blur' }],
        merchantName: [{ required: true, message: '商户名称不能为空', trigger: 'blur' }],
        payway: [{ required: true, message: '请选择充值渠道', trigger: 'blur' }]
      },
      serviceSettingDialog: false,
      serviceSettingDialogFormData: {
        service_fee_type: 1,
        quota: '',
        discount: ''
      },
      serviceSettingDialogRuls: {
        quota: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { validator: validataPrice, trigger: 'blur' }
        ],
        discount: [
          { required: true, message: '请输入折扣', trigger: 'blur' },
          { validator: validateStock, trigger: 'blur' }
        ]
      },
      serviceSettingData: {}
    }
  },
  computed: {
    checkIsFormStatus: function() {
      let show = false
      switch (this.formOperate) {
        case 'detail':
          show = false
          break
        case 'add':
          show = true
          break
      }
      return show
    }
  },
  watch: {
    type(val) {
      // this.initLoad()
    },
    organizationData(val) {
      setTimeout(() => {
        // 给个延时，防止意外
        this.searchHandle()
      }, 50)
    }
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.setChargeSetting({ organization_id: this.infoData.id })
      this.getWalletPayList('online')
      this.getWalletPayList('instore')
      this.getSettingInfo()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    // 获取钱包支付顺序表数据
    async getWalletPayList(type) {
      this.isLoading = true
      let params = {
        organizations: [this.organizationData.id],
        pay_scenes: [type],
        company: this.organizationData.company
      }
      // this.$sleep(2000)
      const [err, res] = await to(
        this.$apis.apiBackgroundPaymentPayInfoGetOrderPayinfosPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (type === 'online') {
          this.onlineWalletList = res.data.results.sort((a, b) => {
            return a.weight - b.weight
          })
          if (!this.onlineSortable) {
            this.$nextTick(() => {
              this.initSortable(type)
            })
          }
        } else {
          this.instoreWalletList = res.data.results.sort((a, b) => {
            return a.weight - b.weight
          })
          if (!this.instoreSortable) {
            this.$nextTick(() => {
              this.initSortable(type)
            })
          }
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化
    initSortable(type) {
      this[type + 'SortList'] = this[type + 'WalletList'].map(item => item.id)
      const tableEl = this.$refs[type + 'WalletRef'].$el.querySelector(
        '.el-table__body-wrapper > table > tbody'
      )
      this[type + 'Sortable'] = Sortable.create(tableEl, {
        ghostClass: 'sortable-active',
        animation: 300,
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const currentRow = this[type + 'WalletList'].splice(evt.oldIndex, 1)[0]
          this[type + 'WalletList'].splice(evt.newIndex, 0, currentRow)
          console.log(this[type + 'SortList'])
          const tempIndex = this[type + 'SortList'].splice(evt.oldIndex, 1)[0]
          this[type + 'SortList'].splice(evt.newIndex, 0, tempIndex)
        }
      })
    },
    determineServiceSettingDialog() {
      this.$refs.serviceSettingForm.validate(valid => {
        if (valid) {
          // 塞到列表里面
          this[this.serviceSettingData.pay_scene + 'WalletList'].map((item, index) => {
            if (this.serviceSettingData.id === item.id) {
              item.service_fee_type = this.serviceSettingDialogFormData.service_fee_type
              item.service_fee_value =
                this.serviceSettingDialogFormData.service_fee_type === 1
                  ? NP.times(Number(this.serviceSettingDialogFormData.quota), 100)
                  : this.serviceSettingDialogFormData.discount
            }
          })
          console.log(this[this.serviceSettingData.pay_scene + 'WalletList'])
          this.serviceSettingDialog = false
        } else {
          console.log(valid)
        }
      })
    },
    async getSettingInfo() {
      this.isLoading = true
      // this.$sleep(2000)
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGetSettingsPost({
          id: this.organizationData.id,
          company: this.organizationData.company
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.settingInfo = res.data
        // eslint-disable-next-line no-unneeded-ternary
        this.walletFormData.isDuplicatePayLimit = res.data.is_duplicate_pay_limit ? true : false
        this.walletFormData.duplicatePaySecondLimit = res.data.duplicate_pay_second_limit
      } else {
        this.$message.error(res.msg)
      }
    },
    serviceSetting(data) {
      this.serviceSettingData = data
      this.serviceSettingDialogFormData.service_fee_type = data.service_fee_type
      if (data.service_fee_type === 1) {
        this.serviceSettingDialogFormData.discount = ''
        this.serviceSettingDialogFormData.quota = String(NP.divide(data.service_fee_value, 100))
      }
      if (data.service_fee_type === 2) {
        this.serviceSettingDialogFormData.discount = data.service_fee_value
        this.serviceSettingDialogFormData.quota = ''
      }
      this.serviceSettingDialog = true
    },
    // 保存支付顺序
    async saveWalletWeightHandle(type) {
      // 为啥不能直接拿列表数据
      // let weightList = this[type + 'SortList'].map((id, index) => {
      //   return {
      //     id: id,
      //     weight: index + 1
      //   }
      // })
      let weightList = this[type + 'WalletList'].map((item, index) => {
        return {
          id: item.id,
          weight: index + 1,
          service_fee_type: item.service_fee_type,
          service_fee_value: item.service_fee_value
        }
      })
      this.isLoading = true
      let params = {
        organizations: [this.organizationData.id],
        pay_scene: type,
        payinfos: weightList,
        company: this.organizationData.company
      }
      // this.$sleep(2000)
      const [err, res] = await to(
        this.$apis.apiBackgroundPaymentPayInfoSetOrderPayinfosPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getWalletPayList(type)
      } else {
        this.$message.error(res.msg)
      }
    },
    async setSeniorSettingHandle() {
      this.isLoading = true
      // this.$sleep(2000)
      let params = {
        id: this.organizationData.id,
        company: this.organizationData.company,
        is_duplicate_pay_limit: this.walletFormData.isDuplicatePayLimit ? 1 : 0,
        duplicate_pay_second_limit: this.walletFormData.duplicatePaySecondLimit
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundOrganizationOrganizationModifySettingsPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.payTemplateList = res.data
        this.$message.success(res.msg)
        this.getSettingInfo()
      } else {
        this.$message.error(res.msg)
      }
    },
    changeCommissionsChargeType() {
      let params = {
        type: 0,
        organization_id: this.infoData.id, // 组织id
        commissions_charge_type: this.commissionsChargeType
      }
      this.setChargeSetting(params)
    },
    // 单独设置一级组织手续费
    async setChargeSetting(params) {
      const [err, res] = await to(
        this.$apis.apiBackgroundPaymentPayInfoSetCommissionChargeSettingPost(params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 如果有传这个进来就弹成功，不传就是获取参数赋值
        if (params.commissions_charge_type === 0 || params.commissions_charge_type === 1) {
          this.$message.success('配置成功')
        }
        this.commissionsChargeType = res.data.commissions_charge_type
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化金额
    servicePirceFormat(row) {
      return row.service_fee_type === 1 ? NP.divide(row.service_fee_value, 100) : row.service_fee_value
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.deductsetting-wrapper {
  position: relative;
  margin-top: 10px;
  .wrapper-title{
    text-align: right;
    padding-bottom:20px;
    color: #fd9445;
  }
  .table-box {
    padding: 10px 0;
    .ps-table-header-row {
      .cell {
        font-size: 14px;
        font-weight: bold;
        color: #23282d;
      }
    }
    .drop-img {
      vertical-align: middle;
      cursor: move;
    }
  }
  .wallet-margin {
    margin: 0 40px 0 5px;
  }
  .wallet-margin-l {
    margin-left: 10px;
  }
  .add-wrapper {
    margin: 60px 0;
    text-align: center;
    .el-button {
      width: 120px;
    }
  }
  .form-flex {
    display: flex;
  }
}
</style>

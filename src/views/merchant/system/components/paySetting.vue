<template>
  <div class="paysetting-wrapper">
    <!-- child paysetting start -->
    <div class="paysetting-sub" v-loading="subIsLoading">
      <div class="sub-wrapper" v-for="(info, key) in collapseInfo" :key="key">
        <div class="l-title">
          <span>{{ info.name }}</span>
          <el-switch v-if="organizationData.level!==0" style="margin-left: 15px;" v-model="info.isOpen" active-color="#ff9b45" @change="changeSceneHandle($event, info.key)"></el-switch>
          <el-button v-permission="['background_payment.pay_info.org_bind']" v-if="showBindBtnHandle(info.key) && organizationData.level!==0" type="primary" class="ps-origin-btn float-r save-m-r" size="small" @click="clickBindOrgsHandle(info.key)" >保存</el-button>
        </div>
        <el-collapse v-model="info.activePayCollapse" @change="changeCollapseHandle">
          <el-collapse-item v-for="payway in info.payways" :key="payway.key" :title="payway.name" :name="payway.key">
            <template slot="title">
              <el-checkbox class="ps-checkbox" v-model="payway.isOpen" :disabled="!info.isOpen || organizationData.level===0" @change="changePaywayHandle($event, payway.key, info)">{{ payway.name }}</el-checkbox>
              <!-- <el-switch style="margin-left: 15px;" v-model="xxx" active-color="#ff9b45"></el-switch> -->
              <span class="tips-r">
                <span class="open">展开</span>
                <span class="close">收起</span>
              </span>
            </template>
            <el-table
              :ref="`subPayInfoListRef${info.key}-${payway.key}`"
              width="100%"
              :data="payway.sub_payways"
              tooltip-effect="dark"
              v-loading="isLoading"
            >
              <el-table-column class-name="ps-checkbox" width="50" align="center">
                <template slot-scope="scope">
                  <el-checkbox class="ps-checkbox" :disabled="!(info.isOpen && payway.isOpen)" v-model="scope.row.binded" @change="changeSubPayHandle($event, scope.row, payway.sub_payways, `${info.key}-${payway.key}`)"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="商户名称" prop="merchant_name" align="center"></el-table-column>
              <el-table-column label="商户号" prop="merchant_id" align="center"></el-table-column>
              <el-table-column label="支付类型" prop="payway_alias" align="center"></el-table-column>
              <el-table-column label="支付方式" prop="sub_payway_alias" align="center"></el-table-column>
              <el-table-column show-overflow-tooltip label="适用层级" prop="" align="center">
                <template slot-scope="scope">
                  <span>{{ showOrganizationsText(scope.row.organizations) }}</span>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip label="备注" prop="remark" align="center"></el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <!-- child paysetting end -->
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'PaySetting',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  data() {
    return {
      isLoading: false,
      formOperate: 'detail',
      pageSize: 10,
      currentPage: 1,
      totalCount: 0,
      subPaywayList: [],
      organizationList: [], // 适用组织列表
      selectTableCoumn: [],
      activePayCollapse: [], // 手风琴选项
      subIsLoading: false,
      subPayInfoList: [], // 手风琴源数据
      collapseInfo: {}, // 处理下手风琴需要的参数
      selectSubInfo: {} // 选中的数据
    }
  },
  computed: {
    checkIsFormStatus: function() {
      let show = false
      switch (this.formOperate) {
        case 'detail':
          show = false
          break;
        case 'add':
          show = true
          break;
      }
      return show
    }
  },
  watch: {
    type(val) {
      // this.initLoad()
    },
    organizationData(val) {
      setTimeout(() => { // 给个延时，防止意外
        this.searchHandle()
      }, 50)
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getSubOrgsAllList()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    // 过滤tree数据
    filterTreeNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 设置配置字段的前缀，仅对二级使用
    setTemplatePrefix(list) {
      let result = deepClone(list)
      result.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            child.parent = item.key
            child.key = item.key + '-' + child.key
          })
        }
      })
      return result
    },
    // join organizations
    showOrganizationsText(list) {
      let text = ''
      list.forEach((item) => {
        if (!text) {
          text = item.name
        } else {
          text += `，${item.name}`
        }
      })
      return text
    },
    // 动态添加参数
    setDynamicParams(type, list, extraList) {
      // return
      if (type === 'add') {
        extraList.forEach(v => {
          switch (v.type) {
            case 'checkbox':
              if (v.default) {
                let values = JSON.parse(v.default)
                this.$set(list, v.key, values)
              } else {
                this.$set(list, v.key, [])
              }
              break;
            default:
              if (v.default) {
                this.$set(list, v.key, v.default)
              } else {
                this.$set(list, v.key, '')
              }
              break;
          }
        })
      } else {
        extraList.forEach(v => {
          // this.$set(this[list], v.key, info && info[v.key] ? info[v.key] : '')
          switch (v.type) {
            case 'checkbox':
              this.$set(list, v.key, this.dialogData.extra[v.key])
              break;
            default:
              this.$set(list, v.key, this.dialogData.extra[v.key])
              break;
          }
        })
      }
    },
    paySettingNormalizer(node) {
      if (!node) { return }
      return {
        id: node.key,
        label: node.name,
        children: node.children
      }
    },
    // 适用组织key，配置
    organizationNormalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children_list
      }
    },
    // 通过固定字段查表数据，并返回
    findKeyTreeList(list, key, id) {
      let result = []
      list.forEach(item => {
        if (item[key] === id) {
          result = [item]
        } else {
          if (item.children_list && item.children_list.length > 0) {
            result = this.findKeyTreeList(item.children_list, key, id)
          }
        }
      })
      return result
    },
    // 删除空的子级
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          // if (_that.checkIsFormStatus) {
          //   item.isDisabled = false
          // } else {
          //   item.isDisabled = true
          // }
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    handleSelectionChange(e) {
      this.selectTableCoumn = e.map(v => {
        return v.id
      })
    },
    // 非root 操作
    changeCollapseHandle(e) {

    },
    // 获取支付配置列表
    async getSubOrgsAllList() {
      this.subIsLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundPaymentPayInfoSubOrgsAllListPost({
        organizations: [this.organizationData.id],
        pay_scenes: ['instore', 'online'],
        company: this.organizationData.company
      }))
      this.subIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.collapseInfo = {}
        this.selectSubInfo = {}
        this.subPayInfoList = res.data.sort((a, b) => {
          return b.key.charCodeAt(0) - a.key.charCodeAt(0);
        })
        let openList = []
        deepClone(res.data).map(item => {
          let sceneOpen = false
          let activePayCollapse = []
          item.payways = item.payways.map(payway => {
            let paywayOpen = false
            payway.sub_payways.forEach(sub => {
              if (sub.binded) {
                sceneOpen = true
                paywayOpen = true
                if (this.selectSubInfo[`${item.key}-${payway.key}`]) {
                  this.selectSubInfo[`${item.key}-${payway.key}`].push(sub.id)
                } else {
                  this.$set(this.selectSubInfo, `${item.key}-${payway.key}`, [sub.id])
                }
                if (!activePayCollapse.includes(payway.key)) {
                  activePayCollapse.push(payway.key)
                }
                openList.push({
                  type: item.key + '-' + payway.key,
                  list: sub
                })
              }
            })
            payway.isOpen = paywayOpen
            return payway
          })
          this.$set(this.collapseInfo, item.key, {
            ...item,
            activePayCollapse: activePayCollapse,
            isOpen: sceneOpen
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // select tabel
    setDefaultTableSelect(rows) {
      rows.forEach(item => {
        let rowRefs = this.$refs[`subPayInfoListRef${item.type}`][0]
        rowRefs.toggleRowSelection(item.list, true)
      })
    },
    // switch 开关
    changeSceneHandle(e, type) {
      // for (let scene in this.collapseInfo) {
      //   if (scene === type && !e) {
      //     this.collapseInfo[scene].payways.forEach(payway => {
      //       payway.isOpen = false
      //       this.changePaywayHandle(false, payway.key, scene)
      //     })
      //   }
      // }
    },
    // 设置table 是否可选
    selectableHandle(row, index) {
      let canSelect = true
      if (!this.collapseInfo[row.pay_scene].isOpen) {
        canSelect = false
      }
      if (this.collapseInfo[row.pay_scene].isOpen) {
        this.collapseInfo[row.pay_scene].payways.forEach(item => {
          if (!item.isOpen && row.payway === item.key) {
            canSelect = false
          }
        })
      }
      if (this.organizationData.level === 0) {
        canSelect = false
      }
      return canSelect
    },
    // change
    changePaywayHandle(e, curent, parent) {
      if (e && !parent.activePayCollapse.includes(curent)) {
        parent.activePayCollapse.push(curent)
      }
    },
    // show bind button
    showBindBtnHandle(type) {
      let isShow = false
      for (let k in this.selectSubInfo) {
        if (k.indexOf(type) > -1) {
          isShow = true
        }
        if (isShow) {
          break
        }
      }
      return isShow
    },
    clickBindOrgsHandle(type) {
      let params = []
      this.collapseInfo[type].payways.forEach(payway => {
        if (this.collapseInfo[type].isOpen && payway.isOpen) {
          let selectSubInfoId = this.selectSubInfo[type + '-' + payway.key]
          payway.sub_payways.forEach(subPayways => {
            if (selectSubInfoId.includes(subPayways.id)) {
              params.push({
                id: subPayways.id
              })
            }
          })
        }
      })
      this.setSubOrgsBind(type, params)
    },
    // 支付设置-设置子组织绑定关联支付配置
    async setSubOrgsBind(type, ids) {
      this.subIsLoading = true
      // await this.$sleep(2000)
      let params = {
        pay_scene: type,
        organizations: [this.organizationData.id],
        payinfo: ids,
        company: this.organizationData.company
      }
      const [err, res] = await to(this.$apis.apiBackgroundPaymentPayInfoSubOrgsBindPost(params))
      this.subIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 1
        this.$message.success(res.msg)
        this.getSubOrgsAllList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当treeselect 打开时要取消
    openTreeHandle(e) {
      if (this.$refs.subPayway) {
        // this.$refs.subPayway.blur()
      }
    },
    changeSubPayHandle(e, row, data, key) {
      let selectsubpayway = []
      data.forEach(item => {
        if (item.binded && item.id !== row.id) {
          selectsubpayway.push(item.sub_payway)
        }
      })
      data.forEach(item => {
        // if (item.id !== row.id) {
        //   item.binded = false
        // } else {
        //   if (e) {
        //     this.$set(this.selectSubInfo, key, row.id)
        //   } else {
        //     this.$set(this.selectSubInfo, key, '')
        //   }
        // }
        if (e) {
          if (!selectsubpayway.includes(row.sub_payway)) {
            if (this.selectSubInfo[key] && this.selectSubInfo[key].length) {
              if (!this.selectSubInfo[key].includes(row.id)) {
                this.selectSubInfo[key].push(row.id)
              }
            } else {
              this.$set(this.selectSubInfo, key, [row.id])
            }
          } else {
            if (item.id === row.id) {
              this.$nextTick(() => {
                item.binded = false
                let index = this.selectSubInfo[key].indexOf(row.id)
                if (index > -1) {
                  this.selectSubInfo[key].splice(index, 1)
                }
              })
            }
            this.$message.error('请勿选择相同支付类型！')
          }
        } else {
          let index = this.selectSubInfo[key].indexOf(row.id)
          if (index > -1) {
            this.selectSubInfo[key].splice(index, 1)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.paysetting-wrapper {
  position: relative;
  margin-top: 10px;
  .save-m-r{
    margin-right: 10px;
  }
  .tree-wrapper{
    width: auto;
    max-width: 250px;
    background-color: $organizationTreeBg;
    .tree-search {
      margin-bottom: 10px;
    }
    .all-tree{
      display: flex;
      align-items: center;
      line-height: 30px;
      font-size: 14px;
      cursor: pointer;
      padding-left: 10px;
      &.is-current {
        position: relative;
        color: #fda04d;
        background-color: #fbeee6;
        font-weight: 600;
        // &::before{
        //   content: '';
        //   position: absolute;
        //   width: 20px;
        //   left: -20px;
        //   top: 0;
        //   bottom: 0;
        //   background-color: #fbeee6;
        // }
        // &::after{
        //   content: '';
        //   position: absolute;
        //   width: 20px;
        //   right: -20px;
        //   top: 0;
        //   bottom: 0;
        //   background-color: #fbeee6;
        // }
      }
      .tree-search-icon{
        position: relative;
        display: inline-block;
        width: 18px;
        height: 40px;
        margin-right: 5px;
        img {
          display: inline-block;
          width: 18px;
          height: 18px;
          vertical-align: middle;
        }
      }
    }
    .el-tree{
      background-color: $organizationTreeBg;
      .el-tree-node__label {
        font-size: 14px;
        color: #23282d;
      }
      .el-tree-node__content{
        height: 30px;
      }
    }
    .tree-box {
      .is-current>.el-tree-node__content {
        position: relative;
        // background-color: #fbeee6;
        background-color: #fbeee6 !important;
      }
    }
  }
  .paysetting-container{
    display: flex;
    .paysetting-r{
      flex: 1;
      min-width: 0;
      margin-left: 10px;
    }
  }
  .paysetting-sub {
    $collapsebg: #f8f9fa;
    .sub-wrapper{
      margin-bottom: 15px;
      border-radius: 4px;
      border: solid 1px #e0e6eb;
      background-color: $collapsebg;
      .el-collapse{
        padding: 0 20px;
        background-color: $collapsebg;
        .el-collapse-item__header{
          background-color: $collapsebg;
        }
      }
    }
  }
  .tips-r{
    position: absolute;
    right: 45px;
    .close{
      display: none;
    }
  }
  .is-active .tips-r{
    .open{
      display: none;
    }
    .close{
      display: inline-block;
    }
  }
}
.ps-paysetting-dialog{
  .el-dialog__body{
    max-height: calc(100vh - 40vh);
    overflow-y: auto;
  }
  .paysetting-dialog{
    .el-form-item{
      // width: 45%;
      // &:nth-child(even) {
      //   margin-right: 5%;
      // }
      // &:first-child {
      //   margin-right: 5%;
      // }
    }
    .el-form-item__content{
      // max-width: 200px;
      line-height: 32px;
    }
    .tree-item .el-form-item__content{
      // max-width: 96%;
    }
    .remark-item {
      // width: 100%;
      // .el-form-item__content{
      //   width: 100%;
      //   max-width: 92%;
      // }
    }
    .vue-treeselect__control{
      height: 32px;
    }
    .el-form-item__label{
      // display: block;
      // text-align: left;
    }
    .el-select{
      width: 100%;
    }
  }
}
</style>

<template>
  <div class="deductsetting-wrapper" v-loading="isLoading">
    <div class="l-title">
      <span>充值设置</span>
    </div>
    <div class="form-wrapper" style="max-width: 700px;">
      <el-form
        :model="seniorFormData"
        ref="seniorFormRef"
        :rules="seniorFormRuls"
        label-width="120px"
      >
        <el-form-item prop="money" label="可选充值金额">
          <el-input class="ps-input" style="width: 300px;" :maxlength="9" v-model="seniorFormData.money"></el-input>
          <el-button v-if="seniorFormData.rechargeAmountList.length < 5" :disabled="!seniorFormData.money" class="add-btn" icon="el-icon-circle-plus" type="text" circle @click="addMoneyList"></el-button>
          <div class="money-tag m-t-10">
            <el-tag
              v-for="(tag, i) in seniorFormData.rechargeAmountList"
              :key="tag + i"
              closable
              @close="closeMoneyTag(tag, i)">
              {{tag + '元'}}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item prop="abcPayTime" label="可充值任意金额">
          <el-switch style="margin-right: 25px;" v-model="seniorFormData.allowCustomAmount" active-color="#ff9b45"></el-switch>
          <el-checkbox class="ps-checkbox" :disabled="!seniorFormData.allowCustomAmount" v-model="seniorFormData.openMinimumRechargeAmount" >最低需要充值</el-checkbox>
          <el-input class="ps-input" :maxlength="9" :disabled="!(seniorFormData.openMinimumRechargeAmount && seniorFormData.allowCustomAmount)" style="width: 80px; margin: 0 10px;" v-model="seniorFormData.minimumRechargeAmount"></el-input>
          元
        </el-form-item>
        <div class="m-b-20" style="color: red; font-size: 12px;">
          注：最低充值对应当前组织的充值限制
        </div>
        <el-form-item prop="rechargeDateType" label="指定日期可充值">
          <div v-for="(paySceneName, payScene) in rechargePaySceneType" :key="paySceneName">
            <label style="    margin-right: 10px;float: left;">{{paySceneName}} </label>
            <div class="inline-block">
              <el-checkbox-group class="ps-checkbox" v-model="seniorFormData.rechargeDateType[payScene]" @change="changeRechargeDate">
                <div class="money-tag">
                  <el-checkbox label="month">每月</el-checkbox>
                  <el-tag
                    class="m-l-10 m-r-10 m-b-10"
                    v-for="(tag, i) in seniorFormData.allowRechargeDateList[payScene]"
                    :key="tag + i"
                    closable
                    @close="closeDateHandle(tag, i, payScene)">
                    {{tag + ' 号'}}
                  </el-tag>
                  <!-- <el-button  :disabled="!seniorFormData.money" class="add-btn" icon="el-icon-circle-plus" type="text" circle @click="addMoneyList"></el-button> -->
                  <span v-if="seniorFormData.allowRechargeDateList[payScene].length < 6">
                  <el-form-item v-if="inputVisible[payScene]" prop="dateValue" label="" class="inline-label">
                    <el-input
                      class="input-new-tag ps-input m-l-10"
                      v-model="seniorFormData.dateValue"
                      :ref="payScene + 'saveTagInput'"
                      size="small"
                      @keyup.enter.native="handleInputConfirm(payScene)"
                      @blur="handleInputConfirm(payScene)"
                      :disabled="isDisabledDate(payScene)"
                    >
                    </el-input>
                  </el-form-item>
                  <el-button v-else :disabled="isDisabledDate(payScene)" class="button-new-tag" size="small" @click="showInput(payScene)">+</el-button>
                </span>
                </div>
                <div class="">
                  <el-checkbox label="lastDay">每月最后一天</el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
        <div class="form-line ps-line"></div>
        <div class="l-title">
          <span>其它设置</span>
        </div>
        <div class="inline">
          <el-form-item prop="limitTodayRechargeAmount" label="单日累计充值上限">
            <el-input class="ps-input" :maxlength="9" v-model="seniorFormData.limitTodayRechargeAmount"></el-input>
          </el-form-item>
          <el-form-item prop="limitTodayConsumeAmount" label="单日累计消费上限">
            <el-input class="ps-input" :maxlength="9" v-model="seniorFormData.limitTodayConsumeAmount"></el-input>
          </el-form-item>
          <el-form-item prop="limitBalanceAmount" label="钱包累计余额上限">
            <el-input class="ps-input" :maxlength="9" v-model="seniorFormData.limitBalanceAmount"></el-input>
          </el-form-item>
        </div>
        <div class="" style="color: red; font-size: 12px;">
          注：该设置只针对当前层级的储值钱包进行设置
        </div>
      </el-form>
      <div class="add-wrapper">
        <el-button v-permission="['background_organization.organization.modify_settings']" type="primary" class="ps-origin-btn" @click="saveWalletHandle">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { to, debounce, divide, times, deepClone } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'SeniorSetting',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  data() {
    let validataPrice = (rule, value, callback) => {
      let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      // eslint-disable-next-line eqeqeq
      if (value === '' && (rule.field === 'money' || rule.field === 'limitBalanceAmount')) {
        callback()
        return
      }
      if (value === '0') {
        return callback(new Error('金额格式有误'))
      }
      if (!reg.test(value)) {
        callback(new Error('金额格式有误'))
      } else {
        callback()
      }
    }
    let validataDateValue = (rule, value, callback) => {
      // let reg = /^\\d+$/
      let reg = /^\+?[1-9][0-9]*$/
      if (value === '0') {
        return callback(new Error('日期不能为0'))
      }
      if (!reg.test(value)) {
        callback(new Error('日期格式有误'))
      } else {
        if (value > 28) {
          callback(new Error('不能超过28'))
        }
        callback()
      }
    }
    return {
      isLoading: false,
      settingInfo: null,
      formOperate: 'detail',
      rechargePaySceneType: { charge: '线上', charge_offline: '线下' },
      seniorFormData: {
        money: '',
        rechargeAmountList: [],
        allowCustomAmount: false,
        openMinimumRechargeAmount: false,
        minimumRechargeAmount: '',
        rechargeDateType: { charge: [], charge_offline: [] }, // 指定日期可充值 month lastDay
        allowRechargeDateList: { charge: [], charge_offline: [] }, // 可充值日期 每月最后一天填-1
        limitTodayRechargeAmount: '',
        limitTodayConsumeAmount: '',
        limitBalanceAmount: ''
      },
      seniorFormRuls: {
        limitTodayRechargeAmount: [{ required: true, validator: validataPrice, trigger: "blur" }],
        limitTodayConsumeAmount: [{ required: true, validator: validataPrice, trigger: "blur" }],
        limitBalanceAmount: [{ validator: validataPrice, trigger: "blur" }],
        money: [{ validator: validataPrice, trigger: "change" }],
        dateValue: [{ validator: validataDateValue, trigger: "change" }]
      },
      inputVisible: { charge: false, charge_offline: false },
      inputValue: { charge: '', charge_offline: '' }
    }
  },
  computed: {
    isDisabledDate() {
      return function (payScene) {
        return !this.seniorFormData.rechargeDateType[payScene].includes('month')
      }
    }
  },
  watch: {
    type(val) {
      // this.initLoad()
    },
    organizationData(val) {
      setTimeout(() => { // 给个延时，防止意外
        this.searchHandle()
      }, 50)
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getSettingInfo()
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    async getSettingInfo() {
      this.isLoading = true
      // this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationGetSettingsPost({
        id: this.organizationData.id,
        company: this.organizationData.company
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.settingInfo = res.data
        this.initSettingInfo(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化
    initSettingInfo(data) {
      this.seniorFormData.rechargeAmountList = data.recharge_amount_list.map(amount => {
        return divide(amount)
      })
      this.seniorFormData.limitTodayRechargeAmount = divide(data.limit_today_recharge_amount)
      this.seniorFormData.limitTodayConsumeAmount = divide(data.limit_today_consume_amount)
      this.seniorFormData.limitBalanceAmount = divide(data.limit_balance_amount)
      this.seniorFormData.allowCustomAmount = data.allow_custom_amount
      if (data.minimum_recharge_amount) {
        this.seniorFormData.openMinimumRechargeAmount = true
        this.seniorFormData.minimumRechargeAmount = divide(data.minimum_recharge_amount)
      }
      // 初始化下可充值日期
      if (data.allow_recharge_date_list instanceof Array) {
        data.allow_recharge_date_list = { charge: deepClone(data.allow_recharge_date_list), charge_offline: [] }
      }

      for (let payScene in this.rechargePaySceneType) {
        // 先重置，再赋值
        this.seniorFormData.rechargeDateType[payScene] = []
        if (data.allow_recharge_date_list[payScene] && data.allow_recharge_date_list[payScene].length) {
          let index = data.allow_recharge_date_list[payScene].indexOf(-1)
          if (index > -1) {
            if (data.allow_recharge_date_list[payScene].length > 1) {
              this.seniorFormData.rechargeDateType[payScene].push('month')
            }
            data.allow_recharge_date_list[payScene].splice(index, 1)
            this.seniorFormData.rechargeDateType[payScene].push('lastDay')
            this.seniorFormData.allowRechargeDateList[payScene] = data.allow_recharge_date_list[payScene]
          } else {
            this.seniorFormData.rechargeDateType[payScene].push('month')
            this.seniorFormData.allowRechargeDateList[payScene] = data.allow_recharge_date_list[payScene]
          }
        } else {
          this.seniorFormData.rechargeDateType[payScene] = []
        }
      }
    },
    // 动态添加金额
    addMoneyList() {
      let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      if (reg.test(this.seniorFormData.money)) {
        this.seniorFormData.rechargeAmountList.push(this.seniorFormData.money)
        this.seniorFormData.money = ''
        this.$nextTick(() => {
          this.$refs.seniorFormRef.clearValidate('money')
        })
      } else {
        this.$message.error('金额格式有误，请重新输入！')
      }
    },
    // 移出金额
    closeMoneyTag(data, index) {
      this.seniorFormData.rechargeAmountList.splice(index, 1)
    },
    changeRechargeDate(e) {

    },
    saveWalletHandle() {
      this.$refs.seniorFormRef.validate(valid => {
        if (valid) {
          if (this.isLoading) {
            return this.$message.error('请勿重复提交!')
          }
          this.setSeniorSettingHandle()
        }
      })
    },
    // 保存
    async setSeniorSettingHandle() {
      this.isLoading = true
      // this.$sleep(2000)
      let params = {
        id: this.organizationData.id,
        company: this.organizationData.company,
        allow_custom_amount: this.seniorFormData.allowCustomAmount,
        limit_today_recharge_amount: times(this.seniorFormData.limitTodayRechargeAmount),
        limit_today_consume_amount: times(this.seniorFormData.limitTodayConsumeAmount),
        allow_recharge_date_list: { charge: [], charge_offline: [] }
      }
      if (this.seniorFormData.rechargeAmountList.length > 0) {
        params.recharge_amount_list = this.seniorFormData.rechargeAmountList.map(amount => {
          return times(amount)
        })
      }
      if (this.seniorFormData.limitBalanceAmount) {
        params.limit_balance_amount = times(this.seniorFormData.limitBalanceAmount)
      }
      if (this.seniorFormData.openMinimumRechargeAmount) {
        params.minimum_recharge_amount = times(this.seniorFormData.minimumRechargeAmount)
      } else {
        params.minimum_recharge_amount = 0
      }
      // 可充值日期参数格式化
      for (let payScene in this.rechargePaySceneType) {
        if (this.seniorFormData.rechargeDateType[payScene].length > 0) {
          if (this.seniorFormData.rechargeDateType[payScene].includes('month')) {
            params.allow_recharge_date_list[payScene] = deepClone(this.seniorFormData.allowRechargeDateList[payScene])
          }
          if (this.seniorFormData.rechargeDateType[payScene].includes('lastDay')) {
            if (params.allow_recharge_date_list[payScene] && params.allow_recharge_date_list[payScene].length) {
              params.allow_recharge_date_list[payScene].push(-1)
            } else {
              params.allow_recharge_date_list[payScene] = [-1]
            }
          }
        } else {
          params.allow_recharge_date_list[payScene] = []
        }
      }

      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationModifySettingsPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getSettingInfo()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 可充值日期关闭
    closeDateHandle(tag, index, payScene) {
      this.seniorFormData.allowRechargeDateList[payScene].splice(index, 1)
    },
    // 显示可充值日期输入框
    showInput(payScene) {
      this.inputVisible[payScene] = true;
      this.$nextTick(_ => {
        this.$refs[payScene + 'saveTagInput'][0].$refs.input.focus();
      });
    },
    // 关闭可充值输入框
    handleInputConfirm(payScene) {
      let inputValue = this.seniorFormData.dateValue;
      let index = this.seniorFormData.allowRechargeDateList[payScene].indexOf(Number(inputValue))
      let reg = /^\+?[1-9][0-9]*$/
      // let reg = /^\\d+$/
      let pass = true
      if (inputValue === '0') {
        pass = false
      }
      if (!reg.test(inputValue) || Number(inputValue) > 28 || Number(inputValue) < 1) {
        pass = false
      }
      if (inputValue && pass && Number(inputValue)) {
        if (index < 0) {
          this.seniorFormData.allowRechargeDateList[payScene].push(Number(inputValue));
          this.sortList(this.seniorFormData.allowRechargeDateList[payScene])
        } else {
          this.$message.warning('请不要添加相同的日期')
        }
      }
      if (pass || inputValue === '') { // 如果输入格式错误不关闭输入框
        this.inputVisible[payScene] = false;
        this.seniorFormData.dateValue = '';
      }
    },
    // 排序
    sortList(list) {
      list = list.sort((a, b) => {
        return a - b
      })
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.deductsetting-wrapper {
  position: relative;
  margin-top: 10px;
  .money-tag{
    .el-tag{
      position: relative;
      color: #23282d;
      border-radius: 3px;
      border: solid 1px #dae1ea;
      background-color: transparent;
      &:not(:last-child) {
        margin-right: 10px;
      }
      .el-tag__close{
        position: absolute;
        right: -3px;
        top: -3px;
        height: 12px;
        width: 12px;
        line-height: 12px;
        font-size: 12px;
        color: #FFF;
        background-color: #dae1ea;
        &:hover {
          color: #FFF;
          background-color: #ff9b45;
      }
      }
    }
  }
  .inline{
    .el-form-item{
      display: inline-block;
      margin-right: 15px;
      .el-form-item__label{
        display: block;
        text-align: left;
        width: auto !important;
      }
      .el-form-item__content{
        margin-left: 0 !important;
      }
    }
  }
  .add-btn{
    padding: 0;
    .el-icon-circle-plus{
      margin-left: 10px;
      font-size: 35px;
      vertical-align: middle;
      color: #ff9b45;
      cursor: pointer;
    }
    &.is-disabled{
      .el-icon-circle-plus{
        color: #C0C4CC;
        cursor: not-allowed;
      }
    }
  }
  .add-wrapper{
    margin-top: 60px;
    text-align: center;
    .el-button{
      width: 120px;
    }
  }
  .input-new-tag{
    width: 70px;
  }
  .inline-label{
    display: inline-block;
  }
}
</style>

<template>
  <div class="paysetting-wrapper">
    <div class="m-b-10">
      <span class="p-r-10" style="font-size:14px">充值退款是否退手续费</span>
      <el-radio-group class="ps-radio" v-model="commissionsChargeType" @change="changeCommissionsChargeType">
        <el-radio :label="0">不退</el-radio>
        <el-radio :label="1">退款（部分退款不退手续费）</el-radio>
      </el-radio-group>
    </div>
    <!-- child paysetting start -->
    <div class="wrapper-title">注：优先使用【营销活动-手续费】中的设置，当用户无分组或所在分组无规则时，生效当前【充值设置】的手续费规则</div>
    <div class="paysetting-sub" v-loading="subIsLoading">
      <div class="sub-wrapper" v-for="(info, key) in collapseInfo" :key="key">
        <div class="l-title">
          <span>{{ info.name }}</span>
          <el-switch
            v-if="organizationData.level !== 0"
            style="margin-left: 15px;"
            v-model="info.isOpen"
            active-color="#ff9b45"
            @change="changeSceneHandle($event, info.key)"
          ></el-switch>
          <el-button
            v-permission="['background_payment.pay_info.org_bind']"
            v-if="showBindBtnHandle(info.key) && organizationData.level !== 0"
            type="primary"
            class="ps-origin-btn float-r save-m-r"
            size="small"
            @click="clickBindOrgsHandle(info.key)"
          >
            保存
          </el-button>
        </div>
        <el-collapse v-model="info.activePayCollapse" @change="changeCollapseHandle">
          <el-collapse-item
            v-for="payway in info.payways"
            :key="payway.key"
            :title="payway.name"
            :name="payway.key"
          >
            <template slot="title">
              <el-checkbox
                class="ps-checkbox"
                v-model="payway.isOpen"
                :disabled="!info.isOpen || organizationData.level === 0"
                @change="changePaywayHandle($event, payway.key, info)"
              >
                {{ payway.name }}
              </el-checkbox>
              <!-- <el-switch style="margin-left: 15px;" v-model="xxx" active-color="#ff9b45"></el-switch> -->
              <span class="tips-r">
                <span class="open">展开</span>
                <span class="close">收起</span>
              </span>
            </template>
            <el-table
              :ref="`subPayInfoListRef${info.key}-${payway.key}`"
              width="100%"
              :data="payway.sub_payways"
              tooltip-effect="dark"
              v-loading="isLoading"
            >
              <el-table-column class-name="ps-checkbox" width="50" align="center">
                <template slot-scope="scope">
                  <el-checkbox
                    class="ps-checkbox"
                    :disabled="!(info.isOpen && payway.isOpen)"
                    v-model="scope.row.binded"
                    @change="
                      changeSubPayHandle(
                        $event,
                        scope.row,
                        payway.sub_payways,
                        `${info.key}-${payway.key}`
                      )
                    "
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column
                label="商户名称"
                prop="merchant_name"
                align="center"
              ></el-table-column>
              <el-table-column label="商户号" prop="merchant_id" align="center"></el-table-column>
              <el-table-column
                label="充值渠道"
                prop="payway_alias"
                align="center"
              ></el-table-column>
              <el-table-column label="充值类型" prop="sub_payway_alias" align="center"></el-table-column>
              <el-table-column show-overflow-tooltip label="适用层级" prop="" align="center">
                <template slot-scope="scope">
                  <span>{{ showOrganizationsText(scope.row.organizations) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                label="备注"
                prop="remark"
                align="center"
              ></el-table-column>
              <el-table-column show-overflow-tooltip label="手续费" align="center" key="payway">
                <template slot-scope="scope">
                <el-button type="text" size="small" class="ps-text" :disabled="scope.row.payway === 'CashPay' || scope.row.payway === 'PushiPay'?true:false" v-if="!scope.row.service_fee_value" @click="serviceSetting(scope.row)">
                  设置
                </el-button>
                <span v-else-if="scope.row.service_fee_value" @click="serviceSetting(scope.row)" class="ps-origin" style="cursor: pointer;">
                  {{ servicePirceFormat(scope.row) }} <span>{{scope.row.service_fee_type === 1?'元':'%'}}</span>
                </span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <!-- child paysetting end -->
    <el-dialog
      title="手续费设置"
      :visible.sync="serviceSettingDialog"
      width="400px"
      custom-class="ps-dialog"
    >
      <el-form
        ref="serviceSettingForm"
        :rules="serviceSettingDialogRuls"
        :model="serviceSettingDialogFormData"
      >
        <div class="ps-flex">
          <el-form-item class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="1"
            >
              定额
            </el-radio>
          </el-form-item>
          <el-form-item prop="quota" v-if="serviceSettingDialogFormData.service_fee_type !== 2">
            <div class="ps-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.quota"
              ></el-input>
              <span>元</span>
            </div>
            <span>实收金额=订单金额+定额</span>
          </el-form-item>
        </div>
        <div class="ps-flex">
          <el-form-item label="" class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="2"
            >
              百分比
            </el-radio>
          </el-form-item>
          <el-form-item prop="discount" v-if="serviceSettingDialogFormData.service_fee_type !== 1">
            <div class="ps-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.discount"
              ></el-input>
              <span>%</span>
            </div>
            <span>实收金额=订单金额+（订单金额*折扣）</span>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="serviceSettingDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="determineServiceSettingDialog">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { to, debounce, deepClone } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { validataPrice, validateStock } from '@/assets/js/validata'
import NP from 'number-precision'
export default {
  name: 'RechargeSetting',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: {
      // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  data() {
    return {
      isLoading: false,
      commissionsChargeType: 0,
      formOperate: 'detail',
      subPaywayList: [],
      organizationList: [], // 适用组织列表
      selectTableCoumn: [],
      activePayCollapse: [], // 手风琴选项
      subIsLoading: false,
      subPayInfoList: [], // 手风琴源数据
      collapseInfo: {}, // 处理下手风琴需要的参数
      selectSubInfo: {}, // 选中的数据
      // 手续费
      serviceSettingDialog: false,
      serviceSettingDialogFormData: {
        service_fee_type: 1,
        quota: '',
        discount: ''
      },
      serviceSettingDialogRuls: {
        quota: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { validator: validataPrice, trigger: 'blur' }
        ],
        discount: [
          { required: true, message: '请输入折扣', trigger: 'blur' },
          { validator: validateStock, trigger: 'blur' }
        ]
      },
      serviceSettingData: {}
    }
  },
  computed: {
    checkIsFormStatus: function() {
      let show = false
      switch (this.formOperate) {
        case 'detail':
          show = false
          break
        case 'add':
          show = true
          break
      }
      return show
    }
  },
  watch: {
    organizationData(val) {
      setTimeout(() => {
        // 给个延时，防止意外
        this.searchHandle()
      }, 50)
    }
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getSubOrgsAllList()
      this.setChargeSetting({ organization_id: this.infoData.id })
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    // 过滤tree数据
    filterTreeNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 设置配置字段的前缀，仅对二级使用
    setTemplatePrefix(list) {
      let result = deepClone(list)
      result.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            child.parent = item.key
            child.key = item.key + '-' + child.key
          })
        }
      })
      return result
    },
    // join organizations
    showOrganizationsText(list) {
      let text = ''
      list.forEach(item => {
        if (!text) {
          text = item.name
        } else {
          text += `，${item.name}`
        }
      })
      return text
    },
    paySettingNormalizer(node) {
      if (!node) {
        return
      }
      return {
        id: node.key,
        label: node.name,
        children: node.children
      }
    },
    // 适用组织key，配置
    organizationNormalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children_list
      }
    },
    // 通过固定字段查表数据，并返回
    findKeyTreeList(list, key, id) {
      let result = []
      list.forEach(item => {
        if (item[key] === id) {
          result = [item]
        } else {
          if (item.children_list && item.children_list.length > 0) {
            result = this.findKeyTreeList(item.children_list, key, id)
          }
        }
      })
      return result
    },
    // 删除空的子级
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          // if (_that.checkIsFormStatus) {
          //   item.isDisabled = false
          // } else {
          //   item.isDisabled = true
          // }
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 非root 操作
    changeCollapseHandle(e) {},
    // 获取支付配置列表
    async getSubOrgsAllList() {
      this.subIsLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(
        this.$apis.apiBackgroundPaymentPayInfoSubOrgsAllListPost({
          organizations: [this.organizationData.id],
          pay_scenes: ['charge', 'charge_offline'], // zrj+ charge_offline
          company: this.organizationData.company
        })
      )
      this.subIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.collapseInfo = {}
        this.selectSubInfo = {}
        this.subPayInfoList = res.data.sort((a, b) => {
          return b.key.charCodeAt(0) - a.key.charCodeAt(0);
        })
        let openList = []
        deepClone(res.data).map(item => {
          let sceneOpen = false
          let activePayCollapse = []
          item.payways = item.payways.map(payway => {
            let paywayOpen = false
            payway.sub_payways.forEach(sub => {
              if (sub.binded) {
                sceneOpen = true
                paywayOpen = true
                this.$set(this.selectSubInfo, `${item.key}-${payway.key}`, sub.id)
                if (!activePayCollapse.includes(payway.key)) {
                  activePayCollapse.push(payway.key)
                }
                openList.push({
                  type: item.key + '-' + payway.key,
                  list: sub
                })
              }
            })
            payway.isOpen = paywayOpen
            return payway
          })
          this.$set(this.collapseInfo, item.key, {
            ...item,
            activePayCollapse: activePayCollapse,
            isOpen: sceneOpen
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // select tabel
    setDefaultTableSelect(rows) {
      rows.forEach(item => {
        let rowRefs = this.$refs[`subPayInfoListRef${item.type}`][0]
        rowRefs.toggleRowSelection(item.list, true)
      })
    },
    // switch 开关
    changeSceneHandle(e, type) {
      // for (let scene in this.collapseInfo) {
      //   if (scene === type && !e) {
      //     this.collapseInfo[scene].payways.forEach(payway => {
      //       payway.isOpen = false
      //       this.changePaywayHandle(false, payway.key, scene)
      //     })
      //   }
      // }
    },
    // 设置table 是否可选
    selectableHandle(row, index) {
      let canSelect = true
      if (!this.collapseInfo[row.pay_scene].isOpen) {
        canSelect = false
      }
      if (this.collapseInfo[row.pay_scene].isOpen) {
        this.collapseInfo[row.pay_scene].payways.forEach(item => {
          if (!item.isOpen && row.payway === item.key) {
            canSelect = false
          }
        })
      }
      if (this.organizationData.level === 0) {
        canSelect = false
      }
      return canSelect
    },
    // change
    changePaywayHandle(e, curent, parent) {
      if (e && !parent.activePayCollapse.includes(curent)) {
        parent.activePayCollapse.push(curent)
      }
    },
    // show bind button
    showBindBtnHandle(type) {
      let isShow = false
      for (let k in this.selectSubInfo) {
        if (k.indexOf(type) > -1) {
          isShow = true
        }
        if (isShow) {
          break
        }
      }
      return isShow
    },
    clickBindOrgsHandle(type) {
      let params = []
      this.collapseInfo[type].payways.forEach(payway => {
        if (this.collapseInfo[type].isOpen && payway.isOpen) {
          let selectSubInfoId = this.selectSubInfo[type + '-' + payway.key]
          payway.sub_payways.forEach(subPayways => {
            if (selectSubInfoId === subPayways.id) {
              params.push({
                id: subPayways.id,
                service_fee_type: subPayways.service_fee_type,
                service_fee_value: subPayways.service_fee_value
              })
            }
          })
        }
      })
      console.log(params)
      this.setSubOrgsBind(type, params)
    },
    // 支付设置-设置子组织绑定关联支付配置
    async setSubOrgsBind(type, ids) {
      this.subIsLoading = true
      // await this.$sleep(2000)
      let params = {
        pay_scene: type,
        organizations: [this.organizationData.id],
        payinfo: ids,
        company: this.organizationData.company
      }
      const [err, res] = await to(this.$apis.apiBackgroundPaymentPayInfoSubOrgsBindPost(params))
      this.subIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 1
        this.$message.success(res.msg)
        this.getSubOrgsAllList()
      } else {
        this.$message.error(res.msg)
      }
    },
    changeSubPayHandle(e, row, data, key) {
      data.forEach(item => {
        if (item.id !== row.id) {
          item.binded = false
        } else {
          if (e) {
            this.$set(this.selectSubInfo, key, row.id)
          } else {
            this.$set(this.selectSubInfo, key, '')
          }
        }
      })
    },
    // 手续费
    serviceSetting(data) {
      this.serviceSettingData = data
      this.serviceSettingDialogFormData.service_fee_type = data.service_fee_type
      if (data.service_fee_type === 1) {
        this.serviceSettingDialogFormData.discount = ''
        this.serviceSettingDialogFormData.quota = String(NP.divide(data.service_fee_value, 100))
      }
      // NP.times(Number(this.serviceSettingDialogFormData.quota), 100)
      // String(NP.divide(data.service_fee_value, 100))
      if (data.service_fee_type === 2) {
        this.serviceSettingDialogFormData.discount = data.service_fee_value
        this.serviceSettingDialogFormData.quota = ''
      }
      this.serviceSettingDialog = true
    },
    determineServiceSettingDialog() {
      this.$refs.serviceSettingForm.validate(valid => {
        if (valid) {
          if (this.organizationData.level === 0) {
            let params = {
              payinfo_id: this.serviceSettingData.id,
              organization_id: this.infoData.id, // 组织id
              service_fee_type: this.serviceSettingDialogFormData.service_fee_type,
              service_fee_value: this.serviceSettingDialogFormData.service_fee_type === 1 ? NP.times(Number(this.serviceSettingDialogFormData.quota), 100) : this.serviceSettingDialogFormData.discount
            }
            this.setCommissionChargeValue(params)
          } else {
            // 塞到列表里面
            this.serviceSettingData.service_fee_type = this.serviceSettingDialogFormData.service_fee_type
            this.serviceSettingData.service_fee_value =
                  this.serviceSettingDialogFormData.service_fee_type === 1
                    ? NP.times(Number(this.serviceSettingDialogFormData.quota), 100)
                    : this.serviceSettingDialogFormData.discount
            this.serviceSettingDialog = false
          }
        } else {
          console.log(valid)
        }
      })
    },
    // 单独设置一级组织手续费
    async setCommissionChargeValue(params) {
      // this.subIsLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminPayInfoSetCommissionChargeValuePost(params)
      )
      // this.subIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.serviceSettingDialog = false
        this.getSubOrgsAllList()
      } else {
        this.$message.error(res.msg)
      }
    },
    changeCommissionsChargeType() {
      let params = {
        type: 1,
        organization_id: this.infoData.id, // 组织id
        commissions_charge_refund: this.commissionsChargeType
      }
      this.setChargeSetting(params)
    },
    // 单独设置一级组织手续费
    async setChargeSetting(params) {
      const [err, res] = await to(
        this.$apis.apiBackgroundPaymentPayInfoSetCommissionChargeSettingPost(params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 如果有传这个进来就弹成功，不传就是获取参数赋值
        if (params.commissions_charge_refund === 0 || params.commissions_charge_refund === 1) {
          this.$message.success('配置成功')
        }
        this.commissionsChargeType = res.data.commissions_charge_refund
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化金额
    servicePirceFormat(row) {
      return row.service_fee_type === 1 ? NP.divide(row.service_fee_value, 100) : row.service_fee_value
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.paysetting-wrapper {
  position: relative;
  margin-top: 10px;
  .wrapper-title{
    text-align: right;
    padding-bottom:20px;
    color: #fd9445;
  }
  .save-m-r {
    margin-right: 10px;
  }
  .tree-wrapper {
    width: auto;
    max-width: 250px;
    background-color: $organizationTreeBg;
    .tree-search {
      margin-bottom: 10px;
    }
    .all-tree {
      display: flex;
      align-items: center;
      line-height: 30px;
      font-size: 14px;
      cursor: pointer;
      padding-left: 10px;
      &.is-current {
        position: relative;
        color: #fda04d;
        background-color: #fbeee6;
        font-weight: 600;
        // &::before{
        //   content: '';
        //   position: absolute;
        //   width: 20px;
        //   left: -20px;
        //   top: 0;
        //   bottom: 0;
        //   background-color: #fbeee6;
        // }
        // &::after{
        //   content: '';
        //   position: absolute;
        //   width: 20px;
        //   right: -20px;
        //   top: 0;
        //   bottom: 0;
        //   background-color: #fbeee6;
        // }
      }
      .tree-search-icon {
        position: relative;
        display: inline-block;
        width: 18px;
        height: 40px;
        margin-right: 5px;
        img {
          display: inline-block;
          width: 18px;
          height: 18px;
          vertical-align: middle;
        }
      }
    }
    .el-tree {
      background-color: $organizationTreeBg;
      .el-tree-node__label {
        font-size: 14px;
        color: #23282d;
      }
      .el-tree-node__content {
        height: 30px;
      }
    }
    .tree-box {
      .is-current > .el-tree-node__content {
        position: relative;
        // background-color: #fbeee6;
        background-color: #fbeee6 !important;
      }
    }
  }
  .paysetting-container {
    display: flex;
    .paysetting-r {
      flex: 1;
      min-width: 0;
      margin-left: 10px;
    }
  }
  .paysetting-sub {
    $collapsebg: #f8f9fa;
    .sub-wrapper {
      margin-bottom: 15px;
      border-radius: 4px;
      border: solid 1px #e0e6eb;
      background-color: $collapsebg;
      .el-collapse {
        padding: 0 20px;
        background-color: $collapsebg;
        .el-collapse-item__header {
          background-color: $collapsebg;
        }
      }
    }
  }
  .tips-r {
    position: absolute;
    right: 45px;
    .close {
      display: none;
    }
  }
  .is-active .tips-r {
    .open {
      display: none;
    }
    .close {
      display: inline-block;
    }
  }
}
.ps-paysetting-dialog {
  .el-dialog__body {
    max-height: calc(100vh - 40vh);
    overflow-y: auto;
  }
  .paysetting-dialog {
    .el-form-item {
      // width: 45%;
      // &:nth-child(even) {
      //   margin-right: 5%;
      // }
      // &:first-child {
      //   margin-right: 5%;
      // }
    }
    .el-form-item__content {
      // max-width: 200px;
      line-height: 32px;
    }
    .tree-item .el-form-item__content {
      // max-width: 96%;
    }
    .remark-item {
      // width: 100%;
      // .el-form-item__content{
      //   width: 100%;
      //   max-width: 92%;
      // }
    }
    .vue-treeselect__control {
      height: 32px;
    }
    .el-form-item__label {
      // display: block;
      // text-align: left;
    }
    .el-select {
      width: 100%;
    }
  }
}
</style>

<template>
  <div class="agreement-box">
    <div class="title-box t-a-c">
      <div class="title">{{ current.agreement_type_alias }}</div>
      <div class="update-time">[更新时间：{{ parseTime(current.update_time?current.update_time:current.create_time, '{y}年{m}月{d}日') }}]</div>
    </div>
    <div class="content" v-html="content"></div>
  </div>
</template>

<script>
// import agreementList from "./constants"
import { unescapeHTML, parseTime } from '@/utils'

export default {
  name: 'Agreement',
  data() {
    return {
      type: '',
      id: '',
      current: {},
      content: '',
      agreementList: []
    }
  },
  created () {
    this.initLoad(this.$route.query)
  },
  methods: {
    initLoad(option) {
      // if (option.type) {
      //   this.type = option.type
      //   this.content = agreementList[option.type]
      //   // agreement_list.forEach(v => {
      //   //   if (v.value === option.type) {
      //   //     this.title = v.name
      //   //   }
      //   // })
      // }
      if (option.type) {
        // this.id = option.id
        // let list = getSessionStorage(option.key)
        // if (list) {
        //   this.agreementList = JSON.parse(list)
        //   // eslint-disable-next-line eqeqeq
        //   this.current = this.agreementList.find(v => v.id == this.id)
        //   if (this.current.content) {
        //     this.content = unescapeHTML(this.current.content)
        //   }
        // }
        this.getAgreementDetails(option.type)
      }
    },
    // 编辑获取详情
    async getAgreementDetails(type) {
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundAdminAgreementAgreementDetailByTypePost({
          agreement_type: type
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.current = res.data.results
        if (res.data.results.content) {
          this.content = unescapeHTML(res.data.results.content)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    parseTime
  }
}
</script>

<style lang="scss" scope>
.agreement-box{
  background-color: white;
  .title{
    padding: 40px 0 10px;
    font-size: 20px;
    letter-spacing: 5px;
    font-weight: 600;
  }
  .content{
    padding: 20px 40px;
    user-select: none;
  }
}
</style>

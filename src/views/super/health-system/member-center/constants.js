import * as dayjs from 'dayjs'

export const getRequestParams = (searchFormSetting, page, pageSize) => {
  const searchData = {}
  Object.keys(searchFormSetting).forEach(key => {
    if (
      key !== 'select_time' &&
      searchFormSetting[key].value !== '' &&
      searchFormSetting[key].value &&
      searchFormSetting[key].value.length !== 0
    ) {
      searchData[key] = searchFormSetting[key].value
    } else if (typeof searchFormSetting[key].value === 'boolean') {
      searchData[key] = searchFormSetting[key].value
    }
  })
  const params = {
    page,
    page_size: pageSize,
    ...searchData
  }

  if (searchFormSetting.select_time?.value?.length === 2) {
    params.start_date = searchFormSetting.select_time.value[0]
    params.end_date = searchFormSetting.select_time.value[1]
  }

  return params
}

export const RECENTSEVEN = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

<template>
  <div class="SuperMemberLabel container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="gotoAddOrEdit('add')">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="name" label="标签名称" align="center" width="180"></el-table-column>
          <el-table-column prop="type" label="标签类型" align="center">
            <template slot-scope="scope">
              <span>{{scope.row.type === 'auto' ? '自动' : '手动'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="标签条件" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                v-if="scope.row.type === 'auto'"
                @click="openDialog(scope.row)"
                >查看</el-button>
                <div v-else>无</div>
            </template>
          </el-table-column>
          <el-table-column prop="update_time" label="操作时间" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="gotoAddOrEdit('edit', scope.row)"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="delMemberLabel(scope.row.id)"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <el-dialog
      title="标签条件"
      :visible.sync="dialogVisible"
      width="400px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false">
      <div>
        <div class="dialog-item" v-if="selectInfo.continuous_member_count !== -1">续费会员次数：{{selectInfo.continuous_member_count}}次</div>
        <div class="dialog-item" v-if="selectInfo.grand_total_sign_days !== -1">累计签到天数：{{selectInfo.grand_total_sign_days}}天</div>
        <div class="dialog-item" v-if="selectInfo.continuous_sign_days !== -1">连续签到天数：{{selectInfo.continuous_sign_days}}天</div>
        <div class="dialog-item" v-if="selectInfo.member_grade_name">会员等级：{{selectInfo.member_grade_name}}</div>
        <div class="dialog-item" v-if="selectInfo.integrals !== -1">积分达到：{{selectInfo.integrals}}分</div>
        <div class="dialog-item" v-if="selectInfo.not_login_days !== -1">连续未登录天数：{{selectInfo.not_login_days}}天</div>
        <div class="dialog-item" v-if="selectInfo.not_sign_days !== -1">连续未签到天数：{{selectInfo.not_sign_days}}天</div>
        <div class="dialog-item" v-if="selectInfo.not_login">从未登录过</div>
        <div class="dialog-item" v-if="selectInfo.not_sign">从未签到过</div>
        <div class="dialog-item" v-if="selectInfo.not_buy">从未购买/续费过会员</div>
        <div v-if="selectInfo.labels.length" class="dialog-item">触发指定用户标签：
          <el-tag v-for="(item, index) in selectInfo.labels_name" :key="index" style="margin-right: 8px">
            {{ item }}
          </el-tag>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-btn" type="primary" @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { getRequestParams } from './constants'

export default {
  name: 'SuperMemberLabel',
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '标签名称',
          value: '',
          placeholder: '请输入标签名称'
        },
        type: {
          type: 'select',
          value: [],
          label: '标签类型',
          dataList: [{
            value: 'auto',
            label: '自动'
          }, {
            value: 'manual',
            label: '手动'
          }],
          clearable: true
        }
      },
      dialogVisible: false,
      selectInfo: {
        labels: []
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMemberLabel()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.getMemberLabel()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getMemberLabel() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMemberLabel()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getMemberLabel()
    },
    async delMemberLabel(id) {
      this.$confirm(`确定删除会员标签？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundMemberMemberLabelDeletePost({
              ids: [id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getMemberLabel()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    openDialog(info) {
      this.dialogVisible = true
      this.selectInfo = info
    },
    gotoAddOrEdit(type, data) {
      let query = {}
      if (type === 'edit') {
        query = { data: encodeURIComponent(JSON.stringify(data)) }
      }
      this.$router.push({
        name: 'SuperAddOrEditMemberLabel',
        params: {
          type
        },
        query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
  .dialog-item{
    margin-bottom: 15px;
    margin-left: 15px;
  }
</style>

<template>
  <div class="SuperMemberDetail container-wrapper">
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">个人信息</div>
      </div>
      <div class="user-info-wrapper">
        <div class="user-img">
          <!-- <el-image :src="userDetail.img_url" lazy></el-image> -->
          <el-avatar :size="120" fit="cover" :src="userInfo.headimgurl"></el-avatar>
        </div>
        <div class="user-info-r">
          <!-- 左右布局吧，上下宽度没保障 -->
          <div class="float-l clearfixt">
            <div class="info-item">
              <div class="label">姓名：</div>
              <div class="value">{{userInfo.nickname}}</div>
            </div>
            <div class="info-item">
              <div class="label">性别：</div>
              <div class="value">{{userInfo.gender_alias}}</div>
            </div>
            <div class="info-item">
              <div class="label">用户ID：</div>
              <div class="value">{{userInfo.user_id}}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">来源渠道：</div>
              <div class="value">{{userInfo.source_alias}}</div>
            </div>
            <div class="info-item">
              <div class="label">openid：</div>
              <div class="value">{{userInfo.openid}}</div>
            </div>
            <div class="info-item">
              <div class="label">注册时间：</div>
              <div class="value">{{userInfo.create_time}}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">微信号：</div>
              <div class="value">{{userInfo.nickname}}</div>
            </div>
            <div class="info-item">
              <div class="label">手机号码：</div>
              <div class="value">{{userInfo.phone}}</div>
            </div>
            <div class="info-item">
              <span style="margin-right: 20px;">
                <span class="label">关联用户：</span>
                <span class="value">{{userInfo.nickname}}</span>
              </span>
              <span>
                <span class="label">关联企业:</span>
                <span class="value">{{userInfo.company.length}}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">会员信息</div>
      </div>
      <div class="member-info-wrapper">
        <div class="info-item">
          <div class="title">会员标签：</div>
          <div class="info-item-label">
            <div class="info-item-label-item" v-for="item in userInfo.member_labels_list" :key="item.id">{{item.name}}</div>
          </div>
        </div>
        <div class="info-item">
          <div class="title">会员信息：</div>
          <ul class="member-info">
            <li class="member-info-title">会员等级</li>
            <li>{{userInfo.member_grade_name}}</li>
          </ul>
          <ul class="member-info">
            <li class="member-info-title">会员有效期</li>
            <li>{{userInfo.end_time}}</li>
          </ul>
          <ul class="member-info">
            <li class="member-info-title">积分</li>
            <li>{{userInfo.integral}}</li>
          </ul>
        </div>
      </div>
    </div>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">个人信息</div>
      </div>
      <div class="table-content">
        <el-radio-group class="ps-radio-btn m-b-20" v-model="tableType" size="mini" @change="changTableType">
          <el-radio-button label="receive">会员领取记录</el-radio-button>
          <el-radio-button label="growth">会员等级成长记录</el-radio-button>
        </el-radio-group>
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <template v-if="tableType ==='receive'">
            <el-table-column prop="trade_no" label="订单号" align="center" width="180"></el-table-column>
            <el-table-column prop="receive_time" label="领取时间" align="center"></el-table-column>
            <el-table-column prop="days" label="领取天数" align="center"></el-table-column>
            <el-table-column prop="end_time" label="到期时间" align="center"></el-table-column>
            <el-table-column prop="receive_type_alias" label="领取方式" align="center"></el-table-column>
          </template>
          <template v-if="tableType ==='growth'">
            <el-table-column prop="create_time" label="获取时间" align="center"></el-table-column>
            <el-table-column prop="add_growth_value" label="分值" align="center"></el-table-column>
            <el-table-column prop="obtain_type_alias" label="获取方式" align="center"></el-table-column>
          </template>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { debounce } from '@/utils'

export default {
  name: 'SuperMemberDetail',
  props: {},
  data() {
    return {
      userInfo: {},
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableType: 'receive'
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.userInfo = JSON.parse(this.$route.query.data)
      if (this.tableType === 'receive') {
        this.getMemberReceive()
      } else {
        this.getMemberGradeGrowth()
      }
    },
    changTableType() {
      this.currentPage = 1
      if (this.tableType === 'receive') {
        this.getMemberReceive()
      } else {
        this.getMemberGradeGrowth()
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      if (this.tableType === 'receive') {
        this.getMemberReceive()
      } else {
        this.getMemberGradeGrowth()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getMemberReceive() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMemberMemberReceiveListPost({
        user_id: this.userInfo.id,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    async getMemberGradeGrowth() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMemberMemberGradeGrowthListPost({
        user_id: this.userInfo.id,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      if (this.tableType === 'receive') {
        this.getMemberReceive()
      } else {
        this.getMemberGradeGrowth()
      }
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      if (this.tableType === 'receive') {
        this.getMemberReceive()
      } else {
        this.getMemberGradeGrowth()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.SuperMemberDetail{
  .user-info-wrapper {
    display: flex;
    align-items: center;
    padding: 20px;
    .user-img {
      width: 120px;
      margin-right: 20px;
    }
    .user-info-r {
      .float-l {
        float: left;
      }
      .info-item {
        display: flex;
        margin-right: 40px;
        padding: 5px 0;
        height: 30px;
        line-height: 31px;
      }
    }
  }
  .member-info-wrapper{
    padding: 0 20px 20px;
    .info-item{
      display: flex;
      margin-top: 20px;
    }
    .title{
      line-height: 30px;
      margin-right: 20px;
      width: 90px;
    }
    .info-item-label{
      display: flex;
      flex-wrap: wrap;
      width: 95%;
      .info-item-label-item{
        border-radius: 5px;
        padding: 5px 15px;
        background-color: #e4e4e4;
        margin-right: 30px;
        margin-bottom: 15px;
      }
    }
    .member-info{
      width: 200px;
      text-align: center;
      margin: 3px 15px 0;
      .member-info-title{
        color: #ff9b45;
        margin-bottom: 10px;
      }
    }
  }
}
</style>

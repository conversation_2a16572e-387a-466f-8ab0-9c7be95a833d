<template>
  <div class="AddOrEditMemberLabel container-wrapper">
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">{{type==='add'?'新建':'编辑'}}会员标签</div>
      </div>
      <div>
        <el-form
          :model="memberForm"
          :rules="memberFormRules"
          ref="memberFormRef"
          label-width="120px"
        >
          <el-form-item label="标签名称：" prop="name">
            <el-input v-model="memberForm.name" maxlength="15" class="ps-input w-180"></el-input>
          </el-form-item>
          <el-form-item label="标签类型：">
            <el-radio-group v-model="memberForm.labelType">
              <el-radio class="ps-radio" label="auto">自动</el-radio>
              <el-radio class="ps-radio" label="manual">手动</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
          <el-radio-group class="ps-radio-btn" v-model="memberForm.labelDirection">
            <el-radio-button class="ps-radio" label="positive">正向标签</el-radio-button>
            <el-radio-button class="ps-radio" label="negative">负面标签</el-radio-button>
          </el-radio-group>
          </el-form-item>
          <div v-if="memberForm.labelType === 'auto'">
            <div v-if="memberForm.labelDirection === 'positive'">
              <el-form-item :prop="memberForm.isTotalCount?'totalCount':''" :key="memberForm.isTotalCount + 'totalCount'">
                <el-checkbox v-model="memberForm.isTotalCount" class="ps-checkbox">累计签到天数</el-checkbox>
                <el-input v-model="memberForm.totalCount" class="ps-input w-180 m-l-5"></el-input>
              </el-form-item>
              <el-form-item :prop="memberForm.isContinueCount?'continueCount':''" :key="memberForm.isContinueCount + 'continueCount'">
                <el-checkbox v-model="memberForm.isContinueCount" class="ps-checkbox">连续签到天数</el-checkbox>
                <el-input v-model="memberForm.continueCount" class="ps-input w-180 m-l-5"></el-input>
              </el-form-item>
              <el-form-item :prop="memberForm.isLevel?'level':''" :key="memberForm.isLevel + 'level'">
                <el-checkbox v-model="memberForm.isLevel" class="ps-checkbox">会员等级</el-checkbox>
                <el-select
                  v-model="memberForm.level"
                  placeholder="请下拉选择"
                  class="ps-select w-180 m-l-5"
                  popper-class="ps-popper-select"
                >
                  <el-option
                    v-for="item in levelList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :prop="memberForm.isIntegral?'integral':''" :key="memberForm.isIntegral + 'integral'">
                <el-checkbox v-model="memberForm.isIntegral" class="ps-checkbox">积分达到</el-checkbox>
                <el-input v-model="memberForm.integral" class="ps-input w-180 m-l-5"></el-input>
              </el-form-item>
            </div>
            <div v-if="memberForm.labelDirection === 'negative'">
              <el-form-item :prop="memberForm.isNotLoginCount?'notLoginCount':''" :key="memberForm.isNotLoginCount + 'notLoginCount'">
                <el-checkbox v-model="memberForm.isNotLoginCount" class="ps-checkbox">连续未登录天数</el-checkbox>
                <el-input v-model="memberForm.notLoginCount" class="ps-input w-180 m-l-5"></el-input>
                （从未登陆过的用户不在此条件范围内）
              </el-form-item>
              <el-form-item :prop="memberForm.isNotSignCount?'notSignCount':''" :key="memberForm.isNotSignCount + 'notSignCount'">
                <el-checkbox v-model="memberForm.isNotSignCount" class="ps-checkbox">连续未签到天数</el-checkbox>
                <el-input v-model="memberForm.notSignCount" class="ps-input w-180 m-l-5"></el-input>
                （从未签到过的用户不在此条件范围内）
              </el-form-item>
              <!-- <el-form-item :prop="memberForm.isBuyMemberDaysUnder?'buyMemberDaysUnder':''" :key="memberForm.isBuyMemberDaysUnder + 'buyMemberDaysUnder'">
                <el-checkbox v-model="memberForm.isBuyMemberDaysUnder" class="ps-checkbox">购买会员天数不足</el-checkbox>
                <el-input v-model="memberForm.buyMemberDaysUnder" class="ps-input w-180 m-l-5"></el-input>
              </el-form-item> -->
              <el-form-item>
                <el-checkbox v-model="memberForm.isNeverLogin" class="ps-checkbox">从未登陆过</el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="memberForm.isNeverSign" class="ps-checkbox">从未签到过</el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="memberForm.isNeverBuy" class="ps-checkbox">从未购买过会员</el-checkbox>
              </el-form-item>
            </div>
            <el-form-item :prop="memberForm.isLabel?'label':''" :key="memberForm.isLabel + 'label'">
              <el-checkbox v-model="memberForm.isLabel" class="ps-checkbox">触发指定用户标签</el-checkbox>
              <el-select
                v-model="memberForm.label"
                :multiple="true"
                placeholder="请下拉选择"
                class="ps-select w-180 m-l-5"
                popper-class="ps-popper-select"
              >
                <el-option
                  v-for="item in userLabelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <div class="m-l-20 label-list">
                <div class="label-list-item" v-for="(item, index) in labelNameList" :key="item">
                  <span class="m-r-5">标签{{item}}</span>
                  <i @click="delLabel(index)" class="el-icon-close del-icon"></i>
                </div>
              </div>
            </el-form-item>
            <!-- <div v-if="memberForm.labelDirection === 'positive'">
              <el-form-item :prop="memberForm.isBuyMemberDays?'buyMemberDays':''" :key="memberForm.isBuyMemberDays + 'buyMemberDays'">
                <el-checkbox v-model="memberForm.isBuyMemberDays" class="ps-checkbox">购买会员天数</el-checkbox>
                <el-input v-model="memberForm.buyMemberDays" class="ps-input w-180 m-l-5"></el-input>
              </el-form-item>
            </div> -->
          </div>
          <el-form-item>
            <el-button size="small" type="primary" class="ps-origin-btn w-150" @click="saveSetting">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'AddOrEditMemberLabel',
  components: {},
  props: {},
  data() {
    let validataNumber = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /^\d+$/
        if (!number.test(value)) {
          callback(new Error('请输入正整数'))
        } else {
          callback()
        }
      }
    };
    return {
      isLoading: false, // 刷新数据
      type: '',
      settingData: {},
      memberForm: {
        name: '',
        labelType: 'auto',
        labelDirection: 'positive',
        isTotalCount: false, // 累计签到天数
        totalCount: '',
        isContinueCount: false, // 连续签到天数
        continueCount: '',
        isLevel: false, // 会员等级
        level: '',
        isIntegral: false, // 积分达到
        integral: '',
        isLabel: false, // 触发指定用户标签
        label: [],
        // isBuyMemberDays: false, // 购买会员天数不足
        // buyMemberDays: '',
        isNotLoginCount: false, // 连续未登录天数
        notLoginCount: '',
        isNotSignCount: false, // 连续未签到天数
        notSignCount: '',
        // isBuyMemberDaysUnder: false, // 购买会员天数不足
        // buyMemberDaysUnder: '',
        isNeverLogin: false, // 从未登陆过
        isNeverSign: false, // 从未签到过
        isNeverBuy: false // 从未购买过会员
      },
      memberFormRules: {
        name: [{ required: true, message: '请输入会员名称', trigger: 'blur' }],
        totalCount: [{ validator: validataNumber, trigger: 'blur' }],
        continueCount: [{ validator: validataNumber, trigger: 'blur' }],
        level: [{ required: true, message: '请选择会员等级', trigger: 'change' }],
        integral: [{ validator: validataNumber, trigger: 'blur' }]
      },
      levelList: [],
      userLabelList: [],
      labelNameList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.$route.query.data) {
        this.settingData = JSON.parse(decodeURIComponent(this.$route.query.data))
      }
      if (this.$route.params.type) {
        this.type = this.$route.params.type
        if (this.type === 'edit') {
          this.initData()
        }
      }
      this.getMemberLevel()
      this.getUserLabelList()
    },
    initData() {
      this.memberForm.name = this.settingData.name
      this.memberForm.labelType = this.settingData.type
      this.memberForm.labelDirection = this.settingData.direction
      this.memberForm.isTotalCount = !(this.settingData.grand_total_sign_days === -1) // 累计签到天数
      this.memberForm.totalCount = this.memberForm.isTotalCount ? this.settingData.grand_total_sign_days : ''
      this.memberForm.isContinueCount = !(this.settingData.continuous_sign_days === -1) // 连续签到天数
      this.memberForm.continueCount = this.memberForm.isContinueCount ? this.settingData.continuous_sign_days : ''
      this.memberForm.isLevel = !(this.settingData.member_grade === null) // 会员等级
      this.memberForm.level = this.memberForm.isLevel ? this.settingData.member_grade : ''
      this.memberForm.isIntegral = !(this.settingData.integrals === -1) // 积分达到
      this.memberForm.integral = this.memberForm.isIntegral ? this.settingData.integrals : ''
      this.memberForm.isLabel = !!(this.settingData.labels.length) // 触发指定用户标签
      this.memberForm.label = this.memberForm.isLabel ? this.settingData.labels : []
      this.memberForm.isNotLoginCount = !(this.settingData.not_login_days === -1) // 连续未登录天数
      this.memberForm.notLoginCount = this.memberForm.isNotLoginCount ? this.settingData.not_login_days : ''
      this.memberForm.isNotSignCount = !(this.settingData.not_sign_days === -1) // 连续未签到天数
      this.memberForm.notSignCount = this.memberForm.isNotSignCount ? this.settingData.not_sign_days : ''
      this.memberForm.isNeverLogin = this.settingData.not_login // 从未登陆过
      this.memberForm.isNeverSign = this.settingData.not_sign // 从未签到过
      this.memberForm.isNeverBuy = this.settingData.not_buy // 从未购买过会员
    },
    saveSetting() {
      if ((this.memberForm.labelType === 'auto' && this.memberForm.labelDirection === 'positive' && !this.memberForm.isTotalCount &&
       !this.memberForm.isContinueCount && !this.memberForm.isLevel &&
       !this.memberForm.isIntegral && !this.memberForm.isLabel) ||
       (this.memberForm.labelType === 'auto' && this.memberForm.labelDirection === 'negative' && !this.memberForm.isNotLoginCount &&
       !this.memberForm.isNotSignCount && !this.memberForm.isNeverLogin &&
       !this.memberForm.isNeverSign && !this.memberForm.isNeverBuy && !this.memberForm.isLevel)) {
        return this.$message.error('请选择标签条件')
      }
      this.$refs.memberFormRef.validate(valid => {
        if (valid) {
          let api
          let params = {
            name: this.memberForm.name,
            type: this.memberForm.labelType,
            direction: this.memberForm.labelDirection,
            grand_total_sign_days: this.memberForm.isTotalCount && this.memberForm.labelDirection === 'positive' ? this.memberForm.totalCount : -1, // 累计签到天数
            continuous_sign_days: this.memberForm.isContinueCount && this.memberForm.labelDirection === 'positive' ? this.memberForm.continueCount : -1, // 连续签到天数
            member_grade: this.memberForm.isLevel && this.memberForm.labelDirection === 'positive' ? this.memberForm.level : null, // 会员等级
            integrals: this.memberForm.isIntegral && this.memberForm.labelDirection === 'positive' ? this.memberForm.integral : -1, // 积分达到
            labels: this.memberForm.isLabel && this.memberForm.labelDirection === 'positive' ? this.memberForm.label : [], // 用户标签
            not_login_days: this.memberForm.isNotLoginCount && this.memberForm.labelDirection === 'negative' ? this.memberForm.notLoginCount : -1, // 连续未登录天数
            not_sign_days: this.memberForm.isNotSignCount && this.memberForm.labelDirection === 'negative' ? this.memberForm.notSignCount : -1, // 连续未签到天数
            not_login: this.memberForm.labelDirection === 'negative' ? this.memberForm.isNeverLogin : false, // 从未登陆过
            not_sign: this.memberForm.labelDirection === 'negative' ? this.memberForm.isNeverSign : false, // 从未签到过
            not_buy: this.memberForm.labelDirection === 'negative' ? this.memberForm.isNeverBuy : false // 从未购买过会员
          }
          switch (this.type) {
            case 'add':
              api = this.$apis.apiBackgroundMemberMemberLabelAddPost(params)
              break;
            case 'edit':
              params.id = Number(this.settingData.id)
              api = this.$apis.apiBackgroundMemberMemberLabelModifyPost(params)
              break;
          }
          this.confirmOperation(api)
        } else {
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 会员等级
    async getMemberLevel() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundMemberMemberGradeListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.levelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 用户标签
    async getUserLabelList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundHealthyLabelListPost({
        page: 1,
        page_size: 99999,
        type: 'user'
      })
      this.isLoading = false
      if (res.code === 0) {
        this.userLabelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    addLabel() {
      this.memberForm.permissions.push('')
    },
    delLabel(index) {
      this.memberForm.permissions.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.m-l-100{
  margin-left: 100px;
}
.label-list{
  display: flex;
  flex-wrap: wrap;
  color: #fff;
  .label-list-item {
    margin-bottom: 10px;
    line-height: 30px;
    background-color: #ff9b45;
    padding: 0 10px;
    margin-right: 15px;
    border-radius: 5px;
    .del-icon{
      cursor: pointer;
    }
  }
}
</style>

<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      ref="memberFormRef"
      :rules="dialogFormRules"
      label-width="120px"
      class="member-form"
    >
      <div v-if="type === 'label'">
        <el-form-item label="自动标签：">
          {{autoLabelNameList.join('，')}}
        </el-form-item>
        <el-form-item label="手动标签：">
          <div class="label-list">
            <div class="label-list-item" v-for="(item, index) in labelNameList" :key="item">
              <span class="m-r-5">{{item}}</span>
              <i @click="delLabel(index)" class="el-icon-close del-icon"></i>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="新增手动标签：">
          <el-select
            v-model="dialogForm.selectLabelList"
            placeholder="请选择手动标签"
            class="ps-input"
            @change="changeSelectLabel"
            multiple
            collapse-tags
          >
            <el-option
              v-for="item in labelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div v-if="type === 'integral'">
        <el-form-item label="当前积分：">{{selectInfo.integral}}</el-form-item>
        <el-form-item label="修改后积分：" prop="score">
          <el-input v-model="dialogForm.score" maxlength="7" class="ps-input w-180"></el-input>
        </el-form-item>
      </div>
      <div v-if="type === 'growthScore'">
        <el-form-item label="当前成长分：">{{selectInfo.growth_points}}</el-form-item>
        <el-form-item label="添加成长分：" prop="score">
          <el-input v-model="dialogForm.score" maxlength="7" class="ps-input w-180"></el-input>
        </el-form-item>
        <el-form-item label="修改后成长分：">{{Number(selectInfo.growth_points) + Number(dialogForm.score)}}</el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
export default {
  name: 'ForLeaveRuleDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '450px'
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    let validataNumber = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('不能为空'))
      } else {
        let number = /^\d+$/
        if (!number.test(value)) {
          callback(new Error('请输入正整数'))
        } else {
          callback()
        }
      }
    };
    return {
      isLoading: false,
      dialogForm: {
        selectLabelList: [],
        score: ''
      },
      dialogFormRules: {
        score: [{ required: true, validator: validataNumber, trigger: 'blur' }]
      },
      autoLabelNameList: [],
      autoLabelIdList: [],
      labelNameList: [],
      labelList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.labelNameList = []
        this.autoLabelNameList = []
        this.autoLabelIdList = []
        this.dialogForm.selectLabelList = []
        this.selectInfo.member_labels_list.map(item => {
          if (item.type === 'auto') {
            this.autoLabelNameList.push(item.name)
            this.autoLabelIdList.push(item.id)
          } else {
            this.dialogForm.selectLabelList.push(item.id)
          }
        })
        this.getMemberLabel()
      } else {
        this.$refs.memberFormRef.resetFields()
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    clickConfirmHandle() {
      this.$refs.memberFormRef.validate(valid => {
        if (valid) {
          let params = {}
          let api
          switch (this.type) {
            case 'growthScore':
              params = {
                user_id: this.selectInfo.id,
                add_growth_value: this.dialogForm.score,
                obtain_type: 'background_add'
              }
              api = this.$apis.apiBackgroundMemberMemberGradeGrowthAddPost(params)
              break;
            case 'label':
              params = {
                id: this.selectInfo.id,
                member_labels: this.autoLabelIdList.concat(this.dialogForm.selectLabelList)
              }
              api = this.$apis.apiBackgroundMemberMemberUserModifyPost(params)
              break;
          }
          this.confirmOperation(api)
        } else {
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.memberFormRef.resetFields()
    },
    delLabel(index) {
      this.dialogForm.selectLabelList.splice(index, 1)
      this.labelNameList.splice(index, 1)
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost({
        page: 1,
        page_size: 99999,
        type: 'manual'
      })
      if (res.code === 0) {
        this.labelList = res.data.results
        this.labelList.map(item => {
          if (this.dialogForm.selectLabelList.indexOf(item.id) !== -1) {
            this.labelNameList.push(item.name)
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    changeSelectLabel() {
      this.labelNameList = []
      this.labelList.map(item => {
        if (this.dialogForm.selectLabelList.indexOf(item.id) !== -1) {
          this.labelNameList.push(item.name)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.member-form{
  .label-list{
    display: flex;
    flex-wrap: wrap;
    color: #fff;
    .label-list-item {
      background-color: #ff9b45;
      padding: 0 10px;
      margin-right: 15px;
      margin-bottom: 10px;
      border-radius: 5px;
      .del-icon{
        cursor: pointer;
      }
    }
  }
  .m-r-5{
    margin-right: 5px;
  }
}

</style>

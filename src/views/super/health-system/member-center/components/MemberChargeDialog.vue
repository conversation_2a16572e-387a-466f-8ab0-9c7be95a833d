<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      ref="dialogFormRef"
      :rules="dialogFormRules"
      label-width="120px"
      class="member-form"
    >
      <div v-if="type === 'add' || type === 'edit'">
        <el-form-item label="规则名称：" prop="name">
          <el-input v-model="dialogForm.name" maxlength="20" :disabled="selectInfo.is_base" class="ps-input w-250"></el-input>
        </el-form-item>
        <el-form-item label="符合标签：" prop="label" v-if="!selectInfo.is_base">
          <el-select
            v-model="dialogForm.label"
            placeholder="请选择标签"
            class="ps-input w-250"
            multiple
          >
            <el-option
              v-for="item in labelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会员周期：" prop="cycle" v-if="!selectInfo.is_base">
          <el-select
            v-model="dialogForm.cycle"
            placeholder="请选择会员周期"
            class="ps-input w-250"
          >
            <el-option
              v-for="item in cycleList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="价格：" prop="price">
          <el-input v-model="dialogForm.price" maxlength="20" class="ps-input w-250"></el-input>
        </el-form-item>
        <el-form-item label="单人可购次数：" prop="count" v-if="!selectInfo.is_base">
          <el-input v-model="dialogForm.count" maxlength="20" class="ps-input w-250"></el-input>次
        </el-form-item>
        <el-form-item label="说明：" prop="remark">
          <el-input
            class="ps-input w-250"
            v-model="dialogForm.remark"
            type="textarea"
            :rows="3"
            maxlength="140"
          ></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { divide, times } from '@/utils'
export default {
  name: 'ForLeaveRuleDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '450px'
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    return {
      isLoading: false,
      dialogForm: {
        name: '',
        label: [],
        cycle: '',
        price: '',
        count: '',
        remark: ''
      },
      dialogFormRules: {
        name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
        label: [{ required: true, message: '请选择符合标签', trigger: 'change' }],
        cycle: [{ required: true, message: '请选择会员周期', trigger: 'change' }],
        price: [{ required: true, message: '请输入价格', trigger: 'blur' }]
      },
      labelList: [],
      cycleList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.getMemberLabel()
        this.getMemberCycle()
        if (this.type === 'edit') {
          this.dialogForm.name = this.selectInfo.name
          this.dialogForm.label = this.selectInfo.member_labels
          this.dialogForm.cycle = this.selectInfo.member_cycle
          this.dialogForm.price = divide(this.selectInfo.origin_fee)
          this.dialogForm.count = this.selectInfo.buy_count
          this.dialogForm.remark = this.selectInfo.remark
        }
      } else {
        this.$refs.dialogFormRef.resetFields()
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    clickConfirmHandle() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          let params = {
            name: this.dialogForm.name,
            member_labels: this.dialogForm.label,
            member_cycle: this.dialogForm.cycle,
            origin_fee: times(this.dialogForm.price)
          }
          if (this.dialogForm.count) {
            params.buy_count = this.dialogForm.count
          }
          if (this.dialogForm.remark) {
            params.remark = this.dialogForm.remark
          }
          let api
          switch (this.type) {
            case 'add':
              api = this.$apis.apiBackgroundMemberMemberChargeRuleAddPost(params)
              break;
            case 'edit':
              params.id = Number(this.selectInfo.id)
              api = this.$apis.apiBackgroundMemberMemberChargeRuleModifyPost(params)
              break;
          }
          this.confirmOperation(api)
        } else {
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.dialogFormRef.resetFields()
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.labelList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取会员周期
    async getMemberCycle() {
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleGetMemberCyclePost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        let results = []
        for (let key in res.data) {
          results.push({
            value: key,
            label: res.data[key]
          })
        }
        this.cycleList = results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.member-form{
  .label-list{
    display: flex;
    flex-wrap: wrap;
    color: #fff;
    .label-list-item {
      margin-bottom: 10px;
      line-height: 30px;
      background-color: #ff9b45;
      padding: 0 10px;
      margin-right: 15px;
      border-radius: 5px;
      .del-icon{
        cursor: pointer;
      }
    }
  }
  .m-r-5{
    margin-right: 5px;
  }
}

</style>

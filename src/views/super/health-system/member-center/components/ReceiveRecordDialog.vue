<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      ref="memberFormRef"
      :rules="dialogFormRules"
      label-width="120px"
      class="member-form"
    >
      <div v-if="type === 'grant'">
        <el-form-item label="会员手机号：" prop="userid">
          <el-select
            v-model="dialogForm.userid"
            filterable
            remote
            placeholder="请选择"
            :remote-method="getMemberList"
            :loading="loadingMemberList"
            class="ps-input w-250"
            @change="changeMemberPhone">
            <el-option
              v-for="item in memberList"
              :key="item.id"
              :label="item.phone"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会员姓名：">{{userInfo.nickname}}</el-form-item>
        <el-form-item label="会员ID：">{{userInfo.user_id}}</el-form-item>
        <el-form-item label="发放天数：" prop="days">
          <el-input v-model="dialogForm.days" maxlength="20" class="ps-input w-250"></el-input>天
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
export default {
  name: 'ForLeaveRuleDialog',
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '450px'
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isshow: Boolean,
    confirm: Function
  },
  data() {
    return {
      isLoading: false,
      loadingMemberList: false,
      dialogForm: {
        userid: '',
        days: ''
      },
      dialogFormRules: {
        userid: [{ required: true, message: '请输入搜索手机号', trigger: 'blur' }],
        days: [{ required: true, message: '请输入发放天数', trigger: 'change' }]
      },
      userInfo: {},
      memberList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
      } else {
        this.$refs.memberFormRef.resetFields()
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    clickConfirmHandle() {
      this.$refs.memberFormRef.validate(valid => {
        if (valid) {
          let params = {
            user_id: this.dialogForm.userid,
            days: this.dialogForm.days,
            receive_type: 'manual_release'
          }
          let api
          switch (this.type) {
            case 'grant':
              api = this.$apis.apiBackgroundMemberMemberReceiveAddPost(params)
              break;
          }
          this.confirmOperation(api)
        } else {
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        this.confirm()
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.memberFormRef.resetFields()
    },
    // 获取会员列表
    async getMemberList(phone) {
      if (!phone) return
      this.loadingMemberList = true
      const res = await this.$apis.apiBackgroundMemberMemberUserListPost({
        phone,
        page: 1,
        page_size: 99999
      })
      this.loadingMemberList = false
      if (res.code === 0) {
        this.memberList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    changeMemberPhone(e) {
      let list = this.memberList.filter(item => item.id === e)
      if (list.length) {
        this.userInfo = list[0]
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.member-form{
  .label-list{
    display: flex;
    flex-wrap: wrap;
    color: #fff;
    .label-list-item {
      margin-bottom: 10px;
      line-height: 30px;
      background-color: #ff9b45;
      padding: 0 10px;
      margin-right: 15px;
      border-radius: 5px;
      .del-icon{
        cursor: pointer;
      }
    }
  }
  .m-r-5{
    margin-right: 5px;
  }
}

</style>

<template>
  <div class="SuperMemberList container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="user_id" label="用户ID" align="center" width="180"></el-table-column>
          <el-table-column prop="nickname" label="姓名" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
          <el-table-column prop="member_grade_name" label="会员等级" align="center"></el-table-column>
          <el-table-column prop="member_labels" label="会员标签" align="center" width="250">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.member_labels_list"
                style="margin-right: 8px"
                :key="item.id"
              >
                {{ item.name }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="integral" label="积分" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="gotoMemberDetail(scope.row)"
                >详情</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-green"
                @click="openDialog('label', scope.row)"
                >编辑标签</el-button>
              <!-- <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDialog('integral', scope.row)"
                >修改积分</el-button> -->
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="openDialog('growthScore', scope.row)"
                >修改成长分</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <member-list-dialog
      :isshow.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :select-info="selectInfo"
      :confirm="searchHandle"/>

  </div>
</template>

<script>
import { debounce } from '@/utils'
import MemberListDialog from './components/MemberListDialog.vue'
import { getRequestParams } from './constants'

export default {
  name: 'SuperMemberList',
  components: { MemberListDialog },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        nickname: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          placeholder: '请输入手机号'
        },
        member_grade_ids: {
          type: 'select',
          value: [],
          label: '会员等级',
          dataList: [],
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          clearable: true
        },
        member_labels: {
          type: 'select',
          value: [],
          label: '会员标签',
          dataList: [],
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          clearable: true
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      selectInfo: {}
    }
  },
  created() {
    this.getMemberLabel()
    this.getMemberGrade()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMemberList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getMemberList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getMemberList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundMemberMemberUserListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMemberList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getMemberList()
    },
    openDialog(type, data) {
      this.dialogType = type
      this.selectInfo = data
      if (type === 'label') {
        this.dialogTitle = '编辑标签'
      } else if (type === 'integral') {
        this.dialogTitle = '修改积分'
      } else if (type === 'growthScore') {
        this.dialogTitle = '修改成长分'
      }
      this.dialogVisible = true
    },
    gotoMemberDetail(data) {
      this.$router.push({
        name: 'SuperMemberDetail',
        query: {
          data: JSON.stringify(data)
        }
      })
    },
    // 获取会员等级
    async getMemberGrade() {
      const res = await this.$apis.apiBackgroundMemberMemberGradeListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.searchFormSetting.member_grade_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.searchFormSetting.member_labels.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>

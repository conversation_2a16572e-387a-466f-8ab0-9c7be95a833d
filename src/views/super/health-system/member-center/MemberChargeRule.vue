<template>
  <div class="SuperMemberList container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('add')">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="name" label="规则名称" align="center"></el-table-column>
          <el-table-column prop="member_labels_list" label="会员标签" width="250" align="center">
            <template slot-scope="scope">
              <el-tag
                v-for="item in scope.row.member_labels_list"
                style="margin-right: 8px"
                :key="item.id"
              >
                {{ item.name }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="" label="会员价格" align="center">
            <template slot-scope="scope">
              <span>{{scope.row.origin_fee | formatMoney}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="member_cycle_alias" label="会员周期" align="center"></el-table-column>
          <el-table-column prop="remark" label="说明" align="center">
            <template slot-scope="scope">
              <div v-if="!scope.row.show_all_remark && scope.row.remark.length > 20">
                {{textFormat(scope.row.remark, 20)}}
                <el-button
                  type="text"
                  size="small"
                  @click="scope.row.show_all_remark = true"
                  >查看更多</el-button>
              </div>
              <div v-else>
                {{scope.row.remark}}
                <el-button
                  v-if="scope.row.remark.length > 20"
                  type="text"
                  size="small"
                  @click="scope.row.show_all_remark = false"
                >收起</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="update_time" label="操作时间" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
          <el-table-column prop="score" label="状态" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.is_enable" @change="switchFacePay(scope.row)" :disabled="scope.row.is_base" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDialog('edit', scope.row)"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="delMemberCharge(scope.row.id)"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <member-charge-dialog
      :isshow.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :select-info="selectInfo"
      :confirm="searchHandle"/>

  </div>
</template>

<script>
import { debounce, textFormat } from '@/utils'
import MemberChargeDialog from './components/MemberChargeDialog.vue'
import { getRequestParams } from './constants'

export default {
  name: 'SuperMemberList',
  components: { MemberChargeDialog },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_time',
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '操作时间',
              value: 'update_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: false,
          value: []
        },
        name: {
          type: 'input',
          label: '规则名称',
          value: '',
          placeholder: '请输入规则名称'
        },
        member_labels: {
          type: 'select',
          value: [],
          label: '会员标签',
          dataList: [],
          multiple: true,
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'id',
          clearable: true
        },
        member_cycle: {
          type: 'select',
          value: [],
          label: '会员周期',
          dataList: [],
          multiple: true,
          collapseTags: true,
          clearable: true
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      selectInfo: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMemberCharge()
      this.getMemberLabel()
      this.getMemberCycle()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getMemberCharge()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getMemberCharge() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results.map(item => {
          item.show_all_remark = false
          return item
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMemberCharge()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getMemberCharge()
    },
    async delMemberCharge(id) {
      this.$confirm(`确定删除该会员收费规则？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundMemberMemberChargeRuleDeletePost({
              ids: [id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getMemberCharge()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    async switchFacePay(data) {
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleModifyPost({
        id: data.id,
        name: data.name,
        member_labels: data.member_labels,
        member_cycle: data.member_cycle,
        is_enable: data.is_enable
      })
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
      } else {
        this.$message.error(res.msg)
      }
    },
    openDialog(type, data) {
      this.dialogType = type
      this.selectInfo = data
      if (type === 'add') {
        this.dialogTitle = '新建规则'
      } else if (type === 'edit') {
        this.dialogTitle = '编辑规则'
      }
      this.dialogVisible = true
    },
    // 获取会员标签
    async getMemberLabel() {
      const res = await this.$apis.apiBackgroundMemberMemberLabelListPost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.searchFormSetting.member_labels.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取会员周期
    async getMemberCycle() {
      const res = await this.$apis.apiBackgroundMemberMemberChargeRuleGetMemberCyclePost({
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        let results = []
        for (let key in res.data) {
          results.push({
            value: key,
            label: res.data[key]
          })
        }
        this.searchFormSetting.member_cycle.dataList = results
      } else {
        this.$message.error(res.msg)
      }
    },
    textFormat
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>

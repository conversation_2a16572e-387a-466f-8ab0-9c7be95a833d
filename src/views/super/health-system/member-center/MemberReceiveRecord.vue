<template>
  <div class="SuperMemberList container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('grant')">手动发放</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="trade_no" label="订单号" align="center" width="180"></el-table-column>
          <el-table-column prop="receive_time" label="领取时间" align="center"></el-table-column>
          <el-table-column prop="nickname" label="用户姓名" align="center"></el-table-column>
          <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
          <el-table-column prop="user_id" label="用户ID" align="center"></el-table-column>
          <el-table-column prop="days" label="领取天数" align="center"></el-table-column>
          <el-table-column prop="end_time" label="到期时间" align="center"></el-table-column>
          <el-table-column prop="receive_type_alias" label="领取方式" align="center"></el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <receive-record-dialog
      :isshow.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :confirm="searchHandle"/>

  </div>
</template>

<script>
import { debounce } from '@/utils'
import ReceiveRecordDialog from './components/ReceiveRecordDialog.vue'
import { RECENTSEVEN, getRequestParams } from './constants'

export default {
  name: 'SuperMemberList',
  components: { ReceiveRecordDialog },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '时间',
          clearable: false,
          value: RECENTSEVEN
        },
        nickname: {
          type: 'input',
          label: '用户姓名',
          value: '',
          placeholder: '请输入用户姓名'
        },
        phone: {
          type: 'input',
          label: '手机号',
          value: '',
          placeholder: '请输入手机号'
        },
        receive_type: {
          type: 'select',
          value: [],
          label: '领取方式',
          multiple: true,
          collapseTags: true,
          dataList: [{
            value: 'buy',
            label: '购买'
          }, {
            value: 'continuous',
            label: '续费'
          }, {
            value: 'points_redemption',
            label: '积分兑换'
          }, {
            value: 'sign_in_reward',
            label: '签到奖励'
          }, {
            value: 'invitation_bonus',
            label: '邀请奖励'
          }, {
            value: 'manual_release',
            label: '手动发放'
          }],
          clearable: true
        }
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      selectInfo: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMemberReceive()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getMemberReceive()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getMemberReceive() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundMemberMemberReceiveListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMemberReceive()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getMemberReceive()
    },
    openDialog(type, data) {
      this.dialogType = type
      if (type === 'grant') {
        this.dialogTitle = '手动发放'
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>

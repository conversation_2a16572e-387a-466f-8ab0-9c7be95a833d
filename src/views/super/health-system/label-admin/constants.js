// import { getDateRang } from '@/utils'
import * as dayjs from 'dayjs'

// getDateRang(-7)

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const PLATFORM_LABEL = {
  name: {
    type: 'input',
    label: '标签组/标签名称',
    value: '',
    placeholder: '请输入标签组/标签名称'
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '可见范围',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    clearable: true,
    role: 'super'
  }
}
export const INGREDIENTS_LABEL = {
  name: {
    type: 'input',
    label: '标签组/标签名称',
    value: '',
    placeholder: '请输入标签组/标签名称',
    maxlength: 15
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '可见范围',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    clearable: true,
    role: 'super'
  }
}
export const MERCHANT_LABEL = {
  name: {
    type: 'input',
    label: '标签组/标签名称',
    value: '',
    placeholder: '请输入标签组/标签名称',
    maxlength: 15
  },
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '同步时间',
    value: '',
    clearable: true
  },
  sync_status: {
    type: 'select',
    value: '',
    label: '是否同步',
    clearable: false,
    dataList: [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]
  }
}
export const USER_LABEL = {
  name: {
    type: 'input',
    label: '标签组/标签名称',
    value: '',
    placeholder: '请输入标签组/标签名称'
  }
}

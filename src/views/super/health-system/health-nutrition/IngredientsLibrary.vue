<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="super-ingredients-library container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    >
      <template slot="perv">
        <div class="tab">
          <div
            :class="['tab-item', tabType === 'system' ? 'active' : '']"
            @click="tabClick('system')"
          >
            系统食材
          </div>
          <div
            :class="['tab-item', tabType === 'merchant' ? 'active' : '']"
            @click="tabClick('merchant')"
          >
            商户食材
          </div>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon v-if="tabType === 'system'" color="plain" type="refresh" @click="distributeHandler">更新到商户</button-icon> -->
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            type="del"
            @click="batchLabelClick('batchLabelDel')"
          >
            批量移除标签
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            type="mul"
            @click="batchLabelClick('batchLabelAdd')"
          >
            批量打标签
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="origin"
            type="add"
            @click="addIngredients"
          >
            添加食材
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            type="Import"
            @click="importHandler('modify_import')"
          >
            导入编辑
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            type="Import"
            @click="importHandler('import')"
          >
            批量导入食材
          </button-icon>
          <button-icon
            v-if="tabType === 'system'"
            color="plain"
            type="Import"
            @click="importHandler('importImage')"
            v-permission="['background.admin.ingredient.ingredient_image_bat_add']"
          >
            导入食材图片
          </button-icon>
          <button-icon v-if="tabType === 'system'" color="plain" type="menu" @click="gotoCategory">
            分类管理
          </button-icon>
          <span v-if="tabType === 'merchant'" style="font-size: 12px;">
            是否允许商户上传信息
            <el-switch
              v-model="updateSetting"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
              @change="updateSettingHandler"
            ></el-switch>
          </span>
          <button-icon color="origin" type="export" @click="gotoExport">导出EXCEL</button-icon>
        </div>
      </div>
      <div class="table-content" :key="tabType">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          row-key="id"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="tabType === 'system'"
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
            :reserve-selection="true"
          ></el-table-column>
          <!-- <el-table-column type="index" label="序号" align="center"></el-table-column> -->
          <el-table-column type="index" width="80" label="图片" align="center">
            <template slot-scope="scope">
              <el-image class="column-image" :lazy="true" :src="scope.row.image?scope.row.image:require('@/assets/img/ingredients_default.png')" :preview-src-list="[scope.row.image?scope.row.image:require('@/assets/img/ingredients_default.png')]">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="食材ID" align="center"></el-table-column>
          <el-table-column prop="name" label="食材名称" align="center"></el-table-column>
          <el-table-column
            prop="all_alias_name"
            label="食材别名"
            align="center"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column prop="category_name" label="一级分类" align="center"></el-table-column>
          <el-table-column prop="sort_name" label="二级分类" align="center"></el-table-column>
          <el-table-column
            v-if="tabType === 'merchant'"
            prop="create_source_name"
            label="来源"
            align="center"
          ></el-table-column>
          <el-table-column prop="nutrition" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.is_enable_nutrition"
                type="text"
                size="small"
                class="ps-text"
                @click="showDialogHandler('nutrition', scope.row)"
              >
                查看
              </el-button>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="xx"
            label="标签"
            align="center"
            width="220px"
            v-if="tabType === 'system'"
          >
            <template slot-scope="scope">
              <div class="collapse-wrapper">
                <div class="collapse-list hide">
                  <el-tag
                    class="m-r-5 m-t-5 collapse-data"
                    v-for="(item, index) in scope.row.label"
                    :key="index"
                    size="medium"
                    effect="plain"
                    type="light"
                    closable
                    @close="closeTag(item, scope.row)"
                  >
                    {{ item.name }}
                  </el-tag>
                  <template v-if="scope.row.label && scope.row.label.length > 3">
                    <span class="collapse-more" @click="showMoreHandler">
                      查看更多
                      <i class="el-icon-arrow-down"></i>
                    </span>
                    <span class="collapse-hide" @click="hideMoreHandler">
                      收起
                      <i class="el-icon-arrow-up"></i>
                    </span>
                  </template>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="tabType === 'merchant'"
            prop="is_repeat"
            label="已有食材"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.is_repeat ? '是' : '否' }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="is_enable_nutrition" label="营养录入" align="center">
            <template slot-scope="scope">
              {{ scope.row.is_enable_nutrition ? '是' : '否' }}
            </template>
          </el-table-column> -->
          <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column
            v-if="tabType === 'system'"
            prop="update_time"
            label="修改时间"
            align="center"
          ></el-table-column>
          <el-table-column label="操作" width="180" align="center">
            <template slot-scope="scope">
              <template v-if="tabType === 'merchant'">
                <el-button type="text" size="small" @click="addToSystem(scope.row.id)">
                  创建到食材库
                </el-button>
              </template>
              <template v-else>
                <el-button type="text" size="small" class="" @click="modifyIngredients(scope.row)">
                  编辑
                </el-button>
                <span style="margin: 0 10px; color: #e2e8f0">|</span>
                <el-button
                  type="text"
                  size="small"
                  class="ps-warn-text"
                  @click="deleteHandler('single', scope.row.id)"
                >
                  删除
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 start -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="700px"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      @closed="dialogHandleClose"
    >
      <el-form v-loading="isLoading" :model="formData" class="" size="small">
        <div class="table-content">
          <template v-for="nutrition in nutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
              <el-form-item :prop="nutrition.key">
                <el-input
                  style="width: 120px"
                  readonly
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                ></el-input>
                <span style="margin-left: 10px">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
        </div>
      </el-form>
      <!-- <span slot="footer" class="dialog-footer">
          <el-button size="small" class="ps-cancel-btn"  @click="dialogVisible = false">
            {{ $t('dialog.cancel_btn') }}
          </el-button>
          <el-button
            class="ps-origin-btn"
            type="primary"
            size="small"
          >
          </el-button>
        </span> -->
    </el-dialog>
    <!-- 弹窗 end -->
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      :title="titleSelectLaber"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, to, replaceSingleQuote } from '@/utils'
import {
  NUTRITION_LIST,
  LIBRARY_SEARCH_SETTING_SUPER,
  LIBRARY_SEARCH_SETTING_MERCHANT
} from '../health-nutrition/constants'
import selectLaber from '../components/selectLaber.vue'
export default {
  name: 'SuperIngredientsLibrary',
  mixins: [exportExcel],
  data() {
    return {
      tabType: 'system',
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      updateSetting: false, // 是否允许商户上传信息
      tableData: [], // table
      searchFormSetting: LIBRARY_SEARCH_SETTING_SUPER, // 搜索设置
      dialogData: {}, // 弹窗源数据
      dialogTitle: '营养信息', // 弹窗title
      dialogType: '', // 弹窗类型
      dialogVisible: false, // 弹窗开关
      dialogLoading: false, // 弹窗状态
      nutritionList: NUTRITION_LIST, // 营养列表
      formData: {}, // 弹窗formData
      checkList: [],
      selectLaberDialogVisible: false,
      titleSelectLaber: '',
      batchLabelType: '', // 批量标签type
      ruleSingleInfo: {} // 标签
    }
  },
  components: { selectLaber },
  created() {
    this.initLoad()
    this.getCategoryCategoryNameList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getListHandler()
      this.getAllLabelGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getListHandler()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // tab 栏点击事件
    tabClick(type) {
      this.tabType = type
      this.currentPage = 1
      if (type === 'system') {
        this.searchFormSetting = LIBRARY_SEARCH_SETTING_SUPER
      } else {
        this.searchFormSetting = LIBRARY_SEARCH_SETTING_MERCHANT
      }
      this.searchHandle()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value || (data[key].value && data[key].value.length)) {
          if (key !== 'select_time') {
            // 因为要区分一级和二级id 字段不同 如果是一级就用category_id 二级sort_id 目前只有二级分类
            if (key === 'sort_id') {
              data[key].dataList.map(v => {
                if (data[key].value.split('_')[0] === '1') {
                  params.category_id = Number(data[key].value.split('_')[1])
                } else {
                  if (data[key].value.split('_')[0] === '2') {
                    params.sort_id = Number(data[key].value.split('_')[1])
                  }
                }
              })
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取系统食材列表
    async getIngredientslist() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          if (item.alias_name !== null) {
            item.all_alias_name = item.alias_name.join(',')
          } else {
            item.alias_name = []
          }
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取商户食材列表
    async getIngredientsManchantlist() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientMerchantIngredientPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          if (item.alias_name !== null) {
            item.all_alias_name = item.alias_name.join(',')
          } else {
            item.alias_name = []
          }
          return item
        })
        this.updateSetting = res.data.ingredient_upload
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取所有一级食材以及下面的二级食材
    async getCategoryCategoryNameList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminIngredientCategoryCategoryNameListPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // this.searchFormSetting.sort_id.dataList = this.deleteEmptyGroup(res.data)
        LIBRARY_SEARCH_SETTING_MERCHANT.sort_id.dataList = this.deleteEmptyGroup(res.data)
        LIBRARY_SEARCH_SETTING_SUPER.sort_id.dataList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.sort_list) {
            if (item.sort_list.length > 0) {
              traversal(item.sort_list)
            } else {
              _that.$delete(item, 'sort_list')
            }
          } else {
            _that.$delete(item, 'sort_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 打开弹窗
    showDialogHandler(type, data) {
      if (type === 'nutrition') {
        this.setDialogNutriton(data)
      }
      this.dialogVisible = true
    },
    // 设置弹窗营养的数据
    setDialogNutriton(row) {
      this.formData = {}
      if (!row.nutrition_info) row.nutrition_info = {}
      let element = row.nutrition_info.element
        ? JSON.parse(replaceSingleQuote(row.nutrition_info.element))
        : {}
      let vitamin = row.nutrition_info.vitamin
        ? JSON.parse(replaceSingleQuote(row.nutrition_info.vitamin))
        : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(
            this.formData,
            nutrition.key,
            row.nutrition_info[nutrition.key] ? row.nutrition_info[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'element') {
          this.$set(
            this.formData,
            nutrition.key,
            element[nutrition.key] ? element[nutrition.key] : 0
          )
        }
        if (nutrition.type === 'vitamin') {
          this.$set(
            this.formData,
            nutrition.key,
            vitamin[nutrition.key] ? vitamin[nutrition.key] : 0
          )
        }
      })
    },
    // 更新菜品到商户端
    distributeHandler() {
      this.$confirm(`是否更新食材到商户端食材库？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundAdminIngredientSyncIngredientPost())
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getListHandler()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 删除
    deleteHandler(type, id) {
      let ids = []
      if (type === 'single') {
        ids = [id]
      } else {
        if (!this.checkList.length) return this.$message.error('请先选择要删除的数据！')
        ids = this.checkList
      }
      this.$confirm(`确定删除？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminIngredientDeletePost({
                ids: ids
              })
            )
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getListHandler()
              this.checkList = []
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 是否允许商户上传信息
    updateSettingHandler(e) {
      let title = '是否允许商户上传信息?'
      if (!this.updateSetting) {
        title = '是否关闭用户上传信息?'
      }
      this.$confirm(title, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminIngredientChangeUploadPost({
                is_enable: this.updateSetting ? 1 : 0
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getListHandler()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              this.updateSetting = !this.updateSetting
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 创建到食材库
    addToSystem(id) {
      this.$confirm(`是否创建到食材库?<p style="color:red;">注：食材重名自动覆盖！<p>`, {
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminIngredientAddToSystemPost({
                id: id
              })
            )
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.getListHandler()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 跳转添加食材页面
    addIngredients() {
      this.$router.push({
        name: 'SuperAddIngredients',
        query: {
          type: 'add'
        },
        params: {
          type: 'add'
        }
      })
    },
    // 编辑
    modifyIngredients(row) {
      this.$router.push({
        name: 'SuperAddIngredients',
        query: {
          type: 'modify',
          data: this.$encodeQuery(row)
        },
        params: {
          type: 'modify'
        }
      })
    },
    handleSelectionChange(val) {
      this.checkList = []
      val.map(item => {
        this.checkList.push(item.id)
      })
    },
    showMoreHandler(e) {
      e.target.parentNode.classList.remove('hide')
    },
    hideMoreHandler(e) {
      e.target.parentNode.classList.add('hide')
    },
    batchLabelClick(type) {
      this.batchLabelType = type
      if (type === 'batchLabelDel') {
        this.titleSelectLaber = '批量移除标签'
      } else if (type === 'batchLabelAdd') {
        this.titleSelectLaber = '批量打标签'
      }
      if (!this.checkList.length) {
        return this.$message.error(`请先选择要${this.titleSelectLaber}的数据！`)
      }
      // 保存一下 选择标签后 需要返显
      this.ruleSingleInfo = {
        labelType: 'ingredient'
      }
      this.selectLaberDialogVisible = true
    },
    // 获取所有的标签
    async getAllLabelGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundHealthyAdminLabelGroupAllLabelGroupListPost({
          page_size: 999999,
          page: 1,
          type: 'ingredient'
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        res.data.results.map(v => {
          if (!v.label_list.length) {
            v.isDisabled = true
          }
          return v
        })
        this.searchFormSetting.label_list.dataList = res.data.results
      } else {
        // this.$message.error(res.msg)
        this.$message({
          type: 'error',
          duration: 1000,
          message: res.msg
        })
      }
    },
    selectLaberData(params) {
      this.$confirm(`是否${this.titleSelectLaber}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            if (this.dialogLoading) return this.$message.error('请勿重复提交！')
            this.dialogLoading = true
            instance.confirmButtonLoading = true
            let labelParams = {
              ids: this.checkList,
              label_list: params.selectLabelIdList
            }
            let [err, res] = ''
            if (this.batchLabelType === 'batchLabelAdd') {
              ;[err, res] = await to(
                this.$apis.apiBackgroundAdminIngredientBatchAddLabelPost(labelParams)
              )
            } else {
              ;[err, res] = await to(
                this.$apis.apiBackgroundAdminIngredientBatchDeleteLabelPost(labelParams)
              )
            }
            this.dialogLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              this.$refs.tableData.clearSelection()
              this.getListHandler()
              this.checkList = []
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 移除标签
    closeTag(data, row) {
      this.batchLabelType = 'delSingleTag'
      this.titleSelectLaber = '删除该标签'
      let params = {
        selectLabelIdList: [data.id]
      }
      this.checkList = [row.id]
      this.selectLaberData(params)
    },
    getListHandler() {
      if (this.tabType === 'system') {
        this.getIngredientslist()
      } else {
        this.getIngredientsManchantlist()
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getListHandler()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getListHandler()
    },
    dialogHandleClose() {
      this.formData = {}
    },
    // 导入食材
    importHandler(type) {
      if (type === 'import') {
        this.$router.push({
          name: 'SuperImportIngredients',
          params: {
            type: type
          }
        })
      } else if (type === 'importImage') {
        this.$router.push({
          name: 'SuperImportIngredientImage',
          params: {
            type: type
          }
        })
      }
    },
    // 分类
    gotoCategory() {
      this.$router.push({
        name: 'SuperImportIngredientsCategory'
      })
    },
    // 导出弹窗
    gotoExport() {
      const option = {
        type: 'SuperIngredientsLibrary',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      if (this.tabType === 'system') {
        option.type = 'SuperIngredientsLibrarySystem'
      } else {
        option.type = 'SuperIngredientsLibraryMerchant'
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.super-ingredients-library {
  .tab {
    margin-bottom: 20px;
    .tab-item {
      display: inline-block;
      width: 90px;
      height: 28px;
      line-height: 28px;
      margin-right: 10px;
      text-align: center;
      border-radius: 14px;
      border: solid 1px #dae1ea;
      font-size: 16px;
      color: #7b7c82;
      vertical-align: middle;
      cursor: pointer;
      &.active {
        color: #ffffff;
        background-color: #fd953c;
      }
    }
  }
  .column-image{
    max-width: 80px;
    max-height: 80px;
  }
}
.ps-dialog {
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
}
.collapse-wrapper {
  .collapse-list {
    // text-align: left;
    .collapse-data {
      display: inline-block;
      height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .collapse-more {
      display: none;
    }
    .food-ellipsis {
      display: none;
    }
    .collapse-hide {
      display: block;
      color: #f3b687;
      font-size: 12px;
      cursor: pointer;
    }
    &.hide {
      .collapse-data:nth-child(n + 4) {
        display: none;
      }
      .collapse-more {
        text-align: center;
        display: block;
        color: #f3b687;
        font-size: 12px;
        cursor: pointer;
      }
      .food-ellipsis {
        display: inline-block;
        width: 100px;
      }
      .collapse-hide {
        display: none;
      }
    }
  }
}
</style>

<template>
  <div class="super-add-commodity container-wrapper">
    <el-form v-loading="isLoading" :rules="formRuls" :model="formData" ref="foodRef" class="" size="small">
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div style="max-width: 50%; padding: 0 20px">
          <el-form-item label="菜品/商品名称" prop="name" class="block-label form-content-flex">
            <el-input
              v-model="formData.name"
              placeholder="请输入菜品/商品名称"
              class="ps-input"
            ></el-input>
            <el-tooltip effect="dark" content="增加菜品别名" placement="top">
              <img class="add-btn-img" @click="addFoodAliasName" src="@/assets/img/plus.png" alt="">
            </el-tooltip>
          </el-form-item>
          <div v-if="formData.aliasName.length">
            <el-form-item label="菜品别名：" class="block-label">
              <el-form-item
                :class="[index>0?'m-t-10':'','food-alias-name-form']"
                v-for="(item,index) in formData.aliasName"
                :key="index"
                :rules="formRuls.aliasName"
                :prop="`aliasName[${index}]`">
                <el-input style="width: 80%" v-model="formData.aliasName[index]" placeholder="请输入菜品别名" class="ps-input"></el-input>
                <img src="@/assets/img/plus.png" @click="addFoodAliasName" alt="">
                <img src="@/assets/img/reduce.png" @click="delFoodAliasName(index)" alt="">
              </el-form-item>
            </el-form-item>
          </div>
          <el-form-item label="属性" prop="attributes" class="block-label">
            <!-- <el-select
              v-model="formData.attributes"
              filterable
              allow-create
              style="width: 100%;"
              placeholder="请选择规格">
              <el-option value="goods" label="商品"></el-option>
              <el-option value="foods" label="菜品"></el-option>
            </el-select> -->
            <el-radio-group v-model="formData.attributes" class="ps-radio">
              <el-radio label="goods">商品</el-radio>
              <el-radio label="foods">菜品</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="口味" prop="taste" class="">
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              style="margin-right: 10px;"
              @keyup.enter.native="inputTasteConfirm"
              @blur="inputTasteConfirm"
            ></el-input>
            <el-button v-else class="ps-btn button-new-tag" type="primary" icon="el-icon-plus" @click="showTasteInput">添加</el-button>
            <el-tag
              :key="tag"
              v-for="tag in formData.tasteList"
              closable
              :disable-transitions="false"
              @close="closeTasteHandle(tag)"
              color="#fff"
            >
              {{ tag }}
            </el-tag>
          </el-form-item>
          <el-form-item label="分类：" prop="categoryId">
            <el-select
              v-model="formData.categoryId"
              placeholder="请下拉选择分类"
              class="ps-select"
              popper-class="ps-popper-select"
              collapse-tags
              clearable
              style="width: 190px;"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in foodCategoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div style="max-width: 700px;padding-left: 20px;">
          <el-form-item label="菜品/商品图片" class="upload-block-label">
            <div class="inline-block upload-w">
              <el-upload
                class="file-upload"
                ref="fileUpload"
                drag
                :action="serverUrl"
                :data="uploadParams"
                :file-list="fileLists"
                :on-success="uploadSuccess"
                :before-upload="beforeFoodImgUpload"
                :limit="limit"
                :multiple="true"
                :show-file-list="false"
                :headers="headersOpts"
                >
                <slot>
                  <div class="upload-t">
                    <i class="el-icon-circle-plus"></i>
                    <div class="el-upload__text">
                      上传菜品/商品图片
                    </div>
                  </div>
                </slot>
              </el-upload>
            </div>
            <div class="inline-block upload-tips">
              上传：菜品/商品图片。<br />
              建议图片需清晰，图片内容与名称相符。<br />
              仅支持jpg、png、bmp格式，大小不超过5M
            </div>
            <div v-show="showFoodImg" @click="showFoodImg = false" style="cursor: pointer;">
              查看已上传的图片（{{formData.imageList.length}}张）
              <i class="el-icon-arrow-up"></i>
            </div>
            <div v-show="!showFoodImg" @click="showFoodImg = true" style="cursor: pointer;">
              查看已上传的图片（{{formData.imageList.length}}张）
              <i class="el-icon-arrow-down"></i>
            </div>
            <div v-show="showFoodImg" class="food-img-wrap">
              <div class="food-img-item" v-for="(item, index) in formData.imageList" :key="index">
                <img :src="item" alt="" srcset="">
                <div class="food-img-mask">
                  <i @click="perviewFoodImg(item)" class="el-icon-zoom-in"></i>
                  <i @click="removeFoodImg(index)" class="el-icon-delete"></i>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            食材占比
            <span class="tip-o-7">（菜品每100g所含食材占比，相加必须等于100%）</span>
            <el-button class="ps-btn float-r" style="margin-right: 15px;" type="primary" size="small" icon="el-icon-plus" @click="addIngredients">添加</el-button>
          </div>
        </div>
        <div :class="['table-content', errorMsg.percentageError?'error-border':'']" :key="allSelectIngredient.join('')">
          <el-table
          v-loading="isLoading"
          :data="formData.ingredientList"
          ref="tableData"
          style="width: 100%"
          header-row-class-name="ps-table-header-row"
          >
            <el-table-column prop="no" label="食材" align="center">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.selectId"
                  placeholder="请下拉选择"
                  class="ps-select margin-right"
                  collapse-tags
                  clearable
                  filterable
                  @change="changeIngredient"
                  :disabled="disabledIngredient.includes(scope.row.selectId)"
                >
                  <el-option
                    v-for="ingredientItem in scope.row.selectFoodIngredient"
                    :key="ingredientItem.id"
                    :label="ingredientItem.name"
                    :value="ingredientItem.id"
                    :disabled="ingredientItem.disabled"
                  ></el-option>
                </el-select>
                <div v-if="disabledIngredient.includes(scope.row.selectId)" style="color: red;">菜品添加成功将自动添加此缺失食材</div>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="占比" align="center">
              <template slot-scope="scope">
              <!-- <el-progress
                :percentage="50"
                color="#fd953c"
                class="cantent"
              ></el-progress> -->
                <div class="cantent ps-flex-align-c flex-align-c">
                  <el-slider class="cantent" v-model="scope.row.percentage" @change="changePercentage" show-input></el-slider>%
                  <!-- <div style="width: 60px;">{{scope.row.percentage}}%</div> -->
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="xx" label="操作" align="center" width="150px">
              <template slot-scope="scope">
                <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                @click="deleteIngredientHandle(scope.row.index)"
              >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="color: red;padding: 20px;" v-if="errorMsg.percentageError">{{errorMsg.percentageError}}</div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            营养信息
          </div>
        </div>
        <div class="table-content">
          <template v-for="nutrition in nutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
              <!-- :rules="formRuls.nutrition" -->
              <el-form-item :prop="nutrition.key">
                <el-input
                  style="width: 120px;"
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                  readonly
                ></el-input>
                <span style="margin-left: 10px;">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
        </div>
      </div>
      <div class="footer" style="margin-top: 20px;">
        <el-button style="width: 120px;" :disabled="isLoading" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px;" type="primary" :disabled="isLoading" @click="submitHandler">添加</el-button>
      </div>
    </el-form>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone, replaceSingleQuote, getToken } from '@/utils'
import { NUTRITION_LIST } from './constants'
import NP from 'number-precision'
// import { dtConfirm } from '@/utils/decorator'
import { confirm } from '@/utils/message'

export default {
  name: 'SuperAddIngredients',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    // let validataNutrition = (rule, value, callback) => {
    //   if (value) {
    //     let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,3}|[1-9][0-9]*\.\d{1,3})))$/
    //     if (!reg.test(value)) {
    //       callback(new Error('营养数据有误，仅支持保留三位小数'))
    //     } else {
    //       callback()
    //     }
    //   } else {
    //     callback()
    //   }
    // }
    let validatorImage = (rule, value, callback) => {
      if (!this.formData.imageList.length) {
        return callback(new Error('请上传菜品图片'))
      } else {
        callback()
      }
    }
    return {
      type: 'modify',
      isLoading: false, // 刷新数据
      formData: {
        name: '', // 食材名
        aliasName: [], // 菜品别名
        attributes: '',
        tasteList: [],
        imageList: [], // 图片
        ingredientList: [], // 食材组成
        food_id: '', // 重复的id
        categoryId: '' // 分类
      },
      fileLists: [],
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      formRuls: {
        name: [{ required: true, message: '食材名称不能为空', trigger: 'blur' }],
        aliasName: [{ required: true, message: '请输入菜品别名', trigger: 'blur' }],
        attributes: [{ required: true, message: '请选择属性', trigger: 'blur' }],
        // nutrition: [{ validator: validataNutrition, trigger: 'change' }]
        imageList: [{ required: true, validator: validatorImage, trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择分类', trigger: 'blur' }]
      },
      nutritionList: NUTRITION_LIST,
      inputVisible: false,
      inputValue: '',
      limit: 25,
      actionUrl: '',
      uploadParams: {},
      uploadUrl: '',
      tableData: [{}],
      ingredientList: [], // 食材列表
      allSelectIngredient: [], // 所有选中的食材的集合
      errorMsg: { // 独立的form表单错误提示
        percentageError: ''
      },
      needCreateIngredients: [], // 需要创建的食材
      disabledIngredient: [],
      foodCategoryList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      showFoodImg: true
    }
  },
  created() {
    this.type = this.$route.query.type
    this.id = this.$route.query.id
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      await this.getIngredientslist()
      await this.foodFoodCategoryList()
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        this.formData.id = data.id
        this.formData.name = data.name
        this.formData.aliasName = data.alias_name
        this.formData.attributes = data.attributes
        if (data.taste_list) {
          this.formData.tasteList = data.taste_list.map(v => {
            return v.name
          })
        }
        this.formData.imageList = [data.image].concat(data.extra_image)
        let allImg = [data.image].concat(data.extra_image)
        allImg.forEach(item => {
          this.fileLists.push({
            url: item
          })
        })
        await this.getFoodIngredient()
        this.initIngredient(data)
        this.setNutritonData(data)
        this.isDisabledOtherIngredients()
      } else {
        this.initIngredient()
        this.setNutritonData({})
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
    }, 300),
    // 设置营养的数据
    setNutritonData(row) {
      if (!row.nutrition) row.nutrition = {}
      let element = row.nutrition.element ? JSON.parse(replaceSingleQuote(row.nutrition.element)) : {}
      let vitamin = row.nutrition.vitamin ? JSON.parse(replaceSingleQuote(row.nutrition.vitamin)) : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.formData, nutrition.key, row.nutrition[nutrition.key] ? row.nutrition[nutrition.key] : 0)
        }
        if (nutrition.type === 'element') {
          this.$set(this.formData, nutrition.key, element[nutrition.key] ? element[nutrition.key] : 0)
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.formData, nutrition.key, vitamin[nutrition.key] ? vitamin[nutrition.key] : 0)
        }
      })
    },
    // 获取系统食材列表
    async getIngredientslist() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminIngredientIngredientNamePost({
        page: 1,
        page_size: 999999
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.ingredientList = res.data
        // this.initIngredient()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取需要创建食材的信息
    async getFoodIngredient() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodGetFoodIngredientPost({
        id: this.formData.id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data) {
          this.needCreateIngredients = res.data
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化下食材占比
    initIngredient(data) {
      this.formData.ingredientList = []
      if (this.type === 'add') {
        this.formData.ingredientList.push({
          index: this.formData.ingredientList.length,
          selectId: '',
          percentage: 0,
          selectFoodIngredient: deepClone(this.ingredientList)
        })
      } else if (data) {
        this.setDiffIngredient(data.ingredients_list)
        this.formData.ingredientList = data.ingredients_list.map((v, index) => {
          v.index = index
          v.selectId = Number(v.ingredient_id)
          v.percentage = v.ingredient_scale
          v.selectFoodIngredient = deepClone(this.ingredientList)
          return v
        })
        this.isDisabledOtherIngredients()
      }
    },
    // 将详情中有的食材信息但食材接口中没有的添加到列表中（为了在显示）
    setDiffIngredient(list) {
      let listObj = {}
      list.forEach(item => {
        listObj[item.ingredient_name] = item
      })
      this.disabledIngredient = []
      this.needCreateIngredients.map(v => {
        if (v.need_create === 1 && listObj[v.name]) {
          this.disabledIngredient.push(Number(listObj[v.name].ingredient_id))
        } else {
          if (listObj[v.name] && v.id) { // 需要更新下id
            listObj[v.name].ingredient_id = v.id
          }
        }
      })
      Object.keys(listObj).forEach(key => {
        if (this.ingredientList.every(v => key !== v.name)) {
          this.ingredientList.push({
            id: Number(listObj[key].ingredient_id),
            name: key,
            need_create: key.need_create
          })
        }
      })
    },
    // 格式化下参数
    formatParams() {
      let params = {
        name: this.formData.name,
        alias_name: this.formData.aliasName,
        attributes: this.formData.attributes,
        taste_list: this.formData.tasteList,
        // formData.imageList的第一张图片传给image，剩余的传给extra_image，因为图片列表是分两个字段上传的，为了兼容原来只有一张图片的情况
        image: this.formData.imageList[0], // 菜品主图
        ingredient_list: [],
        nutrition_info: {},
        add_system_food_id: this.formData.id,
        category_id: this.formData.categoryId
      }
      let imageList = deepClone(this.formData.imageList)
      params.extra_image = imageList.splice(1, this.formData.imageList.length - 1) // 菜品其他图片
      if (this.type === 'modify') {
        // params.id = this.formData.id
      }
      if (this.formData.food_id && this.type === 'modify') {
        params.food_id = this.formData.food_id
      }
      this.formData.ingredientList.map(v => {
        if (v.selectId) {
          let obj = {
            ingredient_scale: v.percentage
          }
          if (!this.disabledIngredient.includes(v.selectId)) {
            obj.ingredient_id = v.selectId
            v.selectFoodIngredient.forEach(item => {
              if (item.id === v.selectId) {
                obj.is_enable_nutrition = item.is_enable_nutrition
                obj.sort_id = item.sort
                if (item.nutrition_info) {
                  for (let k in item.nutrition_info) {
                    obj[k] = item.nutrition_info[k]
                  }
                }
              }
            })
          } else {
            obj.ingredient_name = v.ingredient_name
            this.needCreateIngredients.forEach(item => {
              for (let k in item) {
                if (!item.id) {
                  obj[k] = item[k]
                }
              }
            })
          }
          params.ingredient_list.push(obj)
        }
      })
      // 营养
      let element = {}
      let vitamin = {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          params.nutrition_info[nutrition.key] = this.formData[nutrition.key]
        }
        if (nutrition.type === 'element') {
          element[nutrition.key] = this.formData[nutrition.key]
        }
        if (nutrition.type === 'vitamin') {
          vitamin[nutrition.key] = this.formData[nutrition.key]
        }
      })
      params.nutrition_info.element = JSON.stringify(element)
      params.nutrition_info.vitamin = JSON.stringify(vitamin)

      return params
    },
    // 添加食材
    addIngredients() {
      this.formData.ingredientList.push({
        index: this.formData.ingredientList.length,
        selectId: '',
        percentage: 0,
        selectFoodIngredient: deepClone(this.ingredientList)
      })
      this.isDisabledOtherIngredients()
    },
    // 删除食材
    deleteIngredientHandle(index) {
      this.formData.ingredientList.splice(index, 1)
      this.isDisabledOtherIngredients()
      this.computedNutritionAndPercentage()
    },
    // 食材选择修改
    changeIngredient(val) {
      let obj = {}
      this.ingredientList.map(item => {
        if (item.id === val) {
          obj = item
        }
      })
      this.formData.ingredientList.forEach(item => {
        if (item.selectId === obj.id) {
          item.nutrition = obj.nutrition_info
        }
      })
      this.isDisabledOtherIngredients()
      this.computedNutritionAndPercentage()
    },
    // 设置食材disabled
    isDisabledOtherIngredients() {
      this.allSelectIngredient = []
      this.formData.ingredientList.map((item, k) => {
        if (item.selectId) {
          this.allSelectIngredient.push(item.selectId)
        }
      })
      this.formData.ingredientList.forEach((v, k) => {
        v.selectFoodIngredient.forEach(item => {
          if (this.allSelectIngredient.includes(item.id) && v.selectId !== item.id) {
            item.disabled = true
          } else {
            item.disabled = false
          }
        })
      })
    },
    // 计算营养和食材占比
    computedNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      let percentageCount = 0
      this.formData.ingredientList.map((v, index) => {
        if (v.selectId) {
          // 计算食材占比 按100克计算
          if (index < this.allSelectIngredient.length - 1) {
            v.percentage = parseInt(NP.divide(100, this.allSelectIngredient.length))
            percentageCount = NP.plus(v.percentage, percentageCount)
          } else {
            v.percentage = parseInt(NP.minus(100, percentageCount))
          }
          const percentage = v.percentage / 100
          if (!v.nutrition) {
            v.nutrition = {}
          }
          objNutrition.energy_mj = +v.nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, v.nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = +v.nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, v.nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = +v.nutrition.protein ? NP.plus(objNutrition.protein, v.nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = +v.nutrition.axunge ? NP.plus(objNutrition.axunge, v.nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = +v.nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, v.nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          if (v.nutrition.element && v.nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(v.nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(v.nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
          if (this.deepFormIngredients && this.deepFormIngredients.length) {
            this.deepFormIngredients.forEach(item => {
              if (item.id === v.id) {
                v.status = true
              }
            })
          }
        }
      })

      this.nutritionList.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.formData, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.formData, item.key, objNutrition[item.key])
        }
      })
    },
    // 计算营养和食材占比
    setNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      this.formData.ingredientList.map((v, index) => {
        if (v.selectId) {
          // 计算食材占比 按100克计算
          if (!v.nutrition) {
            v.nutrition = {}
          }
          const percentage = v.percentage / 100
          objNutrition.energy_mj = +v.nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, v.nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = +v.nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, v.nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = +v.nutrition.protein ? NP.plus(objNutrition.protein, v.nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = +v.nutrition.axunge ? NP.plus(objNutrition.axunge, v.nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = +v.nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, v.nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          if (v.nutrition.element && v.nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(v.nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(v.nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
        }
      })

      this.nutritionList.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.formData, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.formData, item.key, objNutrition[item.key])
        }
      })
    },
    // 食材占比
    changePercentage(e) {
      this.setNutritionAndPercentage()
      let percentageCount = this.formData.ingredientList.reduce((total, current) => {
        return NP.plus(current.percentage, total)
      }, 0)
      if (percentageCount > 100 || percentageCount < 100) {
        this.errorMsg.percentageError = '菜品每100g所含食材占比，相加必须等于100%'
      } else {
        this.errorMsg.percentageError = ''
      }
    },
    // 口味
    closeTasteHandle(tag) {
      this.formData.tasteList.splice(this.formData.tasteList.indexOf(tag), 1)
    },
    // 口味
    showTasteInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    // 口味
    inputTasteConfirm() {
      let inputValue = this.inputValue
      if (inputValue) {
        this.formData.tasteList.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    // 预览图片
    perviewFoodImg(item) {
      this.dialogImageUrl = item;
      this.dialogVisible = true;
    },
    // 移除图片
    removeFoodImg(index) {
      this.formData.imageList.splice(index, 1)
      this.fileLists.splice(index, 1)
    },
    uploadSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.fileLists = fileList
        this.formData.imageList.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 上传图片
    beforeFoodImgUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = (file.size / 1024 / 1024) < 5
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return isJPG && isLt5M
    },
    // 移除图片
    remove() {
      this.formData.image = ''
    },
    // 上传图片成功回调
    getSuccessUploadRes(res) {
      // 每次上传成功先清空上一次的结果，否则下一次无法再次选择
      this.$refs.uploadImg.clearHandle()
      if (res.length) {
        this.formData.image = res[0].url
      }
    },
    // 提交数据
    submitHandler() {
      this.$refs.foodRef.validate(valid => {
        if (valid && !this.errorMsg.percentageError) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (this.type === 'modify') {
            // this.modifyFoodList()
            confirm({ content: '请确认菜品食材信息，确定要创建到菜品/商品库？' }, this.addFoodList)
          } else {
            confirm({ content: '是否确定创建该菜品？' }, this.addFoodList)
          }
        } else {
          console.log('error validate')
          this.$message.error('请认真检查数据格式！')
        }
      })
    },
    // 添加
    async addFoodList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodAddPost(this.formatParams()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else if (res.code === 2) {
        this.formData.food_id = res.data.food_id
        confirm({ content: res.msg }, this.addFoodList).catch(e => {
          this.formData.food_id = ''
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyFoodList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodModifyPost(this.formatParams()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 关闭当前页面
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 添加菜品别名
    addFoodAliasName() {
      this.formData.aliasName.push('')
    },
    delFoodAliasName(index) {
      this.formData.aliasName.splice(index, 1);
    },
    // 二级列表
    async foodFoodCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryListPost({
          page: 1,
          page_size: 999999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodCategoryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
.super-add-commodity {
  .block-label {
    width: 100%;
    .el-form-item__label {
      display: block;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
  }
  .form-content-flex{
    .el-form-item__content{
      display: flex;
    }
  }
  .add-btn-img{
    width:25px;
    height:25px;
    margin:3px 0 0 10px;
  }
  .food-alias-name-form{
    margin-bottom: 0px!important;
    .el-form-item__content{
      display: flex;
      align-items: center;
      img{
        width: 25px;
        height: 25px;
        margin-left: 10px;
      }
    }
  }
  .upload-block-label{
    .el-form-item__content{
      padding: 20px;
      background-color: #fff;
      border-radius: 4px;
    }
    .el-form-item__label{
      display: block;
      width: 100%;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
    .inline-block{
      margin-top: 15px;
    }
    .upload-w{
      width: 224px;
      height: 142px;
      border-radius: 4px;
      border: solid 1px #e0e6eb;
      text-align: center;
      vertical-align: top;
      // display: flex;
      // justify-content: center;
      // align-items: center;
    }
    .el-upload-dragger{
      width: 224px;
      height: 142px;
    }
    .avatar {
      display: block;
      width: 100%;
      max-height: 142px;
    }
    .upload-t{
      vertical-align: top;
      margin-top: 35px;
      color: #ff9b45;
      .el-icon-circle-plus{
        font-size: 30px;
        color: #ff9b45;
      }
    }
    .upload-tips{
      margin-top: 30px;
      padding-left: 20px;
      color: #9fa7ad;
    }
  }
  .food-img-wrap{
    display: flex;
    flex-wrap: wrap;
    .food-img-item{
      width: 150px;
      height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: solid 1px #e0e6eb;
      margin: 20px 20px 0 0;
      overflow: hidden;
      position: relative;
      img{
        max-width: 150px;
        max-height: 150px;
      }
      .food-img-mask{
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: #2e313474;
        display: none;
        line-height: 150px;
        text-align: center;
        font-size: 30px;
        color: #FFF;
        i{
          cursor: pointer;
        }
      }
    }
    .food-img-item:hover{
      .food-img-mask{
        display: block;
      }
    }
  }
  .tip-o-7{
    font-size: 14px;
    color: #23282d;
    opacity: 0.7;
  }
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-right: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  .cantent {
    flex: 1;
    text-align: center;
    // color: #fff;
  }
}
</style>

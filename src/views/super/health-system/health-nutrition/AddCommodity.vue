<template>
  <div class="super-add-commodity container-wrapper">
    <el-form v-loading="isLoading" :rules="formRuls" :model="formData" ref="foodRef" class="" size="small">
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div style="max-width: 50%; padding: 0 20px">
          <el-form-item label="菜品/商品名称" prop="name" class="block-label form-content-flex">
            <el-input
              v-model="formData.name"
              placeholder="请输入菜品/商品名称"
              class="ps-input"
              style="width: 80%"
            ></el-input>
            <el-tooltip effect="dark" content="增加菜品别名" placement="top">
              <img class="add-btn-img" @click="addFoodAliasName" src="@/assets/img/plus.png" alt="">
            </el-tooltip>
          </el-form-item>
          <div v-if="formData.aliasName.length">
            <el-form-item label="菜品别名：" class="block-label">
              <el-form-item
                :class="[index>0?'m-t-10':'','food-alias-name-form']"
                v-for="(item,index) in formData.aliasName"
                :key="index"
                :rules="formRuls.aliasName"
                :prop="`aliasName[${index}]`">
                <el-input style="width: 80%" v-model="formData.aliasName[index]" placeholder="请输入菜品别名" class="ps-input"></el-input>
                <img src="@/assets/img/plus.png" @click="addFoodAliasName" alt="">
                <img src="@/assets/img/reduce.png" @click="delFoodAliasName(index)" alt="">
              </el-form-item>
            </el-form-item>
          </div>
          <el-form-item label="属性" prop="attributes" class="block-label">
            <!-- <el-select
              v-model="formData.attributes"
              filterable
              allow-create
              style="width: 100%;"
              placeholder="请选择规格">
              <el-option value="goods" label="商品"></el-option>
              <el-option value="foods" label="菜品"></el-option>
            </el-select> -->
            <el-radio-group v-model="formData.attributes" class="ps-radio">
              <el-radio label="goods">商品</el-radio>
              <el-radio label="foods">菜品</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="分类：" prop="categoryId">
            <el-select
              v-model="formData.categoryId"
              placeholder="请下拉选择分类"
              class="ps-select"
              popper-class="ps-popper-select"
              collapse-tags
              clearable
              style="width: 190px;"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in foodCategoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="口味" prop="taste" class="">
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              style="margin-right: 10px;"
              @keyup.enter.native="inputTasteConfirm"
              @blur="inputTasteConfirm"
            ></el-input>
            <el-button v-else class="ps-btn button-new-tag" type="primary" icon="el-icon-plus" @click="showTasteInput">添加</el-button>
            <el-tag
              :key="tag"
              v-for="tag in formData.tasteList"
              closable
              :disable-transitions="false"
              @close="closeTasteHandle(tag)"
              color="#fff"
            >
              {{ tag }}
            </el-tag>
          </el-form-item>
          <el-form-item label="标签" prop="" class="">
            <el-button
              class="ps-origin-btn"
              type="primary"
              size="small"
              @click="labelClick"
            >
              选择标签
            </el-button>
          </el-form-item>
          <el-form-item :label="`${groupKey}:`" prop="" class="" v-for="(labelGroupItem,groupKey,labelGroupIndex) in formData.labelGroupInfoList" :key="labelGroupIndex">
            <el-tag
              class="m-r-5 collapse-data"
              v-for="(item, index) in labelGroupItem"
              :key="index"
              size="medium"
              effect="plain"
              type="info"
              color="#fff"
              closable
              @close="closeTag(groupKey,index,item)"
            >
              <!-- light -->
              {{item.name}}
            </el-tag>
          </el-form-item>
        </div>
        <div style="max-width: 900px;padding-left: 20px;">
          <el-form-item label="菜品/商品图片" class="upload-block-label upload-hidden">
            <div class="inline-block upload-w">
              <el-upload
                :class="{'file-upload': true, 'hide-upload':formData.foodImagesList.length>0}"
                ref="fileUpload"
                drag
                :action="serverUrl"
                :data="uploadParams"
                :file-list="formData.foodImagesList"
                :on-success="handleFoodImgSuccess"
                :on-change="handelChange"
                :before-upload="beforeFoodImgUpload"
                :limit="1"
                list-type="picture-card"
                :multiple="false"
                :headers="headersOpts"
                accept=".jpeg,.jpg,.png,.bmp"
                >
                <div v-if="fileLists.length<1" class="upload-t">
                  <i class="el-icon-circle-plus"></i>
                  <div class="el-upload__text">
                    上传菜品/商品图片
                  </div>
                </div>
                <div slot="file" slot-scope="{file}" v-loading="file.status==='uploading'" element-loading-text="上传中">
                  <div class="upload-food-img">
                    <el-image
                      class="el-upload-dragger"
                      :src="file.url"
                      fit="contain">
                    </el-image>
                  </div>
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'foodImages')">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
            </div>
            <div class="inline-block upload-tips">
              上传：菜品/商品图片。<br />
              建议图片需清晰，图片内容与名称相符。<br />
              仅支持jpg、png、bmp格式，大小不超过5M
            </div>
          </el-form-item>
          <!-- <el-form-item label="识别图片" class="upload-block-label">
            <div class="">
              <el-upload
                class="extra-upload"
                ref="extraUpload"
                drag
                :action="serverUrl"
                :data="uploadParams"
                :file-list="formData.extraImagesList"
                :on-success="handleExtraImgSuccess"
                :on-change="handelChange"
                :before-upload="beforeFoodImgUpload"
                :limit="25"
                list-type="picture-card"
                :multiple="true"
                :headers="headersOpts"
                >
                <div v-if="formData.extraImages.length<25" class="upload-t">
                  <i class="el-icon-circle-plus"></i>
                  <div class="el-upload__text">
                    上传识别图片
                  </div>
                </div>
                <div slot="file" slot-scope="{file}">
                  <div class="upload-food-img">
                    <el-image
                      class="el-upload-dragger"
                      :src="file.url"
                      fit="contain">
                    </el-image>
                  </div>
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleImgRemove(file, 'extraImages')">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
            </div>
          </el-form-item> -->
          <el-form-item label="识别图片" class="upload-block-label">
            <div class="inline-block upload-w">
              <el-upload
                class="file-upload"
                ref="fileUpload"
                v-loading="uploadingExtra"
                element-loading-text="上传中"
                drag
                :action="serverUrl"
                :data="uploadParams"
                :file-list="formData.extraImagesList"
                :on-success="handleExtraImgSuccess"
                :on-change="handelChange"
                :before-upload="beforeExtraImgUpload"
                :limit="25"
                :multiple="true"
                :show-file-list="false"
                :headers="headersOpts"
                accept=".jpeg,.jpg,.png,.bmp"
                >
                  <div class="upload-t">
                    <i class="el-icon-circle-plus"></i>
                    <div class="el-upload__text">
                      上传识别图片
                    </div>
                  </div>
              </el-upload>
            </div>
            <div class="inline-block upload-tips">
              上传：识别图片。最多25张<br />
              建议图片需清晰，图片内容与名称相符。<br />
              仅支持jpg、png、bmp格式，大小不超过5M
            </div>
            <div v-show="showFoodImg" @click="showFoodImg = false" style="cursor: pointer;">
              查看已上传的图片（{{formData.extraImages.length}}张）
              <i class="el-icon-arrow-up"></i>
            </div>
            <div v-show="!showFoodImg" @click="showFoodImg = true" style="cursor: pointer;">
              查看已上传的图片（{{formData.extraImages.length}}张）
              <i class="el-icon-arrow-down"></i>
            </div>
            <div v-show="showFoodImg" class="food-img-wrap">
              <div class="food-img-item" v-for="(item, index) in formData.extraImages" :key="index">
                <img :src="item" alt="" srcset="">
                <div class="food-img-mask">
                  <i @click="perviewFoodImg(item)" class="el-icon-zoom-in"></i>
                  <i @click="handleImgRemove({url:item}, 'extraImages')" class="el-icon-delete"></i>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            食材占比
            <span class="tip-o-7">（菜品每100g所含食材占比，相加必须等于100%）</span>
            <el-button class="ps-btn float-r" style="margin-right: 15px;" type="primary" size="small" icon="el-icon-plus" @click="addIngredients">添加</el-button>
          </div>
        </div>
        <div :class="['table-content', errorMsg.percentageError?'error-border':'']">
          <el-table
          v-loading="isLoading"
          :data="formData.ingredientList"
          ref="tableData"
          style="width: 100%"
          header-row-class-name="ps-table-header-row"
          >
            <el-table-column prop="no" label="食材" align="center">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.selectId"
                  placeholder="请下拉选择"
                  class="ps-select margin-right"
                  collapse-tags
                  clearable
                  filterable
                  @change="changeIngredient"
                >
                  <el-option
                    v-for="ingredientItem in scope.row.selectFoodIngredient"
                    :key="ingredientItem.id"
                    :label="ingredientItem.name"
                    :value="ingredientItem.id"
                    :disabled="ingredientItem.disabled"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="占比" align="center">
              <template slot-scope="scope">
              <!-- <el-progress
                :percentage="50"
                color="#fd953c"
                class="cantent"
              ></el-progress> -->
                <div class="cantent ps-flex-align-c flex-align-c">
                  <el-slider class="cantent" v-model="scope.row.percentage" @change="changePercentage" show-input></el-slider>%
                  <!-- <div style="width: 60px;">{{scope.row.percentage}}%</div> -->
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="xx" label="操作" align="center" width="180px">
              <template slot-scope="scope">
                <el-button
                type="text"
                size="small"
                class="ps-warn-text"
                @click="deleteIngredientHandle(scope.row.index)"
              >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="color: red;padding: 20px;" v-if="errorMsg.percentageError">{{errorMsg.percentageError}}</div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">
            营养信息
          </div>
        </div>
        <div class="table-content">
          <template v-for="nutrition in currentNutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
              <!-- :rules="formRuls.nutrition" -->
              <el-form-item :prop="nutrition.key">
                <el-input
                  style="width: 120px;"
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                  readonly
                  disabled
                ></el-input>
                <span style="margin-left: 10px;">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
          <div class="text-center pointer">
            <span @click="showAll = !showAll" style="color:#027DB4;">{{ showAll ? '收起' : '查看更多营养信息' }}</span>
          </div>
        </div>
      </div>
      <div class="footer" style="margin-top: 20px;">
        <el-button style="width: 120px;" :disabled="isLoading" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px;" type="primary" :disabled="isLoading" @click="submitHandler">{{type === 'add' ? '添加' : '编辑'}}</el-button>
      </div>
    </el-form>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <select-laber
      v-if="selectLaberDialogVisible"
      :isshow.sync="selectLaberDialogVisible"
      width="600px"
      @selectLaberData="selectLaberData"
      :ruleSingleInfo="ruleSingleInfo"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone, replaceSingleQuote, getToken, getSuffix } from '@/utils'
import { NUTRITION_LIST } from './constants'
import NP from 'number-precision'
// import { dtConfirm } from '@/utils/decorator'
import { confirm } from '@/utils/message'
import selectLaber from '../components/selectLaber.vue'
export default {
  name: 'SuperAddIngredients',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    let validataNutrition = (rule, value, callback) => {
      if (value) {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('营养数据有误，仅支持保留两位小数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let validatorImage = (rule, value, callback) => {
      if (!this.formData.imageList.length) {
        return callback(new Error('请上传菜品图片'))
      } else {
        callback()
      }
    }
    return {
      type: 'add',
      isLoading: false, // 刷新数据
      formData: {
        name: '', // 菜品名
        aliasName: [], // 菜品别名
        attributes: 'foods',
        tasteList: [],
        foodImages: [],
        foodImagesList: [],
        extraImages: [],
        extraImagesList: [],
        ingredientList: [], // 食材组成
        food_id: '', // 重复的id
        categoryId: '',
        selectLabelListData: [], // 标签列表
        selectLabelIdList: [], // 标签id列表
        labelGroupInfoList: {} // 带标签组名字的数据
      },
      fileLists: [],
      serverUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      formRuls: {
        name: [{ required: true, message: '食材名称不能为空', trigger: 'blur' }],
        aliasName: [{ required: true, message: '请输入菜品别名', trigger: 'blur' }],
        attributes: [{ required: true, message: '请选择属性', trigger: 'blur' }],
        nutrition: [{ validator: validataNutrition, trigger: 'change' }],
        imageList: [{ required: true, validator: validatorImage, trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择分类', trigger: 'blur' }]
      },
      nutritionList: NUTRITION_LIST,
      inputVisible: false,
      inputValue: '',
      limit: 25,
      actionUrl: '',
      uploadParams: {
        prefix: 'super_food_img'
      },
      uploadUrl: '',
      tableData: [{}],
      ingredientList: [], // 食材列表
      allSelectIngredient: [], // 所有选中的食材的集合
      errorMsg: { // 独立的form表单错误提示
        percentageError: ''
      },
      selectLaberDialogVisible: false,
      // 点击当前添加标签每条的数据
      ruleSingleInfo: {},
      dialogImageUrl: '',
      dialogVisible: false,
      showFoodImg: true,
      foodCategoryList: [],
      showAll: false,
      uploading: false,
      uploadingExtra: false
    }
  },
  computed: {
    currentNutritionList: function () {
      let result = []
      if (!this.showAll) {
        result = this.nutritionList.slice(0, 4)
      } else {
        result = this.nutritionList
      }
      return result
    }
  },
  components: { selectLaber },
  created() {
    this.type = this.$route.query.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.isLoading = true
      await this.getIngredientslist()
      await this.foodFoodCategoryList()
      this.isLoading = false
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        console.log(data)
        this.formData.id = data.id
        this.formData.aliasName = data.alias_name
        this.formData.name = data.name
        this.formData.attributes = data.attributes
        this.formData.categoryId = data.category
        if (data.taste_list) {
          this.formData.tasteList = data.taste_list.map(v => {
            return v.name
          })
        }
        //  回显示标签组名字 {'aa':[{xx:xx}]}
        if (data.label.length) {
          // 格式化标签
          this.initLabelGroup(data.label)
        }
        this.formData.selectLabelListData = data.label
        this.formData.selectLabelIdList = data.label.map(v => { return v.id })
        // 展示图片
        if (data.image) {
          this.formData.foodImages = [data.image]
          this.formData.foodImagesList = [
            {
              url: data.image,
              name: data.image,
              status: "success",
              uid: data.image
            }
          ]
        }
        // 识别图片
        if (data.extra_image) {
          data.extra_image.forEach(item => {
            this.formData.extraImagesList.push({
              url: item,
              name: item,
              status: "success",
              uid: item
            })
          })
          this.formData.extraImages = data.extra_image
        }

        this.initIngredient(data)
        this.setNutritonData(data)
        this.isDisabledOtherIngredients()
      } else {
        this.initIngredient()
        this.setNutritonData({})
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
    }, 300),
    // 设置营养的数据
    setNutritonData(row) {
      if (!row.nutrition) row.nutrition = {}
      let element = row.nutrition.element ? JSON.parse(replaceSingleQuote(row.nutrition.element)) : {}
      let vitamin = row.nutrition.vitamin ? JSON.parse(replaceSingleQuote(row.nutrition.vitamin)) : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.formData, nutrition.key, row.nutrition[nutrition.key] ? row.nutrition[nutrition.key] : 0)
        }
        if (nutrition.type === 'element') {
          this.$set(this.formData, nutrition.key, element[nutrition.key] ? element[nutrition.key] : 0)
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.formData, nutrition.key, vitamin[nutrition.key] ? vitamin[nutrition.key] : 0)
        }
      })
    },
    // 获取系统食材列表
    async getIngredientslist() {
      const [err, res] = await to(this.$apis.apiBackgroundAdminIngredientIngredientNamePost({
        page: 1,
        page_size: 999999
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.ingredientList = res.data
        // this.initIngredient()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化下食材占比
    initIngredient(data) {
      this.formData.ingredientList = []
      if (this.type === 'add') {
        this.formData.ingredientList.push({
          index: this.formData.ingredientList.length,
          selectId: '',
          percentage: 0,
          selectFoodIngredient: deepClone(this.ingredientList)
        })
      } else if (data) {
        this.formData.ingredientList = data.ingredients_list.map((v, index) => {
          v.index = index
          v.selectId = Number(v.ingredient_id)
          v.percentage = v.ingredient_scale
          v.selectFoodIngredient = deepClone(this.ingredientList)
          this.ingredientList.map(item => {
            if (item.id === v.selectId) {
              v.nutrition = item.nutrition_info
            }
          })
          return v
        })
        this.isDisabledOtherIngredients()
      }
    },
    // 格式化下参数
    formatParams() {
      let params = {
        name: this.formData.name,
        alias_name: this.formData.aliasName,
        attributes: this.formData.attributes,
        taste_list: this.formData.tasteList,
        // formData.imageList的第一张图片传给image，剩余的传给extra_image，因为图片列表是分两个字段上传的，为了兼容原来只有一张图片的情况
        image: this.formData.foodImages[0], // 菜品主图
        extra_image: this.formData.extraImages, // 菜品其他图片
        label_list: this.formData.selectLabelIdList,
        ingredient_list: [],
        nutrition_info: {},
        category_id: this.formData.categoryId
      }
      if (this.type === 'modify') {
        params.id = this.formData.id
      }
      if (this.formData.food_id && this.type === 'add') {
        params.food_id = this.formData.food_id
      }
      this.formData.ingredientList.map(v => {
        if (v.selectId) {
          let obj = {
            ingredient_id: v.selectId,
            ingredient_scale: v.percentage
          }
          params.ingredient_list.push(obj)
        }
      })
      // 营养
      let element = {}
      let vitamin = {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          params.nutrition_info[nutrition.key] = this.formData[nutrition.key]
        }
        if (nutrition.type === 'element') {
          element[nutrition.key] = this.formData[nutrition.key]
        }
        if (nutrition.type === 'vitamin') {
          vitamin[nutrition.key] = this.formData[nutrition.key]
        }
      })
      params.nutrition_info.element = JSON.stringify(element)
      params.nutrition_info.vitamin = JSON.stringify(vitamin)
      return params
    },
    // 添加食材
    addIngredients() {
      this.formData.ingredientList.push({
        index: this.formData.ingredientList.length,
        selectId: '',
        percentage: 0,
        selectFoodIngredient: deepClone(this.ingredientList)
      })
      this.isDisabledOtherIngredients()
    },
    // 删除食材
    deleteIngredientHandle(index) {
      this.formData.ingredientList.splice(index, 1)
      this.isDisabledOtherIngredients()
      this.computedNutritionAndPercentage()
      this.changePercentage()
    },
    // 食材选择修改
    changeIngredient(val) {
      let obj = {}
      this.ingredientList.map(item => {
        if (item.id === val) {
          obj = item
        }
      })
      this.formData.ingredientList.forEach(item => {
        if (item.selectId === obj.id) {
          item.nutrition = obj.nutrition_info
        }
      })
      this.errorMsg.percentageError = ''
      this.isDisabledOtherIngredients()
      this.computedNutritionAndPercentage()
    },
    // 设置食材disabled
    isDisabledOtherIngredients() {
      this.allSelectIngredient = []
      this.formData.ingredientList.map((item, k) => {
        if (item.selectId) {
          this.allSelectIngredient.push(item.selectId)
        }
      })
      this.formData.ingredientList.forEach((v, k) => {
        v.selectFoodIngredient.forEach(item => {
          if (this.allSelectIngredient.includes(item.id) && v.selectId !== item.id) {
            item.disabled = true
          } else {
            item.disabled = false
          }
        })
      })
    },
    // 计算营养和食材占比
    computedNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      let percentageCount = 0
      this.formData.ingredientList.map((v, index) => {
        if (v.selectId) {
          // 计算食材占比 按100克计算
          if (index < this.allSelectIngredient.length - 1) {
            v.percentage = parseInt(NP.divide(100, this.allSelectIngredient.length))
            percentageCount = NP.plus(v.percentage, percentageCount)
          } else {
            v.percentage = parseInt(NP.minus(100, percentageCount))
          }
          const percentage = v.percentage / 100
          if (!v.nutrition) {
            v.nutrition = {}
          }
          // objNutrition.energy_mj = +v.nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, v.nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = +v.nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, v.nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = +v.nutrition.protein ? NP.plus(objNutrition.protein, v.nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = +v.nutrition.axunge ? NP.plus(objNutrition.axunge, v.nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = +v.nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, v.nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          if (v.nutrition.element && v.nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(v.nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(v.nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
          if (this.deepFormIngredients && this.deepFormIngredients.length) {
            this.deepFormIngredients.forEach(item => {
              if (item.id === v.id) {
                v.status = true
              }
            })
          }
        }
      })
      this.nutritionList.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.formData, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.formData, item.key, objNutrition[item.key])
        }
      })
    },
    // 计算营养和食材占比
    setNutritionAndPercentage() {
      let objNutrition = {}
      NUTRITION_LIST.forEach(v => {
        objNutrition[v.key] = 0
      })
      this.formData.ingredientList.map((v, index) => {
        if (v.selectId) {
          // 计算食材占比 按100克计算
          if (!v.nutrition) {
            v.nutrition = {}
          }
          const percentage = v.percentage / 100
          // objNutrition.energy_mj = +v.nutrition.energy_mj ? NP.plus(objNutrition.energy_mj, v.nutrition.energy_mj * percentage) : objNutrition.energy_mj ? objNutrition.energy_mj : 0
          objNutrition.energy_kcal = +v.nutrition.energy_kcal ? NP.plus(objNutrition.energy_kcal, v.nutrition.energy_kcal * percentage) : objNutrition.energy_kcal ? objNutrition.energy_kcal : 0
          objNutrition.protein = +v.nutrition.protein ? NP.plus(objNutrition.protein, v.nutrition.protein * percentage) : objNutrition.protein ? objNutrition.protein : 0
          objNutrition.axunge = +v.nutrition.axunge ? NP.plus(objNutrition.axunge, v.nutrition.axunge * percentage) : objNutrition.axunge ? objNutrition.axunge : 0
          objNutrition.carbohydrate = +v.nutrition.carbohydrate ? NP.plus(objNutrition.carbohydrate, v.nutrition.carbohydrate * percentage) : objNutrition.carbohydrate ? objNutrition.carbohydrate : 0
          if (v.nutrition.element && v.nutrition.vitamin) {
            try { // 防止JSON.parse出错
              let element = JSON.parse(replaceSingleQuote(v.nutrition.element))
              let vitamin = JSON.parse(replaceSingleQuote(v.nutrition.vitamin))
              // 找到对应的营养渲染 和累加进去
              for (const key in element) {
                objNutrition[key] = NP.plus(objNutrition[key], +element[key] ? element[key] * percentage : 0)
              }
              for (const key in vitamin) {
                objNutrition[key] = NP.plus(objNutrition[key], +vitamin[key] ? vitamin[key] * percentage : 0)
              }
            } catch (error) {
              // console.log('error', error)
            }
          }
        }
      })

      this.nutritionList.forEach(item => {
        let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(objNutrition[item.key])) {
          this.$set(this.formData, item.key, objNutrition[item.key].toFixed(2))
        } else {
          this.$set(this.formData, item.key, objNutrition[item.key])
        }
      })
    },
    // 食材占比
    changePercentage(e) {
      this.setNutritionAndPercentage()
      let percentageCount = this.formData.ingredientList.reduce((total, current) => {
        return NP.plus(current.percentage, total)
      }, 0)
      if (percentageCount > 100 || percentageCount < 100) {
        this.errorMsg.percentageError = '菜品每100g所含食材占比，相加必须等于100%'
      } else {
        this.errorMsg.percentageError = ''
      }
      if (!this.formData.ingredientList.length) {
        this.errorMsg.percentageError = ''
      }
    },
    // 口味
    closeTasteHandle(tag) {
      this.formData.tasteList.splice(this.formData.tasteList.indexOf(tag), 1)
    },
    // 口味
    showTasteInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    // 口味
    inputTasteConfirm() {
      let inputValue = this.inputValue
      if (inputValue) {
        this.formData.tasteList.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    labelClick() {
      this.ruleSingleInfo = {
        labelType: 'food',
        selectLabelIdList: this.formData.selectLabelIdList,
        selectLabelListData: this.formData.selectLabelListData
      }
      this.selectLaberDialogVisible = true
    },
    // 删除标签
    closeTag(key, index, item) {
      // 删除
      let idx = this.formData.selectLabelIdList.indexOf(item.id)
      let ids = this.formData.selectLabelListData.indexOf(item)
      this.formData.selectLabelIdList.splice(idx, 1)
      this.formData.selectLabelListData.splice(ids, 1)
      // 重置数据
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    // 选择标签
    selectLaberData(params) {
      this.formData.selectLabelIdList = params.selectLabelIdList
      this.formData.selectLabelListData = params.selectLabelListData
      this.formData.labelGroupInfoList = {}
      this.initLabelGroup(this.formData.selectLabelListData)
    },
    initLabelGroup(data) {
      data.forEach(v => {
        if (!this.formData.labelGroupInfoList[v.label_group_name]) {
          this.formData.labelGroupInfoList[v.label_group_name] = []
        }
        if (this.formData.labelGroupInfoList[v.label_group_name] && !this.formData.labelGroupInfoList[v.label_group_name].includes(v)) {
          this.formData.labelGroupInfoList[v.label_group_name].push(v)
        }
      })
    },
    handelChange(file, fileList) {
      this.uploadParams.key =
        this.uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    // 预览图片
    perviewFoodImg(item) {
      this.dialogImageUrl = item;
      this.dialogVisible = true;
    },
    handleFoodImgSuccess(res, file, fileList) {
      this.uploading = false
      if (res.code === 0) {
        this.formData.foodImagesList = fileList
        this.formData.foodImages.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExtraImgSuccess(res, file, fileList) {
      this.uploadingExtra = false
      if (res.code === 0) {
        this.formData.extraImagesList = fileList
        this.formData.extraImages.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleImgRemove(file, type) {
      let index = this.formData[type + 'List'].findIndex(item => item.url === file.url)
      this.formData[type].splice(index, 1)
      this.formData[type + 'List'].splice(index, 1)
    },
    beforeFoodImgUpload(file) {
      return this.beforeImgUpload(file, 'uploading')
    },
    beforeExtraImgUpload(file) {
      return this.beforeImgUpload(file, 'uploadingExtra')
    },
    beforeImgUpload(file, type) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 < 5
      console.log(getSuffix(file.name))
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是 JPG 格式或者PNG格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      if (type) this[type] = true
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 提交数据
    submitHandler() {
      this.$refs.foodRef.validate(valid => {
        if (valid && !this.errorMsg.percentageError) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          if (this.type === 'modify') {
            this.modifyFoodList()
          } else {
            confirm({ content: '是否确定创建该菜品？' }, this.addFoodList)
          }
        } else {
          console.log('error validate')
          this.$message.error('请认真检查数据格式！')
        }
      })
    },
    // 添加
    async addFoodList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodAddPost(this.formatParams()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else if (res.code === 2) {
        this.formData.food_id = res.data.food_id
        confirm({ content: res.msg }, this.addFoodList).catch(e => {
          this.formData.food_id = ''
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyFoodList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminFoodModifyPost(this.formatParams()))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 关闭当前页面
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 添加菜品别名
    addFoodAliasName() {
      this.formData.aliasName.push('')
    },
    delFoodAliasName(index) {
      this.formData.aliasName.splice(index, 1);
    },
    // 二级列表
    async foodFoodCategoryList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminFoodCategoryListPost({
          page: 1,
          page_size: 999999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.foodCategoryList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
.super-add-commodity {
  .block-label {
    width: 100%;
    .el-form-item__label {
      display: block;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
  }
  .form-content-flex{
    .el-form-item__content{
      display: flex;
    }
  }
  .add-btn-img{
    width:25px;
    height:25px;
    margin:3px 0 0 10px;
  }
  .food-alias-name-form{
    margin-bottom: 0px!important;
    .el-form-item__content{
      display: flex;
      align-items: center;
      img{
        width: 25px;
        height: 25px;
        margin-left: 10px;
      }
    }
  }
  .upload-hidden{
    overflow: hidden;
  }
  .upload-block-label{
    .el-form-item__content{
      padding: 20px 0 20px 20px;
      background-color: #fff;
      border-radius: 4px;
    }
    .el-form-item__label{
      display: block;
      width: 100%;
      text-align: left;
      line-height: 1.5;
      float: none;
    }
    .inline-block{
      margin-top: 15px;
    }
    .upload-w{
      width: 224px;
      height: 142px;
      border-radius: 4px;
      border: solid 1px #e0e6eb;
      text-align: center;
      vertical-align: top;
      // display: flex;
      // justify-content: center;
      // align-items: center;
    }
    .file-upload{
      height: 142px;
      .el-upload-list--picture-card .el-upload-list__item{
        width: 100%;
        height: 142px;
        border: none;
      }
      .el-upload--picture-card{
        width: auto;
        height: auto;
      }
      &.hide-upload{
        .el-upload--picture-card{
          display: none;
        }
        .el-upload-dragger{
          border: none;
        }
      }
    }
    .el-upload-dragger{
      width: 224px;
      height: 142px;
    }
    .avatar {
      display: block;
      width: 100%;
      max-height: 142px;
    }
    .upload-t{
      vertical-align: top;
      margin-top: 35px;
      line-height: 2;
      color: #ff9b45;
      .el-icon-circle-plus{
        font-size: 30px;
        color: #ff9b45;
      }
    }
    .upload-tips{
      margin-top: 30px;
      padding-left: 20px;
      color: #9fa7ad;
    }
  }
  .food-img-wrap{
    display: flex;
    flex-wrap: wrap;
    .food-img-item{
      width: 150px;
      height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: solid 1px #e0e6eb;
      margin: 20px 20px 0 0;
      overflow: hidden;
      position: relative;
      img{
        max-width: 150px;
        max-height: 150px;
      }
      .food-img-mask{
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: #2e313474;
        display: none;
        line-height: 150px;
        text-align: center;
        font-size: 30px;
        color: #FFF;
        i{
          cursor: pointer;
        }
      }
    }
    .food-img-item:hover{
      .food-img-mask{
        display: block;
      }
    }
  }
  .tip-o-7{
    font-size: 14px;
    color: #23282d;
    opacity: 0.7;
  }
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-right: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  .cantent {
    flex: 1;
    text-align: center;
    // color: #fff;
  }
}
</style>

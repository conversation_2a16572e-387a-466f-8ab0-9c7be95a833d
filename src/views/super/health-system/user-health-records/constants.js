// import { getDateRang } from '@/utils'
import * as dayjs from 'dayjs'

// getDateRang(-7)

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

export const USERHEALTHRECORDS = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '创建时间',
    value: [],
    clearable: false
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  company_ids: {
    type: 'CompanySelect',
    value: [],
    label: '组织',
    dataList: [],
    multiple: true,
    checkStrictly: true,
    collapseTags: true,
    clearable: true,
    companyOpts: {
      label: 'name',
      value: 'company'
    },
    // 前面添加全局用户筛选
    companyKey: 'all'
  },
  status: {
    type: 'select',
    value: '',
    label: '档案状态',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '使用中',
        value: 'enable'
      },
      {
        label: '注销中',
        value: 'logoff'
      }
    ]
  }
}
// 健康分
export const RADAROPTION = {
  // 中间添加文字
  title: {
    text: 0,
    x: 'center',
    y: 'center',
    textStyle: {
      color: '#fd953c',
      fontWeight: 'bolder',
      fontSize: 28
    }
  },
  tooltip: {
    trigger: 'axis'
  },
  // // 去除换行/n
  // formatter: function(params) {
  //   var names = ['食物多样性：', '营养均衡：', '能量摄入：', 'BMI：', '运动情况：']
  //   var data = ''
  //   for (var i = 0; i < params.data.value.length; i++) {
  //     data += names[i] + params.data.value[i] + '<br/>'
  //   }
  //   return params.data.name + '<br/>' + data
  // },
  radar: {
    name: {
      textStyle: {
        padding: [-10, -5] // 控制文字padding
      },
      // 注意 echarts 版本不一样 最新版是axisName:{color:"xxx"} 控制提示文字颜色
      color: '#23282d'
      // 格式化文字
      // formatter: function(text) {
      //   // 文字换行
      //   if (text === '营养均衡' || text === '运动情况') {
      //     text = text.replace(/\S{2}/g, function(match) {
      //       console.log(match)
      //       return match + '\n'
      //     })
      //   }
      //   return text
      // }
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        width: 1
        // 使用深浅的间隔色
        // color: ['#e1e5ec', '#e1e5ec']
      }
    },
    // 雷达图背景的颜色，在这儿随便设置了一个颜色，完全不透明度为0，就实现了透明背景
    splitArea: {
      show: false,
      areaStyle: {
        color: 'rgba(255,0,0,0)' // 图表背景的颜色
      }
    },
    // 默认数据
    indicator: [
      {
        name: '食物多样性',
        max: 100
      },
      {
        name: '营养均衡',
        max: 100
      },
      {
        name: '能量摄入',
        max: 100
      },
      {
        name: 'BMI',
        max: 100
      },
      {
        name: '运动情况',
        max: 100
      }
    ]
  },
  series: [
    {
      tooltip: {
        trigger: 'item'
      },
      type: 'radar',
      label: {
        show: false
      },
      areaStyle: {
        color: '#fad1ae' // 图表背景的颜色
      },
      data: [
        // 需要的数据
        {
          name: '健康分',
          value: [0, 0, 0, 0, 0]
        }
      ]
    }
  ],
  color: ['#fca255']
}
export const MEALTIME_SETTING = {
  tooltip: {
    trigger: 'item',
    borderColor: '#FCA155',
    textStyle: {
      color: '#000',
      fontWeight: 500
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      let marker = params.marker
      let percent = params.percent
      return marker + params.name + '&nbsp;&nbsp;&nbsp;' + percent + '%'
    }
  },
  title: {
    text: '0',
    x: 'center',
    y: 'center',
    top: '25%',
    textStyle: {
      color: '#fd953c',
      fontSize: 18
    }
  },
  legend: {
    top: '62%',
    // bottom: '30%',
    // left: '30%',
    // x: 'left',
    // formatter: function(name) {
    //   return name
    // },
    // icon: 'circle',
    orient: 'vertical',
    y: 'bottom',
    // itemWidth: 8, // 设置宽度
    // itemHeight: 8 // 设置高度
    padding: [
      0, // 上
      0, // 右
      0, // 下
      0 // 左
    ]
    // textStyle: {
    //   color: 'red',
    //   width: 100,
    //   overflow: 'truncate'
    // }
    // itemWidth: 50
  },

  series: [
    {
      // center: ['15%', '50%'],
      type: 'pie',
      radius: ['45%', '60%'],
      avoidLabelOverlap: false,
      top: '-10%',
      height: 200,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#f1f2f5',
        borderWidth: 3
      },
      hoverAnimation: false,
      label: {
        show: false,
        position: 'center'
        // normal: {
        //   formatter: '{{b},{d}%}'
        // }
      },
      emphasis: {
        label: {
          show: false,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ],
  color: ['#07DED0', '#FE985F', '#e98397', '#F97C95', '#58AFFE', '#F8C345']
}
export const BODY_DETAIL = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '创建时间',
    value: [],
    clearable: false
  }
}

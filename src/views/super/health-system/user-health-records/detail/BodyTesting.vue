<template>
  <div class="body-testing records-wrapp-bg">
    <div class="ps-flex-bw flex-align-c">
      <div class="ps-flex flex-wrap">
        <span class="p-r-10" style="font-weight: bold;">身体检测</span>
        <span class="testing-time">更新时间：{{ dartime }}</span>
      </div>
      <div class="ps-flex flex-align-c flex-wrap">
        <button-icon color="plain" type="Import">
          导入数据
        </button-icon>
        <button-icon color="plain" type="export" size="small">
          导出数据
        </button-icon>
        <div class="m-l-5">
          <el-button
            class="ps-origin-btn m-l-30"
            type="primary"
            size="mini"
            @click="gotoBodyDetail"
          >
            <div class="ps-flex flex-align-c">
              <i class="iconfont icon-gengduo el-icon--left" style="font-size:13px"></i>
              更多数据
            </div>
          </el-button>
        </div>
      </div>
    </div>
    <div style="font-weight: bold;">科室检查</div>
    <div class="inspect-wrapp">
      <div v-if="Object.keys(formData) && Object.keys(formData).length">
        <div v-for="(item, key, index) in formData" :key="index">
          <div class="l-title clearfix">
            <span> {{ item.name }}</span>
          </div>
          <div class="inspect-content  ps-flex flex-wrap">
            <div
              class="content-wrapp ps-flex-bw p-r-20 p-b-15"
              v-for="(info, infoKey, infoIndex) in item.children"
              :key="infoIndex"
            >
              <span class="text">{{ info.name }}：<span class="shuzi">{{ info.value }}</span></span>
              <span >-- {{ info.unit }}</span>
            </div>
          </div>
        </div>
      </div>
      <el-empty description="暂无数据" v-else></el-empty>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
export default {

  props: {
    formInfoData: {
      type: Array,
      default() {
        return []
      }
    },
    paramsInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      formData: {},
      dartime: '',
      aa: {
        基本信息: {
          姓名: '100(cm)',
          脉率: '100(cm)'
        },
        人体成分: {
          BMI: '300(cm)',
          基础代谢: '100(cm)'
        }
      }
    }
  },
  watch: {
    formInfoData(val) {
      // this.initLoad()
      this.formData = val
    }
  },
  created() {
    this.gartime()
  },
  mounted() {
    console.log(this.paramsInfo, 22)
  },
  methods: {
    gartime() {
      this.dartime = dayjs().format('YYYY-MM-DD hh-mm-ss')
    },
    gotoBodyDetail() {
      this.$router.push({
        name: 'SuperBodyDetail',
        query: { ...this.paramsInfo }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.records-wrapp-bg {
  background-color: #f8f9fa;
  box-shadow: 3px 3px 5px 0px rgba(202, 210, 221, 0.3), inset 1px 1px 0px 0px #ffffff;
  border-radius: 6px;
}
.body-testing {
  // width: 100%;
  height: 400px;
  padding: 10px 20px;
  margin-bottom: 20px;
  // min-width: 581px;
  width: 820px;
  .testing-time {
    font-size: 14px;
    color: #7b7c82;
  }
  .inspect-wrapp {
    max-height: 300px;
    overflow: auto;
    .inspect-content {
      display: flex;
      justify-content: space-between;
      padding: 15px 15px 0 15px;
      background-color: #f1f2f5;
      border-radius: 10px;
      overflow-y: auto;
      .text {
        color: #23282d;
        opacity: 0.5;
        font-size: 14px;
        .shuzi{
        color: #000;
        font-size: 16px;
      }
      }
      .content-wrapp {
        width: 33.33%;
      }
    }
    .inspect-content:after {
      content: '';
      flex: auto;
    }
  }

}
</style>

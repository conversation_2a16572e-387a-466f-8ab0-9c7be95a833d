<template>
  <div class="label-wrapp records-wrapp-bg m-r-20 m-b-20">
    <div class="p-b-10" style="font-weight: bold;">标签属性</div>
    <div class="label-form">
      <el-form ref="form" size="mini" :model="formData" label-position="left" label-width="90px">
        <el-form-item
          :label="item.name"
          class="p-b-10"
          v-for="(item, index) in labelList"
          :key="index"
        >
          <div v-if="item.key === 'ingredient_taboo'">
            <el-tag
              class="m-r-10"
              size="small"
              effect="plain"
              type="warning"
              color="#fff"
              v-for="(tagItem, tagIndex) in item.label_name"
              :key="tagIndex"
            >
              <i class="el-icon-warning  ps-i"></i>
              {{ tagItem }}
            </el-tag>
          </div>
          <div v-else-if="item.key === 'taste'">
            <el-tag
              class="m-r-10"
              size="small"
              :effect="tagItem.is_have ? 'plain' : 'dark'"
              type="info"
              v-for="(tagItem, tagIndex) in item.label_name"
              :key="tagIndex"
            >
              <div
                :style="{
                  color: tagItem.is_have ? '' : '#fff'
                }"
              >
                <span>{{ tagItem.name }}</span>
                <span v-if="tagItem.count">*{{ tagItem.count }}</span>
              </div>
            </el-tag>
          </div>
          <div v-else>
            <el-tag
              size="small"
              effect="plain"
              class="m-r-10"
              type="info"
              color="#fff"
              v-for="(tagItem, tagIndex) in item.label_name"
              :key="tagIndex"
            >
              {{ tagItem }}
            </el-tag>
          </div>
        </el-form-item>
        <!-- <el-form-item label="食材标签：" class="p-b-10">
          <el-tag size="mini" effect="plain" type="info" color="#fff">
            123
          </el-tag>
        </el-form-item> -->
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    formInfoData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      labelList: [],
      formData: {}
    }
  },
  watch: {
    formInfoData(val) {
      // this.initLoad()
      if (val.length) {
        this.labelList = []
        val.forEach(v => {
          if (v.label_name.length) {
            this.labelList.push(v)
          }
        })
      }
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.records-wrapp-bg {
  background-color: #f8f9fa;
  box-shadow: 3px 3px 5px 0px rgba(202, 210, 221, 0.3), inset 1px 1px 0px 0px #ffffff;
  border-radius: 6px;
}
.label-wrapp {
  padding: 10px 20px;
  // min-width: 400px;
  width: 400px;
  height: 400px;
  .label-form {
    height: 350px;
    overflow: auto;
  }
  .el-tag--plain.el-tag--info {
    border-color: #dae1ea;
  }
  .el-tag--plain.el-tag--warning {
    background-color: #fae8daec !important;
  }
}
</style>

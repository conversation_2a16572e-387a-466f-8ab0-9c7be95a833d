<template>
  <div class="records-detail">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="title-wrapp ps-flex-bw  flex-align-c">
      <div class="ps-flex flex-align-c">
        <div class="icon-box m-r-20">
          <i class="el-icon-s-order"></i>
        </div>
        <h4 class="m-r-20">用户健康档案</h4>
        <div class="title-time">档案使用：{{ useDate }}</div>
      </div>
      <button-icon color="origin" type="export">导出档案</button-icon>
    </div>
    <!-- 左边 -->
    <div class="ps-flex">
      <div class="content-left m-r-20">
        <!-- 基本属性 -->
        <basic-info :formInfoData="baseData" v-loading="isLoading" />
        <!-- 健康目标 -->
        <healthy-target :formInfoData="baseData" v-loading="isLoading" />
        <!-- 健康分 -->
        <healthy-score :formInfoData="baseData" v-loading="isLoading" />
        <!-- 所属组织 -->
        <healthy-org :formInfoData="baseData" v-loading="isLoading" />
        <!-- 习惯养成 -->
        <healthy-habit :formInfoData="habitData" v-loading="isLoading" />
      </div>
      <!-- 右边 -->
      <div class="content-right">
        <!-- 标签属性 -->
        <div class="ps-flex flex-wrap">
          <healthy-label :formInfoData="labelData" v-loading="isLoading" />
          <!-- 身体检查 -->
          <body-testing :paramsInfo="params" :formInfoData="physicalData" v-loading="isLoading" />
        </div>
        <!-- 饮食数据 -->
        <diet :formInfoData="nutrientIntakeData" v-loading="isLoading" />
        <!-- 运动数据 -->
        <motion :formInfoData="sportData" v-loading="isLoading" />
      </div>
    </div>
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import BasicInfo from './detail/BasicInfo'
import HealthyTarget from './detail/HealthyTarget'
import HealthyScore from './detail/HealthyScore'
import HealthyOrg from './detail/HealthyOrg'
import HealthyHabit from './detail/HealthyHabit'
import HealthyLabel from './detail/HealthyLabel'
import BodyTesting from './detail/BodyTesting'
import Diet from './detail/Diet'
import Motion from './detail/Motion'
// import { objectProperty } from '@babel/types'
export default {
  components: {
    BasicInfo,
    HealthyTarget,
    HealthyScore,
    HealthyOrg,
    HealthyHabit,
    HealthyLabel,
    BodyTesting,
    Diet,
    Motion
  },
  data() {
    return {
      isLoading: false,
      useDate: '',
      params: {},
      baseData: {},
      habitData: {},
      nutrientIntakeData: {},
      sportData: {},
      labelData: [],
      physicalData: []
    }
  },
  created() {
    this.params = this.$route.query
    this.searchHandle()
    // console.log(Object.defineProperty.toString.call(this.labelData));
  },
  mounted() {},
  methods: {
    // 节下流咯
    searchHandle: debounce(function() {
      this.getHealthyInfoDetails()
    }, 300),
    async getHealthyInfoDetails() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminHealthyInfoHealthyInfoDetailsPost(this.params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.useDate = res.data.use_date
        // 基本数据
        this.baseData = res.data.base_data
        // 习惯养成
        this.habitData = res.data.habit_data
        // 饮食数据
        this.nutrientIntakeData = res.data.nutrient_intake_data
        // 运动数据
        this.sportData = res.data.sport_data
        // 标签属性
        this.labelData = res.data.label_data
        // 身体检测
        this.physicalData = res.data.physical_data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      // this.$refs.searchRef.resetForm()
      // this.currentPage = 1
      // this.initLoad()
      console.log(123)
      this.searchHandle()
    }
  }
}
</script>

<style lang="scss" scoped>
.records-detail {
  .title-wrapp {
    .icon-box {
      width: 40px;
      height: 40px;
      border-radius: 50px;
      background-color: #fff;
      text-align: center;
      line-height: 48px;
      .el-icon-s-order {
        font-size: 25px;
        color: #fd953c;
      }
    }
    .title-time {
      padding: 8px 15px 8px 15px;
      background-color: #ffffff;
      border-radius: 19px;
      font-size: 13px;
    }
  }
  .content-left {
    flex: 1;
    min-width: 300px;
  }
  .content-right {
    flex: 1;
    width: 80%;
  }
}
</style>

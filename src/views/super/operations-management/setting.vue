<template>
  <div v-loading="isLoading" class="operations-setting-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="tab-box">
      <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
        <el-radio-button label="evalute">评价设置</el-radio-button>
        <el-radio-button label="customer">客服设置</el-radio-button>
      </el-radio-group>
    </div>
    <div class="operations-content">
      <transition-group :name="slideTransition">
        <div v-if="tabType === 'evalute'" key="evalute" class="setting-box">
          <div>可评价时间：</div>
          <el-form
            ref="settingFormRef"
            :rules="settingFormDataRuls"
            :model="settingFormData"
            label-width="10px"
            size="small"
            class="m-l-30 m-t-20"
          >
            <el-form-item prop="on_scene" label="" :rules="settingFormDataRuls.number">
              <span class="m-r-10">堂食订单：订单支付完成后</span>
              <el-input class="ps-input w-150" v-model="settingFormData.on_scene" placeholder=""></el-input>
              <!-- <el-input-number v-model="settingFormData.on_scene" controls-position="right" :min="1"></el-input-number> -->
              <span class="m-l-10">天内</span>
            </el-form-item>
            <el-form-item>
              <span>预约订单：订单核销后</span>
              <el-form-item class="form-item-inline" prop="reservation_order" label="" :rules="settingFormDataRuls.number">
                <el-input class="ps-input w-150" v-model="settingFormData.reservation_order" placeholder=""></el-input>
              </el-form-item>
              <span>天内 或订单支付后</span>
              <el-form-item class="form-item-inline" prop="reservation_order_pay_end" label="" :rules="settingFormDataRuls.number">
                <el-input class="ps-input w-150" v-model="settingFormData.reservation_order_pay_end" placeholder=""></el-input>
              </el-form-item>
              <span>天内 或餐段结束后</span>
              <el-form-item class="form-item-inline" prop="reservation_order_meal_end" label="" :rules="settingFormDataRuls.number">
                <el-input class="ps-input w-150" v-model="settingFormData.reservation_order_meal_end" placeholder=""></el-input>
              </el-form-item>
              <span>天内</span>
            </el-form-item>
            <el-form-item prop="upload_image_number" label="" :rules="settingFormDataRuls.number">
              <span class="m-r-10">可上传图片数：</span>
              <el-input class="ps-input w-150" v-model="settingFormData.upload_image_number" placeholder=""></el-input>
              <!-- <el-input-number v-model="settingFormData.upload_image_number" controls-position="right" :min="1"></el-input-number> -->
              <span class="m-l-10">张</span>
            </el-form-item>
            <el-form-item class="m-t-36" prop="anonymous" label="">
              <span class="m-r-10">匿名：</span>
              <el-switch class="ps-switch" active-color="#ff9b45" v-model="settingFormData.anonymous"></el-switch>
              <div>开启时，用户在对订单、食堂建议、食堂投诉时可选择匿名提交</div>
            </el-form-item>
            <el-form-item class="m-t-36" label="">
              <el-button class="ps-btn" style="width: 120px;" type="primary" @click="saveEvaluteSetting">确 定</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="tabType === 'customer'" key="customer" class="setting-box">
          <div>客服微信：<el-button type="text" @click="importShowDialog = true">上传</el-button></div>
        </div>
      </transition-group>
    </div>
    <el-dialog
      :title="importTitle"
      :visible.sync="importShowDialog"
      width="500px"
      custom-class="ps-dialog"
    >
      <div class="m-l-50">
        <el-upload
          ref="fileUpload"
          drag
          :data="uploadParams"
          :limit="limit"
          :on-success="getSuccessUploadRes"
          :before-upload="beforeUpload"
          :action="actionUrl"
          :on-remove="remove"
          :headers="headersOpts"
          show-file-list
        >
          <div class="">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
          </div>
          <!-- <div class="el-upload__tip" slot="tip">只能上传{{fileType}}文件</div> -->
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="importShowDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="imortHandler">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { debounce, getToken, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'SuperOperationsManagement',
  components: {
  },
  props: {

  },
  mixins: [exportExcel],
  data() {
    let validateNumber = (rule, value, callback) => {
      let reg = /^[0-9]$/
      if (!reg.test(value)) {
        callback(new Error('格式不正确'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      tabType: 'evalute',
      settingFormData: {
        reservation_order: '', // 预约订单核销后X天内
        reservation_order_pay_end: '', // 预约订单支付后X天内
        reservation_order_meal_end: '', // 预约餐段结束X天内
        on_scene: '', // 堂食订单核销后X天内
        upload_image_number: '', // 图片上传数量限制
        anonymous: false // 是否可匿名
      },
      settingFormDataRuls: {
        number: [
          { validator: validateNumber, trigger: "blur" }
        ]
      },
      slideTransition: 'slide-left', // 默认切换动画
      importType: 'SuperOperationsManagement',
      importShowDialog: false,
      importTitle: '上传图片',
      limit: 1,
      actionUrl: '/api/background/file/upload',
      uploadParams: {},
      uploadUrl: '',
      headersOpts: {
        TOKEN: getToken()
      },
      fileType: ['jpg', 'png']
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.getOperationData()
  },
  mounted() {

  },
  methods: {
    refreshHandle() {
      this.getOperationData()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.getOperationData()
    }, 300),
    // tab
    changeTabHandle(e) {
      this.slideTransition = this.slideTransition === 'slide-left' ? 'slide-right' : 'slide-left'
    },
    async getOperationData() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundOperationManagementAdminEvaluationSettingListPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.settingFormData.reservation_order = res.data.reservation_order
        this.settingFormData.reservation_order_pay_end = res.data.reservation_order_pay_end
        this.settingFormData.reservation_order_meal_end = res.data.reservation_order_meal_end
        this.settingFormData.on_scene = res.data.on_scene
        this.settingFormData.upload_image_number = res.data.upload_image_number
        this.settingFormData.anonymous = res.data.anonymous
      } else {
        this.$message.error(res.msg)
      }
    },
    // 评价设置
    saveEvaluteSetting() {
      this.$refs.settingFormRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return;
          this.sendEvaluteSetting()
        }
      })
    },
    async sendEvaluteSetting() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOperationManagementAdminEvaluationSettingModifyPost({
        reservation_order: this.settingFormData.reservation_order,
        reservation_order_pay_end: this.settingFormData.reservation_order_pay_end,
        reservation_order_meal_end: this.settingFormData.reservation_order_meal_end,
        on_scene: this.settingFormData.on_scene,
        upload_image_number: this.settingFormData.upload_image_number,
        anonymous: this.settingFormData.anonymous
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getOperationData()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 上传
    beforeUpload(file) {
      console.log(this.getSuffix(file.name))
      if (!this.fileType.includes(this.getSuffix(file.name))) {
        this.$message.error('请上传JPG/PNG的文件')
        return false
      }
    },
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos + 1)
      }
      return suffix
    },
    remove() {
      this.$refs.fileUpload.clearFiles()
      this.uploadUrl = ''
    },
    getSuccessUploadRes(res) {
      if (res.code === 0) {
        this.uploadUrl = res.data.public_url
      }
    },
    async imortHandler() {
      this.importShowDialog = false
      const option = {
        type: this.importType,
        immediate: true, // 立刻执行不弹窗
        url: '',
        params: {
          oss_url: this.importUrl
        }
      }
      this.exportHandle(option)
    }
  }
};
</script>

<style scoped lang="scss">
.operations-setting-wrapper{
  width: 100%;
  .operations-content{
    width: 100%;
    .setting-box{
      margin-top: 20px;
      width: 100%;
      padding: 20px;
      box-shadow: 6px 6px 10px 0px rgb(202 210 221 / 30%), inset 2px 2px 0px 0px #ffffff;
      border-radius: 12px;
      overflow: hidden;
      background-color: #f8f9fa;
    }
    .w-150{
      width: 150px;
    }
    .form-item-inline{
      display: inline-block;
      margin: 0 8px;
      .el-form-item__content{
        display: inline-block;
      }
    }
  }
}
</style>

<template>
  <div class="container-wrapper super-add-organization is-fixed-footer">
    <!-- tab end -->
    <el-form
      ref="organizationFormRef"
      v-loading="isLoading"
      :rules="formDataRuls"
      :model="formData"
      class="organization-form-wrapper"
      size="small"
    >
      <div v-if="operate === 'add'" class="add-title">新建组织</div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">基本信息</span>
        <el-button
          v-permission="['background.admin.organization.modify']"
          v-if="!checkIsFormStatus"
          @click="changeOperate"
          size="mini"
          class="float-r"
        >
          编辑
        </el-button>
      </div>
      <div class="item-box clearfix">
        <div class="item-b-l">{{ labelName }}</div>
        <div class="item-b-r">
          <el-form-item class="block-label" label="组织名称：" prop="name">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.name"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.name }}</div>
          </el-form-item>
        </div>
      </div>
      <!-- 当前组织层次 -->
      <el-form-item class="block-label form-item-box" label="当前组织层次：" prop="levelName">
        <!-- <el-input v-if="checkIsFormStatus" class="ps-input" size="small" v-model="formData.levelName"></el-input> -->
        <div class="item-form-text">{{ formData.levelName }}</div>
      </el-form-item>
      <!-- 官网 -->
      <el-form-item class="block-label form-item-box" label="官网：" prop="url">
        <el-input
          v-if="checkIsFormStatus"
          class="ps-input"
          size="small"
          v-model="formData.url"
        ></el-input>
        <div class="item-form-text" v-else>{{ formData.url }}</div>
      </el-form-item>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 固定电话 -->
        <el-col :span="12">
          <el-form-item class="block-label form-item-box" label="固定电话：" prop="tel">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.tel"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.tel }}</div>
          </el-form-item>
        </el-col>
        <!-- 组织邮箱 -->
        <el-col :span="12">
          <div class="form-item-box">
            <el-form-item class="block-label" label="组织邮箱：" prop="mailAddress">
              <el-input
                v-if="checkIsFormStatus"
                class="ps-input"
                size="small"
                v-model="formData.mailAddress"
              ></el-input>
              <div class="item-form-text" v-else>{{ formData.mailAddress }}</div>
            </el-form-item>
          </div>
        </el-col>
      </el-row>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 行业性质 -->
        <el-col class="block-label form-item-box" :span="12">
          <el-form-item label="行业性质：" prop="industry">
            <el-select
              v-model="formData.industry"
              placeholder="请选择行业性质"
              class="ps-select"
              style="width: 100%"
              size="small"
              :disabled="!checkIsFormStatus"
            >
              <el-option
                v-for="item in industryTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- 所在地址 -->
        <el-col class="block-label form-item-box" :span="12">
          <el-form-item label="所在地址：" prop="district">
            <el-cascader
              size="small"
              :options="addrOptions"
              v-model="formData.district"
              style="display: block"
              :disabled="!checkIsFormStatus"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">功能配置</span>
      </div>
      <!-- 添加组织层级 -->
      <el-form-item v-if="operate === 'add'" class="block-label form-item-box" label="添加组织层级：" prop="initOrganizationLevel">
        <el-select clearable v-model="formData.initOrganizationLevel" style="width: 100%;" placeholder="请选择">
          <el-option
            v-for="item in levelList"
            :key="item.level"
            :label="item.name"
            :value="item.level"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 功能菜单配置 -->
      <el-form-item class="block-label form-item-box" label="功能菜单配置：" prop="permission">
        <div style="margin: 3px 0 5px" v-if="checkIsFormStatus">
          <el-button type="" size="mini" @click="clickSelectPermissionTree(1)">全选</el-button>
          <el-button type="" size="mini" @click="clickSelectPermissionTree(0)">全不选</el-button>
        </div>
        <!-- <tree-select
          :multiple="true"
          :options="permissionTree"
          :limit="5"
          :limitText="count => '+' + count"
          :default-expand-level="6"
          :normalizer="permissionNormalizer"
          placeholder="请选择"
          v-model="formData.permission"
          value-consists-of="ALL"
          no-results-text="暂无数据"
          :flat="true"
        >
        </tree-select> -->
        <select-tree
          v-model="formData.permission"
          class="search-item-w ps-input"
          placeholder="请选择适用的下级组织"
          :multiple="true"
          :check-strictly="true"
          :append-to-body="true"
          :is-select-child="true"
          :default-expand-all="true"
          :treeData="permissionTree"
          :treeProps="permissionTreeProps"
          :clearable="true"
          :disabled="!checkIsFormStatus"
        ></select-tree>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="useCardNoLimit">
        <span>
          <span class="warn">*</span>
          IC卡验证
          <!-- <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.useCardNoLimit"
            active-color="#ff9b45"
          ></el-switch> -->
          <el-radio-group v-model="formData.useCardNoLimit" :disabled="!checkIsFormStatus" class="ps-radio">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </span>
      </el-form-item>
      <!-- 账号 -->
      <el-form-item v-if="type==='root'" class="block-label form-item-box fixed-login-box" label="账号：" prop="username">
        <span v-if="type==='root' && operate !== 'add'" class="fixed-login"><el-button type="text" size="mini" @click="gotoLogin">登录</el-button></span>
        <el-input v-if="type==='root' && operate === 'add'" class="ps-input" size="small" v-model="formData.username"></el-input>
        <div v-else class="item-form-text">{{formData.username}}</div>
      </el-form-item>
      <!-- 密码 -->
      <el-form-item
        v-if="type === 'root' && operate === 'add'"
        class="block-label form-item-box"
        label="密码："
        prop="password"
      >
        <el-input
          :disabled="!checkIsFormStatus"
          class="ps-input"
          size="small"
          v-model="formData.password"
        ></el-input>
        <div style="margin-top:3px; color: #F56C6C; line-height: 1; font-size: 12px;">密码有效期为90天，请在期限前重置密码</div>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span >
          到期修改密码
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.isExpireChangePwd"
            active-color="#ff9b45"
          ></el-switch>
          <el-checkbox
            v-model="formData.allowJumpChangePwd"
            v-if="formData.isExpireChangePwd"
            style="margin-left: 10px;"
            :disabled="!checkIsFormStatus"
            class="ps-checkbox"
          >
            允许跳过本次
          </el-checkbox>
        </span>
      </el-form-item>
      <!-- <el-form-item class="block-label form-item-box" label="退款密码：" prop="refundPassword">
        <el-input :disabled="!checkIsFormStatus" class="ps-input" size="small" v-model="formData.refundPassword"></el-input>
      </el-form-item> -->

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">联系方式</span>
      </div>

      <el-row class="form-item-row-box" :gutter="24">
        <!-- 联系人 -->
        <el-col :span="12">
          <el-form-item class="block-label" label="联系人：" prop="contact">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.contact"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.contact }}</div>
          </el-form-item>
        </el-col>
        <!-- 手机号码 -->
        <el-col :span="12">
          <el-form-item class="block-label" label="手机号码：" prop="mobile">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.mobile"
            ></el-input>
            <div class="item-form-text" v-else>{{ formData.mobile }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 短信模板 -->
      <el-form-item class="block-label form-item-box" label="短信模板：" prop="smsTemplateId">
        <el-input
          v-if="checkIsFormStatus"
          class="ps-input"
          v-model="formData.smsTemplateId"
          type="textarea"
          :rows="3"
        ></el-input>
        <div class="item-form-text" v-else>{{ formData.smsTemplateId }}</div>
      </el-form-item>
      <!-- 备注 -->
      <el-form-item class="block-label form-item-box" label="备注：" prop="remark">
        <el-input
          v-if="checkIsFormStatus"
          class="ps-input"
          v-model="formData.remark"
          type="textarea"
          :rows="3"
        ></el-input>
        <div class="item-form-text" v-else>{{ formData.remark }}</div>
      </el-form-item>

      <div class="form-line"></div>
      <div class="l-title clearfix">
        <span class="float-l min-title-h">其它设置</span>
      </div>
      <!-- 钱包设置 -->
      <el-form-item class="block-label form-item-box" label="钱包设置" prop="">
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.storeWalletOn"
        >
          储值钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.electronicWalletOn"
        >
          电子钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.subsidyWalletOn"
        >
          补贴钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.complimentaryWalletOn"
        >
          赠送钱包
        </el-checkbox>
        <el-checkbox
          :disabled="!checkIsFormStatus"
          class="ps-checkbox"
          v-model="formData.otherWalletOn"
        >
          第三方钱包
        </el-checkbox>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        钱包扣款规则
        <el-radio-group v-model="formData.isWalletPayOrderAsc" :disabled="!checkIsFormStatus" class="ps-radio">
          <el-radio :label="false">扣上级钱包余额</el-radio>
          <el-radio :label="true">扣下级钱包余额</el-radio>
        </el-radio-group>
        <div style="margin-left: 88px;color: #ff9b45">当订单所属组织余额不足时将扣取余额充足的上级/下级钱包金额;该规则仅适合线下消费</div>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span >
          组合支付
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.combineWalletOn"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <!-- 开关设置 -->
      <el-form-item class="form-item-box" label="开关设置" prop="">
        <span style="margin-right: 25px">
          人脸支付
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.facepay"
            active-color="#ff9b45"
          ></el-switch>
        </span>
        <span>
          支持退款
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.refundOn"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span >
          是否农行项目点展示
          <el-switch
            :disabled="!checkIsFormStatus"
            v-model="formData.isAbcProject"
            active-color="#ff9b45"
          ></el-switch>
        </span>
      </el-form-item>
      <el-form-item class="block-label form-item-box" label="" prop="">
        <span >
          人脸更新消息提醒：
        </span>
        <el-switch
          class="m-r-20"
          :disabled="!checkIsFormStatus"
          v-model="formData.enableUpdateNotify"
          active-color="#ff9b45"
        ></el-switch>
        <div v-if="formData.enableUpdateNotify" style="margin-left: 125px;">
          <span class="m-r-20">上传人脸时间每隔</span>
          <el-form-item class="inline-label form-item-box m-t-2 m-b-2 m-r-20" label="" prop="faceUpdateTime">
            <el-select class="w-110"  clearable  v-model="formData.faceUpdateTime" :disabled="!checkIsFormStatus" placeholder="请选择">
              <el-option
                v-for="item in faceUploadOptions"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="formData.faceUpdateTime === 'auto'" class="inline-label form-item-box m-t-2 m-b-2 m-r-20" label="" prop="customFaceDate">
            <el-input v-model="formData.customFaceDate" :disabled="!checkIsFormStatus" class="w-100"></el-input>
            <span class="m-l-10">天</span>
          </el-form-item>
          <span class="">进行消息提醒</span>
          <el-form-item class="block-label form-item-box" label="" prop="">
            <span style="vertical-align: top;">提醒内容：</span>
            <el-input v-model="formData.notifyMsg" :disabled="!checkIsFormStatus" type="textarea" :rows="2" style="width:70%;"></el-input>
          </el-form-item>
        </div>
      </el-form-item>
      <div v-if="operate !== 'add'">
        <div class="form-line"></div>
        <div class="l-title clearfix">
          <span class="float-l min-title-h">
            第三方设置
            <el-switch
              :disabled="!checkIsFormStatus"
              style="margin-left: 15px"
              v-model="formData.isThirdInterface"
              active-color="#ff9b45"
            ></el-switch>
          </span>
        </div>
        <div v-loading="loadingThirdInfo" v-show="formData.isThirdInterface">
          <el-row class="form-item-row-box" :gutter="24">
            <!-- 应用key -->
            <el-col :span="12">
              <el-form-item class="block-label" label="应用key：" prop="thirdAppKey">
                <el-input
                  v-if="checkIsFormStatus"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdAppKey"
                ></el-input>
                <div v-else class="item-form-text">{{ formData.thirdAppKey }}</div>
              </el-form-item>
            </el-col>
            <!-- 应用secret -->
            <el-col :span="12">
              <el-form-item class="block-label" label="应用secret：" prop="thirdSecretKey">
                <el-input
                  v-if="checkIsFormStatus"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdSecretKey"
                ></el-input>
                <el-tooltip
                  v-else
                  class="item"
                  effect="dark"
                  :content="formData.thirdSecretKey"
                  placement="top"
                >
                  <div class="item-form-text ellipsis">{{ formData.thirdSecretKey }}</div>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item class="block-label" label="应用名称：" prop="thirdAppName">
            <el-input
              v-if="checkIsFormStatus"
              class="ps-input"
              size="small"
              v-model="formData.thirdAppName"
            ></el-input>
            <div class="item-form-text ellipsis" v-else>{{ formData.thirdAppName }}</div>
          </el-form-item>
          <el-row class="form-item-row-box" :gutter="24">
            <!-- 联系人 -->
            <el-col :span="12">
              <el-form-item class="block-label" label="跳转地址：" prop="thirdAppUrl">
                <el-input
                  v-if="checkIsFormStatus"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdAppUrl"
                ></el-input>
                <div class="item-form-text" v-else>{{ formData.thirdAppUrl }}</div>
              </el-form-item>
            </el-col>
            <!-- 回调地址 -->
            <el-col :span="12">
              <el-form-item class="block-label" label="回调地址：" prop="thirdAppCallbackUrl">
                <el-input
                  v-if="checkIsFormStatus"
                  placeholder="http://127.0.0.1/?userId={0}&bb=1，{0}将会被替换掉"
                  class="ps-input"
                  size="small"
                  v-model="formData.thirdAppCallbackUrl"
                ></el-input>
                <div class="item-form-text" v-else>{{ formData.thirdAppCallbackUrl }}</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item v-if="checkIsFormStatus" class="block-center" label="" prop="">
            <el-button class="ps-origin-btn" type="primary" @click="generateThirdAppinfo">
              重新生成
            </el-button>
          </el-form-item>
        </div>
      </div>

      <div v-if="checkIsFormStatus" class="form-footer">
        <el-button @click="cancelFormHandle" size="small">取消</el-button>
        <el-button
          v-permission="[
            'background.admin.organization.add_root',
            'background.admin.organization.modify'
          ]"
          @click="sendFormdataHandle"
          class="ps-origin-btn"
          type="primary"
          size="small"
        >
          保存
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { to, debounce, getTreeDeepkeyList, camelToUnderline } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { regionData } from 'element-china-area-data'
import industryType from '@/assets/data/industryType.json'
import { validateTelphone, validateName } from '@/assets/js/validata'
import { validateEmail, validateTel } from '@/utils/form-validata'
import SelectTree from '@/components/SelectTree'
// import md5 from 'js-md5';

export default {
  name: 'SuperAddRootOrganization',
  components: { SelectTree },
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: {
      // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    treeData: Object,
    id: [String, Number],
    operate: String,
    restoreHandle: Function
  },
  data() {
    let validateAccount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('账号不能为空'))
      } else {
        // let regNum = /(^\d{3,12}$)|(^[a-zA-Z]{3,12}$)/;
        let regNum = /^\w{5,20}$/
        if (!regNum.test(value)) {
          callback(new Error('账号长度5到20位，只支持数字、大小写英文或下划线组合'))
        } else {
          callback()
        }
      }
    }
    let validatePass = (rule, value, callback) => {
      let regPass = /^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/
      // let regRPass = /(^\w{6,32}$)/;
      if (!value) {
        if (this.formOperate === 'modify') {
          callback()
          return
        }
        return callback(new Error('密码不能为空'))
      } else {
        if (!regPass.test(value)) {
          callback(new Error('密码长度8到20位，字母和数组组合'))
        } else {
          callback()
        }
      }
    }
    let validatePermission = (rule, value, callback) => {
      if (value.length > 0) {
        callback()
      } else {
        callback(new Error('功能菜单配置不能为空！'))
      }
    }
    let validateCustomFaceDate = (rule, value, callback) => {
      if (value === '' || value === '0') {
        return callback(new Error('请输入大于0的数字'))
      } else {
        let number = /^\d+$/
        if (!number.test(value)) {
          callback(new Error('请输入正确数字'))
        } else {
          callback()
        }
      }
    }
    return {
      labelName: '',
      formOperate: 'detail',
      isLoading: false,
      industryTypeList: industryType,
      addrOptions: regionData,
      formData: {
        id: '',
        appid: '',
        secretKey: '',
        name: '', // 组织名称
        levelName: '', // 当前组织层次名称
        initOrganizationLevel: '', // 组织层级
        permission: [], // 功能菜单配置
        username: '', // 账号
        password: '', // 密码
        url: '', // 官网
        district: [], // 所在地址
        contact: '', // 联系人
        mobile: '', // 手机
        mailAddress: '', // 邮箱
        tel: '', // 电话
        industry: '', // 行业性质
        remark: '', // 备注
        facepay: false, // 人脸支付开关
        refundOn: false, // 支持退款
        refundPassword: '', // 退款密码
        storeWalletOn: false, // 储值钱包开关
        electronicWalletOn: false, // 电子钱包开关
        subsidyWalletOn: false, // 补贴钱包开关
        complimentaryWalletOn: false, // 赠送钱包开关
        otherWalletOn: false, // 第三方钱包开关
        isThirdInterface: false, // 是否开启第三方配置
        combineWalletOn: false, // 组合支付开关
        thirdAppKey: '',
        thirdSecretKey: '', //
        thirdAppName: '', // 第三方名称
        thirdAppUrl: '', // 第三方配置地址
        thirdAppCallbackUrl: '', // 第三方配置回调url
        smsTemplateId: '', // 短信模板
        isAbcProject: false, // 是否农行项目点展示
        isExpireChangePwd: false, // 到期修改密码
        allowJumpChangePwd: false, // 允许跳过修改密码
        useCardNoLimit: false, // IC卡校验
        enableUpdateNotify: false, // 是否开启人脸更新提醒
        faceUpdateTime: '', // 人脸更新提醒日期
        notifyMsg: '', // 消息内容
        isWalletPayOrderAsc: false // 钱包扣款顺序
      },
      formDataRuls: {
        name: [{ required: true, message: '组织名称不能为空', trigger: 'blur' },
          { validator: validateName, trigger: 'blur' }],
        level_name: [{ required: true, message: '层级名称不能为空', trigger: 'blur' }],
        mobile: [{ required: true, validator: validateTelphone, trigger: 'blur' }],
        username: [{ required: true, validator: validateAccount, trigger: 'blur' }],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        refundPassword: [{ validator: validatePass, trigger: 'blur' }],
        tel: [{ validator: validateTel, trigger: 'blur' }],
        mailAddress: [{ validator: validateEmail, trigger: 'blur' }],
        permission: [{ required: true, validator: validatePermission, trigger: 'blur' }],
        useCardNoLimit: [{ required: true, message: '不能为空', trigger: 'blur' }],
        faceUpdateTime: [{ required: true, message: '请选择人脸更新天数', trigger: 'blur' }],
        customFaceDate: [{ validator: validateCustomFaceDate, trigger: 'blur' }]
      },
      levelList: [],
      permissionTree: [
        {
          id: 'folder',
          label: 'Normal Folder',
          children: [
            { id: 'disabled-checked', label: 'Checked', isDisabled: true },
            { id: 'disabled-unchecked', label: 'Unchecked', isDisabled: true },
            { id: 'item-1', label: 'Item' }
          ]
        }
      ],
      permissionTreeProps: {
        value: 'key',
        label: 'verbose_name',
        isLeaf: 'is_leaf',
        children: 'children'
      },
      loadingThirdInfo: false,
      faceUploadOptions: [
        { name: '60天', value: 60 },
        { name: '90天', value: 90 },
        { name: '180天', value: 180 },
        { name: '1年', value: 365 },
        { name: '自定义', value: 'auto' }
      ]
    }
  },
  computed: {
    // formOperate: {
    //   get() {
    //     return this.operate
    //   },
    //   set(val) {
    //     // this.$emit('update:operate', val)
    //   }
    // },
    // 检查当前状态，编辑还是详情
    checkIsFormStatus: function () {
      // 默认为false
      let show = false
      switch (
        this.operate // 目前从父组件传过来的操作类型只会有2个add和detail
      ) {
        case 'add':
          show = true
          break
        case 'detail':
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break
        default:
          // 没传的话
          if (this.formOperate === 'detail') {
            show = false
          } else {
            show = true
          }
          break
      }
      return show
    }
  },
  watch: {
    operate: function (val, old) {
      if (!val) {
        this.formOperate = 'detail'
      }
    }
  },
  created() {},
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.getLevelList(this.id)
      this.getPermissionTreeList(this.id)
      if (this.id && this.operate !== 'add') {
        this.initInfoHandle()
      }
      if (this.operate) {
        this.formOperate = this.operate
      }
      if (this.treeData && this.operate !== 'add') {
        this.labelName = this.treeData.name.substring(0, 1)
      }
      if (this.operate === 'add') {
        this.labelName = '朴'
      }
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function () {}, 300),
    initInfoHandle() {
      for (let key in this.formData) {
        let val = this.infoData[camelToUnderline(key)]
        if (val) {
          switch (key) {
            case 'industry':
              this.formData[key] = val.toString()
              break
            case 'district':
              this.formData[key] = JSON.parse(val)
              break
            case 'faceUpdateTime':
              // this.formData[key] = JSON.parse(val)
              // eslint-disable-next-line no-case-declarations
              let hasVal = false
              this.faceUploadOptions.forEach(v => {
                // eslint-disable-next-line eqeqeq
                if (v.value == val) {
                  hasVal = true
                }
              })
              if (hasVal) {
                this.formData[key] = val
              } else {
                if (val) {
                  this.formData[key] = 'auto'
                  this.formData.customFaceDate = val
                }
              }
              break
            default:
              this.formData[key] = val
              break
          }
        }
      }
    },
    // z
    permissionNormalizer(node) {
      return {
        id: node.key,
        label: node.verbose_name,
        children: node.children
      }
    },
    // 处理下没有children_list
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (_that.checkIsFormStatus) {
            item.isDisabled = false
          } else {
            item.isDisabled = true
          }
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 获取指定公司的层级列
    async getLevelList(companyId) {
      let params = {}
      if (companyId) {
        params.company_id = companyId
      }
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGetLevelNameMapPost(params)
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.levelList = res.data
        if (res.data.length > 0 && this.operate === 'add') {
          this.formData.levelName = res.data[0].name
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取权限树状结构
    async getPermissionTreeList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost()
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.permissionTree = this.deleteEmptyChildren(res.data, 'children')
        if (this.operate === 'add') {
          this.formData.permission = getTreeDeepkeyList(this.permissionTree, 'key')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    clickSelectPermissionTree(type) {
      if (type === 1) {
        this.formData.permission = getTreeDeepkeyList(this.permissionTree, 'key')
      } else {
        this.formData.permission = []
      }
    },
    changeOperate() {
      switch (
        this.operate // 目前从父组件传过来的操作类型只会有2个add和detail
      ) {
        case 'add': // noth
          break
        default:
          if (this.formOperate === 'detail') {
            this.formOperate = 'modify'
          } else {
            this.formOperate = 'detail'
          }
          break
      }
      this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
    },
    // 取消
    cancelFormHandle() {
      if (this.operate === 'add') {
        this.$refs.organizationFormRef.resetFields() // 重置表单数据
      } else {
        this.$refs.organizationFormRef.clearValidate() // 清空表单校验
        this.formOperate = 'detail'
        this.permissionTree = this.deleteEmptyChildren(this.permissionTree, 'children')
      }
      this.restoreHandle(this.type, this.formOperate)
    },
    async generateThirdAppinfo() {
      this.loadingThirdInfo = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminOrganizationGenerateThirdAppinfoPost({
          id: this.id
        })
      )
      this.loadingThirdInfo = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formData.thirdAppKey = res.data.third_app_key
        this.formData.thirdSecretKey = res.data.third_secret_key
      } else {
        this.$message.error(res.msg)
      }
    },
    // 发送请求
    sendFormdataHandle() {
      this.$refs.organizationFormRef.validate(valid => {
        if (valid) {
          if (this.operate === 'add') {
            // 添加
            this.addRootOrganization(this.formatData())
          } else {
            // 编辑
            this.modifyOrganization(this.formatData())
          }
        }
      })
    },
    // 格式化下传给后台的参数
    formatData() {
      let params = {
        status: 'enable'
      }
      for (let key in this.formData) {
        let val = this.formData[key]
        if (val !== '') {
          switch (key) {
            case 'district':
              val = JSON.stringify(val)
              break
            case 'password':
              // val = md5(val)
              break
            case 'refundPassword':
              // val = md5(val)
              break
            case 'thirdAppUrl':
              val = encodeURIComponent(val)
              break
            case 'faceUpdateTime': // 人脸更新天数
              val = val === 'auto' ? this.formData.customFaceDate : val
              break
            // case 'permission':
            //   console.log(this.getPermissionLevelParent(val))
            //   break;
          }
          if (key !== 'levelName' && key !== 'customFaceDate') {
            params[camelToUnderline(key)] = val
          }
        }
        if (this.formOperate === 'modify') {
          params.company = this.treeData.company
        }
      }
      return params
    },
    // 权限的处理
    getPermissionLevelParent(data) {
      let parentKeys = []
      // this.permissionTree = this.deleteEmptyChildren(res.data, 'children')
      // this.formData.permission
      function checkKeyinData(list, key, arr) {
        let keys = []
        for (let i = 0; i < list.length; i++) {
          let current = list[i]
          if (current.key === key) {
            keys = arr
            break
          } else {
            arr.push(key)
            if (current.children && current.children.length > 0) {
              checkKeyinData(current.children, key, arr)
            }
          }
        }
        return keys
      }
      data.forEach(item => {
        let arr = []
        let result = checkKeyinData(this.permissionTree, item, arr)
        parentKeys = parentKeys.concat(result)
      })
      return parentKeys
    },
    // 根添加
    async addRootOrganization(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationAddRootPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('添加成功')
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 根编辑
    async modifyOrganization(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.formOperate = 'detail'
        this.restoreHandle(this.type, this.formOperate)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 登录
    gotoLogin() {
      if (this.infoData.login_token) {
        window.open(location.origin + '/#/login?token=' + this.infoData.login_token, "_blank")
      } else {
        this.$message.error('无法获取token!')
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/variables.scss';

.super-add-organization {
  position: relative;
  .add-title {
    font-size: 18px;
    text-align: center;
    font-family: 600;
  }
  .min-title-h {
    height: 28px;
    line-height: 28px;
  }
  .inline-label{
    display: inline-block;
  }
  .item-box {
    // display: flex;
    padding: 10px 0 0 !important;
    .item-b-l {
      // display: flex;
      // justify-content: center;
      // align-items: center;
      float: left;
      width: 56px;
      height: 56px;
      line-height: 56px;
      text-align: center;
      vertical-align: middle;
      background-color: #ff9b45;
      border-radius: 8px;
      font-size: 30px;
      letter-spacing: 2px;
      color: #ffffff;
    }
    .item-b-r {
      margin-left: 76px;
    }
    .item-text-box {
      display: flex;
      padding: 5px 0;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 24px;
      letter-spacing: 1px;
      color: #23282d;
      .item-label {
        opacity: 0.7;
      }
      .item-text {
        flex: 1;
      }
    }
  }
  .organization-form-wrapper {
    width: 100%;
    max-width: 800px;
    .block-label {
      width: 100%;
      .el-form-item__label {
        display: block;
        text-align: left;
        line-height: 1.5;
        float: none;
      }
    }
    .form-line {
      width: 100%;
      height: 1px;
      background-color: #e0e6eb;
    }
    .block-center {
      text-align: center;
    }
    .item-form-text {
      padding: 0 15px;
      color: #23282d;
      font-weight: bold;
      min-height: 32px;
      border: 1px solid #e0e6eb;
      opacity: 0.7;
      background-color: #fff;
    }
  }
  .form-footer {
    text-align: center;
    .el-button{
      width: 120px;
    }
  }
  &.is-fixed-footer {
    // padding-bottom: 30px;
    .form-footer {
      margin-top: 30px;
      // position: fixed;
      // padding: 10px 20px;
      // left: 263px;
      // right: 40px;
      // bottom: 0;
      // background-color: #fff;
      // box-shadow: -4px 0px 6px 0px rgba(214, 214, 214, 0.6);
      z-index: 99;
    }
  }
  .fixed-login-box{
    position: relative;
  }
  .fixed-login{
    position: absolute;
    top: -28px;
    right: 0;
    .el-button--mini{
      padding: 4px 15px;
    }
  }
  .el-radio__input.is-checked .el-radio__inner{
    border-color: #ff9b45;
    background: #ff9b45;
  }
  .el-radio__input.is-checked+.el-radio__label{
    color: #ff9b45
  }
}
</style>

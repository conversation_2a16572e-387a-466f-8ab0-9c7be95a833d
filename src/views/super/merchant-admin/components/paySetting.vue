<template>
  <div class="paysetting-wrapper">
    <!-- root paysetting start -->
    <div v-if="type === 'root'" class="paysetting-container">
      <!-- 组织结构 start -->
      <div class="tree-wrapper paysetting-l">
        <el-input
          class="tree-search ps-input"
          type="primary"
          placeholder="请输入"
          clearable
          size="small"
          v-model="treeFilterText">
        </el-input>
        <div v-if="!treeFilterText" :class="['all-tree', !selectKey?'is-current':'']" @click="treeHandleNodeClick('', 'all')">
          <span>
            <!-- <i class="tree-search-icon"><img src="@/assets/img/icon all.png" alt="" /></i> -->
            全部
          </span>
        </div>
        <!-- :load="load"
          lazy -->
        <el-tree
          v-loading="treeLoading"
          :data="paySettingList"
          :props="treeProps"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :highlight-current="true"
          :filter-node-method="filterTreeNode"
          :current-node-key="selectKey"
          :class="{ 'tree-box': selectKey} "
          ref="treeRef"
          node-key="key"
          @node-click="treeHandleNodeClick($event, 'tree')"
        >
        </el-tree>
      </div>
      <!-- end -->
      <!--  -->
      <div class="paysetting-r" v-loading="isLoading">
        <div v-permission="['background.admin.pay_info.add']" class="" style="margin-bottom: 10px;">
          <el-button  size="small" class="add-paysetting-btn" @click="openDialogHandle('add')">添加支付渠道</el-button>
        </div>
        <el-table
          ref="payInfoListRef"
          width="100%"
          :data="queryPayInfoList"
          tooltip-effect="dark"
          header-row-class-name="ps-table-header-row"
          stripe
          @selection-change="handleSelectionChange"
          v-loading="isLoading"
        >
          <!-- <el-table-column type="selection" width="50" align="center"></el-table-column> -->
          <el-table-column label="商户名称" prop="merchant_name" align="center"></el-table-column>
          <el-table-column label="商户号" prop="merchant_id" align="center"></el-table-column>
          <el-table-column label="支付类型" prop="payway_alias" align="center"></el-table-column>
              <el-table-column label="支付方式" prop="sub_payway_alias" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="适用层级" prop="" align="center">
            <template slot-scope="scope">
              <span>{{ showOrganizationsText(scope.row.organizations) }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="备注" prop="remark" align="center"></el-table-column>
          <el-table-column label="操作" prop="" align="center" width="150px" fixed="right">
            <!-- eslint-disable-next-line vue/no-unused-vars -->
            <template slot-scope="scope">
              <el-button v-permission="['background.admin.pay_info.modify']" type="text" size="small" @click="openDialogHandle('modify', scope.row)">编辑</el-button>
              <el-button v-permission="['background.admin.pay_info.delete']" type="text" size="small" class="ps-warn" @click="deletePayInfo('one', scope.row.id)">删除</el-button>
              <el-switch style="margin-left: 10px;" v-permission="['background.admin.pay_info.modify']" v-model="scope.row.enable" active-color="#ff9b45" @change="enablePayInfo(scope.row)"></el-switch>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="totalCount > pageSize" class="" style="text-align:right; margin-top: 20px;">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            popper-class="ps-popper-select"
            :total="totalCount">
          </el-pagination>
        </div>
      </div>

      <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      top="20vh"
      custom-class="ps-dialog ps-paysetting-dialog"
      :close-on-click-modal="false"
      :before-close="beforeCloseDialogHandle"
      @closed="closeDialogHandle"
      width="520px"
    >
      <el-form
        :model="payFormData"
        @submit.native.prevent
        status-icon
        ref="payFormDataRef"
        :rules="payFormDataRuls"
        label-width="110px"
        class="paysetting-dialog"
        v-loading="dialogIsLoading"
      >
        <el-form-item prop="merchantId" label="商户号">
          <el-input size="small" v-model="payFormData.merchantId"></el-input>
        </el-form-item>
        <el-form-item prop="merchantName" label="商户名称">
          <el-input size="small" v-model="payFormData.merchantName"></el-input>
        </el-form-item>
        <div>
          <el-form-item class="tree-item" label="支付类型" prop="payway">
            <tree-select
              :multiple="false"
              :options="paywayList"
              :normalizer="paySettingNormalizer"
              placeholder="请选择"
              :default-expand-level="1"
              v-model="payFormData.payway"
              :disable-branch-nodes="true"
              :show-count="true"
              @input="changePayway"
              @open="openTreeHandle"
              :disabled="formOperate !== 'add'"
              :append-to-body="true"
              :z-index="3000"
              no-results-text="暂无数据"
            >
            </tree-select>
          </el-form-item>
        </div>
        <el-form-item v-if="payFormData.payway" label="支付方式" prop="subPayway">
          <el-select ref="subPayway" @change="changeSubPayway" :disabled="formOperate !== 'add'" size="small" v-model="payFormData.subPayway" placeholder="">
            <el-option v-for="option in subPaywayList" :key="option.key" :label="option.name" :value="option.key"></el-option>
          </el-select>
        </el-form-item>
        <template v-for="item in formSettingList">
          <el-form-item v-if="!item.hidden&&item.key!='abc_subinfo'" :key="item.key" :prop="item.key" :label="item.name">
            <el-input size="small" v-if="!item.type || item.type==='input'" :disabled="item.disabled" v-model="payFormData[item.key]"></el-input>
            <el-input size="small" v-if="item.type==='textarea'" type="textarea" :rows="3" :disabled="item.disabled" v-model="payFormData[item.key]"></el-input>
            <el-select ref="forRef" size="small" v-if="item.type==='select'" class="search-item-w" :disabled="item.disabled" v-model="payFormData[item.key]" placeholder="">
              <el-option v-for="option in item.value" :key="option.value" :label="option.name" :value="option.value"></el-option>
            </el-select>
            <el-switch v-if="item.type==='switch'" :disabled="item.disabled" v-model="payFormData[item.key]"></el-switch>
            <el-checkbox-group v-if="item.type==='checkbox'" :disabled="item.disabled" v-model="payFormData[item.key]" >
              <el-checkbox v-for="(checkbox, i) in item.value" :key="i" :label="checkbox.value" :name="item.name">{{ checkbox.name }}</el-checkbox>
            </el-checkbox-group>
            <el-radio-group v-if="item.type==='radio'" :disabled="item.disabled" v-model="item.value">
              <el-radio v-for="radio in item.value" :key="radio.value" :label="radio.value" :name="item.radio">{{ radio.name }}</el-radio>
            </el-radio-group>
            <el-tooltip v-if="item.help_text" class="item" effect="dark" :content="item.help_text" placement="top-start">
              <i class="el-icon-info"></i>
            </el-tooltip>
          </el-form-item>
          <template v-if="!item.hidden&&item.key==='abc_subinfo'&&payFormData['abc_type']==='1'">
            <el-form-item v-for="subinfo in item.value" :key="subinfo.key" :prop="subinfo.key" :label="subinfo.name">
              <el-input size="small" v-if="!subinfo.type || subinfo.type==='input'" :disabled="subinfo.disabled" v-model="payFormData[subinfo.key]"></el-input>
              <el-input size="small" v-if="subinfo.type==='textarea'" type="textarea" :rows="3" :disabled="subinfo.disabled" v-model="payFormData[subinfo.key]"></el-input>
              <el-select ref="forRef" size="small" v-if="subinfo.type==='select'" class="search-item-w" :disabled="subinfo.disabled" v-model="payFormData[subinfo.key]" placeholder="">
                <el-option v-for="option in subinfo.value" :key="option.value" :label="option.name" :value="option.value"></el-option>
              </el-select>
              <el-tooltip v-if="item.help_text" class="item" effect="dark" :content="item.help_text" placement="top-start">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </el-form-item>
          </template>
        </template>
        <el-form-item label="适用组织" prop="organizations" class="remark-item">
          <tree-select
            :multiple="true"
            :options="organizationList"
            :normalizer="organizationNormalizer"
            placeholder=""
            v-model="payFormData.organizations"
            :limit="2"
            :limitText="count => '+' + count"
            :default-expand-level="6"
            value-consists-of="ALL"
            :flat="true"
            :append-to-body="true"
            :z-index="3000"
            no-results-text="暂无数据"
            />
            <!-- :load-options="loadOrganization" -->
        </el-form-item>
        <el-form-item label="备注" prop="remark" class="remark-item">
          <el-input class="ps-input" style="width: 100%;" v-model="payFormData.remark" type="textarea" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button :disabled="dialogIsLoading" class="ps-cancel-btn" size="small" @click="clickCancleHandle">取消</el-button>
        <el-button :disabled="dialogIsLoading" class="ps-btn" type="primary" size="small" @click="clickConfirmHandle">确定</el-button>
      </div>
    </el-dialog>
    </div>
    <!-- root paysetting end -->
    <!-- child paysetting start -->
    <div v-else class="paysetting-sub" v-loading="subIsLoading">
      <div class="sub-wrapper" v-for="(info, key) in collapseInfo" :key="key">
        <div class="l-title">
          <span>{{ info.name }}</span>
          <el-switch style="margin-left: 15px;" v-model="info.isOpen" active-color="#ff9b45" @change="changeSceneHandle($event, info.key)"></el-switch>
          <el-button v-if="showBindBtnHandle(info.key)" type="primary" class="ps-origin-btn float-r save-m-r" size="small" @click="clickBindOrgsHandle(info.key)" >保存</el-button>
        </div>
        <el-collapse v-if="info.payways.length > 0" v-model="info.activePayCollapse">
          <el-collapse-item v-for="payway in info.payways" :key="payway.key" :title="payway.name" :name="payway.key">
            <template slot="title">
              <el-checkbox class="ps-checkbox" v-model="payway.isOpen" :disabled="!info.isOpen" @change="changePaywayHandle($event, payway.key, info)">{{ payway.name }}</el-checkbox>
              <!-- <el-switch style="margin-left: 15px;" v-model="xxx" active-color="#ff9b45"></el-switch> -->
              <span class="tips-r">
                <span class="open">展开</span>
                <span class="close">收起</span>
              </span>
            </template>
            <el-table
              :ref="`subPayInfoListRef${info.key}-${payway.key}`"
              width="100%"
              :data="payway.sub_payways"
              tooltip-effect="dark"
              v-loading="isLoading"
            >
              <el-table-column class-name="ps-checkbox" width="50" align="center">
                <template slot-scope="scope">
                  <!-- <span>{{scope.row}}</span> -->
                  <el-checkbox class="ps-checkbox" :disabled="!(info.isOpen && payway.isOpen)" v-model="scope.row.binded" @change="changeSubPayHandle($event, scope.row, payway.sub_payways, `${info.key}-${payway.key}`)"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="商户名称" prop="merchant_name" align="center"></el-table-column>
              <el-table-column label="商户号" prop="merchant_id" align="center"></el-table-column>
              <el-table-column label="支付类型" prop="payway_alias" align="center"></el-table-column>
              <el-table-column label="支付方式" prop="sub_payway_alias" align="center"></el-table-column>
              <el-table-column show-overflow-tooltip label="适用层级" prop="" align="center">
                <template slot-scope="scope">
                  <span>{{ showOrganizationsText(scope.row.organizations) }}</span>
                </template>
              </el-table-column>
              <el-table-column show-overflow-tooltip label="备注" prop="remark" align="center"></el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
        <div v-else class="empty-collapse-text">暂无更多数据</div>
      </div>
    </div>
    <!-- child paysetting end -->
  </div>
</template>

<script>
import { to, debounce, deepClone, getTreeDeepkeyList } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用

export default {
  name: 'SuperPaySetting',
  // mixins: [activatedLoadData],
  props: {
    type: String, // 区别是否是顶级的
    infoData: { // 查看或者修改的数据
      type: Object,
      default() {
        return {}
      }
    },
    organizationData: Object,
    restoreHandle: Function
  },
  data() {
    return {
      treeLoading: false,
      treeProps: {
        children: 'children',
        label: 'name'
      },
      treeFilterText: '',
      selectKey: '',
      selectData: null,
      isLoading: false,
      formOperate: 'detail',
      formSettingList: [], // Form表单字段, 需要动态渲染的数据
      payFormData: {
        organizations: [], // 适用层级
        merchantId: '',
        merchantName: '',
        remark: '',
        payScene: '',
        payway: null,
        subPayway: ''
      },
      payFormDataRuls: {
        merchantId: [{ required: true, message: '商户号不能为空', trigger: "blur" }],
        merchantName: [{ required: true, message: '商户名称不能为空', trigger: "blur" }],
        payway: [{ required: true, message: '请选择支付渠道', trigger: "blur" }],
        subPayway: [{ required: true, message: '请选择支付方式', trigger: "blur" }],
        organizations: [{ required: true, message: '请选择适用组织', trigger: "blur" }]
      },
      payTemplateList: {}, // 从后台获取到的template设置列表
      paySettingList: [], // 格式化的左侧配置文件
      payInfoList: [], // 支付配置列表
      queryPayInfoList: [], // 筛选的支付配置列表
      pageSize: 10,
      currentPage: 1,
      totalCount: 0,
      dialogVisible: false,
      dialogTitle: '',
      dialogData: null,
      dialogIsLoading: false,
      paywayList: [],
      subPaywayList: [],
      organizationList: [], // 适用组织列表
      selectTableCoumn: [],
      activePayCollapse: [], // 手风琴选项
      subIsLoading: false,
      subPayInfoList: [], // 手风琴源数据
      collapseInfo: {}, // 处理下手风琴需要的参数
      selectSubInfo: {} // 选中的数据
    }
  },
  computed: {
    checkIsFormStatus: function() {
      let show = false
      switch (this.formOperate) {
        case 'detail':
          show = false
          break;
        case 'add':
          show = true
          break;
      }
      return show
    }
  },
  watch: {
    type(val) {
      // this.initLoad()
    },
    organizationData(val) {
      setTimeout(() => { // 给个延时，防止意外
        this.searchHandle()
      }, 50)
    },
    treeFilterText(val) {
      this.$refs.treeRef.filter(val);
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      if (this.type === 'root') {
        this.getPaySettingTemplate()
        this.getPayInfoList()
      } else {
        this.getSubOrgsAllList()
      }
    },
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    // 获取左侧tree菜单数据
    async getPaySettingTemplate(companyId) {
      this.treeLoading = true
      // this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoTemplateListPost({
        pay_scenes: ['instore', 'online'],
        company: this.organizationData.company
      }))
      this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.payTemplateList = res.data
        let sceneResult = res.data.scene.sort((a, b) => {
          return b.key.charCodeAt(0) - a.key.charCodeAt(0);
        })
        this.paySettingList = this.setTemplatePrefix(sceneResult) // 子字段是重复的，加下前缀吧，key: 'parentKey-chikdKey'
        if (!this.selectKey) {
          this.paywayList = this.paySettingList
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 过滤tree数据
    filterTreeNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 设置配置字段的前缀，仅对二级使用
    setTemplatePrefix(list) {
      let result = deepClone(list)
      result.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            child.parent = item.key
            child.key = item.key + '-' + child.key
          })
        }
      })
      return result
    },
    // 获取支付配置列表
    async getPayInfoList(subPayType) {
      this.isLoading = true
      // await this.$sleep(2000)
      let params = {
        company: this.organizationData.company,
        organizations: [this.organizationData.id],
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (this.selectData) {
        if (this.selectData.parent) {
          params.pay_scene = this.selectData.parent
        } else {
          params.pay_scene = this.selectData.key
        }
      } else {
        params.pay_scenes = ['instore', 'online']
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.payInfoList = res.data.results.map(v => {
          v.enable = !!v.enable
          return v
        })
        if (subPayType) {
          this.queryPayInfoList = this.payInfoList.filter(v => {
            return v.payway === subPayType
          })
        } else {
          this.queryPayInfoList = this.payInfoList
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPayInfoList()
    },
    // join organizations
    showOrganizationsText(list) {
      let text = ''
      list.forEach((item) => {
        if (!text) {
          text = item.name
        } else {
          text += `，${item.name}`
        }
      })
      return text
    },
    // 点击左侧paySetting tree事件
    treeHandleNodeClick(data, type) {
      this.$nextTick(() => {
        let isRepeat = false
        // 感觉有点多余 start
        if (data && data.key === this.selectKey) {
          isRepeat = true
        }
        if (data && data.parent === this.selectKey) {
          isRepeat = true
        }
        let index = data ? data.key.indexOf('-') : -1
        let subPayType = ''
        if (index > -1) {
          // let p = data.key.substring(0, index)
          subPayType = data.key.substring(index + 1)
          // if (data.parent === p) {
          //   isRepeat = true
          // }
          // if (data.key === p) {
          //   isRepeat = true
          // }
        } else { // 点的是父级
          // if (data.parent === this.selectKey) {
          //   isRepeat = true
          // }
        }
        // end
        if (data) {
          if (index > -1) {
            this.selectKey = data.key.substring(0, index)
          } else {
            this.selectKey = data.key
          }
          this.selectData = data
        } else {
          this.selectKey = ''
          this.selectData = null
        }
        if (!isRepeat) { // 防止点击同一个重复调用接口
          this.payInfoList = []
          this.getPayInfoList(subPayType)
        } else {
          if (subPayType) {
            this.queryPayInfoList = []
            this.queryPayInfoList = this.payInfoList.filter(v => {
              return v.payway === subPayType
            })
          } else {
            this.queryPayInfoList = this.payInfoList
          }
        }
        // this.initPayawyList(data)
      })
    },
    // 初始化下支付渠道
    initPayawyList(data) {
      // this.paywayList = []
      this.subPaywayList = []
      if (this.selectKey) { // 选中左侧节点的情况下
        const len = this.paySettingList.length
        let keys = []
        for (let index = 0; index < len; index++) {
          keys.push(this.paySettingList[index].key)
          if (data.parent) {
            if (this.paySettingList[index].key === data.parent) {
              // this.paywayList.push(this.paySettingList[index])
              if (this.paySettingList[index].children && this.paySettingList[index].children.length) {
                this.paySettingList[index].children.forEach(item => {
                  if (data.key === item.key) {
                    this.payFormData.payScene = item.parent
                    this.subPaywayList = item.sub_payway
                  }
                })
              }
            } else {
              continue;
            }
          } else {
            if (this.paySettingList[index].key === this.selectKey) {
              // this.paywayList.push(this.paySettingList[index])
              this.payFormData.payScene = this.selectKey
            } else {
              continue;
            }
          }
        }
        if (!keys.includes(this.selectKey)) {
          this.payFormData.payway = this.selectKey
        } else {
          this.payFormData.payway = null
        }
      } else {
        // this.paywayList = this.paySettingList
      }
    },
    // 支付渠道tree change事件
    changePayway(e) {
      // 清空动态生成的表单字段
      if (this.formOperate === 'add') {
        this.formSettingList = []
        this.payFormData.subPayway = ''
      }
      if (e && this.payFormData.payway) {
        let keys = e.split('-')
        if (this.payFormData.payScene !== keys[0]) {
          this.payFormData.payScene = keys[0]
        }
        const len = this.paySettingList.length
        for (let index = 0; index < len; index++) {
          if (this.paySettingList[index].children && this.paySettingList[index].children.length) {
            this.paySettingList[index].children.forEach(item => {
              if (this.payFormData.payway === item.key) {
                this.subPaywayList = item.sub_payway
                // this.payFormData.payway = data.key
              }
            })
          }
        }
      }
    },
    // 二级支付方式change事件
    changeSubPayway(key) {
      let setting = this.payTemplateList.template[this.payFormData.payway.substring(this.payFormData.payway.indexOf('-') + 1)]
      this.initFormSettingList(setting)
    },
    // 初始化报表字段 initformSettingList
    initFormSettingList(data) {
      this.formSettingList = []
      let results = []
      if (data.defaults && data.defaults.length > 0) {
        this.setDynamicParams(this.formOperate, this.payFormData, data.defaults)
        results = deepClone(data.defaults)
      }
      let otherList = data[this.payFormData.subPayway]
      if (otherList && otherList.length) {
        this.setDynamicParams(this.formOperate, this.payFormData, otherList)
        results = results.concat(deepClone(otherList))
      }
      this.formSettingList = results
    },
    // 动态添加参数
    setDynamicParams(type, list, extraList) {
      // return
      if (type === 'add') {
        extraList.forEach(v => {
          switch (v.type) {
            case 'checkbox':
              if (v.default) {
                let values = JSON.parse(v.default)
                this.$set(list, v.key, values)
              } else {
                this.$set(list, v.key, [])
              }
              break;
            default:
              if (v.key === 'abc_subinfo') {
                v.value.forEach(subinfo => {
                  if (subinfo.default) {
                    this.$set(list, subinfo.key, subinfo.default)
                  } else {
                    this.$set(list, subinfo.key, '')
                  }
                })
              } else {
                if (v.default) {
                  this.$set(list, v.key, v.default)
                } else {
                  this.$set(list, v.key, '')
                }
              }
              break;
          }
        })
      } else {
        extraList.forEach(v => {
          // this.$set(this[list], v.key, info && info[v.key] ? info[v.key] : '')
          switch (v.type) {
            case 'checkbox':
              this.$set(list, v.key, this.dialogData.extra[v.key])
              break;
            default:
              if (v.key === 'abc_subinfo') {
                v.value.forEach(subinfo => {
                  this.$set(list, subinfo.key, this.dialogData.extra[subinfo.key])
                })
              } else {
                this.$set(list, v.key, this.dialogData.extra[v.key])
              }
              break;
          }
        })
      }
    },
    // paySetting option
    paySettingNormalizer(node) {
      if (!node) { return }
      return {
        id: node.key,
        label: node.name,
        children: node.children
      }
    },
    // 适用组织key，配置
    organizationNormalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children_list
      }
    },
    // 一次性加载适用组织所以数据，还有对数据进行层级裁剪
    async loadCurrentLevelOrganization() {
      if (this.formOperate === 'modify') {
        this.dialogIsLoading = true
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationTreeListPost({
        company_id: this.organizationData.company
      }));
      this.dialogIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(this.findKeyTreeList(res.data, 'company', this.organizationData.company))
        this.organizationList = this.deleteEmptyChildren(this.findKeyTreeList(res.data, 'company', this.organizationData.company))
        console.log('organizationList', this.organizationList)
        if (this.formOperate === 'add') {
          this.payFormData.organizations = getTreeDeepkeyList(this.organizationList, 'id', 'children_list')
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 通过固定字段查表数据，并返回
    findKeyTreeList(list, key, id) {
      let result = []
      list.forEach(item => {
        if (item[key] === id) {
          result.push(item)
        } else {
          if (item.children_list && item.children_list.length > 0) {
            let list = this.findKeyTreeList(item.children_list, key, id)
            if (list) {
              result.push(list)
            }
          }
        }
      })
      return [result[0]] // 临时修改下，后台目前会返回已删除父级的字节的（按理说应该不要返回这些被删除的父级组织的子级才对）
      // return result
    },
    // 远程加载适用组织tree数据
    async loadOrganization({ action, parentNode, callback }) {
      let params = {
        status__in: ['enable', 'disable'],
        page: 1,
        page_size: 99999,
        company: this.organizationData.company
      }
      if (parentNode && parentNode.id) {
        params.parent__in = parentNode.id
      } else {
        params.parent__is_null = '1'
        this.treeLoading = true
      }
      // 强制睡眠
      // await this.$sleep(1000);
      const [err, res] = await to(this.$apis.apiBackgroundAdminOrganizationListPost(params));
      this.treeLoading = false
      if (err) {
        callback()
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let results = res.data.results.map(item => {
          if (item.has_children) {
            item.children = null
          }
          return item
        })
        if (this.organizationList) {
          parentNode.children = results
        } else {
          this.organizationList = results
        }
        callback()
      } else {
        callback()
        this.$message.error(res.msg)
      }
    },
    // 删除空的子级
    deleteEmptyChildren(treeData, key) {
      key = key || 'children_list'
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item[key]) {
            if (item[key].length > 0) {
              traversal(item[key])
            } else {
              _that.$delete(item, key)
            }
          } else {
            _that.$delete(item, key)
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    handleSelectionChange(e) {
      this.selectTableCoumn = e.map(v => {
        return v.id
      })
    },
    // 弹窗事件
    openDialogHandle(type, data) {
      this.formOperate = type
      this.dialogData = data
      this.dialogVisible = true
      this.initPayawyList(this.selectData)
      if (type === 'add') {
        this.dialogTitle = '添加支付渠道'
        this.changePayway(this.payFormData.payway)
      } else {
        this.dialogTitle = '修改支付渠道'
        this.payFormData.merchantId = data.merchant_id
        this.payFormData.merchantName = data.merchant_name
        this.payFormData.payScene = data.pay_scene
        this.payFormData.payway = data.pay_scene + '-' + data.payway
        this.payFormData.subPayway = data.sub_payway
        this.payFormData.remark = data.remark
        this.payFormData.organizations = data.organizations.map(item => {
          return item.id
        })
        this.payFormData.company = data.company
        // 初始化下表单的字段
        this.changePayway(this.payFormData.payway)
        // 编辑的时候一定要在调changePayway时再赋值，不然会还原成默认的
        this.payFormData.subPayway = data.sub_payway
        this.changeSubPayway(data.sub_payway)
      }
      // 加载数据
      this.loadCurrentLevelOrganization()
    },
    // 弹窗取消事件
    clickCancleHandle() {
      this.$refs.payFormDataRef.resetFields()
      this.dialogVisible = false
    },
    // 弹窗确定事件
    async clickConfirmHandle() {
      if (this.dialogIsLoading) {
        return this.$message.error('请勿重复提交！')
      }
      // this.$sleep(2000)
      // this.dialogVisible = false
      this.$refs.payFormDataRef.validate(valid => {
        if (valid) {
          if (this.formOperate === 'add') {
            this.addPayInfo(this.formatData())
          } else {
            this.modifyPayInfo(this.formatData())
          }
        }
      })
    },
    // dialog关闭前的回调
    beforeCloseDialogHandle(done) {
      this.$refs.payFormDataRef.resetFields()
      done()
    },
    // dialog关闭后的事件
    closeDialogHandle() {
      this.formOperate = ''
      this.dialogTitle = ''
      this.dialogData = null
      this.formSettingList = []
    },
    // 格式化下传给后台的参数
    formatData() {
      let params = {
        extra: {},
        organization: this.organizationData.id,
        organizations: this.payFormData.organizations,
        merchant_id: this.payFormData.merchantId,
        merchant_name: this.payFormData.merchantName,
        remark: this.payFormData.remark,
        pay_scene: this.payFormData.payScene,
        payway: this.payFormData.payway.substring(this.payFormData.payway.indexOf('-') + 1),
        sub_payway: this.payFormData.subPayway
      }
      if (this.formOperate === 'modify') {
        params.id = this.dialogData.id
        params.company = this.dialogData.company
      } else {
        params.company = this.organizationData.company
      }
      this.formSettingList.forEach(item => {
        if (item.key === 'abc_subinfo') {
          item.value.forEach(subinfo => {
            params.extra[subinfo.key] = this.payFormData[subinfo.key]
          })
        } else {
          params.extra[item.key] = this.payFormData[item.key]
        }
      })
      return params
    },
    // 添加
    async addPayInfo(params) {
      this.dialogIsLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoAddPost(params))
      this.dialogIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.payInfoList = res.data.results
        this.$refs.payFormDataRef.resetFields() // 清空下表单
        this.dialogVisible = false
        this.$message.success(res.msg)
        this.getPayInfoList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async modifyPayInfo(params) {
      this.dialogIsLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoModifyPost(params))
      this.dialogIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.payInfoList = res.data.results
        this.$refs.payFormDataRef.resetFields() // 清空下表单
        this.dialogVisible = false
        this.$message.success(res.msg)
        this.getPayInfoList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除
    async deletePayInfo(type, id) {
      let ids = []
      if (type === 'one') {
        ids = [id]
      } else {
        ids = this.selectTableCoumn
      }
      if (!ids.length) {
        this.$message.error('请选择要删除的数据！')
        return
      }
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(1000);
            const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoDeletePost({
              ids: ids,
              organization: this.organizationData.id,
              company: this.organizationData.company
            }))
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getPayInfoList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    enablePayInfo(data) {
      this.$confirm(`确定${data.enable ? '启用' : '关闭'}？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(1000);
            const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoModifyPost({
              id: data.id,
              organization: this.organizationData.id,
              company: this.organizationData.company,
              enable: data.enable ? 1 : 0
            }))
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              data.enable = !data.enable
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getPayInfoList()
            } else {
              data.enable = !data.enable
              this.$message.error(res.msg)
            }
          } else {
            console.log(111, data)
            if (!instance.confirmButtonLoading) {
              data.enable = !data.enable
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 获取支付配置列表
    async getSubOrgsAllList() {
      this.subIsLoading = true
      // await this.$sleep(2000)
      const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoSubOrgsAllListPost({
        organizations: [this.organizationData.id],
        pay_scenes: ['instore', 'online'],
        company: this.organizationData.company
      }))
      this.subIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.collapseInfo = {}
        this.selectSubInfo = {}
        this.subPayInfoList = res.data.sort((a, b) => {
          return b.key.charCodeAt(0) - a.key.charCodeAt(0);
        })
        let openList = []
        deepClone(res.data).map(item => {
          let sceneOpen = false
          let activePayCollapse = []
          item.payways = item.payways.map(payway => {
            let paywayOpen = false
            payway.sub_payways.forEach(sub => {
              if (sub.binded) {
                sceneOpen = true
                paywayOpen = true
                if (this.selectSubInfo[`${item.key}-${payway.key}`]) {
                  this.selectSubInfo[`${item.key}-${payway.key}`].push(sub.id)
                } else {
                  this.$set(this.selectSubInfo, `${item.key}-${payway.key}`, [sub.id])
                }
                if (!activePayCollapse.includes(payway.key)) {
                  activePayCollapse.push(payway.key)
                }
                openList.push({
                  type: item.key + '-' + payway.key,
                  list: sub
                })
              }
            })
            payway.isOpen = paywayOpen
            return payway
          })
          this.$set(this.collapseInfo, item.key, {
            ...item,
            activePayCollapse: activePayCollapse,
            isOpen: sceneOpen
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // select tabel
    setDefaultTableSelect(rows) {
      rows.forEach(item => {
        let rowRefs = this.$refs[`subPayInfoListRef${item.type}`][0]
        rowRefs.toggleRowSelection(item.list, true)
      })
    },
    // switch 开关
    changeSceneHandle(e, type) {
      // for (let scene in this.collapseInfo) {
      //   if (scene === type && !e) {
      //     this.collapseInfo[scene].payways.forEach(payway => {
      //       payway.isOpen = false
      //       this.changePaywayHandle(false, payway.key, scene)
      //     })
      //   }
      // }
    },
    // 设置table 是否可选
    selectableHandle(row, index) {
      let canSelect = true
      if (!this.collapseInfo[row.pay_scene].isOpen) {
        canSelect = false
      }
      if (this.collapseInfo[row.pay_scene].isOpen) {
        this.collapseInfo[row.pay_scene].payways.forEach(item => {
          if (!item.isOpen && row.payway === item.key) {
            canSelect = false
          }
        })
      }
      return canSelect
    },
    // change
    changePaywayHandle(e, curent, parent) {
      if (e && !parent.activePayCollapse.includes(curent)) {
        parent.activePayCollapse.push(curent)
      }
    },
    // show bind button
    showBindBtnHandle(type) {
      let isShow = false
      for (let k in this.selectSubInfo) {
        if (k.indexOf(type) > -1) {
          isShow = true
        }
        if (isShow) {
          break
        }
      }
      return isShow
    },
    clickBindOrgsHandle(type) {
      let params = []
      this.collapseInfo[type].payways.forEach(payway => {
        if (this.collapseInfo[type].isOpen && payway.isOpen) {
          let selectSubInfoId = this.selectSubInfo[type + '-' + payway.key]
          payway.sub_payways.forEach(subPayways => {
            if (selectSubInfoId.includes(subPayways.id)) {
              params.push({
                id: subPayways.id
              })
            }
          })
        }
      })
      this.setSubOrgsBind(type, params)
    },
    // 支付设置-设置子组织绑定关联支付配置
    async setSubOrgsBind(type, ids) {
      this.subIsLoading = true
      // await this.$sleep(2000)
      let params = {
        pay_scene: type,
        organizations: [this.organizationData.id],
        payinfo: ids,
        company: this.organizationData.company
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminPayInfoSubOrgsBindPost(params))
      this.subIsLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getSubOrgsAllList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当treeselect 打开时要取消
    openTreeHandle(e) {
      // if (this.$refs.subPayway) {
      //   this.$refs.subPayway.blur()
      // }
    },
    changeSubPayHandle(e, row, data, key) {
      let selectsubpayway = []
      data.forEach(item => {
        if (item.binded && item.id !== row.id) {
          selectsubpayway.push(item.sub_payway)
        }
      })
      data.forEach(item => {
        // if (item.id !== row.id) {
        //   item.binded = false
        // } else {
        if (e) {
          if (!selectsubpayway.includes(row.sub_payway)) {
            // this.$set(this.selectSubInfo, key, row.id)
            if (this.selectSubInfo[key] && this.selectSubInfo[key].length) {
              if (!this.selectSubInfo[key].includes(row.id)) {
                this.selectSubInfo[key].push(row.id)
              }
            } else {
              this.$set(this.selectSubInfo, key, [row.id])
            }
          } else {
            if (item.id === row.id) {
              this.$nextTick(() => {
                item.binded = false
                let index = this.selectSubInfo[key].indexOf(row.id)
                if (index > -1) {
                  this.selectSubInfo[key].splice(index, 1)
                }
              })
            }
            this.$message.error('请勿选择相同支付类型！')
          }
        } else {
          let index = this.selectSubInfo[key].indexOf(row.id)
          if (index > -1) {
            this.selectSubInfo[key].splice(index, 1)
          }
        }
        // }
      })
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.paysetting-wrapper {
  position: relative;
  margin-top: 10px;
  .save-m-r{
    margin-right: 10px;
  }
  .tree-wrapper{
    width: auto;
    max-width: 250px;
    background-color: $organizationTreeBg;
    .tree-search {
      margin-bottom: 10px;
    }
    .all-tree{
      display: flex;
      align-items: center;
      line-height: 30px;
      font-size: 14px;
      cursor: pointer;
      padding-left: 10px;
      &.is-current {
        position: relative;
        color: #fda04d;
        background-color: #fbeee6;
        font-weight: 600;
        // &::before{
        //   content: '';
        //   position: absolute;
        //   width: 20px;
        //   left: -20px;
        //   top: 0;
        //   bottom: 0;
        //   background-color: #fbeee6;
        // }
        // &::after{
        //   content: '';
        //   position: absolute;
        //   width: 20px;
        //   right: -20px;
        //   top: 0;
        //   bottom: 0;
        //   background-color: #fbeee6;
        // }
      }
      .tree-search-icon{
        position: relative;
        display: inline-block;
        width: 18px;
        height: 40px;
        margin-right: 5px;
        img {
          display: inline-block;
          width: 18px;
          height: 18px;
          vertical-align: middle;
        }
      }
    }
    .el-tree{
      background-color: $organizationTreeBg;
      .el-tree-node__label {
        font-size: 14px;
        color: #23282d;
      }
      .el-tree-node__content{
        height: 30px;
      }
    }
    .tree-box {
      .is-current>.el-tree-node__content {
        position: relative;
        // background-color: #fbeee6;
        background-color: #fbeee6 !important;
      }
    }
  }
  .paysetting-container{
    display: flex;
    .paysetting-r{
      flex: 1;
      min-width: 0;
      margin-left: 10px;
    }
  }
  .paysetting-sub {
    $collapsebg: #f8f9fa;
    .sub-wrapper{
      margin-bottom: 15px;
      border-radius: 4px;
      border: solid 1px #e0e6eb;
      background-color: $collapsebg;
      .el-collapse{
        padding: 0 20px;
        background-color: $collapsebg;
        .el-collapse-item__header{
          background-color: $collapsebg;
        }
      }
    }
  }
  .tips-r{
    position: absolute;
    right: 45px;
    .close{
      display: none;
    }
  }
  .is-active .tips-r{
    .open{
      display: none;
    }
    .close{
      display: inline-block;
    }
  }
}
.ps-paysetting-dialog{
  .el-dialog__body{
    max-height: calc(100vh - 40vh);
    overflow-y: auto;
  }
  .paysetting-dialog{
    .el-form-item{
      // width: 45%;
      // &:nth-child(even) {
      //   margin-right: 5%;
      // }
      // &:first-child {
      //   margin-right: 5%;
      // }
    }
    .el-form-item__label{
      line-height: 1.2;
      text-align: justify;
    }
    .el-form-item__content{
      display: flex;
      align-items: center;
      // max-width: 200px;
      line-height: 32px;
      .el-input, .vue-treeselect{
        max-width: 324px;
      }
      .el-tooltip{
        margin-left: 5px;
        cursor: pointer;
      }
    }
    .tree-item .el-form-item__content{
      // max-width: 96%;
    }
    .remark-item {
      // width: 100%;
      // .el-form-item__content{
      //   width: 100%;
      //   max-width: 92%;
      // }
    }
    .vue-treeselect__control{
      height: 32px;
    }
    .el-form-item__label{
      // display: block;
      // text-align: left;
    }
    .el-select{
      width: 100%;
    }
  }
}
</style>

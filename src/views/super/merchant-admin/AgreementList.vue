<template>
  <div class="agreement-list container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
      </div>
      <div class="align-r">
        <button-icon color="origin" @click="gotoAddAgreement('add')"  v-permission="['background.admin.agreement.add']">新建协议</button-icon>
        <!-- v-permission="['background.admin.agreement.list_export']" -->
        <button-icon color="origin" type="export" @click="gotoExport" v-permission="['background.admin.agreement.list_export']">导出</button-icon>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <table-column v-for="item in tableSettings" :key="item.key" :col="item">
            <template #status="{ row }">
              <el-switch v-model="row.is_enable" @change="switchStatus(row)"  active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="showDialogHandle(row)" v-permission="['background.admin.agreement.agreement_detail']">查看</el-button>
              <el-button type="text" size="small" class="ps-text" :disabled="!!row.enable" @click="gotoAddAgreement('modify', row)" v-permission="['background.admin.agreement.modify']">编辑</el-button>
            </template>
          </table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- dialog start -->
    <dialog-message :show.sync="showDialog" :show-footer="false" width="60%" @close="closeDialogHandle">
      <div v-loading="dialogLoading" v-html="dialogContent" class="agreement-content"></div>
    </dialog-message>
    <!-- end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, unescapeHTML } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入

export default {
  name: 'AgreementList',
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSettings: [
        { label: '协议类型', key: 'agreement_type_alias' },
        { label: '协议名称', key: 'agreement_name' },
        { label: '创建时间', key: 'create_time' },
        { label: '启用时间', key: 'enable_time' }, // , isComponents: true, type: 'date', format: 'YYYY-MM-DD'
        { label: '操作人', key: 'operator_name' },
        { label: '状态', key: 'is_enable', type: "slot", slotName: "status" },
        { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "100" }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 1,
          maxWidth: '130px',
          dataList: [
            {
              label: '创建时间',
              value: 1
            },
            {
              label: '启用时间',
              value: 2
            }
          ]
        },
        select_time: {
          type: 'daterange',
          label: '',
          clearable: true,
          format: 'yyyy-MM-dd',
          value: []
        },
        agreement_type: {
          type: 'select',
          value: '',
          label: '协议类型',
          clearable: true,
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '信息保护协议',
              value: 'IPA'
            },
            {
              label: '刷脸支付服务协议',
              value: 'FPSA'
            },
            {
              label: '用户服务协议',
              value: 'USA'
            },
            {
              label: '隐私政策',
              value: 'PA'
            },
            {
              label: '免责声明',
              value: 'DA'
            },
            {
              label: '会员服务协议',
              value: 'MSA'
            }
          ]
        },
        operator: {
          type: 'input',
          value: '',
          label: '操作人',
          placeholder: '请输入'
        }
      },
      showDialog: false,
      dialogLoading: false,
      dialogContent: ''
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getAgreementList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    async getAgreementList() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const [err, res] = await to(this.$apis.apiBackgroundAdminAgreementAdminAgreementListPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.is_enable = !!v.enable
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getAgreementList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    // 修改状态, exist确认切换协议
    switchStatus(data, exist) {
      let title = ''
      let params = {
        id: data.id,
        enable: data.is_enable ? 1 : 0,
        confirm: 0
      }
      if (exist) {
        title = '已存在相同的协议内容，是否更新'
        params.confirm = exist
      } else {
        title = `是否${data.is_enable ? '开启' : '关闭'}当前协议？`
      }
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundAdminAgreementStatusChangePost(params)
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              if (res.data && res.data.exist) {
                await this.$sleep(300)
                this.switchStatus(data, 1)
              } else {
                this.$message.success(res.msg)
                this.searchHandle()
              }
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              data.is_enable = !data.is_enable
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    showDialogHandle(data) {
      this.dialogLoading = true
      this.showDialog = true
      this.getAgreementDetails(data.id)
    },
    // 编辑获取详情
    async getAgreementDetails(id) {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminAgreementAgreementDetailPost({
          id: id
        })
      )
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.dialogContent = unescapeHTML(res.data.content)
      } else {
        this.$message.error(res.msg)
      }
    },
    closeDialogHandle() {
      this.dialogContent = ''
    },
    gotoAddAgreement(type, data) {
      this.$router.push({
        name: 'SuperAddAgreement',
        query: {
          type: type,
          id: (data && data.id) || ''
        }
      })
    },
    // 导出弹窗
    gotoExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.totalCount
      }
      if (params.agreement_type) {
        params.agreement_type = [params.agreement_type]
      }
      const option = {
        type: 'AgreementList',
        url: 'apiBackgroundAdminAgreementListExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss">
.agreement-list {}
.agreement-content{
  img {
    max-width: 100%;
    height: auto;
  }
}
</style>

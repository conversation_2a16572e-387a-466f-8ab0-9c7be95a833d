<template>
  <div id="AddAgreement">
    <div class="form-wrapper" v-loading="isLoading">
      <el-form
        :model="formData"
        label-width="80px"
        :rules="noticeInfoRules"
        ref="noticeInfoForm"
      >
        <el-form-item label="协议类型" prop="agreement_type">
          <el-select
            ref="companyRef"
            v-model="formData.agreement_type"
            clearable
            filterable
            class="ps-input"
            style="width:400px;"
            @change="changeAgreementType"
          >
            <el-option
              v-for="item in agreementTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="协议名称" prop="agreement_name">
          <el-input
            v-model="formData.agreement_name"
            @input="searchHandle"
            placeholder="协议类型+年月日"
            class="ps-input"
            style="width:400px;"
            maxlength="40"
          ></el-input>
        </el-form-item>
        <el-form-item label="协议内容" prop="content">
          <TinymceUeditor
            v-model="formData.content"
            listener="focus"
            :custom-handle="blurSelsectHandle"
          ></TinymceUeditor>
        </el-form-item>
        <el-form-item>
          <div class="footer" style="margin-top: 20px" >
          <el-button style="width: 120px" @click="closeHandler">取消</el-button>
          <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitForm">
            {{ type === 'add' ? '保存' : '编辑' }}
          </el-button>
        </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { to, escapeHTML, unescapeHTML, parseTime } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import TinymceUeditor from '@/components/Tinymce/Tinymce.vue'
import { mapActions } from 'vuex'
export default {
  name: 'NoticeAdd',
  components: {
    TinymceUeditor
  },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      formData: {
        id: '',
        agreement_type: '',
        agreement_name: '',
        content: ''
      },
      agreementTypeList: [
        {
          label: '信息保护协议',
          value: 'IPA'
        },
        {
          label: '刷脸支付服务协议',
          value: 'FPSA'
        },
        {
          label: '用户服务协议',
          value: 'USA'
        },
        {
          label: '隐私政策',
          value: 'PA'
        },
        {
          label: '免责声明',
          value: 'DA'
        },
        {
          label: '会员服务协议',
          value: 'MSA'
        }
      ],
      noticeInfoRules: {
        agreement_type: [{ required: true, message: '请选择协议类型', trigger: 'blur' }],
        agreement_name: [{ required: true, message: '请输入协议名称', trigger: 'blur' }],
        content: [
          { required: true, message: '请输入协议内容', trigger: 'blur' }
        ]
      },
      type: '',
      isLoading: false
    }
  },
  created() {
  },
  mounted() {
    this.initLoad()
    if (this.$route.query.type) {
      this.type = this.$route.query.type
    }
    if (this.$route.query.id && this.$route.query.type === 'modify') {
      this.formData.id = this.$route.query.id
      this.getAgreementDetails(this.formData.id)
    }
  },
  methods: {
    ...mapActions({}),
    initLoad() {},
    // 新增
    async addAgreemeentHandle(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminAgreementAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        // 加个延时防止后端相应不及时
        await this.$sleep(300)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑
    async modifyAgreemeentHandle(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundAdminAgreementModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        // 加个延时防止后端相应不及时
        await this.$sleep(300)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑获取详情
    async getAgreementDetails(id) {
      const [err, res] = await to(
        this.$apis.apiBackgroundAdminAgreementAgreementDetailPost({
          id: id
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.formData.agreement_name = res.data.agreement_name
        this.formData.agreement_type = res.data.agreement_type
        this.formData.content = unescapeHTML(res.data.content)
      } else {
        this.$message.error(res.msg)
      }
    },
    blurSelsectHandle(e) {
      this.$refs.companyRef.blur()
    },
    searchHandle() {},
    submitForm() {
      this.$refs.noticeInfoForm.validate(valid => {
        if (valid) {
          let params = {
            agreement_type: this.formData.agreement_type,
            agreement_name: this.formData.agreement_name,
            content: escapeHTML(this.formData.content) // 为了安全转下码
          }
          if (this.type === 'modify') {
            params.id = this.formData.id
            this.modifyAgreemeentHandle(params)
          } else {
            this.addAgreemeentHandle(params)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 协议类型修改
    changeAgreementType(e) {
      let current = this.agreementTypeList.find(v => v.value === e)
      this.formData.agreement_name = current.label + parseTime(new Date(), '{y}-{m}-{d}')
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss" scoped>
#AddAgreement {
  .form-wrapper {
    margin: 20px;
    padding: 30px 20px;
    background-color: #fff;
    border-radius: 5px;
  }
}
</style>

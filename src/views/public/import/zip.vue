<template>
  <div class="mul-import-img">
    <el-form class="import-form-wrapper" label-width="140px">
      <el-form-item :label="templateLabel">
        <el-link
          class="origin"
          type="primary"
          :href="templateUrl"
        >
          点击下载
        </el-link>
      </el-form-item>
      <el-form-item :label="importLabel">
        <file-upload
          v-loading="upLoading"
          drag
          :data="uploadParams"
          :limit="limit"
          @fileLists="getSuccessUploadRes"
          :before-upload="beforeUpload"
          prefix="food_img_zip"
          :action="actionUrl"
          :on-remove="remove"
        >
          <div class="">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em class="origin el-link">点击上传</em>
            </div>
          </div>
          <div class="el-upload__tip" slot="tip">只能上传zip文件</div>
        </file-upload>
      </el-form-item>
      <el-form-item>
        <el-button :disabled="upLoading" type="primary" class="import-btn-wrapper ps-origin-btn" @click="mulImortFoodImg">
          确定
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  props: {
    templateUrl: String,
    templateLabel: {
      type: String,
      default: '导入模板'
    },
    importLabel: {
      type: String,
      default: '上传文件'
    },
    apiUrl: String
  },
  mixins: [exportExcel],
  data() {
    return {
      limit: 1,
      actionUrl: '',
      uploadParams: {},
      uploadUrl: '',
      upLoading: false,
      isLoading: false
    }
  },
  methods: {
    async getUploadParams() {
      this.uploadParams = {
        key: new Date().getTime() + Math.floor(Math.random() * 150),
        prefix: 'food_img_zip'
      }
    },
    beforeUpload(file) {
      let reg = /application\/\S*zip\S*/
      if (!reg.test(file.type)) {
        this.$message.error('请上传后缀名为.zip的压缩包文件')
        return false
      }
      this.upLoading = true
    },
    remove() {
      this.uploadUrl = ''
    },
    getSuccessUploadRes(res) {
      // if (res.code === 0) {
      //   this.uploadUrl = res.data.public_url
      // }
      this.uploadUrl = res[0].url
      this.upLoading = false
    },
    mulImortFoodImg() {
      if (!this.uploadUrl) {
        this.$message.error('压缩包还没上传完毕或未上传')
        return
      }
      const option = {
        type: 'MulImportIngredientImgs',
        url: this.apiUrl,
        message: '确定导入？',
        params: {
          oss_url: this.uploadUrl
        }
      }
      this.exportHandle(option)
    }
  },
  created() {
    // this.getUploadParams()
  }
}
</script>

<style lang="scss">
.mul-import-img {
  margin-top: 20px;
  padding: 20px 0;
  background-color: #fff;
  border-radius: 12px;
  .origin.el-link{
    color: #FF9B45;
  }
  .import-form-wrapper {
    width: 500px;
  }
  .buttons {
    padding-bottom: 20px;
  }
  .import-btn-wrapper {
    width: 100%;
  }
}
</style>

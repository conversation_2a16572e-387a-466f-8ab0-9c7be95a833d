/* eslint-disable no-unused-vars */
import {
  getToken,
  setToken,
  removeToken,
  getUserInfo,
  setSessionStorage,
  getSessionStorage,
  removeSessionStorage,
  sleep,
  getTreeDeepkeyList,
  getRouterFlattenList
} from '@/utils'
import router, { resetRouter, asyncRoutes } from '@/router'
import apis from '@/api'
import { Message } from 'element-ui'

let organization = getSessionStorage('organization')

const state = {
  token: '',
  userInfo: {}, // 用户信息
  organization: organization ? Number(organization) : '', // 当前登录账号的组织
  permissions: [], // 用户的所有权限key列表
  allPermissions: [], // 当前登录用户的所有权限
  permissionsTree: [] // 用户权限tree
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
  SET_ALLPERMISSIONS: (state, permissions) => {
    state.allPermissions = permissions
  },
  SET_USERINFO: (state, data) => {
    state.userInfo = Object.assign({}, data)
  },
  SET_PERMISSIONTREE: (state, data) => {
    state.permissionsTree = Object.assign([], data)
  },
  SET_ORGANIZATION: (state, organization) => {
    state.organization = organization
  }
}

const actions = {
  // user login
  login({ commit, dispatch }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', data.token)
      setToken(data.token)
      dispatch('setUserInfo', data)
      dispatch('setAllPermissions', data.role_permission)
      resolve()
    })
  },
  // set organization
  setOrganization({ commit, state }, data) {
    return new Promise(resolve => {
      setSessionStorage('organization', data)
      commit('SET_ORGANIZATION', data)
      // 切换组织需要请除菜品数据
      removeSessionStorage('systemIngredientsList')
      removeSessionStorage('foodIngredientList')
      resolve()
    })
  },
  // set user info
  setUserInfo({ commit, dispatch, state }, data) {
    return new Promise(resolve => {
      let orgs = Object.keys(data.orgs)
      let organization = getSessionStorage('organization')
      if (orgs.length > 0) {
        // 处理下orgs 别的地方需要用到
        if (!data.organizationList) {
          data.organizationList = orgs.map((key, index) => {
            if (!organization && !index) {
              dispatch('setOrganization', Number(key))
            }
            return {
              id: Number(key),
              name: data.orgs[key]
              // isSelect: !index
            }
          })
        }
      }
      setSessionStorage('USERINFO', encodeURIComponent(JSON.stringify(data)))
      commit('SET_USERINFO', data)
      resolve()
    })
  },
  // 更新userinfo的数据
  updateUserInfo({ commit, dispatch }, data) {
    return new Promise(resolve => {
      setSessionStorage('USERINFO', encodeURIComponent(JSON.stringify(data)))
      commit('SET_USERINFO', data)
      resolve()
    })
  },
  // Set the permissions owned for the current user
  setAllPermissions({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_ALLPERMISSIONS', data)
      resolve()
    })
  },
  // set permission tree
  setPermissionsTree({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_PERMISSIONTREE', data)
      resolve()
    })
  },
  // set permission key list
  // 注意，这是当前左侧栏线上的菜单的权限表，不是用户所有的，do you konw
  setPermissions({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('SET_PERMISSIONS', data)
      resolve()
    })
  },
  // F5 刷新用户的信息，先从本地拿用户数据先
  async setDefaultInfo({ commit, dispatch }, data) {
    const userInfo = JSON.parse(decodeURIComponent(getSessionStorage('USERINFO')))
    // let organization = getSessionStorage('organization')
    commit('SET_TOKEN', getToken())
    commit('SET_USERINFO', userInfo)
    // if (organization) {
    //   dispatch('setOrganization', organization)
    // }
    await dispatch('setAllPermissions', userInfo.role_permission)
  },
  // 获取菜单列表
  getPermissionList({ dispatch, rootGetters }, data) {
    return new Promise((resolve, reject) => {
      // 调用获取权限的接口
      apis
        .apiBackgroundBaseMenuGetListPost(data)
        .then(async res => {
          if (res.code === 0) {
            let allPermissions = []
            const navMenuList = res.data.filter(v => {
              allPermissions = allPermissions.concat(getTreeDeepkeyList([v]))
              return v.is_menu
            })
            // zrj+ 非超管端添加首页权限
            let id = this.state.user.userInfo.account_type
            // operations_system
            console.log(navMenuList)
            // navMenuList.push({
            //   verbose_name: "运营管理",
            //   index: "1.1",
            //   key: "operations_system",
            //   is_menu: true,
            //   parent: "1",
            //   level: 1,
            //   perm_type: 2
            // })
            // allPermissions.push('operations_system')
            dispatch('setAllPermissions', allPermissions)

            // 控制预约权限
            let reservationData = {
              permissions: 'background_reservation.list',
              api: 'apiBackgroundReservationBackgroundReservationSettingsReservationOrganizationCheckPost'
            }
            await dispatch('isAllowOrder', reservationData)

            // 控制报餐权限
            let mealReportData = {
              permissions: 'background_report_meal.list',
              api: 'apiBackgroundReportMealReportMealSettingsReportMealOrganizationCheckPost'
            }
            await dispatch('isAllowOrder', mealReportData)

            const permissions = await dispatch('navTabs/setDefaultMenu', navMenuList, {
              root: true
            })
            await dispatch('setPermissions', permissions)
            await dispatch('setPermissionsTree', res.data)
            resolve(permissions)
          } else {
            resolve([])
            Message.error(res.msg)
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  },
  // logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', '')
      commit('SET_PERMISSIONS', [])
      commit('SET_PERMISSIONTREE', [])
      commit('SET_ALLPERMISSIONS', [])
      commit('SET_USERINFO', {})

      removeToken()
      removeSessionStorage('USERINFO')
      removeSessionStorage('organization')
      removeSessionStorage('CHECKDOUBLEFACTOR')
      removeSessionStorage('systemIngredientsList')
      removeSessionStorage('foodIngredientList')
      resetRouter()

      dispatch('navTabs/delAllViews', null, { root: true })
      dispatch('permission/clearRoute', null, { root: true })
      dispatch('navTabs/setNavMenuList', [], { root: true })

      resolve()
    })
  },
  getMenuStatus({ state }, api) {
    let id = state.userInfo.company_id
    return new Promise((resolve, reject) => {
      apis[api](id)
        .then(async res => {
          if (res.code === 0) {
            console.log(res)
            resolve('YES')
          } else {
            resolve('NO')
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  },
  async isAllowOrder({ state, dispatch }, data) {
    let allPermissions = state.allPermissions
    let index = allPermissions.findIndex(e => e === data.permissions)
    let status = await dispatch('getMenuStatus', data.api)
    console.log('status', status)
    if (status === 'YES') {
      if (index === -1) {
        allPermissions.push(data.permissions)
      }
    } else {
      if (index >= 0) {
        allPermissions.splice(index, 1)
      }
    }
    dispatch('setAllPermissions', allPermissions)
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_PERMISSIONS', [])
      removeToken()
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

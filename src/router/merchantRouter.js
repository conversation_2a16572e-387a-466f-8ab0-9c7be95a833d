// import Layout from '@/layout'
// 注意layout一定要最后生成再替换成import的方式引入，不然会触发页面重复渲染
let Layout = 'Layout'

const merchant = [
  {
    path: '/organization',
    component: Layout,
    redirect: '/organization/list',
    alwaysShow: true,
    name: 'MerchantSystem',
    meta: {
      title: 'merchant_system',
      icon: 'system',
      permission: ['merchant_system']
    },
    children: [
      {
        path: 'list',

        component: () =>
          import(/* webpackChunkName: "organization" */ '@/views/merchant/system/Organization'),
        name: 'OrganizationList',
        meta: {
          noCache: true,
          title: 'organization_list',
          permission: ['background_organization.organization']
        }
      },
      {
        path: 'role',
        component: () => import(/* webpackChunkName: "role" */ '@/views/merchant/system/RoleList'),
        name: 'RoleList',
        meta: {
          noCache: true,
          title: 'role_management',
          permission: ['background_organization.role']
        }
      },
      {
        path: 'role/setting',
        component: () =>
          import(/* webpackChunkName: "RoleSetting" */ '@/views/merchant/system/RoleSetting'),
        name: 'RoleSetting',
        hidden: true,
        meta: {
          noCache: true,
          title: 'role_setting',
          activeMenu: '/organization/role',
          permission: ['background_organization.role.set_permission']
        }
      },
      {
        path: 'account',

        component: () =>
          import(/* webpackChunkName: "account" */ '@/views/merchant/system/AccountList'),
        name: 'AccountList',
        meta: {
          noCache: true,
          title: 'role_account_management',
          permission: ['background_organization.account']
        }
      },
      {
        path: 'general_settings',
        component: () =>
          import(
            /* webpackChunkName: "general_settings" */ '@/views/merchant/system/GeneralSettings'
          ),
        name: 'GeneralSettings',
        meta: {
          noCache: true,
          title: 'generalSettings',
          permission: ['background_organization.account']
          // permission: ['background.admin.organization.get_common_settings']
        }
      },
      {
        path: 'loglist',

        component: () =>
          import(/* webpackChunkName: "logList" */ '@/views/public/log/OperatingLogList'),
        name: 'LogList',
        meta: {
          noCache: true,
          title: 'list_log',
          permission: ['base.log.list_log']
        }
      },
      {
        path: 'notice_admin',
        component: () =>
          import(
            /* webpackChunkName: "noticeadmin" */ '@/views/merchant/system-settings/NoticeAdmin'
          ),
        name: 'NoticeAdmin',
        meta: {
          noCache: true,
          title: 'notice_admin',
          permission: ['background_messages.messages.list'] // 假权限
        }
      },
      {
        path: 'notice-:type',
        component: () =>
          import(/* webpackChunkName: "notice_add" */ '@/views/merchant/system-settings/NoticeAdd'),
        name: 'NoticeAdd',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice',
          activeMenu: '/organization/notice_admin',
          permission: ['background_messages.messages.add']
        }
      },
      {
        path: 'notice_list',
        component: () =>
          import(
            /* webpackChunkName: "notice_list" */ '@/views/merchant/system-settings/NoticeList'
          ),
        name: 'NoticeList',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice_list',
          // activeMenu: '/organization/notice_admin',
          no_permission: true
          // permission: ['background_messages.messages.get_msg_list']
        }
      },
      {
        path: 'notice_detail',
        component: () =>
          import(
            /* webpackChunkName: "notice_detail" */ '@/views/merchant/system-settings/NoticeDetail'
          ),
        name: 'NoticeDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice_detail',
          activeMenu: '/organization/notice_admin',
          no_permission: true
          // permission: ['background_messages.messages.get_msg_list']
        }
      },
      {
        path: 'dingtalk_message_seting',
        component: () =>
          import(
            /* webpackChunkName: "noticeadmin" */ '@/views/merchant/system/DingTalkMessageSeting'
          ),
        name: 'DingTalkMessageSeting',
        meta: {
          noCache: true,
          title: 'dingTalkMessageSeting',
          permission: ['background_messages.dingtalk_message_setting'] // 假权限
        }
      },
      {
        path: 'module_admin',
        component: () =>
          import(
            /* webpackChunkName: "module_admin" */ '@/views/merchant/system/ModuleAdmin'
          ),
        name: 'ModuleAdmin',
        meta: {
          noCache: true,
          title: 'module_admin',
          permission: ['background_organization.app_permission']
        }
      },
      {
        path: 'qrcode_template',
        component: () =>
          import(/* webpackChunkName: "qrcode_template" */ '@/views/merchant/system/QrcodeTemplate'),
        name: 'QrcodeTemplate',
        meta: {
          noCache: true,
          title: 'qrcode_template',
          permission: ['background_organization.qrcode_template']
        }
      }
    ]
  },
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/board_admin',
    alwaysShow: true,
    name: 'Dashboard',
    meta: {
      title: 'board_admin',
      icon: 'system',
      permission: ['merchant_system']
    },
    children: [
      {
        path: 'board_admin',
        component: () =>
          import(/* webpackChunkName: "board_admin" */ '@/views/merchant/dashboard/DataBoardAdmin'),
        name: 'DataBoardAdmin',
        meta: {
          noCache: true,
          title: 'board_admin',
          permission: ['merchant_system']
        }
      },
      {
        path: 'board_admin/:type',
        component: () =>
          import(/* webpackChunkName: "board_admin" */ '@/views/merchant/dashboard/AddDataBoard'),
        name: 'AddDataBoard',
        hidden: true,
        meta: {
          noCache: true,
          title: 'board_admin',
          activeMenu: '/dashboard/board_admin',
          permission: ['merchant_system']
        }
      }
    ]
  },
  {
    path: '/user-center',
    component: Layout,
    redirect: '/user-center/list',
    alwaysShow: true,
    name: 'userCenter',
    meta: {
      title: 'user_center',
      icon: 'user',
      permission: ['user_center']
    },
    children: [
      {
        path: 'list',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UseCenter'
          ),
        name: 'UseCenter',
        meta: {
          noCache: true,
          title: 'user_admin',
          permission: ['card_service.card_user.list']
        }
      },
      {
        path: 'mul_import_face',
        component: () =>
          import(
            /* webpackChunkName: "merchant-mul_import_face" */ '@/views/merchant/user-center/MulImportFace'
          ),
        name: 'MerchantMulImportFace',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/user-center/list',
          title: 'mul_import_face',
          permission: ['card_service.card_face.batch_import']
        }
      },
      {
        path: 'userDepartment',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserDepartment'
          ),
        name: 'UserDepartment',
        meta: {
          noCache: true,
          title: 'user_department',
          permission: ['card_service.card_department_group']
        }
      },
      {
        path: 'userGroup',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserGroup'
          ),
        name: 'UserGroup',
        meta: {
          noCache: true,
          title: 'user_group',
          permission: ['card_service.card_user_group']
        }
      },
      {
        path: 'userAccountAdmin',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserAccountList'
          ),
        name: 'UserAccountAdmin',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'user_account_admin'
          // permission: ['user_settings']
        },
        children: [
          {
            path: 'userAccountList',
            component: () =>
              import(
                /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserAccountList'
              ),
            name: 'UserAccountList',
            meta: {
              noCache: true,
              title: 'user_account_list'
              // permission: ['user_settings']
            }
          },
          {
            path: 'userAccountSetting',
            component: () =>
              import(
                /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/UserAccountSetting'
              ),
            name: 'UserAccountSetting',
            meta: {
              noCache: true,
              title: 'user_account_setting'
              // permission: ['user_settings']
            }
          },
          {
            path: 'overdraftSetting',
            component: () =>
              import(
                /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/OverdraftSetting'
              ),
            name: 'OverdraftSetting',
            meta: {
              noCache: true,
              title: 'overdraft_setting'
              // permission: ['user_settings']
            }
          },
          {
            path: 'overdraftDetail',
            component: () =>
              import(
                /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/OverdraftDetail'
              ),
            name: 'OverdraftDetail',
            meta: {
              noCache: true,
              title: 'Overdraft_detail'
              // permission: ['user_settings']
            }
          }
        ]
      },
      {
        path: 'withdrawalAdmin',
        component: () =>
          import(
            /* webpackChunkName: "merchant-mul_import_face" */ '@/views/merchant/user-center/WithdrawalAdmin'
          ),
        name: 'WithdrawalAdmin',
        meta: {
          noCache: true,
          title: 'withdrawal_admin',
          permission: ['card_service.card_user.card_person_quit_list']
        }
      },
      {
        path: 'cardLossAdmin',
        component: () =>
          import(
            /* webpackChunkName: "merchant-mul_import_face" */ '@/views/merchant/user-center/CardLossAdmin'
          ),
        name: 'CardLossAdmin',
        meta: {
          noCache: true,
          title: 'card_loss_list',
          permission: ['card_service.card_user.card_loss_list']
        }
      },
      {
        path: 'flatCostListAdmin',
        component: () =>
          import(
            /* webpackChunkName: "merchant-flatcostlist" */ '@/views/merchant/user-center/FlatCostList'
          ),
        name: 'FlatCostListAdmin',
        alwaysShow: true,
        meta: {
          // noCache: true,
          title: 'user_flat_cost',
          permission: ['card_service.card_user.flat_cost_list']
        },
        children: [
          {
            path: 'flatCostList',
            component: () =>
              import(
                /* webpackChunkName: "merchant-flatCostList" */ '@/views/merchant/user-center/FlatCostList'
              ),
            name: 'FlatCostList',
            meta: {
              // noCache: true,
              title: 'user_flat_cost_list',
              permission: ['card_service.card_user.flat_cost_list']
            }
          },
          {
            path: 'repairCardList',
            component: () =>
              import(
                /* webpackChunkName: "merchant-repairCardList" */ '@/views/merchant/user-center/RepairCardList'
              ),
            name: 'RepairCardList',
            meta: {
              // noCache: true,
              title: 'user_repair_card_list'
              // permission: ['card_user.delay_flat_cost_list']
            }
          }
        ]
      },
      {
        path: 'cardSubsidy',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/CardSubsidy'
          ),
        name: 'CardSubsidy',
        meta: {
          noCache: true,
          title: 'user_card_subsidy',
          permission: ['card_service.card_subsidy.list']
        }
      },
      {
        path: 'cardSubsidy_:type',
        component: () =>
          import(/* 2: "merchant-usecenter" */ '@/views/merchant/user-center/AddCardSubsidy'),
        name: 'AddCardSubsidy',
        hidden: true,
        meta: {
          noCache: true,
          title: 'user_card_subsidy_add',
          activeMenu: '/user-center/cardSubsidy',
          permission: ['card_service.card_subsidy.list']
        }
      },
      {
        path: 'cardSubsidyDetail',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/CardSubsidyDetail'
          ),
        name: 'CardSubsidyDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'user_card_subsidy_detail',
          activeMenu: '/user-center/cardSubsidy',
          permission: ['card_service.card_subsidy.list']
        }
      },
      {
        path: 'subsidy_receive_detail',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/SubsidyReceiveDetail'
          ),
        name: 'SubsidyReceiveDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'subsidy_receive_detail',
          activeMenu: '/user-center/cardSubsidy',
          permission: ['card_service.card_subsidy.list']
        }
      },
      {
        path: 'cardLog',
        component: () =>
          import(
            /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/user-center/CardLog'
          ),
        name: 'CardLog',
        meta: {
          noCache: true,
          title: 'user_card_log',
          permission: ['card_service.card_log.get_card_operation_log_list']
        }
      }
    ]
  },
  {
    path: '/device-management',
    component: Layout,
    redirect: '/device-management/device_list',
    alwaysShow: true,
    name: 'DeviceManagement',
    meta: {
      title: 'device_management',
      icon: 'device',
      permission: ['device_management']
    },
    children: [
      {
        path: 'device_list',
        component: () =>
          import(
            /* webpackChunkName: "device_list" */ '@/views/merchant/device-management/DeviceList'
          ),
        name: 'DeviceList',
        meta: {
          noCache: true,
          title: 'device_list',
          permission: ['background_device.device_info.list']
        }
      },
      {
        path: 'cupboard_edit',
        component: () =>
          import(
            /* webpackChunkName: "cupboard_edit" */ '@/views/merchant/device-management/CupboardEdit'
          ),
        name: 'CupboardEdit',
        hidden: true,
        meta: {
          noCache: true,
          title: 'cupboard_edit',
          activeMenu: '/device-management/device_list',
          permission: ['background_device.device_info.list']
        }
      },
      {
        path: 'weightFood',
        component: () =>
          import(
            /* webpackChunkName: "weight_food" */ '@/views/merchant/device-management/WeightFood'
          ),
        name: 'WeightFood',
        hidden: true,
        meta: {
          noCache: true,
          title: 'weight_food',
          activeMenu: '/device-management/deviceList',
          permission: ['device_management']
        }
      },
      {
        path: 'device_cloud_print',
        component: () =>
          import(
            /* webpackChunkName: "device_cloud_print" */ '@/views/merchant/device-management/deviceCloudPrint'
          ),
        name: 'deviceCloudPrint',
        meta: {
          noCache: true,
          title: 'device_cloud_print',
          permission: ['background_printer.printer.list']
        }
      },
      {
        path: 'device_admin_card',
        component: () =>
          import(
            /* webpackChunkName: "device_admin_card" */ '@/views/merchant/device-management/deviceAdminCard'
          ),
        name: 'deviceAdminCard',
        meta: {
          noCache: true,
          title: 'device_admin_card',
          permission: ['background_device.device_info.list']
        }
      },
      {
        path: 'weightSetting',
        name: 'WeightSetting',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'weight_setting',
          permission: ['background_device.device_buffet.details']
        },
        children: [
          {
            path: 'routineSetting',
            component: () =>
              import(
                /* webpackChunkName: "routine_setting" */ '@/views/merchant/device-management/RoutineSetting'
              ),
            name: 'RoutineSetting',
            meta: {
              noCache: true,
              title: 'routine_setting',
              permission: ['device_management']
            }
          },
          {
            path: 'trayQRcode',
            component: () =>
              import(
                /* webpackChunkName: "tray_qrcode" */ '@/views/merchant/device-management/TrayQRcode'
              ),
            name: 'TrayQRcode',
            meta: {
              noCache: true,
              title: 'tray_qrcode',
              permission: ['device_management']
            }
          }
        ]
      },
      // {
      //   path: 'printSetting',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "merchant-usecenter" */ '@/views/merchant/device-management/PrintSetting'
      //     ),
      //   name: 'PrintSetting',
      //   meta: {
      //     noCache: true,
      //     title: 'print_setting',
      //     permission: ['background_device.device_print_conf.list']
      //   }
      // },
      {
        path: 'printDevice_:type',
        component: () =>
          import(
            /* webpackChunkName: "print_device" */ '@/views/merchant/device-management/AddPrintDevice'
          ),
        name: 'AddPrintDevice',
        hidden: true,
        meta: {
          noCache: true,
          title: 'print_device',
          activeMenu: '/device-management/printSetting',
          permission: ['device_management']
        }
      },
      {
        path: 'cupboard_setting',
        component: () =>
          import(
            /* webpackChunkName: "cupboard_setting" */ '@/views/merchant/device-management/CupboardSetting'
          ),
        name: 'CupboardSetting',
        meta: {
          noCache: true,
          title: 'cupboard_setting',
          permission: ['background_device.cupboard_conf.details']
        }
      },
      {
        path: 'third_party_equipment_admin',
        component: () =>
          import(
            /* webpackChunkName: "third_party_equipment_admin" */ '@/views/merchant/device-management/ThirdPartyEquipmentAdmin'
          ),
        name: 'ThirdPartyEquipmentAdmin',
        meta: {
          noCache: true,
          title: 'third_party_equipment_admin',
          permission: ['background_device.third_device.list']
        }
      },
      {
        path: 'consume_machine_setting',
        component: () =>
          import(
            /* webpackChunkName: "consume_machine_setting" */ '@/views/merchant/device-management/ConsumeMachineSetting'
          ),
        name: 'ConsumeMachineSetting',
        meta: {
          noCache: true,
          title: 'consume_machine_setting',
          permission: ['background_device.consume_info.details']
        }
      }
    ]
  },
  {
    path: '/meal_management',
    component: Layout,
    redirect: '/meal_management/food_admin/Ingredients_admin',
    alwaysShow: true,
    name: 'merchant_system',
    meta: {
      title: 'meal_management',
      icon: 'meal_1',
      permission: ['meal_management']
    },
    children: [
      {
        path: 'food_admin',
        name: 'ingredientsLibrary',
        redirect: '/meal_management/food_admin/Ingredients_admin',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'food_admin',
          permission: ['background_food.list']
        },
        children: [
          // {
          //   path: 'ingredients_library',
          //   component: () =>
          //     import(
          //       /* webpackChunkName: "ingredients_library" */ '@/views/merchant/meal-management/food-admin/IngredientsLibrary'
          //     ),
          //   name: 'ingredientsLibrary',
          //   meta: {
          //     noCache: true,
          //     title: 'ingredients_library',
          //     permission: ['meal_management']
          //   }
          // },
          {
            path: 'ingredients_category',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "ingredients_category" */ '@/views/merchant/meal-management/food-admin/IngredientsCategory'
              ),
            name: 'MerchantIngredientsCategory',
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/Ingredients_admin',
              title: 'ingredients_category'
              // no_permission: true
              // permission: ['meal_management']
            }
          },
          {
            path: 'ingredients_detail_:type',
            component: () =>
              import(
                /* webpackChunkName: "add_ingredients_admin" */ '@/views/merchant/meal-management/food-admin/AddIngredients'
              ),
            name: 'MerchantAddIngredients',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/Ingredients_admin',
              title: 'ingredients',
              permission: ['background_food.ingredient.list']
            }
          },
          {
            path: 'Ingredients_admin',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/IngredientsAdmin'
              ),
            name: 'ingredientsAdmin',
            meta: {
              noCache: true,
              title: 'ingredients_library',
              permission: ['background_food.ingredient.list']
            }
          },
          {
            path: 'import_ingredients/:type',
            component: () =>
              import(
                /* webpackChunkName: "merchant_add_ingredients" */ '@/views/merchant/meal-management/food-admin/ImportIngredients'
              ),
            name: 'MerchantImportIngredients',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/Ingredients_admin',
              title: 'import_ingredients',
              permission: ['background_food.ingredient.list']
            }
          },
          {
            path: 'copy_ingredients',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/CopyIngredients'
              ),
            name: 'MerchantCopyIngredients',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/Ingredients_admin',
              title: 'copy_ingredients',
              permission: ['background_food.ingredient.list']
            }
          },
          {
            path: 'import_commodity/:type',
            component: () =>
              import(
                /* webpackChunkName: "merchant_import_commodity" */ '@/views/merchant/meal-management/food-admin/ImportCommodity'
              ),
            name: 'MerchantImportCommodity',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/meal_food_list',
              title: 'import_commodity',
              permission: ['background_food.food.list']
            }
          },
          {
            path: 'import_commodity_image',
            component: () =>
              import(
                /* webpackChunkName: "import_commodity_image" */ '@/views/merchant/meal-management/food-admin/ImportCommodityImage'
              ),
            name: 'MerchantImportCommodityImage',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/meal_food_list',
              title: 'import_commodity_image',
              permission: ['background_food.food.list']
            }
          },
          {
            path: 'import_ingredient_image',
            component: () =>
              import(
                /* webpackChunkName: "import_ingredient_image" */ '@/views/merchant/meal-management/food-admin/ImportIngredientImage'
              ),
            name: 'MerchantImportIngredientImage',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/Ingredients_admin',
              title: 'import_ingredient_image',
              permission: ['background_food.ingredient.ingredient_image_bat_add']
            }
          },
          {
            path: 'meal_food_classification',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/MealFoodClassification'
              ),
            name: 'mealFoodClassification',
            meta: {
              noCache: true,
              title: 'meal_food_classification',
              permission: ['background_food.food_sort.list']
            }
          },
          {
            path: 'meal_food_list',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/MealFoodList'
              ),
            name: 'mealFoodList',
            meta: {
              noCache: true,
              title: 'meal_food_list',
              permission: ['background_food.food.list']
            }
          },
          {
            path: 'copy_foods',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/CopyFoods'
              ),
            name: 'MerchantCopyFoods',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/meal_food_list',
              title: 'copy_foods',
              permission: ['background_food.food.list']
            }
          },
          {
            path: 'collective_admin',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/collective-admin/CollectiveAdmin'
              ),
            name: 'CollectiveAdmin',
            meta: {
              noCache: true,
              title: 'collective_admin',
              permission: ['background_food.collective.list']
            }
          },
          {
            path: 'set_meal_admin',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/set-meal-admin/SetMealAdmin'
              ),
            name: 'SetMealAdmin',
            meta: {
              noCache: true,
              title: 'set_meal_admin',
              permission: ['background_food.set_meal.list']
            }
          },
          {
            path: 'add_edit_set_meal',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/set-meal-admin/AddEditSetMeal'
              ),
            name: 'AddEditSetMeal',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/set_meal_admin',
              title: 'add_edit_set_meal',
              permission: ['background_food.set_meal.list']
            }
          },
          {
            path: 'set_meal_classify',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/set-meal-admin/SetMealClassify'
              ),
            name: 'SetMealClassify',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/set_meal_admin',
              title: 'set_meal_classify',
              permission: ['background_food.set_meal.list']
            }
          },
          {
            path: 'diet_nutrition',
            component: () =>
              import(
                /* webpackChunkName: "Ingredients_library" */ '@/views/merchant/meal-management/food-admin/DietNutrition'
              ),
            name: 'dietNutrition',
            meta: {
              noCache: true,
              title: 'diet_nutrition',
              permission: ['background_food.diet_group.list']
            }
          },
          {
            path: 'diet_crowd',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "ingredients_category" */ '@/views/merchant/meal-management/food-admin/DietCrowd'
              ),
            name: 'MerchantDietCrowd',
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/diet_nutrition',
              title: 'diet_crowd'
              // no_permission: true
              // permission: ['meal_management']
            }
          },
          {
            path: 'recipes_manage',
            component: () =>
              import(
                /* webpackChunkName: "recipes_manage" */ '@/views/merchant/meal-management/menu-admin/RecipesManage'
              ),
            name: 'RecipesManage',
            meta: {
              noCache: true,
              title: 'recipes_manage',
              permission: ['background_food.menu_weekly.list']
            }
          },
          {
            path: 'add_week_recipes_separate',
            name: 'AddWeekRecipesSeparate',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "add_week_recipes" */ '@/views/merchant/meal-management/menu-admin/AddWeekRecipesSeparate'
              ),
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/recipes_manage',
              title: 'add_week_recipes',
              permission: ['background_food.menu_weekly.list']
            }
          },
          {
            path: 'add_month_recipes_separate',
            name: 'AddMonthRecipesSeparate',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "add_month_recipes" */ '@/views/merchant/meal-management/menu-admin/AddMonthRecipesSeparate'
              ),
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/recipes_manage',
              title: 'add_month_recipes',
              permission: ['background_food.menu_weekly.list']
            }
          },
          {
            path: 'add_meal_month_recipes',
            name: 'AddMealMonthRecipes',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "add_meal_month_recipes" */ '@/views/merchant/meal-management/menu-admin/AddMealMonthRecipes'
              ),
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/recipes_manage',
              title: 'add_month_recipes',
              permission: ['background_food.menu_weekly.list']
            }
          },
          // 营养分析 单独一个页面
          {
            path: 'nutrition_analysis',
            name: 'NutritionAnalysis',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "add_meal_month_recipes" */ '@/views/merchant/meal-management/menu-admin/nutritionAnalysis'
              ),
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/recipes_manage',
              title: 'nutrition_analysis',
              permission: ['background_food.menu_weekly.list']
            }
          },
          {
            path: 'menu_catering',
            name: 'MenuCatering',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "add_month_recipes" */ '@/views/merchant/meal-management/menu-admin/MenuCatering'
              ),
            meta: {
              noCache: true,
              activeMenu: '/meal_management/food_admin/recipes_manage',
              title: 'menu_catering',
              permission: ['background_food.menu_weekly.list']
            }
          },
          {
            path: 'menu_rule_setting',
            component: () =>
              import(
                /* webpackChunkName: "intention_menu_rank" */ '@/views/merchant/meal-management/menu-rule-setting/MenuRuleSetting'
              ),
            name: 'MenuRuleSetting',
            meta: {
              noCache: true,
              title: 'menu_rule_setting',
              permission: ['background_food.menu_setting_info.list']
            }
          },
          {
            path: 'intention_menu_rank',
            component: () =>
              import(
                /* webpackChunkName: "intention_menu_rank" */ '@/views/merchant/meal-management/intention-menu-rank/IntentionMenuRank'
              ),
            name: 'IntentionMenu',
            meta: {
              noCache: true,
              title: 'intention_menu_rank',
              permission: ['background_food.intent_food.list']
            }
          }
        ]
      },
      {
        path: 'booking',
        name: 'booking',
        alwaysShow: true,
        meta: {
          title: 'booking',
          noCache: true,
          permission: ['background_reservation.list']
        },
        children: [
          {
            path: 'booking_meal',
            component: () =>
              import(
                /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/booking-setting/Order'
              ),
            name: 'Order',
            meta: {
              noCache: true,
              title: 'booking_meal',
              permission: ['background_reservation.background_reservation_settings.list']
            }
          },
          {
            path: 'add_booking_order',
            component: () =>
              import(
                /* webpackChunkName: "add_booking_order" */ '@/views/merchant/meal-management/booking-setting/AddBookingOrder'
              ),
            name: 'AddBookingOrder',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/booking/booking_meal',
              title: 'add_booking_order',
              permission: ['background_reservation.background_reservation_settings.list']
            }
          },
          {
            path: 'booking_order_list',
            component: () =>
              import(
                /* webpackChunkName: "booking_order_list" */ '@/views/merchant/meal-management/booking-setting/OrderList'
              ),
            name: 'OrderList',
            meta: {
              noCache: true,
              title: 'booking_order_list',
              permission: ['background_order.reservation_order.info_list']
            }
          },
          {
            path: 'canteen_booking',
            component: () =>
              import(
                /* webpackChunkName: "canteen_booking" */ '@/views/merchant/meal-management/booking-setting/CanteenBooking'
              ),
            name: 'CanteenBooking',
            meta: {
              noCache: true,
              title: 'canteen_booking',
              permission: ['background_order.reservation_order.collect_list']
            }
          },
          {
            path: 'group_order',
            component: () =>
              import(
                /* webpackChunkName: "group_order" */ '@/views/merchant/meal-management/booking-setting/GroupOrder'
              ),
            name: 'GroupOrder',
            meta: {
              noCache: true,
              title: 'group_order',
              permission: ['background_order.reservation_order.group_collect_list']
            }
          },
          {
            path: 'food_category',
            component: () =>
              import(
                /* webpackChunkName: "food_category" */ '@/views/merchant/meal-management/booking-setting/CategoryList'
              ),
            name: 'CategoryList',
            meta: {
              noCache: true,
              title: 'food_category',
              permission: ['background_order.reservation_order.food_collect_list']
            }
          },
          {
            path: 'set_meal_summary',
            component: () =>
              import(
                /* webpackChunkName: "set_meal_summary" */ '@/views/merchant/meal-management/booking-setting/SetMealSummary'
              ),
            name: 'SetMealSummary',
            meta: {
              noCache: true,
              title: 'set_meal_summary',
              permission: ['background_food.set_meal.list']
            }
          },
          {
            path: 'cupboard_order',
            component: () =>
              import(
                /* webpackChunkName: "set_meal_summary" */ '@/views/merchant/meal-management/booking-setting/CupboardOrder'
              ),
            name: 'CupboardOrder',
            meta: {
              noCache: true,
              title: 'cupboard_order',
              permission: ['background_order.reservation_order.cupboard_order_list']
            }
          }
        ]
      },
      {
        path: 'address',
        name: 'Address',
        alwaysShow: true,
        meta: {
          title: 'address',
          noCache: true,
          permission: ['address.list']
        },
        children: [
          {
            path: 'addressAdmin',
            component: () =>
              import(
                /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/address-admin/AddressAdmin'
              ),
            name: 'AddressAdmin',
            meta: {
              noCache: true,
              title: 'address_admin',
              permission: ['address.adders_center.list']
            }
          },
          {
            path: 'addressAreaAdmin',
            component: () =>
              import(
                /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/address-admin/AddressAreaAdmin'
              ),
            name: 'AddressAreaAdmin',
            meta: {
              noCache: true,
              title: 'address_area_admin',
              permission: ['address.adders_area.list']
            }
          },
          {
            path: 'deliver_report',
            component: () =>
              import(
                /* webpackChunkName: "deliver_report" */ '@/views/merchant/meal-management/address-admin/deliver/DeliverReport.vue'
              ),
            name: 'DeliverReport',
            meta: {
              noCache: true,
              title: 'deliver_report',
              permission: ['background_order.reservation_order.delivery_area_collect_list']
            }
          }
        ]
      },
      // {
      //   path: 'distribution',
      //   name: 'Distribution',
      //   alwaysShow: true,
      //   meta: {
      //     title: 'distribution',
      //     noCache: true,
      //     permission: ['meal_management']
      //   },
      //   children: [
      //     {
      //       path: 'getMealReport',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/distribution/GetMealReport'
      //         ),
      //       name: 'GetMealReport',
      //       meta: {
      //         noCache: true,
      //         title: 'get_meal_report',
      //         permission: ['meal_management']
      //       }
      //     },
      //     {
      //       path: 'transverseReport',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/distribution/TransverseReport'
      //         ),
      //       name: 'TransverseReport',
      //       meta: {
      //         noCache: true,
      //         title: 'transverse_report',
      //         permission: ['meal_management']
      //       }
      //     },
      //     {
      //       path: 'verticalReport',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/distribution/VerticalReport'
      //         ),
      //       name: 'VerticalReport',
      //       meta: {
      //         noCache: true,
      //         title: 'vertical_report',
      //         permission: ['meal_management']
      //       }
      //     }
      //   ]
      // },
      {
        path: 'meal_report',
        name: 'MealReport',
        alwaysShow: true,
        meta: {
          title: 'meal_report',
          noCache: true,
          permission: ['background_report_meal.list']
        },
        children: [
          {
            path: 'meal_report_admin',
            component: () =>
              import(
                /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/meal-report/MealReportAdmin'
              ),
            name: 'MealReportAdmin',
            meta: {
              noCache: true,
              title: 'meal_report_admin',
              permission: ['background_report_meal.list']
            }
          },
          {
            path: 'meal_report_/:type',
            name: 'AddMealReport',
            hidden: true,
            component: () =>
              import(
                /* webpackChunkName: "add_meal_report" */ '@/views/merchant/meal-management/meal-report/AddMealReport'
              ),
            meta: {
              // noCache: true,
              activeMenu: '/meal_management/meal_report/meal_report_admin',
              title: 'add_meal_report',
              permission: ['background_report_meal.list'] // or you can only set roles in sub nav
            }
          },
          {
            path: 'meal_report_detail',
            component: () =>
              import(
                /* webpackChunkName: "meal_details" */ '@/views/merchant/meal-management/meal-report/MealReportDetail'
              ),
            name: 'MealReportDetail',
            meta: {
              noCache: true,
              title: 'meal_report_detail',
              permission: ['background_report_meal.list']
            }
          },
          {
            path: 'dep_meal_report',
            component: () =>
              import(
                /* webpackChunkName: "dep_meal_report" */ '@/views/merchant/meal-management/meal-report/DepMealReport'
              ),
            name: 'DepMealReport',
            meta: {
              noCache: true,
              title: 'dep_meal_report',
              permission: ['background_report_meal.list']
            }
          },
          {
            path: 'canteen_meal_report',
            component: () =>
              import(
                /* webpackChunkName: "canteen_meal_report" */ '@/views/merchant/meal-management/meal-report/CanteenMealReport'
              ),
            name: 'CanteenMealReport',
            meta: {
              noCache: true,
              title: 'canteen_meal_report',
              permission: ['background_report_meal.list']
            }
          }
        ]
      },
      {
        path: 'label_admin',
        redirect: '/label_admin/food_label',
        // component: () =>
        //   import(
        //     /* webpackChunkName: "" */ '@/views/merchant/meal-management/label-admin/FoodLabel'
        //   ),
        name: 'MerchantLabelAdmin',
        alwaysShow: true,
        meta: {
          noCache: true,
          title: 'label_admin',
          permission: ['label_group']
        },
        children: [
          {
            path: 'food_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/merchant/meal-management/label-admin/FoodLabel'
              ),
            name: 'MerchantFoodLabel',
            meta: {
              noCache: true,
              title: 'food_label',
              permission: ['background_healthy.label_group.list']
            }
          },
          {
            path: 'ingredients_label',
            component: () =>
              import(
                /* webpackChunkName: "" */ '@/views/merchant/meal-management/label-admin/IngredientsLabel'
              ),
            name: 'MerchantientsLabel',
            meta: {
              noCache: true,
              title: 'ingredients_label',
              permission: ['background_healthy.label_group.list']
            }
          }
        ]
      },
      {
        path: 'supplier',
        name: 'Supplier',
        alwaysShow: true,
        meta: {
          title: 'supplier_admin',
          noCache: true,
          permission: ['background_food.ingredient_supplier.list']
        },
        children: [
          {
            path: 'supplier_admin',
            component: () =>
              import(
                /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/supplier-admin/SupplierAdmin'
              ),
            name: 'SupplierAdmin',
            meta: {
              noCache: true,
              title: 'supplier_admin',
              permission: ['background_food.ingredient_supplier.list']
            }
          },
          {
            path: 'add_edit_supplier',
            component: () =>
              import(
                /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/supplier-admin/AddEditSupplier'
              ),
            name: 'MerchantAddEditSupplier',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/supplier/supplier_admin',
              title: 'add_edit_supplier',
              permission: ['background_food.ingredient_supplier.add']
            }
          },

          {
            path: 'relation_supplier_ingredient',
            component: () =>
              import(
                /* webpackChunkName: "meal_report" */ '@/views/merchant/meal-management/supplier-admin/RelationSupplierIngredient'
              ),
            name: 'MerchantRelationSupplierIngredient',
            hidden: true,
            meta: {
              noCache: true,
              activeMenu: '/meal_management/supplier/supplier_admin',
              title: 'relation_supplier_ingredient',
              permission: ['background_food.ingredient_supplier.add_supplier_ingredient']
            }
          }
        ]
      },
      {
        path: 'reservation_management',
        name: 'ReservationManagement',
        alwaysShow: true,
        meta: {
          title: 'reservation_management',
          noCache: true,
          permission: ['background_order.ordering_food']
        },
        children: [
          {
            path: 'reservation_report',
            component: () =>
              import(
                /* webpackChunkName: "reservation_report" */ '@/views/merchant/meal-management/reservation-management/ReservationReport'
              ),
            name: 'MerchantReservationReport',
            meta: {
              noCache: true,
              title: 'reservation_report',
              permission: ['background_order.ordering_food.list']
            }
          },
          {
            path: 'department_report_collect',
            component: () =>
              import(
                /* webpackChunkName: "department_report_collect" */ '@/views/merchant/meal-management/reservation-management/DepartmentReportCollect'
              ),
            name: 'MerchantDepartmentReportCollect',
            meta: {
              noCache: true,
              title: 'department_report_collect',
              permission: ['background_order.ordering_food.ordering_food_summary']
            }
          },
          {
            path: 'user_recharge_refund_summary',
            component: () =>
              import(
                /* webpackChunkName: "department_report_collect" */ '@/views/merchant/meal-management/reservation-management/UserRechargeRefundSummary'
              ),
            name: 'MerchantUserRechargeRefundSummary',
            meta: {
              noCache: true,
              title: 'user_recharge_refund_summary',
              permission: ['background_order.ordering_food.ordering_charge_list']
            }
          }
        ]
      }
      // {
      //   path: 'on_scene',
      //   name: 'OnScene',
      //   alwaysShow: true,
      //   meta: {
      //     title: 'on_scene',
      //     noCache: true,
      //     permission: ['meal_management']
      //   },
      //   children: [
      //     {
      //       path: 'on_scene_admin',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "on_scene" */ '@/views/merchant/meal-management/on-scene-setting/OnSceneAdmin'
      //         ),
      //       name: 'OnSceneAdmin',
      //       meta: {
      //         noCache: true,
      //         title: 'on_scene',
      //         permission: ['meal_management']
      //       }
      //     },
      //     {
      //       path: 'on_scene/:type',
      //       name: 'AddOrEditOnScene',
      //       hidden: true,
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "add_or_edit_on_scene" */ '@/views/merchant/meal-management/on-scene-setting/AddOrEditOnScene'
      //         ),
      //       meta: {
      //         activeMenu: '/meal_management/on_scene/on_scene_admin',
      //         title: 'on_scene',
      //         permission: ['meal_management']
      //       }
      //     },
      //     {
      //       path: 'tableAdmin',
      //       component: () =>
      //         import(
      //           /* webpackChunkName: "booking_meal" */ '@/views/merchant/meal-management/on-scene-setting/TableAdmin'
      //         ),
      //       name: 'TableAdmin',
      //       meta: {
      //         noCache: true,
      //         title: 'table_admin',
      //         permission: ['meal_management']
      //       }
      //     }
      //   ]
      // }
    ]
  },
  {
    path: '/consumption-rules',
    component: Layout,
    redirect: '/consumption-rules/list',
    alwaysShow: false,
    name: 'merchant_system',
    meta: {
      title: 'consumption_rules',
      icon: 'rule',
      permission: ['background_marketing.consume.list']
    },
    children: [
      {
        path: 'list',
        name: 'ConsumptionRules',
        component: () =>
          import(
            /* webpackChunkName: "consumption_rules_list" */ '@/views/merchant/consumption-rules/ConsumptionRules'
          ),
        meta: {
          noCache: true,
          title: 'consumption_rules_list',
          permission: ['background_marketing.consume.list']
        }
      },
      {
        path: 'form/:type',
        name: 'ConsumptionRulesForm',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "consumption_rules_form" */ '@/views/merchant/consumption-rules/AddConsumptionRules'
          ),
        meta: {
          noCache: true,
          activeMenu: '/consumption-rules/list',
          title: 'consumption_rules_form',
          permission: ['background_marketing.consume.list']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/activity',
    component: Layout,
    alwaysShow: true,
    name: 'merchant_system',
    meta: {
      title: 'activity_recharges',
      icon: 'icon3',
      permission: ['background_marketing.recharge']
    },
    children: [
      {
        path: 'recharges',
        name: 'ActivityRecharges',
        component: () =>
          import(
            /* webpackChunkName: "activity_recharges" */ '@/views/merchant/consumption-rules/ActicityRecharges'
          ),
        meta: {
          noCache: true,
          title: 'activity_recharges_list',
          permission: ['background_marketing.recharge.list']
        }
      },
      {
        path: 'activity/:type',
        name: 'MerchantAddActivityRecharge',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_activity_recharges" */ '@/views/merchant/consumption-rules/AddActivityRecharge'
          ),
        meta: {
          noCache: true,
          title: 'activity_recharges',
          activeMenu: '/consumption-rules/activity/recharges',
          permission: ['background_marketing.recharge.list']
        }
      },
      {
        path: 'statistics',
        name: 'ActivityRechargeStatistics',
        component: () =>
          import(
            /* webpackChunkName: "activity_recharges_statistics" */ '@/views/merchant/consumption-rules/ActivityRechargeStatistics'
          ),
        meta: {
          noCache: true,
          title: 'activity_recharges_statistics',
          permission: ['background_marketing.recharge.report']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/coupon',
    component: Layout,
    redirect: '/consumption-rules/coupon/coupon_admin',
    alwaysShow: true,
    name: 'MerchantCoupon',
    meta: {
      noCache: true,
      title: 'coupon',
      permission: ['background_coupon.coupon_manage']
    },
    children: [
      {
        path: 'coupon_admin',
        name: 'MerchantCouponAdmin',
        component: () =>
          import(
            /* webpackChunkName: "coupon_admin" */ '@/views/merchant/consumption-rules/coupon-admin/CouponAdmin'
          ),
        meta: {
          noCache: true,
          title: 'coupon_admin',
          permission: ['background_coupon.coupon_manage.list']
        }
      },
      {
        path: 'coupon_admin/:type',
        name: 'MerchantAddCoupon',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_discount_coupon" */ '@/views/merchant/consumption-rules/coupon-admin/AddCoupon'
          ),
        meta: {
          noCache: true,
          title: 'coupon',
          activeMenu: '/consumption-rules/coupon/coupon_admin',
          permission: ['background_coupon.coupon_manage.add', 'background_coupon.coupon_manage.modify']
        }
      },
      {
        path: 'coupon_admin_detail',
        name: 'MerchantCouponAdminDetail',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "add_discount_coupon" */ '@/views/merchant/consumption-rules/coupon-admin/DetailCoupon'
          ),
        meta: {
          noCache: true,
          title: 'coupon_detail',
          activeMenu: '/consumption-rules/coupon/coupon_admin',
          permission: ['background_coupon.coupon_manage.list']
        }
      }
      // {
      //   path: 'discount_coupon_statistics',
      //   name: 'MerchantDiscountCouponStatistics',
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "discount_coupon_statistics" */ '@/views/merchant/consumption-rules/coupon/DiscountCouponStatistics'
      //     ),
      //   meta: {
      //     noCache: true,
      //     title: 'discount_coupon_statistics',
      //     permission: ['background_coupon.coupon_manage']
      //   }
      // }
    ]
  },
  // {
  //   path: '/consumption-rules/coupon',
  //   component: Layout,
  //   // redirect: '/consumption-rules/coupon/activity-recharges',
  //   alwaysShow: true,
  //   name: 'merchant_system',
  //   meta: {
  //     title: 'discount_coupon',
  //     icon: 'icon3',
  //     permission: ['marketing_activities']
  //   },
  //   children: [
  //     {
  //       path: '',
  //       name: 'MerchantDiscountCoupon',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "discount_coupon" */ '@/views/merchant/consumption-rules/coupon/DiscountCoupon'
  //         ),
  //       meta: {
  //         noCache: true,
  //         title: 'discount_coupon',
  //         permission: ['marketing_activities']
  //       }
  //     },
  //     {
  //       path: 'discount/:type',
  //       name: 'MerchantAddDiscountCoupon',
  //       hidden: true,
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "add_discount_coupon" */ '@/views/merchant/consumption-rules/coupon/AddDiscountCoupon'
  //         ),
  //       meta: {
  //         noCache: true,
  //         title: 'discount_coupon',
  //         activeMenu: '/consumption-rules/coupon',
  //         permission: ['marketing_activities']
  //       }
  //     },
  //     {
  //       path: 'discount_coupon_statistics',
  //       name: 'MerchantDiscountCouponStatistics',
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "discount_coupon_statistics" */ '@/views/merchant/consumption-rules/coupon/DiscountCouponStatistics'
  //         ),
  //       meta: {
  //         noCache: true,
  //         title: 'discount_coupon_statistics',
  //         permission: ['marketing_activities']
  //       }
  //     }
  //   ]
  // },
  {
    path: '/consumption-rules/operations_management',
    redirect: '/consumption-rules/marketing/banner',
    component: Layout,
    name: '1111',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'operations_management',
      icon: 'el-icon-s-operation',
      permission: ['marketing_activities_operation']
    },
    children: [
      {
        path: 'banner',
        component: () =>
          import(/* webpackChunkName: "Merchant_banner" */ '@/views/merchant/consumption-rules/operations-management/banner/banner'),
        name: 'MerchantBanner',
        meta: {
          noCache: true,
          title: 'banner_setting',
          permission: ['background_marketing.marketing_banner']
        }
      },
      {
        path: 'add_banner/:type',
        component: () =>
          import(/* webpackChunkName: "Merchant_add_banner" */ '@/views/merchant/consumption-rules/operations-management/banner/AddBanner'),
        name: 'MerchantAddBanner',
        hidden: true,
        meta: {
          noCache: true,
          title: 'banner',
          activeMenu: '/consumption-rules/operations_management/banner',
          permission: ['background_marketing.marketing_banner.add', 'background_marketing.marketing_banner.modify']
        }
      },
      {
        path: 'mobile_popup',
        component: () =>
          import(/* webpackChunkName: "Merchant_mobile_popup" */ '@/views/merchant/consumption-rules/operations-management/popup/MobilePopup'),
        name: 'MerchantMobilePopup',
        meta: {
          noCache: true,
          title: 'mobile_popup',
          permission: ['background_marketing.marketing_popup']
        }
      },
      {
        path: 'add_mobile_popup/:type',
        component: () =>
          import(/* webpackChunkName: "Merchant_add_mobile_popup" */ '@/views/merchant/consumption-rules/operations-management/popup/AddMobilePopup'),
        name: 'MerchantAddMobilePopup',
        hidden: true,
        meta: {
          noCache: true,
          title: 'mobile_popup',
          activeMenu: '/consumption-rules/operations_management/mobile_popup',
          permission: ['background_marketing.marketing_popup.add', 'background_marketing.marketing_popup.modify']
        }
      },
      {
        path: 'mobile_notice',
        component: () =>
          import(/* webpackChunkName: "Merchant_mobile_notice" */ '@/views/merchant/consumption-rules/operations-management/notice/NoticeAdmin'),
        name: 'MerchantMobileNotice',
        meta: {
          noCache: true,
          title: 'notice',
          permission: ['background_marketing.marketing_notice']
        }
      },
      {
        path: 'add_mobile_notice/:type',
        component: () =>
          import(/* webpackChunkName: "Merchant_add_mobile_notice" */ '@/views/merchant/consumption-rules/operations-management/notice/NoticeAdd'),
        name: 'MerchantAddMobileNotice',
        hidden: true,
        meta: {
          noCache: true,
          title: 'notice',
          activeMenu: '/consumption-rules/operations_management/mobile_notice',
          permission: ['background_marketing.marketing_notice.add', 'background_marketing.marketing_notice.modif']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/service_admin',
    redirect: '/consumption_rules/service_admin/charge_consume_service',
    component: Layout,
    name: 'serviceAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'service_admin',
      icon: 'el-icon-s-operation',
      permission: ['marketing_activities_rate']
    },
    children: [
      {
        path: 'charge_consume_service',
        component: () =>
          import(/* webpackChunkName: "MerchantRechargeDeductionService" */ '@/views/merchant/consumption-rules/service-admin/ChargeConsumeService'),
        name: 'MerchantRechargeDeductionService',
        meta: {
          noCache: true,
          title: 'recharge_deduction_service',
          permission: ['background_marketing.consume_rate', 'background_marketing.charge_rate']
        }
      },
      {
        path: 'service_rule_consume/:type',
        component: () =>
          import(/* webpackChunkName: "MerchantAddServiceRule" */ '@/views/merchant/consumption-rules/service-admin/AddConsumeServiceRule'),
        name: 'MerchantAddConsumeServiceRule',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_service_rule_consume',
          activeMenu: '/consumption-rules/service_admin/charge_consume_service',
          permission: ['background_marketing.consume_rate.add', 'background_marketing.consume_rate.modify']
        }
      },
      {
        path: 'service_rule_charge/:type',
        component: () =>
          import(/* webpackChunkName: "MerchantAddServiceRule" */ '@/views/merchant/consumption-rules/service-admin/AddChargeServiceRule'),
        name: 'MerchantAddChargeServiceRule',
        hidden: true,
        meta: {
          noCache: true,
          title: 'add_service_rule_charge',
          activeMenu: '/consumption-rules/service_admin/charge_consume_service',
          permission: ['background_marketing.charge_rate.add', 'background_marketing.charge_rate.modify']
        }
      }
    ]
  },
  {
    path: '/consumption-rules/messages_admin',
    redirect: '/consumption-rules/messages_admin/messages_setting',
    component: Layout,
    name: 'MerchantMessagesAdmin',
    alwaysShow: true,
    meta: {
      noCache: true,
      title: 'messages_admin',
      icon: 'device',
      permission: ['background_messages.third_messages_settings']
    },
    children: [
      {
        path: 'messages_setting',
        component: () =>
          import(/* webpackChunkName: "messages_setting" */ '@/views/merchant/consumption-rules/messages-admin/MessagesSetting'),
        name: 'MerchantMessagesSetting',
        meta: {
          noCache: true,
          title: 'messages_setting',
          permission: ['background_messages.third_messages_settings.list']
        }
      }
    ]
  },
  {
    path: '/report_center',
    component: Layout,
    redirect: 'report_center/detail_total_list',
    alwaysShow: true,
    name: 'report_center',
    meta: {
      title: 'financialStatement',
      // icon: 'el-icon-setting',
      permission: ['report_center']
    },
    children: [
      {
        path: 'detail_total_list',
        component: () =>
          import(
            /* webpackChunkName: "detail_total_list" */ '@/views/merchant/report/financial-statements/DetailTotalList'
          ),
        name: 'DetailTotalList',
        meta: {
          noCache: true,
          title: 'detail_total_list',
          permission: ['background_report_center.finance_report.unified_order_list']
        }
      },
      {
        path: 'consume_detail_list',
        component: () =>
          import(
            /* webpackChunkName: "consume_detail_list" */ '@/views/merchant/report/financial-statements/ConsumeDetailList'
          ),
        name: 'ConsumeDetailList',
        meta: {
          noCache: true,
          title: 'consume_detail_list',
          permission: ['background_order.finance_report.payment_order_detail_list']
        }
      },
      {
        path: 'top_up_detail',
        component: () =>
          import(
            /* webpackChunkName: "top_up_detail" */ '@/views/merchant/report/financial-statements/TopUpDetail'
          ),
        name: 'TopUpDetail',
        meta: {
          noCache: true,
          title: 'top_up_detail',
          permission: ['background_order.finance_report.pecharge_order_list']
        }
      },
      {
        path: 'consume_reconciliation',
        component: () =>
          import(
            /* webpackChunkName: "consume_reconciliation" */ '@/views/merchant/report/financial-statements/ConsumeReconciliation'
          ),
        name: 'ConsumeReconciliation',
        meta: {
          noCache: true,
          title: 'consume_reconciliation',
          permission: ['background_order.finance_report.reconciliation_statement_list']
        }
      },
      {
        path: 'business_list',
        component: () =>
          import(
            /* webpackChunkName: "business_list" */ '@/views/merchant/report/financial-statements/BusinessList'
          ),
        name: 'ReconciliationStatement',
        meta: {
          noCache: true,
          title: 'business_list',
          permission: ['background_order.finance_report.order_business_list']
        }
      },
      {
        path: 'withdraw_list',
        component: () =>
          import(
            /* webpackChunkName: "business_list" */ '@/views/merchant/report/financial-statements/WithdrawList'
          ),
        name: 'WithdrawList',
        meta: {
          noCache: true,
          title: 'withdraw_list',
          permission: ['report_center']
        }
      },
      {
        path: 'personal_recharge_summary',
        component: () =>
          import(
            /* webpackChunkName: "personal_recharge_summary" */ '@/views/merchant/report/financial-statements/PersonalRechargeSummary'
          ),
        name: 'MerchantPersonalRechargeSummary',
        meta: {
          noCache: true,
          title: 'personal_recharge_summary',
          permission: ['background_order.finance_report.person_charge_list']
        }
      },
      {
        path: 'flat_cost_report',
        component: () =>
          import(
            /* webpackChunkName: "flat_cost_report" */ '@/views/merchant/report/financial-statements/FlatCostReport'
          ),
        name: 'MerchantFlatCostReport',
        meta: {
          noCache: true,
          title: 'flat_cost_report',
          permission: ['report_center']
        }
      },
      {
        path: 'flat_cost_refund',
        component: () =>
          import(
            /* webpackChunkName: "flat_cost_refund" */ '@/views/merchant/report/financial-statements/FlatCostRefund'
          ),
        name: 'MerchantFlatCostRefund',
        meta: {
          noCache: true,
          title: 'flat_cost_refund',
          permission: ['report_center']
        }
      },
      {
        path: 'departmental_consumption_summary',
        component: () =>
          import(
            /* webpackChunkName: "departmental_consumption_summary" */ '@/views/merchant/report/financial-statements/DepartmentalConsumptionSummary'
          ),
        name: 'MerchantDepartmentalConsumptionSummary',
        meta: {
          noCache: true,
          title: 'departmental_consumption_summary',
          permission: ['background_order.finance_report.department_payment_collect_list']
        }
      },
      {
        path: 'personal_consumption_summary',
        component: () =>
          import(
            /* webpackChunkName: "personal_consumption_summary" */ '@/views/merchant/report/financial-statements/PersonalConsumptionSummary'
          ),
        name: 'MerchantPersonalConsumptionSummary',
        meta: {
          noCache: true,
          title: 'personal_consumption_summary',
          permission: ['background_order.finance_report.person_payment_collect_list']
        }
      },
      {
        path: 'account_wallet_daily',
        component: () =>
          import(
            /* webpackChunkName: "account_wallet_daily" */ '@/views/merchant/report/financial-statements/AccountWalletDaily'
          ),
        name: 'MerchantAccountWalletDaily',
        meta: {
          noCache: true,
          title: 'account_wallet_daily',
          permission: ['background_order.finance_report.wallet_daily_list']
        }
      },
      // 个人钱包日报表
      {
        path: 'personal_wallet_daily',
        component: () =>
          import(
            /* webpackChunkName: "account_wallet_daily" */ '@/views/merchant/report/financial-statements/PersonalWalletDaily'
          ),
        name: 'MerchantPersonalWalletDaily',
        meta: {
          noCache: true,
          title: 'personal_wallet_daily',
          permission: ['background_order.finance_report.person_wallet_daily_list']
        }
      },
      // 收款码
      {
        path: 'collectionl_code_report',
        component: () =>
          import(
            /* webpackChunkName: "account_wallet_daily" */ '@/views/merchant/report/financial-statements/CollectionlCodeReport'
          ),
        name: 'MerchantCollectionlCodeReport',
        meta: {
          noCache: true,
          title: 'collectionl_code_report',
          permission: ['background_order.finance_report.instore_payment_detail_list']
        }
      },
      // 手续费总表
      {
        path: 'service_charge_report',
        component: () =>
          import(
            /* webpackChunkName: "service_charge_report" */ '@/views/merchant/report/financial-statements/ServiceChargeReport'
          ),
        name: 'MerchantServiceChargeReport',
        meta: {
          noCache: true,
          title: 'service_charge_report',
          permission: ['background_order.finance_report.commission_consume_list', 'background_order.finance_report.commission_charge_list']
        }
      },
      {
        path: 'receipt_list',
        component: () =>
          import(
            /* webpackChunkName: "receipt_list" */ '@/views/merchant/report/financial-statements/ReceiptList'
          ),
        name: 'ReceiptList',
        meta: {
          noCache: true,
          title: 'receipt_list',
          permission: ['background_invoice.invoice_record.invoice_record_list']
        }
      },
      // 优惠券明细表
      {
        path: 'coupon_detail_list',
        component: () =>
          import(
            /* webpackChunkName: "person_meal_report" */ '@/views/merchant/report/financial-statements/CouponDetailList'
          ),
        name: 'MerchantCouponDetailList',
        meta: {
          noCache: true,
          title: 'coupon_detail_list',
          permission: ['background_report_center.finance_report.coupon_order_list']
        }
      }
    ]
  },
  {
    path: '/report_management',
    component: Layout,
    redirect: 'report_management/food_sale_ranking',
    alwaysShow: true,
    name: 'report_management',
    meta: {
      title: 'report_management',
      // icon: 'el-icon-setting',
      permission: ['report_center']
    },
    children: [
      {
        path: 'food_sale_ranking',
        component: () =>
          import(
            /* webpackChunkName: "detail_total_list" */ '@/views/merchant/report/report-management/FoodSaleRanking'
          ),
        name: 'FoodSaleRanking',
        meta: {
          noCache: true,
          title: 'food_sale_ranking',
          permission: ['background_order.manage_report.food_payment_ranking_list']
        }
      }
    ]
  },
  // 数据报表 设备明细
  {
    path: '/report_center/data_reporting',
    name: 'dataReporting',
    alwaysShow: true,
    component: Layout,
    meta: {
      title: 'dataReporting',
      noCache: true,
      permission: ['report_center']
    },
    children: [
      {
        path: 'device_cost',
        component: () =>
          import(
            /* webpackChunkName: "device_cost" */ '@/views/merchant/report/data_reporting/DeviceCost'
          ),
        name: 'DeviceCost',
        meta: {
          noCache: true,
          title: 'device_cost',
          permission: ['background_order.finance_report.device_consume_list']
        }
      },
      {
        path: 'third_reconciliation',
        component: () =>
          import(
            /* webpackChunkName: "third_reconciliation" */ '@/views/merchant/report/financial-statements/ThirdReconciliation'
          ),
        name: 'MerchantThirdReconciliation',
        meta: {
          noCache: true,
          title: 'third_reconciliation',
          permission: ['report_center']
        }
      }
    ]
  },
  {
    path: '/report_center/bank',
    component: Layout,
    redirect: 'report_center/bank/settlement_details',
    alwaysShow: true,
    name: 'report_center',
    meta: {
      title: 'bankfinancialStatement',
      icon: 'el-icon-office-building',
      permission: ['background_order.finance_report.output_order_list', 'background_order.finance_report.sub_mch_order_list', 'background_order.finance_report.sub_mch_order_detail_list']
    },
    children: [
      {
        path: 'settlement_details',
        component: () =>
          import(
            /* webpackChunkName: "settlement_details" */ '@/views/merchant/report/bank-financial-statements/SettlementDetails'
          ),
        name: 'MerchantSettlementDetails',
        meta: {
          noCache: true,
          title: 'settlement_details',
          permission: ['background_order.finance_report.output_order_list']
        }
      },
      {
        path: 'account_billing_details',
        component: () =>
          import(
            /* webpackChunkName: "account_billing_details" */ '@/views/merchant/report/bank-financial-statements/AccountBillingDetails'
          ),
        name: 'MerchantAccountBillingDetails',
        meta: {
          noCache: true,
          title: 'account_billing_details',
          permission: ['background_order.finance_report.sub_mch_order_list']
        }
      },
      {
        path: 'bank_flow_details',
        component: () =>
          import(
            /* webpackChunkName: "bank_flow_details" */ '@/views/merchant/report/bank-financial-statements/BankFlowDetails'
          ),
        name: 'MerchantBankFlowDetails',
        meta: {
          noCache: true,
          title: 'bank_flow_details',
          permission: ['background_order.finance_report.sub_mch_order_detail_list']
        }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order/consumption',
    alwaysShow: true,
    name: 'order',
    meta: {
      title: 'order',
      icon: 'order',
      permission: ['order_management']
    },
    children: [
      {
        path: 'consumption',
        component: () =>
          import(/* webpackChunkName: "Consumption" */ '@/views/merchant/order/Consumption'),
        name: 'Consumption',
        meta: {
          noCache: true,
          title: 'consumption',
          permission: ['background_order.order_payment.list']
        }
      },
      {
        path: 'consumption_detail',
        component: () =>
          import(/* webpackChunkName: "orderDetail" */ '@/views/merchant/order/ConsumptionDetail'),
        name: 'ConsumptionDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'consumption_detail',
          activeMenu: '/order/consumption',
          permission: ['background_order.order_payment.list']
        }
      },
      {
        path: 'refund_order',
        component: () =>
          import(/* webpackChunkName: "refundOrder" */ '@/views/merchant/order/RefundOrder'),
        name: 'refundOrder',
        meta: {
          noCache: true,
          title: 'order_refund',
          permission: ['order_management']
        }
      },
      {
        path: 'recharge_order',
        component: () =>
          import(/* webpackChunkName: "refundOrder" */ '@/views/merchant/order/RechargeOrder'),
        name: 'rechargeOrder',
        meta: {
          noCache: true,
          title: 'order_recharge',
          permission: ['background_order.order_charge.list']
        }
      },
      {
        path: 'consumption_failure',
        component: () =>
          import(/* webpackChunkName: "refundOrder" */ '@/views/merchant/order/ConsumptionFailure'),
        name: 'consumptionFailure',
        meta: {
          // noCache: true,
          title: 'consumption_failure',
          permission: ['order_management']
        }
      },
      {
        path: 'consumption_failure_detail',
        component: () =>
          import(
            /* webpackChunkName: "consumption_failure_detail" */ '@/views/merchant/order/ConsumptionFailureDetail'
          ),
        name: 'ConsumptionFailureDetail',
        hidden: true,
        meta: {
          noCache: true,
          title: 'consumption_failure_detail',
          activeMenu: '/order/consumption_failure',
          permission: ['background_order.order_payment.list']
        }
      },
      {
        path: 'withdraw_order',
        component: () =>
          import(/* webpackChunkName: "withdraw_order" */ '@/views/merchant/order/WithdrawOrder'),
        name: 'WithdrawOrder',
        meta: {
          noCache: true,
          title: 'withdraw_order',
          permission: ['order_management']
        }
      }, // withdraw
      {
        path: 'appeal_order',
        component: () =>
          import(/* webpackChunkName: "appeal_order" */ '@/views/merchant/order/AppealOrder'),
        name: 'AppealOrder',
        meta: {
          noCache: true,
          title: 'appeal_order',
          permission: ['background_order.order_appeal.list']
        }
      },
      {
        path: 'appeal_order_detail',
        component: () =>
          import(
            /* webpackChunkName: "appeal_order_detail" */ '@/views/merchant/order/AppealOrderDetail'
          ),
        name: 'AppealOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/order/appeal_order',
          title: 'appeal_order_detail',
          permission: ['background_order.order_appeal.list']
        }
      },
      {
        path: 'approve_order',
        component: () =>
          import(/* webpackChunkName: "approve_order" */ '@/views/merchant/order/ApproveOrder'),
        name: 'ApproveOrder',
        meta: {
          noCache: true,
          title: 'approve_order',
          permission: ['order_management']
        }
      },
      {
        path: 'approve_order_detail',
        component: () =>
          import(
            /* webpackChunkName: "approve_order_detail" */ '@/views/merchant/order/ApproveOrderDetail'
          ),
        name: 'ApproveOrderDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/order/approve_order',
          title: 'approve_order_detail',
          permission: ['order_management']
        }
      }
    ]
  },
  {
    path: '/order/operations-management',
    component: Layout,
    // redirect: '/order/operations-management',
    alwaysShow: true,
    name: 'OperationsManagement',
    meta: {
      title: 'evaluate_admin',
      icon: '',
      permission: ['operation_management']
    },
    children: [
      {
        path: '',
        component: () =>
          import(
            /* webpackChunkName: "evaluate_list" */ '@/views/merchant/order/operations-management/EvaluateList'
          ),
        name: 'OperationsManagementEvaluateList',
        meta: {
          noCache: true,
          title: 'evaluate_list',
          permission: ['background_operation_management.order_evaluation.list']
        }
      },
      {
        path: 'feedback',
        component: () =>
          import(
            /* webpackChunkName: "feedback" */ '@/views/merchant/order/operations-management/feedback'
          ),
        name: 'OperationsManagementEvaluateList',
        meta: {
          noCache: true,
          title: 'feedback',
          permission: ['operation_management.feedbackrecord.list']
        }
      },
      {
        path: 'setting',
        component: () =>
          import(
            /* webpackChunkName: "evaluate_list" */ '@/views/merchant/order/operations-management/setting'
          ),
        name: 'OperationsManagementEvaluateList',
        meta: {
          noCache: true,
          title: 'operations_setting',
          permission: ['background_operation_management.operation_management.list']
        }
      }
    ]
  },
  {
    path: '/home',
    component: Layout,
    redirect: '/home/<USER>',
    alwaysShow: false,
    name: 'home',
    meta: {
      title: 'home',
      icon: 'home',
      permission: ['homepage']
    },
    children: [
      {
        path: 'home_page',
        component: () =>
          import(/* webpackChunkName: "home_page" */ '@/views/merchant/home-page/HomePage'),
        name: 'HomePage',
        meta: {
          noCache: true,
          title: 'home_page',
          permission: ['homepage']
        }
      }
    ]
  },
  {
    path: '/home/<USER>',
    component: Layout,
    redirect: '/home/<USER>/jiaofei_data_report',
    alwaysShow: false,
    name: 'jiaofei_data',
    meta: {
      title: 'jiaofei_data_report',
      icon: 'jiaofei_data',
      permission: ['background_order.order_jiao.jiaofei_statistical']
    },
    children: [
      {
        path: 'jiaofei_data_report',
        component: () =>
          import(
            /* webpackChunkName: "jiaofei_data_report" */ '@/views/merchant/jiaofei-center/JiaofeiDataReport'
          ),
        name: 'JiaofeiDataReport',
        meta: {
          noCache: true,
          title: 'jiaofei_data_report',
          permission: ['background_order.order_jiao.jiaofei_statistical']
        }
      }
    ]
  },
  {
    path: '/jiaofei',
    component: Layout,
    redirect: '/jiaofei/jiaofei_admin',
    alwaysShow: true,
    name: 'jiaofei',
    meta: {
      title: 'jiaofei_center',
      icon: 'order',
      permission: ['jiaofei_center']
    },
    children: [
      {
        path: 'jiaofei_admin',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiAdmin" */ '@/views/merchant/jiaofei-center/JiaoFeiAdmin'
          ),
        name: 'JiaoFeiAdmin',
        meta: {
          noCache: true,
          title: 'jiaofei_admin',
          permission: ['background_jiaofei.list']
        }
      },
      {
        path: 'jiaofei/:type',
        component: () =>
          import(
            /* webpackChunkName: "AddOrEditJiaoFei" */ '@/views/merchant/jiaofei-center/AddOrEditJiaoFei'
          ),
        name: 'AddOrEditJiaoFei',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/jiaofei/jiaofei_admin',
          title: 'jiaofei_setting',
          permission: ['background_jiaofei.list']
        }
      },
      {
        path: 'jiaofei_detail',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiDetail" */ '@/views/merchant/jiaofei-center/JiaoFeiDetail'
          ),
        name: 'JiaoFeiDetail',
        hidden: true,
        meta: {
          noCache: true,
          activeMenu: '/jiaofei/jiaofei_admin',
          title: 'jiaofei_detail',
          permission: ['background_jiaofei.list']
        }
      },
      {
        path: 'jiaofei_order',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiOrder" */ '@/views/merchant/jiaofei-center/JiaoFeiOrder'
          ),
        name: 'JiaoFeiOrder',
        meta: {
          noCache: true,
          title: 'jiaofei_order',
          permission: ['background_order.order_jiao.list']
        }
      },
      {
        path: 'jiaofei_refund_order',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiRefundOrder" */ '@/views/merchant/jiaofei-center/JiaoFeiRefundOrder'
          ),
        name: 'JiaoFeiRefundOrder',
        meta: {
          noCache: true,
          title: 'jiaofei_refund_order',
          permission: ['background_order.order_jiao.refund_list']
        }
      },
      {
        path: 'jiaofei_refund_apply',
        component: () =>
          import(
            /* webpackChunkName: "JiaoFeiRefundApply" */ '@/views/merchant/jiaofei-center/JiaoFeiRefundApply'
          ),
        name: 'JiaoFeiRefundApply',
        meta: {
          noCache: true,
          title: 'jiaofei_refund_apply',
          permission: ['background_order.order_jiao.approval_list']
        }
      }
    ]
  },
  {
    path: '/attendance',
    component: Layout,
    redirect: '/attendance/attendance_group_admin',
    alwaysShow: true,
    name: 'attendance',
    meta: {
      title: 'attendance',
      icon: 'device',
      permission: ['attendance_record.list']
    },
    children: [
      // {
      //   path: 'push_setting_admin',
      //   component: () =>
      //     import(/* webpackChunkName: "push_setting_admin" */ '@/views/merchant/accessControl-attendance/attendance/PushSettingAdmin'),
      //   name: 'PushSettingAdmin',
      //   meta: {
      //     noCache: true,
      //     title: 'push_setting_admin',
      //     no_permission: true
      //   }
      // },
      // {
      //   path: 'push_setting/:type',
      //   component: () =>
      //     import(/* webpackChunkName: "push_setting" */ '@/views/merchant/accessControl-attendance/attendance/AddOrEditPushSetting'),
      //   name: 'AddOrEditPushSetting',
      //   hidden: true,
      //   meta: {
      //     noCache: true,
      //     title: 'push_setting',
      //     activeMenu: '/attendance/push_setting_admin',
      //     no_permission: true
      //   }
      // },
      {
        path: 'attendance_group_admin',
        component: () =>
          import(
            /* webpackChunkName: "attendance_group_admin" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceGroupAdmin'
          ),
        name: 'AttendanceGroupAdmin',
        meta: {
          noCache: true,
          title: 'attendance_group_admin',
          permission: ['background_attendance.group']
        }
      },
      {
        path: 'group_administrators',
        component: () =>
          import(
            /* webpackChunkName: "group_administrators" */ '@/views/merchant/accessControl-attendance/attendance/GroupAdministrators'
          ),
        name: 'GroupAdministrators',
        meta: {
          noCache: true,
          title: 'group_administrators',
          permission: ['background_attendance.group_admin']
        }
      },
      {
        path: 'attendance_setting',
        component: () =>
          import(
            /* webpackChunkName: "attendance_setting" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceSetting'
          ),
        name: 'AttendanceSetting',
        meta: {
          noCache: true,
          title: 'attendance_setting',
          permission: ['background_attendance.settings']
        }
      },
      {
        path: 'attendance_setting/:type',
        component: () =>
          import(
            /* webpackChunkName: "attendance_setting" */ '@/views/merchant/accessControl-attendance/attendance/AddOrEditAttendanceSetting'
          ),
        name: 'AddOrEditAttendanceSetting',
        hidden: true,
        meta: {
          noCache: true,
          title: 'attendance_setting',
          activeMenu: '/attendance/attendance_setting',
          permission: ['background_attendance.settings']
        }
      },
      {
        path: 'person_attendance_report',
        component: () =>
          import(
            /* webpackChunkName: "person_attendance_report" */ '@/views/merchant/accessControl-attendance/attendance/PersonAttendanceReport'
          ),
        name: 'PersonAttendanceReport',
        meta: {
          noCache: true,
          title: 'person_attendance_report',
          permission: ['background_attendance.card_user_count']
        }
      },
      {
        path: 'attendance_record_detail',
        component: () =>
          import(
            /* webpackChunkName: "attendance_record_detail" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceRecordDetail'
          ),
        name: 'AttendanceRecordDetail',
        meta: {
          noCache: true,
          title: 'attendance_record_detail',
          permission: ['background_attendance.record_details']
        }
      },
      {
        path: 'attendance_record',
        component: () =>
          import(
            /* webpackChunkName: "attendance_record" */ '@/views/merchant/accessControl-attendance/attendance/AttendanceRecord'
          ),
        name: 'AttendanceRecord',
        meta: {
          noCache: true,
          title: 'attendance_record',
          permission: ['background_attendance.record']
        }
      },
      {
        path: 'miss_card_record',
        component: () =>
          import(
            /* webpackChunkName: "miss_card_record" */ '@/views/merchant/accessControl-attendance/attendance/MissCardRecord'
          ),
        name: 'MissCardRecord',
        meta: {
          noCache: true,
          title: 'miss_card_record',
          permission: ['background_attendance.absence_work_record_details']
        }
      }
    ]
  },
  {
    path: '/access_control',
    component: Layout,
    redirect: '/access_control/access_control_setting',
    alwaysShow: true,
    name: 'AccessControl',
    meta: {
      title: 'access_control',
      icon: 'device',
      permission: ['access_control_record.list']
    },
    children: [
      {
        path: 'access_control_setting',
        component: () =>
          import(/* webpackChunkName: "access_control_setting" */ '@/views/merchant/accessControl-attendance/access-control/AccessControlSetting'),
        name: 'AccessControlSetting',
        meta: {
          noCache: true,
          title: 'access_control_setting',
          permission: ['background_attendance.access_control_settings']
        }
      },
      {
        path: 'access_control_setting/:type',
        component: () =>
          import(/* webpackChunkName: "access_control_setting" */ '@/views/merchant/accessControl-attendance/access-control/AddOrEditControlSetting'),
        name: 'AddOrEditControlSetting',
        hidden: true,
        meta: {
          noCache: true,
          title: 'access_control_setting',
          activeMenu: '/access_control/access_control_setting',
          permission: ['background_attendance.access_control_settings']
        }
      },
      {
        path: 'through_statistics',
        component: () =>
          import(/* webpackChunkName: "through_statistics" */ '@/views/merchant/accessControl-attendance/access-control/ThroughStatistics'),
        name: 'ThroughStatistics',
        meta: {
          noCache: true,
          title: 'through_statistics',
          permission: ['background_attendance.org_punch_status_count']
        }
      },
      {
        path: 'through_record',
        component: () =>
          import(/* webpackChunkName: "through_record" */ '@/views/merchant/accessControl-attendance/access-control/ThroughRecord'),
        name: 'ThroughRecord',
        meta: {
          noCache: true,
          title: 'through_record',
          permission: ['background_attendance.access_control_record']
        }
      }
    ]
  }
]
export default merchant

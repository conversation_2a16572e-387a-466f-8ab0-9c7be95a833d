export default {
  route: {
    no_permission: '暂无权限访问',
    merchant_system: '系统管理',
    organization_list: '组织架构管理',
    account_management: '账号管理',
    role_account_management: '账号管理',
    role_management: '角色管理',
    role_setting: '权限设置',
    excel: '导出Excel',
    user_management: '员工管理',
    merchant_management: '商户管理',
    system_management: '系统管理',
    department_management: '部门管理',
    list_log: '操作日志',
    admin_merchant_management: '商户管理',
    companyadmin: '公司管理',
    organizationAdmin: '组织管理',
    company: '公司',
    user_admin: '用户管理',
    user_admin_list: '用户列表',
    user_admin_list_detail: '用户详情',
    user_center: '用户中心',
    user_department: '用户部门',
    user_group: '用户分组',
    user_account_admin: '账户管理',
    user_account_list: '账户列表',
    user_account_setting: '账户设置',
    overdraft_setting: '透支设置',
    Overdraft_detail: '透支明细',
    withdrawal_admin: '退户管理',
    card_loss_list: '挂失卡管理',
    user_flat_cost: '工本费',
    user_flat_cost_list: '工本费列表',
    user_repair_card_list: '补卡费列表',
    user_card_subsidy: '补贴管理',
    user_card_subsidy_add: '补贴',
    user_card_subsidy_detail: '补贴详情',
    user_card_log: '账户操作日志',
    device_management: '设备管理',
    device_list: '设备列表',
    device_admin_card: '设备管理卡',
    cupboard_edit: '取餐柜编辑',
    cupboard_setting: '取餐柜设置',
    weight_food: '绑定菜品',
    weight_setting: '智能秤设置',
    routine_setting: '常规设置',
    tray_qrcode: '托盘码管理',
    print_setting: '打印设备设置',
    print_device: '打印设备',
    active_code_admin: '激活码管理',
    import_active_code_admin: '批量导入激活码',
    dingTalkMessageSeting: '钉钉消息推送配置',
    notice_admin: '公告管理',
    notice: '公告',
    notice_list: '查看公告',
    notice_detail: '公告详情',
    mul_import_face: '上传人脸',
    accountSetting: '账号设置',
    generalSettings: '常规设置',
    meal_management: '用餐管理',
    food_admin: '菜品 / 商品管理',
    ingredients_admin: '食材管理',
    ingredients_library: '食材库',
    health_nutrition_library: '健康营养库',
    ingredients: '食材',
    ingredients_category: '分类列表',
    meal_food_classification: '菜品/商品分类',
    meal_food_list: '菜品/商品库',
    diet_nutrition: '膳食营养素',
    diet_crowd: '膳食人群',
    recipes_manage: '菜谱管理',
    booking: '预约设置',
    booking_meal: '预约点餐',
    add_booking_order: '新增预约点餐',
    booking_order_list: '预约点餐明细',
    canteen_booking: '食堂预约点餐汇总',
    group_order: '分组点餐汇总',
    food_category: '菜品汇总',
    add_week_recipes: '添加菜品（周）',
    add_month_recipes: '添加菜品（月）',
    menu_catering: '添加菜品',
    nutrition_analysis: '营养分析',
    menu_rule_setting: '菜谱规则设置',
    collective_admin: '集体管理',
    intention_menu_rank: '意向菜谱排行',
    address: '地址管理',
    address_admin: '配送地址',
    address_area_admin: '食堂配送区域',
    distribution: '配送管理',
    get_meal_report: '配送区域领餐表',
    transverse_report: '配送汇总表（横）',
    vertical_report: '配送汇总表（竖）',
    consumption_rules: '消费规则',
    consumption_rules_list: '消费规则',
    consumption_rules_form: '消费规则',
    activity_recharges: '充值活动',
    activity_recharges_list: '活动列表',
    add_activity_recharges: '新建充值活动',
    activity_recharges_statistics: '充值活动统计',
    financialStatement: '财务报表',
    dataReporting: '数据报表',
    consume_detail_list: '消费明细表',
    consume_reconciliation: '消费点对账表',
    detail_total_list: '明细总表',
    device_cost: '设备消费明细',
    top_up_detail: '充值明细表',
    order: '订单管理',
    consumption: '消费订单',
    consumption_detail: '消费订单详情',
    order_refund: '退款订单',
    order_recharge: '充值订单',
    consumption_failure: '脱机扣款失败订单',
    consumption_failure_detail: '脱机扣款失败订单详情',
    withdraw_order: '提现订单',
    appeal_order: '申诉订单',
    appeal_order_detail: '申诉订单详情',
    meal_report: '报餐设置',
    meal_report_admin: '报餐',
    add_meal_report: '报餐',
    meal_report_detail: '报餐点餐明细',
    dep_meal_report: '部门报餐汇总',
    canteen_meal_report: '食堂报餐汇总',
    home: '首页',
    home_page: '首页',
    health_system: '健康系统',
    super_commodity: '菜品/商品库',
    ingredients_commodity: '菜品/商品库',
    super_add_commodity: '创建到菜品/商品库',
    import_ingredients: '食材',
    import_commodity: '菜品/商品',
    business_list: '营业额日报表',
    super_device_log: '设备日志',
    third_party_equipment_admin: '第三方设备管理',
    withdraw_list: '提现明细表',
    copy_ingredients: '拉取食材',
    copy_foods: '添加菜品',
    super_health_assessment: '健康测评',
    add_edit_questionnaire: '添加/编辑问卷',
    super_habit_cultivate: '习惯养成',
    super_article_push: '文章推送',
    super_article_admin: '文章管理',
    add_edit_article: '添加/编辑文章',
    super_article_label: '文章标签',
    flat_cost_report: '工本费收款明细',
    flat_cost_refund: '工本费退款明细',
    personal_recharge_summary: '个人充值汇总',
    departmental_consumption_summary: '部门消费汇总',
    personal_consumption_summary: '个人消费汇总',
    user_health_records: '用户健康档案',
    records_detail: '档案详情',
    body_detail: '身体检测',
    view_report: '查看报告',
    motion_admin: '运动管理',
    add_edit_motion_admin: '添加/编辑运动',
    parameter_config: '配置参数',
    nutrition_health: '营养健康',
    add_modify_nutrition_health: '添加/编辑营养健康',
    health_info_config: '健康信息配置',
    health_fraction_rule: '健康分规则',
    add_modify_health_fraction_rule: '编辑健康分规则',
    score_time: '评分餐段',
    modify_score_time: '修改评分餐段',
    account_wallet_daily: '账户钱包日报表',
    personal_wallet_daily: '个人钱包日报表',
    collectionl_code_report: '收款码明细表',
    service_charge_report: '手续费总表',
    device_ceil_set: '取餐柜设置',
    jiaofei_center: '缴费中心',
    jiaofei_admin: '缴费管理',
    jiaofei_setting: '缴费设置',
    jiaofei_detail: '缴费明细',
    jiaofei_order: '缴费订单',
    jiaofei_refund_order: '退款订单',
    jiaofei_refund_apply: '退款申请',
    jiaofei_data_report: '缴费数据',
    supplier_admin: '供应商管理',
    add_edit_supplier: '新增或编辑供应商',
    relation_supplier_ingredient: '关联食材',
    approve_order: '审核订单',
    approve_order_detail: '审核订单详情',
    set_meal_admin: '套餐管理',
    add_edit_set_meal: '新建或编辑套餐',
    set_meal_classify: '套餐分类',
    set_meal_summary: '套餐汇总',
    crowd_admin: '人群管理',
    add_or_modify_crowd: '人群',
    cupboard_order: '取餐柜订单',
    report_management: '经营报表',
    food_sale_ranking: '菜品销售排行',
    import_third_party_equipment: '导入第三方设备',
    secret_key_admin: '碗碟秘钥管理',
    operations_management: '运营管理',
    evaluate_admin: '评价管理',
    evaluate_list: '评价列表',
    feedback: '建议反馈列表',
    operations_setting: '评价设置',
    attendance: '考勤管理',
    push_setting_admin: '推送设置',
    push_setting: '推送设置',
    attendance_group_admin: '考勤组管理',
    group_administrators: '考勤组管理员',
    attendance_setting: '考勤设置',
    person_attendance_report: '个人考勤统计',
    attendance_record_detail: '考勤记录明细表',
    attendance_record: '考勤记录',
    miss_card_record: '缺卡记录',
    access_control: '门禁管理',
    access_control_setting: '门禁设置',
    through_statistics: '通行统计',
    through_record: '通行记录',
    reservation_management: '订餐管理',
    reservation_report: '订餐明细表',
    department_report_collect: '科室订餐汇总',
    user_recharge_refund_summary: '用户充值/退费汇总',
    qrcode_template: '二维码模板',
    on_scene: '堂食设置',
    table_admin: '包厢/桌台',
    deliver_report: '配送汇总表',
    board_admin: '看板管理',
    third_reconciliation: '第三方对账表',
    bankfinancialStatement: '银行财务报表',
    settlement_details: '结算明细表',
    account_billing_details: '账户结账明细',
    bank_flow_details: '交易流水明细',
    coupon: '优惠券',
    coupon_admin: '优惠券管理',
    coupon_detail: '优惠券详情',
    coupon_detail_list: '优惠券明细',
    // discount_coupon: '优惠券',
    // discount_coupon_statistics: '优惠券统计',
    marketing: '营销活动',
    banner_setting: 'banner图',
    banner: 'banner',
    mobile_popup: '弹窗',
    module_admin: '商户模块管理',
    subsidy_receive_detail: '补贴领取明细',
    import_commodity_image: '批量导入图片',
    nutrition_rules_core: '营养规则中心',
    nutrition_guidance_rules: '营养指导规则',
    modify_guidance_rules: '修改营养指导规则',
    label_admin: '标签管理',
    food_label: '菜品标签',
    ingredients_label: '食材标签',
    user_label: '用户标签',
    device_cloud_print: '云打印管理',
    receipt_list: '开票记录',
    consume_machine_setting: '消费机设置',
    agreement_list: '协议管理',
    add_agreement: '添加/编辑协议',
    agreement_record: '协议记录',
    service_admin: '手续费管理',
    recharge_deduction_service: '充值/扣款手续费',
    add_service_rule_consume: '扣款手续费规则',
    add_service_rule_charge: '充值手续费规则',
    face_traceback: '人脸查询',
    face_traceback_detail: '人脸查询详情',
    import_ingredient_image: '导入食材图片',
    member_center: '会员中心',
    member_list: '会员列表',
    member_detail: '会员详情',
    member_level: '会员等级',
    member_label: '会员标签',
    member_permission: '会员权限管理',
    member_charge_rule: '会员收费规则',
    member_receive_record: '会员领取记录',
    messages_admin: '推送管理',
    messages_setting: '推送配置'
  },
  navHeader: {
    dashboard: '首页'
  },
  navTab: {},
  login: {
    title: '系统登录',
    loginBtn: '登录',
    forgetPassword: '忘记密码',
    rememberPassword: '记住密码'
  },
  form: {
    confirm_btn: '确 定',
    add_btn: '添 加',
    cancel_btn: '取 消',
    submit: '提交',
    superior_organization: '上级组织',
    add_organization: '添加组织架构',
    edit_organization: '修改组织架构',
    role_add_name: '角色名称',
    role_select_organization: '选择组织架构',
    push_range: '推送范围',
    notice_title: '公告标题',
    notice_type: '公告类型',
    notice_content: '公告正文',
    upload_file: '公告附件',
    send_time: '发布时间',
    department: '部门',
    add_department: '添加部门',
    modify_department: '修改部门',
    superior_department: '上级部门',
    select_department: '选择部门'
  },
  placeholder: {
    account: '请输入账号或手机号',
    password: '请输入密码',
    phone: '请输入手机号',
    code: '请输入验证码',
    oldPassword: '请输入旧密码',
    newPassword: '密码长度8~20位，英文加数字',
    changeNewPassword: '请输入新密码',
    checkPassword: '请再次输入新密码',
    name: '请输入姓名',
    newPhone: '请输入新手机号码',
    organization_name: '请输入组织架构名称',
    role_tree_search: '请输入要搜索的组织结构名称',
    role_search: '请输入要搜索的角色名称',
    role_name_empty: '请输入角色名称',
    role_password_empty: '请输入密码',
    role_organization_empty: '请选择组织架构',
    account_search_name: '请输入要搜索的账号',
    account_search_role: '请选择角色',
    account_search_user_name: '请输入要搜索的用户名',
    account_search_status: '请选择状态',
    account_name_empty: '请输入账号',
    account_status_empty: '请选择状态',
    creator: '请输入创建人',
    push_company_id: '请选择接收商户',
    notice_title: '请输入公告标题',
    notice_type: '请输入公告类型',
    notice_content: '请输入公告内容',
    date: '请选择时间',
    notice_type_title: '请输入公告类型 / 标题'
  },
  dialog: {
    confirm_btn: '确 定',
    add_btn: '添 加',
    cancel_btn: '取 消',
    add_title: '新建',
    edit_title: '编辑',
    account_title: '查看账号',
    close_btn: '关 闭',
    tips_title: '提示',
    notice_dialog: '查看公告'
  },
  table: {
    // 公共字段
    create_time: '创建时间',
    operate: '操作',
    edit: '编辑',
    delete: '删除',
    status: '状态',
    // 组织架构独享
    organization_name: '组织架构名称',
    next_organization_name: '下级组织架构名称',
    organization_role_num: '角色数',
    organization_account_num: '账号数',
    organization_add: '增加组织架构',
    account_username: '账号',
    account_member_name: '用户名称',
    role_name: '角色名称',
    role_num: '角色数',
    account_num: '账号数',
    role_account_num: '关联账号数',
    account_organization: '所属组织架构',
    role_setting_btn: '权限设置',
    role_show_account_btn: '查看账号',
    account: '账号',
    l_address: '所属地区',
    // 公告
    post_time: '发送时间',
    notice_type: '公告类型',
    notice_title: '公告标题',
    post_status: '发送状态',
    receiving: '收件人',
    read_num: '阅读统计',
    creator: '创建人',
    department: '部门',
    account_department: '所属部门',
    mobile: '手机号'
  },
  message: {
    delete: '删除',
    loading: '加载中...',
    success: '成功',
    add_success: '添加成功',
    delete_success: '删除成功',
    role_select_empty: '请先选择要删除的数据！',
    role_delete_select: '是否删除所选角色',
    account_delete_select: '是否删除所选账号'
  },
  search: {
    all: '全部',
    btn: '搜索',
    role_add: '新增角色',
    export: '导出EXCEL',
    multi_del: '批量删除',
    account: '账号',
    account_user_name: '用户名称',
    status: '状态',
    account_add: '新增账号',
    operation_type: '操作类型',
    operation_staff: '操作人员',
    post_time: '发送时间',
    creator: '创建人',
    notice_type_title: '类型 / 标题'
  },
  button: {
    add_notice: '新建公告',
    upload_file: '点击上传'
  },
  error: {
    send_time: '发送时间不能早于当前时间'
  }
}

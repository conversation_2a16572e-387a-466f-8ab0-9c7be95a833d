import * as dayjs from 'dayjs'
// 前一天
const beforeDate = dayjs()
  .subtract(1, 'day')
  .format('YYYY/MM/DD')
const nowDate = dayjs()
  .format('YYYY/MM/DD')
// 前一天的pickerOptions
export const beforeRecentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs(beforeDate).format('YYYY-MM-DD')
]
// 当天
export const nowRecentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs(nowDate).format('YYYY-MM-DD')
]
export const pickerOptions = {
  disabledDate(time) {
    return time.getTime() > Date.now()
  },
  shortcuts: [
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        picker.$emit('pick', [start, end])
      }
    }
  ]
}
// 前一天的pickerOptions
export const berforePickerOptions = {
  disabledDate(time) {
    return time.getTime() > new Date(beforeDate + ' 23:59:59')
  },
  shortcuts: [
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date(beforeDate)
        const start = new Date(beforeDate)
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date(beforeDate)
        const start = new Date(beforeDate)
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date(beforeDate)
        const start = new Date(beforeDate)
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        picker.$emit('pick', [start, end])
      }
    }
  ]
}

// 当天的pickerOptions
export const nowPickerOptions = {
  disabledDate(time) {
    return time.getTime() > new Date(nowDate + ' 23:59:59')
  },
  shortcuts: [
    {
      text: '最近一周',
      onClick(picker) {
        const end = new Date(nowDate)
        const start = new Date(nowDate)
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const end = new Date(nowDate)
        const start = new Date(nowDate)
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        picker.$emit('pick', [start, end])
      }
    },
    {
      text: '最近三个月',
      onClick(picker) {
        const end = new Date(nowDate)
        const start = new Date(nowDate)
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        picker.$emit('pick', [start, end])
      }
    }
  ]
}

export const MEAL_TYPES = [
  { label: '早餐', value: 'breakfast', field: 'breakfast_ahead', field2: 'breakfast_fixed' },
  { label: '午餐', value: 'lunch', field: 'lunch_ahead', field2: 'lunch_fixed' },
  { label: '下午茶', value: 'afternoon', field: 'hit_tea_ahead', field2: 'hit_tea_fixed' },
  { label: '晚餐', value: 'dinner', field: 'dinner_ahead', field2: 'dinner_fixed' },
  { label: '夜宵', value: 'supper', field: 'midnight_ahead', field2: 'midnight_fixed' },
  { label: '凌晨餐', value: 'morning', field: 'early_ahead', field2: 'early_fixed' }
]

export const TAKE_MEAL_TYPES = [
  { label: '堂食', value: 'on_scene', field: 'on_scene_ahead', field2: 'on_scene_fixed' },
  { label: '外卖', value: 'waimai', field: 'waimai_ahead', field2: 'waimai_fixed' },
  { label: '食堂自提', value: 'bale', field: 'bale_ahead', field2: 'bale_fixed' },
  { label: '取餐柜', value: 'cupboard', field: 'cupboard_ahead', field2: 'cupboard_fixed' }
]

export const MEAT_TYPE_MAP = {
  早餐: 'breakfast',
  午餐: 'lunch',
  下午茶: 'afternoon',
  晚餐: 'dinner',
  夜宵: 'supper',
  凌晨餐: 'morning'
}
export const PRECHARGE_REFUND_TYPE = [
  { name: '不允许退款', key: 'not_allow' },
  { name: '只允许充值当天退款', key: 'today' },
  { name: '充值后自定义时间内退款', key: 'custom_day' }
]
export const NUTRITION_DATA_LIST = [
  // { name: '兆焦耳', key: 'energy_mj', unit: 'mj/kg', type: 'default' },
  // 用在菜谱中添加菜品营养分析 因为已经单独拿出来了
  { name: '千卡', key: 'energy_kcal', unit: 'kcal/kg', type: 'default' },
  { name: '碳水化合物', key: 'carbohydrate', unit: 'g', type: 'default' },
  { name: '蛋白质', key: 'protein', unit: 'g', type: 'default' },
  { name: '脂肪', key: 'axunge', unit: 'g', type: 'default' },
  { key: 'Ca', name: '钙', unit: 'mg' },
  { key: 'P', name: '磷', unit: 'mg' },
  { key: 'K', name: '钾', unit: 'mg' },
  { key: 'Na', name: '钠', unit: 'mg' },
  { name: '镁', key: 'Mg', unit: 'mg' },
  { key: 'Fe', name: '铁', unit: 'mg' },
  { key: 'I', name: '碘', unit: 'μg' },
  { key: 'Se', name: '硒', unit: 'μg' },
  { key: 'Zn', name: '锌', unit: 'mg' },
  { key: 'Cu', name: '铜', unit: 'mg' },
  { key: 'F', name: '氟', unit: 'mg' },
  { key: 'Cr', name: '铬', unit: 'μg' },
  { key: 'Mo', name: '钼', unit: 'μg' },
  { key: 'Mn', name: '锰', unit: 'mg' },
  { key: 'VA', name: '维生素A', unit: 'μg' },
  { key: 'VD', name: '维生素D', unit: 'μg' },
  { key: 'VE', name: '维生素E', unit: 'mg' },
  { key: 'VK', name: '维生素K', unit: 'μg' },
  { key: 'VB1', name: '维生素B1', unit: 'mg' },
  { key: 'VB2', name: '维生素B2', unit: 'mg' },
  { key: 'VB6', name: '维生素B6', unit: 'mg' },
  { key: 'VB12', name: '维生素B12', unit: 'μg' },
  { key: 'VC', name: '维生素C', unit: 'mg' },
  { key: 'VB5', name: '泛酸', unit: 'mg' },
  { key: 'VM', name: '叶酸', unit: 'μg' },
  { key: 'VB3', name: '烟酸', unit: 'mg' },
  { key: 'Choline', name: ' 胆碱', unit: 'mg' },
  // { key: 'Nicotinamide', name: '烟酰胺', unit: 'mg' },
  { key: 'VH', name: '生物素', unit: 'mg' }
]
export const TAKE_TYPE_LIST = [
  {
    label: '设备堂食',
    value: 'instore'
  },
  {
    label: '预约-堂食',
    value: 'on_scene'
  },
  {
    label: '预约-食堂自取',
    value: 'bale'
  },
  {
    label: '预约-取餐柜取餐',
    value: 'cupboard'
  },
  {
    label: '预约-外卖',
    value: 'waimai'
  },
  {
    label: '报餐',
    value: 'report'
  }
]

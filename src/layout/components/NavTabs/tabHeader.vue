<template>
  <div class="tabs-header-warpper" >
    <div class="tab-header">
      <div class="header-left">
        <!-- <div v-for="menu in navMenuList" :key="menu.key" :class="['client', 'item', activeHeaderMenu===menu.key?'active':'']" @click="clickNavHeaderTab(menu.key)">{{ menu.name }}</div> -->
        <!-- <div :class="['system', 'item', activeHeaderTab==='system'?'active':'']" @click="clickNavHeaderTab('system')">系统配置</div> -->
        <el-tabs v-model="activeHeaderTab" @tab-click="handleClick">
          <el-tab-pane v-for="menu in navMenuList" :key="menu.key"  :label="menu.name" :name="menu.key"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="header-right">
        <el-dropdown style="margin-right:15px;" @command="clickOrganizationHandle">
          <span>
            <i class="el-icon-office-building" style="margin-right: 5px"></i>
            <!-- <img src="@/assets/img/company.png" alt="user"> -->
            <span>{{ organizationName }}</span>
            <!-- <img src="@/assets/img/dropdown.png" alt="user"> -->
            <i class="el-icon-caret-bottom" style="margin-left: 5px"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="org in userInfo.organizationList" :key="org.id" :command="org.id" :class="{'is-select': organization === org.id}" >{{ org.name }}</el-dropdown-item>
            <!-- <el-dropdown-item>{{this.userInfo.company_name}}</el-dropdown-item> -->
            <!-- <el-dropdown-item>新增新增</el-dropdown-item>
            <el-dropdown-item>删除删除</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown style="margin-right:15px;" @command="handleCommand">
          <span>
            <i class="el-icon-s-custom" style="margin-right: 5px"></i>
            <!-- <img src="@/assets/img/user.png" alt="user"> -->
            <span></span>
            <span>{{this.userInfo.role_name}}</span>
            <!-- <img src="@/assets/img/dropdown.png" alt="user"> -->
            <i class="el-icon-caret-bottom" style="margin-left: 5px"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>{{this.userInfo.username}}</el-dropdown-item>
            <el-dropdown-item command='accountSetting'>账号设置</el-dropdown-item>
            <!-- <el-dropdown-item>删除删除</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <!-- <lang-select style="margin-right:8px;" /> -->
        <div
          v-if="userInfo.role_id != 1"
          class="nav-notice"
          @click="gotoNoticeList"
        >
          <span class="nav-h-icon">
            <img src="@/assets/img/notice.png" alt="logout" />
          </span>
          <div class="notice-num" v-if="noticeNum">{{ noticeNum }}</div>
        </div>
        <span class="logout nav-h-icon" @click="clickLogoutBtn">
          <img src="@/assets/img/logout.png" alt="logout">
        </span>
      </div>
    </div>
    <!-- 弹窗 -->
    <dialog-message
      message="确定退出登录吗？"
      :show.sync="showDialog"
      :loading.sync="loading"
      @close="closeDialogHandle"
      @confirm="logoutHandle"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex"
// import LangSelect from '@/components/LangSelect'
import { to } from '@/utils'
export default {
  name: 'tabHeaser',
  components: {
    // LangSelect
  },
  inject: ['reload'],
  data() {
    return {
      activeHeaderTab: '',
      loading: false,
      showDialog: false,
      noticeNum: 0,
      organizationName: '' // 当前登录的组织名
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'navMenuList',
      'activeNavMenu',
      'addRoutes',
      'visitedViews',
      'organization'
    ])
    // activeHeaderTab() {
    //   return this.activeHeaderMenu
    // }
  },
  watch: {
    activeNavMenu(val) {
      if (this.activeHeaderTab !== val) {
        this.activeHeaderTab = val
      }
    },
    organization(val) {
      this.setOrganizationName()
    }
  },
  created() {
    this.setOrganizationName()
  },
  async mounted () {
    this.activeHeaderTab = this.activeNavMenu
    if (this.userInfo.role_id !== 1) {
      this.getMsgNum()
      this.$root.eventHub.$on("updateNoticeCount", (noticeNum) => {
        this.noticeNum = noticeNum
      });
    }
  },
  methods: {
    handleClick() {
      this.clickNavHeaderTab(this.activeHeaderTab)
    },
    async clickNavHeaderTab(type, to = true) {
      await this.$store.dispatch('navTabs/setActiveNavMenu', type)
      // await this.$sleep(5000)
      // this.$store.dispatch('navTabs/upadtePermissions')
      // 切换顶部菜单时默认跳回第一条
      // let currentPath = ''
      for (let index = 0; index < this.navMenuList.length; index++) {
        let item = this.navMenuList[index];
        if (item.key === type) {
          if (item.activePath) {
            if (this.$route.path !== item.activePath) { // 新版vue-router如何跳转到同一个地址会报错的，做个判断吧
              // this.$router.push({
              //   path: item.activePath
              // })
              this.queryStoreVisitedViews(item, 'activePath')
            }
          } else if (item.indexPath) {
            if (this.$route.path !== item.indexPath) {
              // this.$router.push({
              //   path: item.indexPath
              // })
              this.queryStoreVisitedViews(item, 'indexPath')
            }
          } else {
            if (this.$route.path !== '/error/404') {
              this.$router.push({
                path: '/error/404'
              })
            }
          }
        }
      }
      this.$store.dispatch('navTabs/upadtePermissions')
    },
    // 查询下vuex里面是否有这个路由的数据，有就直接用
    queryStoreVisitedViews(item, pathKey) {
      let hasTab = false
      let currentTab = null
      for (let index = 0; index < this.visitedViews.length; index++) {
        currentTab = this.visitedViews[index];
        if (currentTab.path === item[pathKey]) {
          hasTab = true
          break
        }
      }
      if (hasTab) {
        this.$router.push(currentTab)
      } else {
        this.$router.push({
          path: item[pathKey]
        })
      }
    },
    clickLogoutBtn() {
      this.showDialog = true
    },
    async logoutHandle(e) {
      const [err, res] = await this.$to(this.$apis.apiBackgroundLogoutPost())
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        await this.$store.dispatch('user/logout')
        this.$message.success('退出登录成功！')
        this.loading = false
        this.showDialog = false
        this.$router.push('/login')
        // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      } else {
        this.$message.error(res.msg)
      }
    },
    closeDialogHandle(e) {},
    gotoNoticeList() {
      this.$router.push({
        name: 'NoticeList',
        query: {
          // un_read: this.noticeNum
        }
      })
    },
    async getMsgNum() {
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgNumPost({}))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (res.data && res.data.unread_count) {
          this.noticeNum = res.data.unread_count > 99 ? '99+' : res.data.unread_count
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handleCommand(command) {
      if (command === 'accountSetting') {
        this.$router.push({
          name: 'AccountSetting'
        })
      }
    },
    setOrganizationName() {
      if (this.userInfo && !this.userInfo.organizationList) return
      this.userInfo.organizationList.forEach(item => {
        if (this.organization === item.id) {
          this.organizationName = item.name
        }
      });
    },
    // 点击选择组织
    async clickOrganizationHandle(e) {
      if (this.organization === e) return
      this.$confirm(`确定切换组织？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // instance.cancelButtonLoading = true
            if (this.isLoading) return this.$message.error('请勿重复提交！')
            this.isLoading = true
            const [err, res] = await to(this.$apis.apiBackgroundBindOrgPost({
              org_no: e
            }))
            this.isLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              sessionStorage.setItem('isChoiceOrg', 1)
              this.$store.dispatch('user/setOrganization', e)

              // 更新路由
              const permissions = await this.$store.dispatch('user/getPermissionList', { key: '' })
              await this.$store.dispatch('permission/generateRoutes', permissions)
              // 跳转到第一个页面
              let item = this.navMenuList[0]
              if (item.activePath) {
                if (this.$route.path !== item.activePath) { // 新版vue-router如何跳转到同一个地址会报错的，做个判断吧
                  this.queryStoreVisitedViews(item, 'activePath')
                  this.$store.dispatch('navTabs/delAllVisitedViews') // 关闭所有已打开的页面
                } else { // 同一个地址，只关闭其他页面
                  this.$store.dispatch('navTabs/delOthersVisitedViews') // 关闭其他的页面
                }
              } else if (item.indexPath) {
                if (this.$route.path !== item.indexPath) {
                  this.queryStoreVisitedViews(item, 'indexPath')
                  this.$store.dispatch('navTabs/delAllVisitedViews') // 关闭所有已打开的页面
                } else { // 同一个地址，只关闭其他页面
                  this.$store.dispatch('navTabs/delOthersVisitedViews') // 关闭其他的页面
                }
              } else {
                if (this.$route.path !== '/error/404') {
                  this.$router.push({
                    path: '/error/404'
                  })
                }
              }

              // 刷新当前页面的数据
              this.reload()

              this.$message.success(res.msg)
              done()
            } else {
              this.$message.error(res.msg)
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss">
.tabs-header-warpper {
  .tab-header {
    .nav-notice {
      display: inline;
      position: relative;
      margin-right: 10px;
      cursor: pointer;

      .notice-num {
        height: 15px;
        background-color: red;
        color: #fff;
        border-radius: 10px;
        position: absolute;
        top: -7px;
        left: 20px;
        line-height: 16px;
        font-size: 12px;
        padding: 0 4px;
      }
    }
  }
}
.el-dropdown-menu{
  .is-select{
    color: #ff9b45;
  }
}
</style>
